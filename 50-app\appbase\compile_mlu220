#!/bin/sh
cd prj_linux
make -f makefile_appbase_mlu220_release clean
make -f makefile_appbase_mlu220_release 2>&1 |tee ../../../10-common/version/compileinfo/appbase_mlu220_release.txt

make -f makefile_appbase_mlu220_no_service_no_xml_release clean
make -f makefile_appbase_mlu220_no_service_no_xml_release 2>&1 |tee ../../../10-common/version/compileinfo/appbase_mlu220_no_service_no_xmlrelease.txt

cd ../