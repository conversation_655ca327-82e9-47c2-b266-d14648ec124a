# Zoom操作常驻线程优化说明

## 概述
按照C89标准优化了zoom操作逻辑，创建常驻的数字变倍线程来处理数字变倍操作，实现平滑的数字变倍效果。线程在系统初始化时创建并一直存在，通过控制变量来激活/停止数字变倍操作。每秒执行5次NvrFixMcSetCrop调用，每次偏移50个单位。

## 主要修改

### 1. 新增数字变倍常驻线程相关函数
- **`NvrFixDevDigitalZoomThread`**: 常驻数字变倍线程主函数，等待操作指令并执行数字变倍
- **`NvrFixDevInitDigitalZoomThread`**: 初始化并创建常驻数字变倍线程
- **`NvrFixDevSetDigitalZoomOperation`**: 设置数字变倍操作状态

### 2. 修改 `NvrFixDevHandleZoomOperation` 函数
- **位置**: `41-servicefix/nvrfixcore/source/nvrfixdev.c` 第10213行
- **功能**: 处理zoom操作，判断是否需要激活数字变倍操作
- **参数**: `ENvrPtzCtrlType eCtrlType` - zoom控制类型, `BOOL32 bPre` - 预检查标志

### 3. 新增全局变量管理线程状态
- **`g_hDigitalZoomTask`**: 数字变倍线程句柄
- **`g_bDigitalZoomThreadExit`**: 线程退出标志（用于系统关闭时）
- **`g_bDigitalZoomActive`**: 数字变倍操作激活状态
- **`g_eDigitalZoomType`**: 当前数字变倍类型

### 4. 修改ZOOMSTOP处理逻辑
- **位置**: `41-servicefix/nvrfixcore/source/nvrfixdev.c` 第7699行
- **修改**: 在ZOOMSTOP时调用`NvrFixDevSetDigitalZoomOperation(NVR_PTZCTRL_TYPE_ZOOMSTOP)`停止数字变倍操作

### 5. 系统初始化时创建线程
- **位置**: `41-servicefix/nvrfixcore/source/nvrfixdev.c` 第5324行
- **修改**: 在模块初始化时调用`NvrFixDevInitDigitalZoomThread()`创建常驻线程

## 实现逻辑

### 常驻线程数字变倍逻辑
1. **线程初始化**: 系统启动时创建常驻数字变倍线程，线程一直运行等待操作指令
2. **阈值判断**: 当前物理zoom位置 >= 2342时启用数字变倍操作
3. **操作激活**: ZOOMTELE/ZOOMWIDE操作时设置`g_bDigitalZoomActive = TRUE`激活数字变倍
4. **平滑处理**: 线程以200ms间隔连续调用`NvrFixMcSetCrop`，每秒执行5次
5. **步长控制**: 每次循环增加/减少50个单位（优化后的步长）
6. **画布操作**: 在10000×10000的画布上进行裁剪
7. **裁剪计算**:
   - 起始坐标: (posDiff, posDiff)
   - 裁剪尺寸: (10000-2*posDiff, 10000-2*posDiff)
8. **操作停止**: ZOOMSTOP操作时设置`g_bDigitalZoomActive = FALSE`停止数字变倍操作
9. **线程等待**: 无操作时线程进入等待状态，不消耗CPU资源

### 光学变倍逻辑
- 当物理zoom位置 < 2342时，继续使用原有的光学变倍逻辑

## C89标准兼容性

### 变量声明
- ✅ 所有变量在函数开始处声明
- ✅ 使用const关键字定义常量
- ✅ 静态变量使用static关键字
- ✅ 避免在代码块中间声明变量

### 函数设计
- ✅ 函数参数类型明确
- ✅ 返回值类型明确
- ✅ 使用标准C库函数
- ✅ 函数声明和定义分离

### 代码风格
- ✅ 遵循原有代码风格
- ✅ 添加详细注释
- ✅ 使用标准的条件判断和循环结构
- ✅ 使用C风格注释 /* */

### 具体改进
1. **变量声明顺序**: 将所有局部变量声明移到函数开始处
2. **初始化分离**: 变量声明和初始化分开进行
3. **类型转换**: 明确的类型转换 `(u16)s_nPosDiff`
4. **常量定义**: 使用const关键字定义常量值

## 调用流程

```
系统初始化
    ↓
NvrFixDevInitDigitalZoomThread()
    ↓
创建常驻数字变倍线程 (一直运行，等待操作指令)

zoom操作触发 (ZOOMTELE/ZOOMWIDE)
    ↓
NvrFixDevHandleZoomOperation()
    ↓
获取当前zoom位置 (IspActionParam)
    ↓
判断位置 >= 2342?
    ↓
是: 激活数字变倍操作 (NvrFixDevSetDigitalZoomOperation)
    ↓
常驻线程检测到激活状态，开始循环执行:
    - 调整posDiff值 (每次±50)
    - 调用NvrFixMcSetCrop
    - 延时200ms (每秒5次)
    - 检查激活状态
否: 光学变倍 (原有逻辑)

ZOOMSTOP操作触发
    ↓
NvrFixDevSetDigitalZoomOperation(ZOOMSTOP)
    ↓
设置g_bDigitalZoomActive = FALSE
    ↓
常驻线程停止数字变倍操作，进入等待状态
```

## 测试建议

1. **功能测试**:
   - 测试zoom位置在2342以下时的光学变倍
   - 测试zoom位置在2342以上时的数字变倍线程启动
   - 测试ZOOMSTOP操作时线程正确停止
   - 测试zoom wide操作时posDiff不会小于0

2. **线程测试**:
   - 测试数字变倍线程的启动和停止
   - 测试线程运行时的平滑效果
   - 测试多次快速zoom操作时的线程管理
   - 测试线程异常退出的处理

3. **边界测试**:
   - 测试zoom位置正好等于2342时的行为
   - 测试连续zoom操作的累积效果
   - 测试裁剪区域的边界值
   - 测试线程资源的正确释放

4. **性能测试**:
   - 验证线程创建和销毁的开销
   - 验证50ms间隔的平滑效果
   - 验证内存使用情况
   - 验证响应时间

## 核心代码示例

### 常驻数字变倍线程
```c
static void* NvrFixDevDigitalZoomThread(void* param)
{
    /* C89标准：所有变量声明必须在函数开始处 */
    static s32 s_nPosDiff = 0;
    u16 wCropWidth;
    u16 wCropHeight;
    const s32 ZOOM_STEP = 50;        /* 每次偏移50 */
    const u32 CANVAS_SIZE = 10000;
    const u32 ZOOM_INTERVAL_MS = 200; /* 200ms间隔，每秒5次 */

    while (!g_bDigitalZoomThreadExit)
    {
        /* 检查是否有数字变倍操作激活 */
        if (g_bDigitalZoomActive)
        {
            /* 根据zoom类型调整posDiff */
            if (g_eDigitalZoomType == NVR_PTZCTRL_TYPE_ZOOMTELE)
            {
                s_nPosDiff += ZOOM_STEP;
            }
            else if (g_eDigitalZoomType == NVR_PTZCTRL_TYPE_ZOOMWIDE)
            {
                s_nPosDiff -= ZOOM_STEP;
                if (s_nPosDiff < 0)
                {
                    s_nPosDiff = 0;
                    g_bDigitalZoomActive = FALSE; /* 自动停止 */
                }
            }

            /* 执行数字变倍 */
            if (g_bDigitalZoomActive && s_nPosDiff > 0)
            {
                wCropWidth = CANVAS_SIZE - 2 * s_nPosDiff;
                wCropHeight = CANVAS_SIZE - 2 * s_nPosDiff;

                if (wCropWidth > 0 && wCropHeight > 0)
                {
                    NvrFixMcSetCrop(1, 0, (u16)s_nPosDiff, (u16)s_nPosDiff,
                                   wCropWidth, wCropHeight);
                }
            }

            /* 延时200ms，每秒执行5次 */
            OsApi_Delay(ZOOM_INTERVAL_MS);
        }
        else
        {
            /* 没有激活操作时，延时等待 */
            OsApi_Delay(100);
        }
    }
    return NULL;
}
```

### 操作控制函数
```c
static void NvrFixDevSetDigitalZoomOperation(ENvrPtzCtrlType eCtrlType)
{
    if (eCtrlType == NVR_PTZCTRL_TYPE_ZOOMTELE || eCtrlType == NVR_PTZCTRL_TYPE_ZOOMWIDE)
    {
        /* 激活数字变倍操作 */
        g_eDigitalZoomType = eCtrlType;
        g_bDigitalZoomActive = TRUE;
    }
    else if (eCtrlType == NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        /* 停止数字变倍操作 */
        g_bDigitalZoomActive = FALSE;
    }
}
```

## 注意事项

1. **常驻线程**: 线程在系统初始化时创建，一直存在不销毁，减少线程创建/销毁开销
2. **状态控制**: 使用`g_bDigitalZoomActive`控制操作激活状态，线程根据此状态决定是否执行操作
3. **静态变量**: `s_nPosDiff`使用静态变量保持状态，确保多次调用时的连续性
4. **边界检查**: 确保裁剪区域不会过小或为负值，自动停止操作
5. **优化参数**: 每次偏移50个单位，每秒执行5次，提供更精细的控制
6. **资源效率**: 无操作时线程进入等待状态，不消耗CPU资源
7. **调试信息**: 添加了详细的调试输出，便于问题排查
8. **向后兼容**: 保持了原有接口的兼容性
9. **C89兼容**: 严格遵循C89标准，所有变量在函数开始处声明

## 优势

1. **高效资源利用**: 常驻线程避免频繁创建/销毁线程的开销
2. **精细控制**: 每次偏移50个单位，提供更精细的数字变倍控制
3. **平滑体验**: 每秒5次调用确保数字变倍平滑自然
4. **响应性**: 主线程不被阻塞，提高系统响应性
5. **可控性**: 可以随时通过ZOOMSTOP停止数字变倍操作
6. **稳定性**: 常驻线程设计提高系统稳定性
