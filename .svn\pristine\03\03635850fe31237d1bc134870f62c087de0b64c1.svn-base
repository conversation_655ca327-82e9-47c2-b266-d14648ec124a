#ifndef _KNET_CMD_DHCPC_H
#define _KNET_CMD_DHCPC_H

typedef struct {
	bool mDhcpcIsRun;
} dhcpc_stat_t;

typedef struct {
	uint32_t mEthId;
	uint32_t mDuration;
	char     mIpAddr[IP_ADDR_SIZE];
	char     mGateway[IP_ADDR_SIZE];
	uint8_t  mNetmask;
	int8_t   mDnsNum;
	char     mDnsServer[KNET_DNS_MAX_DNS_SERVER][IP_ADDR_SIZE];
} dhcpc_lease_info_t;


typedef int(*dhcpc_cb_t)(uint32_t msgId, void* msgData);

int32_t DhcpcStart(uint32_t ethId, dhcpc_cb_t invokeRoutine, bool setDefaultGateway);
int32_t DhcpcStop(uint32_t ethId);
int32_t DhcpcGetLeaseInfo(uint32_t ethId, dhcpc_lease_info_t* leaseInfo);
int32_t DhcpcGetIfaceStat(uint32_t ethId, dhcpc_stat_t* state);

#endif
