/**
* @file     nvrdhcpserver.proto
* @brief    nvr dhcp server proto
* <AUTHOR>
* @date     2020-09-16
* @version  1.0
* @copyright V1.0  Copyright(C) 2020 NVR All rights reserved.
*/
//省略package

/**网络地址*/
message TPbNvrNetDhcpAddr
{
    optional uint32 IP_Type = 1;        ///<IP类型 must be AF_INET or AF_INET6
    optional bytes IP_V4_Or_V6 = 2;    ///根据ip类型选择IPV4 or IPV6

}

/**蓝牙配置信息，包括以配对蓝牙设备信息*/
message TPbNvrDhcpServerParam		
{
    optional uint32 Enable = 1;              						///<dhco server是否使能
	optional uint32 address_lease = 2;							///<地址租期（单位m）
	optional TPbNvrNetDhcpAddr dhcp_server_Start = 6;           ///<分配起始地址
	optional TPbNvrNetDhcpAddr dhcp_server_End = 7;            ///<分配结束地址   
}