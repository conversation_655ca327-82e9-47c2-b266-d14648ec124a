path="../../10-common/version/compileinfo/nvrlib_his3531dv200.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_usrmgr_linux for his3531dv200          =
echo ==============================================

echo "============compile libnvrusrmgr his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_his3531dv200 clean
make -e DEBUG=0 -f makefile_his3531dv200 2>>../$path

cp -L -r -f libnvrusrmgr.so ../../../10-common/lib/release/his3531dv200/

echo "============compile libnvrusrmgr_ipdt his3531dv200============">>../$path

make -e DEBUG=0 STATIC=0 -f makefile_his3531dv200_ipdt clean
make -e DEBUG=0 STATIC=0 -f makefile_his3531dv200_ipdt 2>>../$path

make -e DEBUG=0 STATIC=1 -f makefile_his3531dv200_ipdt clean
make -e DEBUG=0 STATIC=1 -f makefile_his3531dv200_ipdt 2>>../$path

cp -L -r -f libnvrusrmgr_ipdt.so ../../../10-common/lib/release/his3531dv200/
cp -L -r -f libnvrusrmgr_ipdt.a ../../../10-common/lib/release/his3531dv200/
cd ..
