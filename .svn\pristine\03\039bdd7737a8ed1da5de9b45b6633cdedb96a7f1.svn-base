#ifndef _CAR_COLOR_H_
#define _CAR_COLOR_H_

#include "VideoAlg.h"


//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagCarColorOpen
{
    EMImageFormat emFormat;
    char s8GPUBinPath[200];
    char s8CarColorBinPath[200];
}TKEDACarColorOpen;


//INPUT for TImageInfo.pvImageInfo
// roi info to crop car
typedef struct tagCarcolorROI
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
}TCarcolorROI;


//OUTPUT for TImageInfo.pvImageInfo
//输出车颜色相关信息
typedef struct tagCarcolorInfo
{
	unsigned char pvColor[200];
} TCarcolorInfo;


#endif