path="../../10-common/version/compileinfo/nvrlib_qcom.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_usrmgr_linux for qcom          =
echo ==============================================

###echo "============compile libnvrusrmgr qcom============">>../$path

###make -e DEBUG=0 -f makefile_qcom clean
###make -e DEBUG=0 -f makefile_qcom 2>>../$path

###cp -L -r -f libnvrusrmgr.so ../../../10-common/lib/release/qcom/

echo "============compile libnvrusrmgr_ipdt qcom============">>../$path

make -e DEBUG=0 STATIC=0 -f makefile_qcom_ipdt clean
make -e DEBUG=0 STATIC=0 -f makefile_qcom_ipdt 2>>../$path

make -e DEBUG=0 STATIC=1 -f makefile_qcom_ipdt clean
make -e DEBUG=0 STATIC=1 -f makefile_qcom_ipdt 2>>../$path

cp -L -r -f libnvrusrmgr_ipdt.so ../../../10-common/lib/release/qcom/
cp -L -r -f libnvrusrmgr_ipdt.a ../../../10-common/lib/release/qcom/
cd ..
