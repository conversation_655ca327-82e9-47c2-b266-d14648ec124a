path="../../10-common/version/compileinfo/nvrlib_ssr621q.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_alarm_linux for ssr621q      =
echo ==============================================

echo "============compile libnvralarm ssr621q============">>../$path

make -e DEBUG=0 -f makefile_ssr621q clean
make -e DEBUG=0 -f makefile_ssr621q 2>>../$path

cp -L -r -f libnvralarm.so ../../../10-common/lib/release/ssr621q/

cd ..
