#!/bin/sh
#set -x
path="../../10-common/version/compileinfo/nvrlib_his3519av100.txt"
date>>$path

module_name=$(basename $PWD)
cd ./prj_linux

echo ==============================================
echo =      "$module_name"_linux for his3519av100           =
echo ==============================================

echo "============compile lib$module_name his3519av100============">>../$path

make -e DEBUG=0 -f makefile_his3519av100 clean
make -e DEBUG=0 -f makefile_his3519av100 2>&1 1>/dev/null |tee -a ../$path


cd ..

