#ifndef _CAR_MODEL_H_
#define _CAR_MODEL_H_

#include "VideoAlg.h"


//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagCarModelOpen
{
    EMImageFormat emFormat;
    char s8GPUBinPath[200];
    char s8CarModelBinPath[200];
}TKEDACarModelOpen;


//INPUT for TImageInfo.pvImageInfo
// roi info to crop car
typedef struct tagCarmodelROI
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
}TCarmodelROI;


//OUTPUT for TImageInfo.pvImageInfo
//输出车型相关信息
typedef struct tagCarmodelInfo
{
	unsigned char pvModel[200]; 
	unsigned char pvSubmodel[200]; 
	unsigned char pvYearmodel[200]; 
} TCarmodelInfo;


#endif