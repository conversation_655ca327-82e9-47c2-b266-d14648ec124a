#!/bin/sh
#set -x
path="../../10-common/version/compileinfo/nvrlib_his3531dv200.txt"
date>>$path

module_name=$(basename $PWD)
cd ./prj_linux

echo ==============================================
echo =      "$module_name"_linux for his3531dv200           =
echo ==============================================

echo "============compile lib$module_name his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_his3531dv200 clean
make -e DEBUG=0 -f makefile_his3531dv200 2>&1 1>/dev/null |tee -a ../$path

#cp -L -r -f libnvrsys.so ../../../10-common/lib/release/his3531dv200/

cd ..

