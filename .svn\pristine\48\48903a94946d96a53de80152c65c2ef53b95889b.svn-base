/***********************************************************************
Filename   : rvcbasedefs.h
Description: cbase configuration definitions
************************************************************************
        Copyright (c) 2001 RADVISION Inc. and RADVISION Ltd.
************************************************************************
NOTICE:
This document contains information that is confidential and proprietary
to RADVISION Inc. and RADVISION Ltd.. No part of this document may be
reproduced in any form whatsoever without written prior approval by
RADVISION Inc. or RADVISION Ltd..

RADVISION Inc. and RADVISION Ltd. reserve the right to revise this
publication and make changes without obligation to notify any person of
such revisions or changes.
***********************************************************************/
#ifndef RV_CBASEDEFS_H
#define RV_CBASEDEFS_H

/* rvqueue: RV_QUEUE_TYPE - Select queue type to use */
#define RV_QUEUE_STANDARD 0
#define RV_QUEUE_NONE 1

/* rvtimer: RV_TIMER_TYPE - Select timer type to use */
#define RV_TIMER_STANDARD 0

/* rvtimerengine: RV_TIMERENGINE_TYPE - Select timer engine type to use */
#define RV_TIMERENGINE_STANDARD 0

#endif /* RV_CBASEDEFS_H */
