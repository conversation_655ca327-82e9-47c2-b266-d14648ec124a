#ifndef _RESPONSECODE_H
#define _RESPONSECODE_H

/*
 * This class is designed to unify return-value of KdcNetManager-APIs,
 * And each sub-object could occupy up to 99 codes
 */
class ResponseCode {
    static const int32_t KnetErrBase                  = 1000;
    static const int32_t E1CodeBase                   = 1100;

public:
    /** knet exec success */
    static const int32_t CommandOkay                  = 0000;

	/** knet common error code */
    static const int32_t OperationFailed              = KnetErrBase + 1;
    static const int32_t CommandParameterError        = KnetErrBase + 2;
    static const int32_t JNIException                 = KnetErrBase + 3;
    static const int32_t CommandTimeout               = KnetErrBase + 4;
    static const int32_t NoCommand                    = KnetErrBase + 5;
    static const int32_t RemoteDead                   = KnetErrBase + 6;

	/** Pppoe没有运行 */
    static const int32_t PppoeNotRuning               = KnetErrBase + 7;

	/** Dhcpc进程不存在 */
    static const int32_t NetProcessNotExist           = KnetErrBase + 8;


	/** NetUtil NetPing回调函数没有定义 */
    static const int32_t NetCallbackNull                   = KnetErrBase + 9;
	/** NetUtil NetPing创建回调线程失败 */
    static const int32_t NetPingCreatCbThreadFailed        = KnetErrBase + 10;
	/** NetUtil NetPing启动失败 */
    static const int32_t NetPingStartFailed                = KnetErrBase + 11;
	/** NetUtil NetPing终止失败 */
    static const int32_t NetPingStopFailed                 = KnetErrBase + 12;
	/** NetUtil NetPing Traceroute创建回调线程失败 */
    static const int32_t NetTraceRouteCreatCbThreadFailed  = KnetErrBase + 13;
	/** NetUtil NetPing Traceroute创建信号量失败 */
    static const int32_t NetTraceRouteCreatSemFailed       = KnetErrBase + 14;
	/** NetUtil NetPing Traceroute启动失败 */
    static const int32_t NetTraceRouteStartFailed          = KnetErrBase + 15;
	/** NetUtil NetPing Traceroute终止失败 */
    static const int32_t NetTraceRouteStopFailed           = KnetErrBase + 16;

    /* write socket error */
    static const int32_t SendDataError                     = KnetErrBase + 17;

	/** E1未知错误 */
    static const int32_t E1Unknown                    = E1CodeBase + 0;
	/** E1参数异常 */
    static const int32_t E1ParamException             = E1CodeBase + 1;
	/** E1 SERIAL同步接口号无效 */
    static const int32_t E1SerialIdInvalid            = E1CodeBase + 2;
	/** E1接口号无效 */
    static const int32_t E1E1IdInvalid                = E1CodeBase + 3;
	/** E1时隙掩码无效 */
    static const int32_t E1E1TsmaskInvalid            = E1CodeBase + 4;
	/** E1时隙分配所在的编组号无效 */
    static const int32_t E1E1ChangroupIdInvalid       = E1CodeBase + 5;
	/** E1接口协议封装类型无效 */
    static const int32_t E1SerialProtocolTypeInvalid  = E1CodeBase + 6;
	/** E1同步接口的echo请求时间间隔无效 */
    static const int32_t E1SerialEchoIntervalInvalid  = E1CodeBase + 7;
	/** E1同步接口的echo最大重发次数无效 */
    static const int32_t E1SerialEchoMaxretryInvalid  = E1CodeBase + 8;
	/** E1字符串长度溢出 */
    static const int32_t E1StringLenOverflow          = E1CodeBase + 9;
	/** E1 nip接口调用失败 */
    static const int32_t E1NipOptErr                  = E1CodeBase + 10;
	/** E1 指定的通道号未打开使用 */
    static const int32_t E1ChanNotConf                = E1CodeBase + 11;
	/** E1 通道号冲突，指定的通道号已被使用 */
    static const int32_t E1ChanConflict               = E1CodeBase + 12;
	/** E1 PPP链接的验证方法类型无效 */
    static const int32_t E1MpFuthenTypeInvalid        = E1CodeBase + 13;
	/** E1 最小分片包长无效 */
    static const int32_t E1MpFragMinPackageLenInvalid = E1CodeBase + 14;
};
#endif
