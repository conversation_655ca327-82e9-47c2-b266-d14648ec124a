/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrmbnetwork.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrmbnetwork.pb-c.h"
void   tpb_nvr_net_apn_param__init
                     (TPbNvrNetApnParam         *message)
{
  static TPbNvrNetApnParam init_value = TPB_NVR_NET_APN_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_apn_param__get_packed_size
                     (const TPbNvrNetApnParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_apn_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_apn_param__pack
                     (const TPbNvrNetApnParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_apn_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_apn_param__pack_to_buffer
                     (const TPbNvrNetApnParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_apn_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetApnParam *
       tpb_nvr_net_apn_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetApnParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_apn_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_apn_param__free_unpacked
                     (TPbNvrNetApnParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_apn_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_vpn_list_param__init
                     (TPbNvrNetVpnListParam         *message)
{
  static TPbNvrNetVpnListParam init_value = TPB_NVR_NET_VPN_LIST_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_vpn_list_param__get_packed_size
                     (const TPbNvrNetVpnListParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_list_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_vpn_list_param__pack
                     (const TPbNvrNetVpnListParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_list_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_vpn_list_param__pack_to_buffer
                     (const TPbNvrNetVpnListParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_list_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetVpnListParam *
       tpb_nvr_net_vpn_list_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetVpnListParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_vpn_list_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_vpn_list_param__free_unpacked
                     (TPbNvrNetVpnListParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_list_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_vpn_param__init
                     (TPbNvrNetVpnParam         *message)
{
  static TPbNvrNetVpnParam init_value = TPB_NVR_NET_VPN_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_vpn_param__get_packed_size
                     (const TPbNvrNetVpnParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_vpn_param__pack
                     (const TPbNvrNetVpnParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_vpn_param__pack_to_buffer
                     (const TPbNvrNetVpnParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetVpnParam *
       tpb_nvr_net_vpn_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetVpnParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_vpn_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_vpn_param__free_unpacked
                     (TPbNvrNetVpnParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_vpn_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_flow_monitor_param__init
                     (TPbNvrFlowMonitorParam         *message)
{
  static TPbNvrFlowMonitorParam init_value = TPB_NVR_FLOW_MONITOR_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_flow_monitor_param__get_packed_size
                     (const TPbNvrFlowMonitorParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_flow_monitor_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_flow_monitor_param__pack
                     (const TPbNvrFlowMonitorParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_flow_monitor_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_flow_monitor_param__pack_to_buffer
                     (const TPbNvrFlowMonitorParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_flow_monitor_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrFlowMonitorParam *
       tpb_nvr_flow_monitor_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrFlowMonitorParam *)
     protobuf_c_message_unpack (&tpb_nvr_flow_monitor_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_flow_monitor_param__free_unpacked
                     (TPbNvrFlowMonitorParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_flow_monitor_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mb_net_cfg__init
                     (TPbNvrMbNetCfg         *message)
{
  static TPbNvrMbNetCfg init_value = TPB_NVR_MB_NET_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mb_net_cfg__get_packed_size
                     (const TPbNvrMbNetCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mb_net_cfg__pack
                     (const TPbNvrMbNetCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mb_net_cfg__pack_to_buffer
                     (const TPbNvrMbNetCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMbNetCfg *
       tpb_nvr_mb_net_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMbNetCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mb_net_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mb_net_cfg__free_unpacked
                     (TPbNvrMbNetCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_router_service__init
                     (TPbNvrNetRouterService         *message)
{
  static TPbNvrNetRouterService init_value = TPB_NVR_NET_ROUTER_SERVICE__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_router_service__get_packed_size
                     (const TPbNvrNetRouterService *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_router_service__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_router_service__pack
                     (const TPbNvrNetRouterService *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_router_service__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_router_service__pack_to_buffer
                     (const TPbNvrNetRouterService *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_router_service__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouterService *
       tpb_nvr_net_router_service__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouterService *)
     protobuf_c_message_unpack (&tpb_nvr_net_router_service__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_router_service__free_unpacked
                     (TPbNvrNetRouterService *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_router_service__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mb_net_cfg_all__init
                     (TPbNvrMbNetCfgAll         *message)
{
  static TPbNvrMbNetCfgAll init_value = TPB_NVR_MB_NET_CFG_ALL__INIT;
  *message = init_value;
}
size_t tpb_nvr_mb_net_cfg_all__get_packed_size
                     (const TPbNvrMbNetCfgAll *message)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg_all__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mb_net_cfg_all__pack
                     (const TPbNvrMbNetCfgAll *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg_all__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mb_net_cfg_all__pack_to_buffer
                     (const TPbNvrMbNetCfgAll *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg_all__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMbNetCfgAll *
       tpb_nvr_mb_net_cfg_all__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMbNetCfgAll *)
     protobuf_c_message_unpack (&tpb_nvr_mb_net_cfg_all__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mb_net_cfg_all__free_unpacked
                     (TPbNvrMbNetCfgAll *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mb_net_cfg_all__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_net_apn_param__field_descriptors[24] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetApnParam, has_enable),
    offsetof(TPbNvrNetApnParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Auto_Apn",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetApnParam, has_auto_apn),
    offsetof(TPbNvrNetApnParam, auto_apn),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Agent_Port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_agent_port),
    offsetof(TPbNvrNetApnParam, agent_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MMS_Port",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_mms_port),
    offsetof(TPbNvrNetApnParam, mms_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MCC",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_mcc),
    offsetof(TPbNvrNetApnParam, mcc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MNC",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_mnc),
    offsetof(TPbNvrNetApnParam, mnc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Auth_Type",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_apn_auth_type),
    offsetof(TPbNvrNetApnParam, apn_auth_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Proto",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_apn_proto),
    offsetof(TPbNvrNetApnParam, apn_proto),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Roma_Proto",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_apn_roma_proto),
    offsetof(TPbNvrNetApnParam, apn_roma_proto),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Ap_MVMO",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_ap_mvmo),
    offsetof(TPbNvrNetApnParam, ap_mvmo),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Name",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrNetApnParam, has_apn_name),
    offsetof(TPbNvrNetApnParam, apn_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Name_Len",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_apn_name_len),
    offsetof(TPbNvrNetApnParam, apn_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "APN",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, apn),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Agent",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, agent),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Name",
    15,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, usr_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Pwd",
    16,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, usr_pwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Server",
    17,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, server),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MMSC",
    18,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, mmsc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PPP_Dial_Number",
    19,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, ppp_dial_number),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MMS_Agent",
    20,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, mms_agent),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "APN_Type",
    21,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, apn_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_MVNO",
    22,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetApnParam, apn_mvno),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Bearer_Type",
    23,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, has_apn_bearer_type),
    offsetof(TPbNvrNetApnParam, apn_bearer_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Apn_Bear_Type",
    24,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetApnParam, n_apn_bear_type),
    offsetof(TPbNvrNetApnParam, apn_bear_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_apn_param__field_indices_by_name[] = {
  12,   /* field[12] = APN */
  20,   /* field[20] = APN_Type */
  13,   /* field[13] = Agent */
  2,   /* field[2] = Agent_Port */
  9,   /* field[9] = Ap_MVMO */
  6,   /* field[6] = Apn_Auth_Type */
  23,   /* field[23] = Apn_Bear_Type */
  22,   /* field[22] = Apn_Bearer_Type */
  21,   /* field[21] = Apn_MVNO */
  10,   /* field[10] = Apn_Name */
  11,   /* field[11] = Apn_Name_Len */
  7,   /* field[7] = Apn_Proto */
  8,   /* field[8] = Apn_Roma_Proto */
  1,   /* field[1] = Auto_Apn */
  0,   /* field[0] = Enable */
  4,   /* field[4] = MCC */
  17,   /* field[17] = MMSC */
  19,   /* field[19] = MMS_Agent */
  3,   /* field[3] = MMS_Port */
  5,   /* field[5] = MNC */
  18,   /* field[18] = PPP_Dial_Number */
  16,   /* field[16] = Server */
  14,   /* field[14] = Usr_Name */
  15,   /* field[15] = Usr_Pwd */
};
static const ProtobufCIntRange tpb_nvr_net_apn_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 24 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_apn_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetApnParam",
  "TPbNvrNetApnParam",
  "TPbNvrNetApnParam",
  "",
  sizeof(TPbNvrNetApnParam),
  24,
  tpb_nvr_net_apn_param__field_descriptors,
  tpb_nvr_net_apn_param__field_indices_by_name,
  1,  tpb_nvr_net_apn_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_apn_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_vpn_list_param__field_descriptors[10] =
{
  {
    "Vpn_Type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnListParam, has_vpn_type),
    offsetof(TPbNvrNetVpnListParam, vpn_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_Name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrNetVpnListParam, has_vpn_name),
    offsetof(TPbNvrNetVpnListParam, vpn_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_Name_Len",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnListParam, has_vpn_name_len),
    offsetof(TPbNvrNetVpnListParam, vpn_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Name",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, usr_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Pwd",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, usr_pwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Srv_Addr",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, srv_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Dns_Search_Domain",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, dns_search_domain),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Dns_Srv",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, dns_srv),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Forward_Route",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnListParam, forward_route),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Ppp_Enable",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnListParam, has_ppp_enable),
    offsetof(TPbNvrNetVpnListParam, ppp_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_vpn_list_param__field_indices_by_name[] = {
  6,   /* field[6] = Dns_Search_Domain */
  7,   /* field[7] = Dns_Srv */
  8,   /* field[8] = Forward_Route */
  9,   /* field[9] = Ppp_Enable */
  5,   /* field[5] = Srv_Addr */
  3,   /* field[3] = Usr_Name */
  4,   /* field[4] = Usr_Pwd */
  1,   /* field[1] = Vpn_Name */
  2,   /* field[2] = Vpn_Name_Len */
  0,   /* field[0] = Vpn_Type */
};
static const ProtobufCIntRange tpb_nvr_net_vpn_list_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_vpn_list_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetVpnListParam",
  "TPbNvrNetVpnListParam",
  "TPbNvrNetVpnListParam",
  "",
  sizeof(TPbNvrNetVpnListParam),
  10,
  tpb_nvr_net_vpn_list_param__field_descriptors,
  tpb_nvr_net_vpn_list_param__field_indices_by_name,
  1,  tpb_nvr_net_vpn_list_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_vpn_list_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_vpn_param__field_descriptors[13] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetVpnParam, has_enable),
    offsetof(TPbNvrNetVpnParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_List",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnParam, has_vpn_list),
    offsetof(TPbNvrNetVpnParam, vpn_list),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_Type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnParam, has_vpn_type),
    offsetof(TPbNvrNetVpnParam, vpn_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_Name",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrNetVpnParam, has_vpn_name),
    offsetof(TPbNvrNetVpnParam, vpn_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Vpn_Name_Len",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnParam, has_vpn_name_len),
    offsetof(TPbNvrNetVpnParam, vpn_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Name",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, usr_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Usr_Pwd",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, usr_pwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Srv_Addr",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, srv_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Dns_Search_Domain",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, dns_search_domain),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Dns_Srv",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, dns_srv),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Forward_Route",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetVpnParam, forward_route),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Ppp_Enable",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetVpnParam, has_ppp_enable),
    offsetof(TPbNvrNetVpnParam, ppp_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "net_vpn_list",
    13,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetVpnParam, n_net_vpn_list),
    offsetof(TPbNvrNetVpnParam, net_vpn_list),
    &tpb_nvr_net_vpn_list_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_vpn_param__field_indices_by_name[] = {
  8,   /* field[8] = Dns_Search_Domain */
  9,   /* field[9] = Dns_Srv */
  0,   /* field[0] = Enable */
  10,   /* field[10] = Forward_Route */
  11,   /* field[11] = Ppp_Enable */
  7,   /* field[7] = Srv_Addr */
  5,   /* field[5] = Usr_Name */
  6,   /* field[6] = Usr_Pwd */
  1,   /* field[1] = Vpn_List */
  3,   /* field[3] = Vpn_Name */
  4,   /* field[4] = Vpn_Name_Len */
  2,   /* field[2] = Vpn_Type */
  12,   /* field[12] = net_vpn_list */
};
static const ProtobufCIntRange tpb_nvr_net_vpn_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 13 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_vpn_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetVpnParam",
  "TPbNvrNetVpnParam",
  "TPbNvrNetVpnParam",
  "",
  sizeof(TPbNvrNetVpnParam),
  13,
  tpb_nvr_net_vpn_param__field_descriptors,
  tpb_nvr_net_vpn_param__field_indices_by_name,
  1,  tpb_nvr_net_vpn_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_vpn_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_flow_monitor_param__field_descriptors[5] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrFlowMonitorParam, has_enable),
    offsetof(TPbNvrFlowMonitorParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Over_Flow_Stop_Stream",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrFlowMonitorParam, has_over_flow_stop_stream),
    offsetof(TPbNvrFlowMonitorParam, over_flow_stop_stream),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Stats_Type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFlowMonitorParam, has_stats_type),
    offsetof(TPbNvrFlowMonitorParam, stats_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Total_Flow",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFlowMonitorParam, has_total_flow),
    offsetof(TPbNvrFlowMonitorParam, total_flow),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Early_Warning_Rate",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFlowMonitorParam, has_early_warning_rate),
    offsetof(TPbNvrFlowMonitorParam, early_warning_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_flow_monitor_param__field_indices_by_name[] = {
  4,   /* field[4] = Early_Warning_Rate */
  0,   /* field[0] = Enable */
  1,   /* field[1] = Over_Flow_Stop_Stream */
  2,   /* field[2] = Stats_Type */
  3,   /* field[3] = Total_Flow */
};
static const ProtobufCIntRange tpb_nvr_flow_monitor_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_flow_monitor_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrFlowMonitorParam",
  "TPbNvrFlowMonitorParam",
  "TPbNvrFlowMonitorParam",
  "",
  sizeof(TPbNvrFlowMonitorParam),
  5,
  tpb_nvr_flow_monitor_param__field_descriptors,
  tpb_nvr_flow_monitor_param__field_indices_by_name,
  1,  tpb_nvr_flow_monitor_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_flow_monitor_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mb_net_cfg__field_descriptors[5] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMbNetCfg, has_enable),
    offsetof(TPbNvrMbNetCfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ApnParam",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMbNetCfg, apnparam),
    &tpb_nvr_net_apn_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "VpnParam",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMbNetCfg, vpnparam),
    &tpb_nvr_net_vpn_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "FlowParam",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMbNetCfg, flowparam),
    &tpb_nvr_flow_monitor_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scan_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMbNetCfg, has_scan_mode),
    offsetof(TPbNvrMbNetCfg, scan_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mb_net_cfg__field_indices_by_name[] = {
  1,   /* field[1] = ApnParam */
  0,   /* field[0] = Enable */
  3,   /* field[3] = FlowParam */
  2,   /* field[2] = VpnParam */
  4,   /* field[4] = scan_mode */
};
static const ProtobufCIntRange tpb_nvr_mb_net_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_mb_net_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMbNetCfg",
  "TPbNvrMbNetCfg",
  "TPbNvrMbNetCfg",
  "",
  sizeof(TPbNvrMbNetCfg),
  5,
  tpb_nvr_mb_net_cfg__field_descriptors,
  tpb_nvr_mb_net_cfg__field_indices_by_name,
  1,  tpb_nvr_mb_net_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mb_net_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_router_service__field_descriptors[2] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetRouterService, has_enable),
    offsetof(TPbNvrNetRouterService, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Sim_Num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouterService, has_sim_num),
    offsetof(TPbNvrNetRouterService, sim_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_router_service__field_indices_by_name[] = {
  0,   /* field[0] = Enable */
  1,   /* field[1] = Sim_Num */
};
static const ProtobufCIntRange tpb_nvr_net_router_service__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_router_service__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetRouterService",
  "TPbNvrNetRouterService",
  "TPbNvrNetRouterService",
  "",
  sizeof(TPbNvrNetRouterService),
  2,
  tpb_nvr_net_router_service__field_descriptors,
  tpb_nvr_net_router_service__field_indices_by_name,
  1,  tpb_nvr_net_router_service__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_router_service__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mb_net_cfg_all__field_descriptors[2] =
{
  {
    "NetCfg",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMbNetCfgAll, n_netcfg),
    offsetof(TPbNvrMbNetCfgAll, netcfg),
    &tpb_nvr_mb_net_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Router_Service",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMbNetCfgAll, router_service),
    &tpb_nvr_net_router_service__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mb_net_cfg_all__field_indices_by_name[] = {
  0,   /* field[0] = NetCfg */
  1,   /* field[1] = Router_Service */
};
static const ProtobufCIntRange tpb_nvr_mb_net_cfg_all__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_mb_net_cfg_all__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMbNetCfgAll",
  "TPbNvrMbNetCfgAll",
  "TPbNvrMbNetCfgAll",
  "",
  sizeof(TPbNvrMbNetCfgAll),
  2,
  tpb_nvr_mb_net_cfg_all__field_descriptors,
  tpb_nvr_mb_net_cfg_all__field_indices_by_name,
  1,  tpb_nvr_mb_net_cfg_all__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mb_net_cfg_all__init,
  NULL,NULL,NULL    /* reserved[123] */
};
