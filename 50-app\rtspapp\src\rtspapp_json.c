#include "rtspapp_json.h"
#include "rtspapp_tool.h"

#define RTSPAPPCHECKJSONEXIT(root, p)  \
    if( !(p) ) \
{ \
    AppErrLog( "[RTSPAPP] get or create json object failed!(%s,%d)\n", __FUNCTION__, __LINE__);	\
    if(root){\
    cJSON_Delete(root); }\
    return FALSE; \
}


BOOL32 RtspAppBrokenTDateStrTime(IN const s8* ptTimeStr,OUT TRtspAppBrokenDownTime *ptDownTime)
{
    s32 nRet = 0;
    AppInfLog("*****Run Here!ptTimeStr:%s*****\n",ptTimeStr);

    nRet = AppSscanf(ptTimeStr, "%hu-%hhu-%hhuT%hhu:%hhu:%hhu",
        &ptDownTime->wYear, 
        &ptDownTime->byMonth, 
        &ptDownTime->byDay, 
        &ptDownTime->byHour, 
        &ptDownTime->byMinute, 
        &ptDownTime->bySecond);

    if(6 == nRet)
    {
        AppDbgLog("time:[%hu-%hhu-%hhu %hhu:%hhu:%hhu]\n", 
            ptDownTime->wYear,
            ptDownTime->byMonth, 
            ptDownTime->byDay, 
            ptDownTime->byHour, 
            ptDownTime->byMinute, 
            ptDownTime->bySecond);
        return TRUE;
    }
    else
    {
        AppErrLog("parse start time failed %d\n", nRet);
    }
    return FALSE;
}

BOOL32 RtspappCjsonParseMediaStreamReq(IN const s8* ptStr,OUT RtspAppStreamMediaReq *ptRtspMedia)
{
    cJSON *ptRootJson = NULL;
    cJSON *ptjProtocol = NULL;
    cJSON *ptjDeviceId = NULL;
    cJSON *ptjStartTime = NULL;
    cJSON *ptjEndTime = NULL;
    cJSON *ptjMediaId = NULL;

    ptRootJson = cJSON_Parse(ptStr);
    if (!ptRootJson)  
    {  
        AppErrLog("Error ptStr\n");  
        cJSON_Delete(ptRootJson);
        return FALSE;
    }  

    ptjProtocol = cJSON_GetObjectItem(ptRootJson,"protocol");
    RTSPAPPCHECKJSONEXIT(ptRootJson,ptjProtocol)

    if (cJSON_String == ptjProtocol->type)
    {
        AppStrncpy(ptRtspMedia->achProtocol,ptjProtocol->valuestring,sizeof(ptRtspMedia->achProtocol));
    }

    ptjDeviceId = cJSON_GetObjectItem(ptRootJson,"device_id");
    RTSPAPPCHECKJSONEXIT(ptRootJson,ptjDeviceId)
    if(cJSON_String == ptjDeviceId->type)
    {
        AppStrncpy(ptRtspMedia->achDeviceID,ptjDeviceId->valuestring,sizeof(ptRtspMedia->achDeviceID));
    }

    ptjStartTime = cJSON_GetObjectItem(ptRootJson,"start_time");
    if (ptjStartTime)
    {
        if (cJSON_String == ptjStartTime->type)
        {
            RtspAppBrokenTDateStrTime(ptjStartTime->valuestring,&ptRtspMedia->tStartTime);
        }
    }

    ptjEndTime = cJSON_GetObjectItem(ptRootJson,"end_time");
    if (ptjEndTime)
    {
        if (cJSON_String == ptjEndTime->type)
        {
            RtspAppBrokenTDateStrTime(ptjEndTime->valuestring,&ptRtspMedia->tEndTime);
        }
    }

    ptjMediaId = cJSON_GetObjectItem(ptRootJson,"nmedia_id");
    if (ptjMediaId)
    {
        if (cJSON_Number == ptjMediaId->type)
        {
            ptRtspMedia->nMediaId = ptjMediaId->valueint;
        }
    }

    cJSON_Delete(ptRootJson);
    AppInfLog("Parse Success\n"); 
    return TRUE;

}

s8* RtspappCjsonGetMediaStreamResp(IN RtspAppStreamMediaResp *ptRtspMedia)
{
    cJSON *ptRoot = NULL;
    s8* ptBuff = NULL;

    ptRoot = cJSON_CreateObject();
    if(NULL == ptRoot)
    {
        return NULL;
    }
    cJSON_AddNumberToObject(ptRoot,"code",ptRtspMedia->nCode);
    if(AppStrlen(ptRtspMedia->achUrl))
    {
        cJSON_AddStringToObject(ptRoot,"url",ptRtspMedia->achUrl);
    }
    if (AppStrlen(ptRtspMedia->achMessage))
    {
        cJSON_AddStringToObject(ptRoot,"message",ptRtspMedia->achMessage);
    }
    ptBuff = cJSON_PrintUnformatted(ptRoot);
    cJSON_Delete(ptRoot);
    AppInfLog("Get json Success\n"); 
    return ptBuff;
}