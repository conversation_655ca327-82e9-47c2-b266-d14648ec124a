/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrusrmgr.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrusrmgr.pb-c.h"
void   tpb_nvr_user_chn_perm__init
                     (TPbNvrUserChnPerm         *message)
{
  static TPbNvrUserChnPerm init_value = TPB_NVR_USER_CHN_PERM__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_chn_perm__get_packed_size
                     (const TPbNvrUserChnPerm *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_chn_perm__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_chn_perm__pack
                     (const TPbNvrUserChnPerm *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_chn_perm__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_chn_perm__pack_to_buffer
                     (const TPbNvrUserChnPerm *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_chn_perm__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserChnPerm *
       tpb_nvr_user_chn_perm__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserChnPerm *)
     protobuf_c_message_unpack (&tpb_nvr_user_chn_perm__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_chn_perm__free_unpacked
                     (TPbNvrUserChnPerm *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_chn_perm__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_gst_pwd_info__init
                     (TPbNvrUserGstPwdInfo         *message)
{
  static TPbNvrUserGstPwdInfo init_value = TPB_NVR_USER_GST_PWD_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_gst_pwd_info__get_packed_size
                     (const TPbNvrUserGstPwdInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_gst_pwd_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_gst_pwd_info__pack
                     (const TPbNvrUserGstPwdInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_gst_pwd_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_gst_pwd_info__pack_to_buffer
                     (const TPbNvrUserGstPwdInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_gst_pwd_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserGstPwdInfo *
       tpb_nvr_user_gst_pwd_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserGstPwdInfo *)
     protobuf_c_message_unpack (&tpb_nvr_user_gst_pwd_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_gst_pwd_info__free_unpacked
                     (TPbNvrUserGstPwdInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_gst_pwd_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_info__init
                     (TPbNvrUserInfo         *message)
{
  static TPbNvrUserInfo init_value = TPB_NVR_USER_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_info__get_packed_size
                     (const TPbNvrUserInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_info__pack
                     (const TPbNvrUserInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_info__pack_to_buffer
                     (const TPbNvrUserInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserInfo *
       tpb_nvr_user_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserInfo *)
     protobuf_c_message_unpack (&tpb_nvr_user_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_info__free_unpacked
                     (TPbNvrUserInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_ip_filter_addr__init
                     (TPbNvrUserIpFilterAddr         *message)
{
  static TPbNvrUserIpFilterAddr init_value = TPB_NVR_USER_IP_FILTER_ADDR__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_ip_filter_addr__get_packed_size
                     (const TPbNvrUserIpFilterAddr *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_addr__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_ip_filter_addr__pack
                     (const TPbNvrUserIpFilterAddr *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_addr__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_ip_filter_addr__pack_to_buffer
                     (const TPbNvrUserIpFilterAddr *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_addr__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserIpFilterAddr *
       tpb_nvr_user_ip_filter_addr__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserIpFilterAddr *)
     protobuf_c_message_unpack (&tpb_nvr_user_ip_filter_addr__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_ip_filter_addr__free_unpacked
                     (TPbNvrUserIpFilterAddr *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_addr__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_ip_filter_list__init
                     (TPbNvrUserIpFilterList         *message)
{
  static TPbNvrUserIpFilterList init_value = TPB_NVR_USER_IP_FILTER_LIST__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_ip_filter_list__get_packed_size
                     (const TPbNvrUserIpFilterList *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_list__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_ip_filter_list__pack
                     (const TPbNvrUserIpFilterList *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_list__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_ip_filter_list__pack_to_buffer
                     (const TPbNvrUserIpFilterList *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_list__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserIpFilterList *
       tpb_nvr_user_ip_filter_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserIpFilterList *)
     protobuf_c_message_unpack (&tpb_nvr_user_ip_filter_list__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_ip_filter_list__free_unpacked
                     (TPbNvrUserIpFilterList *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_list__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_ip_filter_info__init
                     (TPbNvrUserIpFilterInfo         *message)
{
  static TPbNvrUserIpFilterInfo init_value = TPB_NVR_USER_IP_FILTER_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_ip_filter_info__get_packed_size
                     (const TPbNvrUserIpFilterInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_ip_filter_info__pack
                     (const TPbNvrUserIpFilterInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_ip_filter_info__pack_to_buffer
                     (const TPbNvrUserIpFilterInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserIpFilterInfo *
       tpb_nvr_user_ip_filter_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserIpFilterInfo *)
     protobuf_c_message_unpack (&tpb_nvr_user_ip_filter_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_ip_filter_info__free_unpacked
                     (TPbNvrUserIpFilterInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_ip_filter_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_group_info__init
                     (TPbNvrUserGroupInfo         *message)
{
  static TPbNvrUserGroupInfo init_value = TPB_NVR_USER_GROUP_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_group_info__get_packed_size
                     (const TPbNvrUserGroupInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_group_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_group_info__pack
                     (const TPbNvrUserGroupInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_group_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_group_info__pack_to_buffer
                     (const TPbNvrUserGroupInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_group_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserGroupInfo *
       tpb_nvr_user_group_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserGroupInfo *)
     protobuf_c_message_unpack (&tpb_nvr_user_group_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_group_info__free_unpacked
                     (TPbNvrUserGroupInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_group_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_user_total_info__init
                     (TPbNvrUserTotalInfo         *message)
{
  static TPbNvrUserTotalInfo init_value = TPB_NVR_USER_TOTAL_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_user_total_info__get_packed_size
                     (const TPbNvrUserTotalInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_user_total_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_user_total_info__pack
                     (const TPbNvrUserTotalInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_user_total_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_user_total_info__pack_to_buffer
                     (const TPbNvrUserTotalInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_user_total_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUserTotalInfo *
       tpb_nvr_user_total_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUserTotalInfo *)
     protobuf_c_message_unpack (&tpb_nvr_user_total_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_user_total_info__free_unpacked
                     (TPbNvrUserTotalInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_user_total_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_user_chn_perm__field_descriptors[1] =
{
  {
    "chn_perm",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserChnPerm, n_chn_perm),
    offsetof(TPbNvrUserChnPerm, chn_perm),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_chn_perm__field_indices_by_name[] = {
  0,   /* field[0] = chn_perm */
};
static const ProtobufCIntRange tpb_nvr_user_chn_perm__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_chn_perm__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserChnPerm",
  "TPbNvrUserChnPerm",
  "TPbNvrUserChnPerm",
  "",
  sizeof(TPbNvrUserChnPerm),
  1,
  tpb_nvr_user_chn_perm__field_descriptors,
  tpb_nvr_user_chn_perm__field_indices_by_name,
  1,  tpb_nvr_user_chn_perm__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_chn_perm__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_gst_pwd_info__field_descriptors[2] =
{
  {
    "enable_gst_pwd",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserGstPwdInfo, has_enable_gst_pwd),
    offsetof(TPbNvrUserGstPwdInfo, enable_gst_pwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gst_pwd",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserGstPwdInfo, gst_pwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_gst_pwd_info__field_indices_by_name[] = {
  0,   /* field[0] = enable_gst_pwd */
  1,   /* field[1] = gst_pwd */
};
static const ProtobufCIntRange tpb_nvr_user_gst_pwd_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_gst_pwd_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserGstPwdInfo",
  "TPbNvrUserGstPwdInfo",
  "TPbNvrUserGstPwdInfo",
  "",
  sizeof(TPbNvrUserGstPwdInfo),
  2,
  tpb_nvr_user_gst_pwd_info__field_descriptors,
  tpb_nvr_user_gst_pwd_info__field_indices_by_name,
  1,  tpb_nvr_user_gst_pwd_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_gst_pwd_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_info__field_descriptors[15] =
{
  {
    "user_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrUserInfo, has_user_name),
    offsetof(TPbNvrUserInfo, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_pass",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserInfo, user_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_sys_perm",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserInfo, n_user_sys_perm),
    offsetof(TPbNvrUserInfo, user_sys_perm),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_chn_perm",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrUserInfo, n_user_chn_perm),
    offsetof(TPbNvrUserInfo, user_chn_perm),
    &tpb_nvr_user_chn_perm__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_disable",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserInfo, has_user_disable),
    offsetof(TPbNvrUserInfo, user_disable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrUserInfo, has_user_type),
    offsetof(TPbNvrUserInfo, user_type),
    &em_pb_nvr_user_cfg_user_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rmt_ctl_enable",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserInfo, has_rmt_ctl_enable),
    offsetof(TPbNvrUserInfo, rmt_ctl_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rmt_ctl_ip_info",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserInfo, rmt_ctl_ip_info),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "password_starttime",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserInfo, has_password_starttime),
    offsetof(TPbNvrUserInfo, password_starttime),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "password_notactive",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserInfo, has_password_notactive),
    offsetof(TPbNvrUserInfo, password_notactive),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_group_id",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserInfo, has_user_group_id),
    offsetof(TPbNvrUserInfo, user_group_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_gst_pwd",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserInfo, user_gst_pwd),
    &tpb_nvr_user_gst_pwd_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "regular_update_password",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserInfo, has_regular_update_password),
    offsetof(TPbNvrUserInfo, regular_update_password),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "password_cycle",
    15,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserInfo, has_password_cycle),
    offsetof(TPbNvrUserInfo, password_cycle),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "last_password_change_utc_time",
    16,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserInfo, has_last_password_change_utc_time),
    offsetof(TPbNvrUserInfo, last_password_change_utc_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_info__field_indices_by_name[] = {
  14,   /* field[14] = last_password_change_utc_time */
  13,   /* field[13] = password_cycle */
  9,   /* field[9] = password_notactive */
  8,   /* field[8] = password_starttime */
  12,   /* field[12] = regular_update_password */
  6,   /* field[6] = rmt_ctl_enable */
  7,   /* field[7] = rmt_ctl_ip_info */
  3,   /* field[3] = user_chn_perm */
  4,   /* field[4] = user_disable */
  10,   /* field[10] = user_group_id */
  11,   /* field[11] = user_gst_pwd */
  0,   /* field[0] = user_name */
  1,   /* field[1] = user_pass */
  2,   /* field[2] = user_sys_perm */
  5,   /* field[5] = user_type */
};
static const ProtobufCIntRange tpb_nvr_user_info__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 8, 6 },
  { 0, 15 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserInfo",
  "TPbNvrUserInfo",
  "TPbNvrUserInfo",
  "",
  sizeof(TPbNvrUserInfo),
  15,
  tpb_nvr_user_info__field_descriptors,
  tpb_nvr_user_info__field_indices_by_name,
  2,  tpb_nvr_user_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_ip_filter_addr__field_descriptors[9] =
{
  {
    "ip_addr",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nType",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrUserIpFilterAddr, has_ntype),
    offsetof(TPbNvrUserIpFilterAddr, ntype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "single_ip_addr",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, single_ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_ip_addr",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, start_ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_ip_addr",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, end_ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mask",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, mask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mac",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterAddr, mac),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable_ip_filter",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserIpFilterAddr, has_enable_ip_filter),
    offsetof(TPbNvrUserIpFilterAddr, enable_ip_filter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable_mac_filter",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserIpFilterAddr, has_enable_mac_filter),
    offsetof(TPbNvrUserIpFilterAddr, enable_mac_filter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_ip_filter_addr__field_indices_by_name[] = {
  7,   /* field[7] = enable_ip_filter */
  8,   /* field[8] = enable_mac_filter */
  4,   /* field[4] = end_ip_addr */
  0,   /* field[0] = ip_addr */
  6,   /* field[6] = mac */
  5,   /* field[5] = mask */
  1,   /* field[1] = nType */
  2,   /* field[2] = single_ip_addr */
  3,   /* field[3] = start_ip_addr */
};
static const ProtobufCIntRange tpb_nvr_user_ip_filter_addr__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_ip_filter_addr__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserIpFilterAddr",
  "TPbNvrUserIpFilterAddr",
  "TPbNvrUserIpFilterAddr",
  "",
  sizeof(TPbNvrUserIpFilterAddr),
  9,
  tpb_nvr_user_ip_filter_addr__field_descriptors,
  tpb_nvr_user_ip_filter_addr__field_indices_by_name,
  1,  tpb_nvr_user_ip_filter_addr__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_ip_filter_addr__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_ip_filter_list__field_descriptors[3] =
{
  {
    "ip_addr",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrUserIpFilterList, n_ip_addr),
    offsetof(TPbNvrUserIpFilterList, ip_addr),
    &tpb_nvr_user_ip_filter_addr__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "list_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserIpFilterList, has_list_num),
    offsetof(TPbNvrUserIpFilterList, list_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "list_num_v2",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserIpFilterList, has_list_num_v2),
    offsetof(TPbNvrUserIpFilterList, list_num_v2),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_ip_filter_list__field_indices_by_name[] = {
  0,   /* field[0] = ip_addr */
  1,   /* field[1] = list_num */
  2,   /* field[2] = list_num_v2 */
};
static const ProtobufCIntRange tpb_nvr_user_ip_filter_list__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_ip_filter_list__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserIpFilterList",
  "TPbNvrUserIpFilterList",
  "TPbNvrUserIpFilterList",
  "",
  sizeof(TPbNvrUserIpFilterList),
  3,
  tpb_nvr_user_ip_filter_list__field_descriptors,
  tpb_nvr_user_ip_filter_list__field_indices_by_name,
  1,  tpb_nvr_user_ip_filter_list__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_ip_filter_list__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_ip_filter_info__field_descriptors[4] =
{
  {
    "enable_ip_filter",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserIpFilterInfo, has_enable_ip_filter),
    offsetof(TPbNvrUserIpFilterInfo, enable_ip_filter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_filter_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrUserIpFilterInfo, has_ip_filter_type),
    offsetof(TPbNvrUserIpFilterInfo, ip_filter_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_white_list",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterInfo, ip_white_list),
    &tpb_nvr_user_ip_filter_list__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_black_list",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserIpFilterInfo, ip_black_list),
    &tpb_nvr_user_ip_filter_list__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_ip_filter_info__field_indices_by_name[] = {
  0,   /* field[0] = enable_ip_filter */
  3,   /* field[3] = ip_black_list */
  1,   /* field[1] = ip_filter_type */
  2,   /* field[2] = ip_white_list */
};
static const ProtobufCIntRange tpb_nvr_user_ip_filter_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_ip_filter_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserIpFilterInfo",
  "TPbNvrUserIpFilterInfo",
  "TPbNvrUserIpFilterInfo",
  "",
  sizeof(TPbNvrUserIpFilterInfo),
  4,
  tpb_nvr_user_ip_filter_info__field_descriptors,
  tpb_nvr_user_ip_filter_info__field_indices_by_name,
  1,  tpb_nvr_user_ip_filter_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_ip_filter_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_group_info__field_descriptors[4] =
{
  {
    "group_group_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserGroupInfo, has_group_group_id),
    offsetof(TPbNvrUserGroupInfo, group_group_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrUserGroupInfo, has_group_name),
    offsetof(TPbNvrUserGroupInfo, group_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_sys_perm",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserGroupInfo, n_group_sys_perm),
    offsetof(TPbNvrUserGroupInfo, group_sys_perm),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_chn_perm",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrUserGroupInfo, n_group_chn_perm),
    offsetof(TPbNvrUserGroupInfo, group_chn_perm),
    &tpb_nvr_user_chn_perm__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_group_info__field_indices_by_name[] = {
  3,   /* field[3] = group_chn_perm */
  0,   /* field[0] = group_group_id */
  1,   /* field[1] = group_name */
  2,   /* field[2] = group_sys_perm */
};
static const ProtobufCIntRange tpb_nvr_user_group_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_group_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserGroupInfo",
  "TPbNvrUserGroupInfo",
  "TPbNvrUserGroupInfo",
  "",
  sizeof(TPbNvrUserGroupInfo),
  4,
  tpb_nvr_user_group_info__field_descriptors,
  tpb_nvr_user_group_info__field_indices_by_name,
  1,  tpb_nvr_user_group_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_group_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_user_total_info__field_descriptors[14] =
{
  {
    "user_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_user_num),
    offsetof(TPbNvrUserTotalInfo, user_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_info",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrUserTotalInfo, n_user_info),
    offsetof(TPbNvrUserTotalInfo, user_info),
    &tpb_nvr_user_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "active_state",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrUserTotalInfo, has_active_state),
    offsetof(TPbNvrUserTotalInfo, active_state),
    &em_pb_nvr_user_dev_act_sta__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_mgr_email",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserTotalInfo, dev_mgr_email),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ssh_enable",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserTotalInfo, has_ssh_enable),
    offsetof(TPbNvrUserTotalInfo, ssh_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "illegal_login_lock",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUserTotalInfo, has_illegal_login_lock),
    offsetof(TPbNvrUserTotalInfo, illegal_login_lock),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_filter_info",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUserTotalInfo, ip_filter_info),
    &tpb_nvr_user_ip_filter_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "login_time",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_login_time),
    offsetof(TPbNvrUserTotalInfo, login_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "lock_time",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_lock_time),
    offsetof(TPbNvrUserTotalInfo, lock_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_exc",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_user_exc),
    offsetof(TPbNvrUserTotalInfo, user_exc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_exc",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_group_exc),
    offsetof(TPbNvrUserTotalInfo, group_exc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_exc_id",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_group_exc_id),
    offsetof(TPbNvrUserTotalInfo, group_exc_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_num",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrUserTotalInfo, has_group_num),
    offsetof(TPbNvrUserTotalInfo, group_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_info",
    14,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrUserTotalInfo, n_group_info),
    offsetof(TPbNvrUserTotalInfo, group_info),
    &tpb_nvr_user_group_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_user_total_info__field_indices_by_name[] = {
  2,   /* field[2] = active_state */
  3,   /* field[3] = dev_mgr_email */
  10,   /* field[10] = group_exc */
  11,   /* field[11] = group_exc_id */
  13,   /* field[13] = group_info */
  12,   /* field[12] = group_num */
  5,   /* field[5] = illegal_login_lock */
  6,   /* field[6] = ip_filter_info */
  8,   /* field[8] = lock_time */
  7,   /* field[7] = login_time */
  4,   /* field[4] = ssh_enable */
  9,   /* field[9] = user_exc */
  1,   /* field[1] = user_info */
  0,   /* field[0] = user_num */
};
static const ProtobufCIntRange tpb_nvr_user_total_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 14 }
};
const ProtobufCMessageDescriptor tpb_nvr_user_total_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUserTotalInfo",
  "TPbNvrUserTotalInfo",
  "TPbNvrUserTotalInfo",
  "",
  sizeof(TPbNvrUserTotalInfo),
  14,
  tpb_nvr_user_total_info__field_descriptors,
  tpb_nvr_user_total_info__field_indices_by_name,
  1,  tpb_nvr_user_total_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_user_total_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_user_sys_perm_type__enum_values_by_number[8] =
{
  { "ADMIN", "EM_PB_NVR_USER_SYS_PERM_TYPE__ADMIN", 0 },
  { "REC_SNAP", "EM_PB_NVR_USER_SYS_PERM_TYPE__REC_SNAP", 1 },
  { "CHN", "EM_PB_NVR_USER_SYS_PERM_TYPE__CHN", 2 },
  { "NET", "EM_PB_NVR_USER_SYS_PERM_TYPE__NET", 3 },
  { "SYS", "EM_PB_NVR_USER_SYS_PERM_TYPE__SYS", 4 },
  { "DISK", "EM_PB_NVR_USER_SYS_PERM_TYPE__DISK", 5 },
  { "GUI", "EM_PB_NVR_USER_SYS_PERM_TYPE__GUI", 6 },
  { "SHUTDOWN", "EM_PB_NVR_USER_SYS_PERM_TYPE__SHUTDOWN", 7 },
};
static const ProtobufCIntRange em_pb_nvr_user_sys_perm_type__value_ranges[] = {
{0, 0},{0, 8}
};
static const ProtobufCEnumValueIndex em_pb_nvr_user_sys_perm_type__enum_values_by_name[8] =
{
  { "ADMIN", 0 },
  { "CHN", 2 },
  { "DISK", 5 },
  { "GUI", 6 },
  { "NET", 3 },
  { "REC_SNAP", 1 },
  { "SHUTDOWN", 7 },
  { "SYS", 4 },
};
const ProtobufCEnumDescriptor em_pb_nvr_user_sys_perm_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUserSysPermType",
  "EmPbNvrUserSysPermType",
  "EmPbNvrUserSysPermType",
  "",
  8,
  em_pb_nvr_user_sys_perm_type__enum_values_by_number,
  8,
  em_pb_nvr_user_sys_perm_type__enum_values_by_name,
  1,
  em_pb_nvr_user_sys_perm_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_user_chn_perm_type__enum_values_by_number[6] =
{
  { "VIEW", "EM_PB_NVR_USER_CHN_PERM_TYPE__VIEW", 0 },
  { "LT", "EM_PB_NVR_USER_CHN_PERM_TYPE__LT", 1 },
  { "PLAYBACK", "EM_PB_NVR_USER_CHN_PERM_TYPE__PLAYBACK", 2 },
  { "BD", "EM_PB_NVR_USER_CHN_PERM_TYPE__BD", 3 },
  { "IS", "EM_PB_NVR_USER_CHN_PERM_TYPE__IS", 4 },
  { "PTZ", "EM_PB_NVR_USER_CHN_PERM_TYPE__PTZ", 5 },
};
static const ProtobufCIntRange em_pb_nvr_user_chn_perm_type__value_ranges[] = {
{0, 0},{0, 6}
};
static const ProtobufCEnumValueIndex em_pb_nvr_user_chn_perm_type__enum_values_by_name[6] =
{
  { "BD", 3 },
  { "IS", 4 },
  { "LT", 1 },
  { "PLAYBACK", 2 },
  { "PTZ", 5 },
  { "VIEW", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_user_chn_perm_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUserChnPermType",
  "EmPbNvrUserChnPermType",
  "EmPbNvrUserChnPermType",
  "",
  6,
  em_pb_nvr_user_chn_perm_type__enum_values_by_number,
  6,
  em_pb_nvr_user_chn_perm_type__enum_values_by_name,
  1,
  em_pb_nvr_user_chn_perm_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_user_dev_act_sta__enum_values_by_number[2] =
{
  { "NOACT", "EM_PB_NVR_USER_DEV_ACT_STA__NOACT", 0 },
  { "ACT", "EM_PB_NVR_USER_DEV_ACT_STA__ACT", 1 },
};
static const ProtobufCIntRange em_pb_nvr_user_dev_act_sta__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_user_dev_act_sta__enum_values_by_name[2] =
{
  { "ACT", 1 },
  { "NOACT", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_user_dev_act_sta__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUserDevActSta",
  "EmPbNvrUserDevActSta",
  "EmPbNvrUserDevActSta",
  "",
  2,
  em_pb_nvr_user_dev_act_sta__enum_values_by_number,
  2,
  em_pb_nvr_user_dev_act_sta__enum_values_by_name,
  1,
  em_pb_nvr_user_dev_act_sta__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_user_cfg_user_type__enum_values_by_number[3] =
{
  { "USER_TYPE_ADMIN", "EM_PB_NVR_USER_CFG_USER_TYPE__USER_TYPE_ADMIN", 0 },
  { "OPERATOR", "EM_PB_NVR_USER_CFG_USER_TYPE__OPERATOR", 1 },
  { "VIEWER", "EM_PB_NVR_USER_CFG_USER_TYPE__VIEWER", 2 },
};
static const ProtobufCIntRange em_pb_nvr_user_cfg_user_type__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_pb_nvr_user_cfg_user_type__enum_values_by_name[3] =
{
  { "OPERATOR", 1 },
  { "USER_TYPE_ADMIN", 0 },
  { "VIEWER", 2 },
};
const ProtobufCEnumDescriptor em_pb_nvr_user_cfg_user_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUserCfgUserType",
  "EmPbNvrUserCfgUserType",
  "EmPbNvrUserCfgUserType",
  "",
  3,
  em_pb_nvr_user_cfg_user_type__enum_values_by_number,
  3,
  em_pb_nvr_user_cfg_user_type__enum_values_by_name,
  1,
  em_pb_nvr_user_cfg_user_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
