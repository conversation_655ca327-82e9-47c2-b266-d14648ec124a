/******************************************************************************

               Copyright 2011, 30KT Technologies Co. Ltd.
                            ALL RIGHTS RESERVED

-----------------------------------------------------------------------------

                            kt_sm.h

  Project Code :
  Module Name  :
  Date Created : 2017/06/28
  Author       : yf
  Description  :

-----------------------------------------------------------------------------
   Modification History
   DATE         NAME            DESCRIPTION
-----------------------------------------------------------------------------
   2017/06/28   yf            create
******************************************************************************/
#ifndef __KT_SM_H__
#define __KT_SM_H__

#ifdef  __cplusplus
extern "C" {
#endif

#if defined(_WIN32) || defined(_WIN64)
#undef	DECLARE
#define DECLARE     __declspec(dllexport)
#ifndef CALLBACK
#define CALLBACK	__stdcall
#endif
#else
#define DECLARE
#define CALLBACK
#endif

typedef void * KT_PIPE_HANDLE;

typedef struct tagKTSMDevInfo
{
    unsigned int uiStatus;
    unsigned int uiSigSpeed;
    unsigned int uiVerifySpeed;
    unsigned int uiHashSpeed;
    unsigned int uiEncSpeed;
    unsigned int uiDecSpeed;
    unsigned char aucVersion[64+1];
	unsigned char aucModInfo[80+1];
}KT_SM_DEV_INFO_ST;

#if defined(MODULE_PCIE)
DECLARE int CALLBACK KT_SM_Login(char *user_name, char *passwd);
#endif

DECLARE int CALLBACK KT_SM_Init(void);
DECLARE void CALLBACK KT_SM_Cleanup(void);
DECLARE int CALLBACK KT_SM_GetVersionNumber(unsigned int *puiVersion);
DECLARE int CALLBACK KT_SM_GetLastError(unsigned int *err_code);
DECLARE char * CALLBACK KT_SM_GetErrInfo(void);
DECLARE int CALLBACK KT_SM_CanRun(void);

DECLARE int CALLBACK KT_SM_OpenEncPipe(KT_PIPE_HANDLE *phHandle);
DECLARE int CALLBACK KT_SM_Encrypt(KT_PIPE_HANDLE hHandle, unsigned char *pucPlainData, unsigned int uiPlainLen,
                                   unsigned char *pucCipherData, unsigned int *puiCipherLen);
DECLARE int CALLBACK KT_SM_CloseEncPipe(KT_PIPE_HANDLE handle);

DECLARE int CALLBACK KT_SM_OpenDecPipe(KT_PIPE_HANDLE *phHandle);
DECLARE int CALLBACK KT_SM_Decrypt(KT_PIPE_HANDLE hHandle, unsigned char *pucCipherData, unsigned int puiCipherLen,
                                   unsigned char *pucPlainData, unsigned int *uiPlainLen);
DECLARE int CALLBACK KT_SM_CloseDecPipe(KT_PIPE_HANDLE handle);

DECLARE int CALLBACK KT_SM_PackSip(char *pcIpAdrr, unsigned char *pucInputData, unsigned int uiInputDataLen, unsigned char *pucOutputData, unsigned int *puiOutputDataLen);
DECLARE int CALLBACK KT_SM_UnPackSip(char *pcIpAdrr, unsigned char *pucInputData, unsigned int uiInputDataLen, unsigned char *pucOutputData, unsigned int *puiOutputDataLen);

DECLARE int CALLBACK KT_SM_PackMsg(char *pcIpAdrr, unsigned char *pucInputData, unsigned int uiInputDataLen, unsigned char *pucOutputData, unsigned int *puiOutputDataLen);
DECLARE int CALLBACK KT_SM_UnPackMsg(char *pcIpAdrr, unsigned char *pucInputData, unsigned int uiInputDataLen, unsigned char *pucOutputData, unsigned int *puiOutputDataLen);

DECLARE int CALLBACK KT_SM_CheckCryptDev(char *dev_ipv4, int *dev_status);
DECLARE int KT_SM_GetModuleStatus(unsigned int *puiStatusCode, char *pcDescribe);
DECLARE int KT_SM_SavePinCode(char *pcPinCode);
DECLARE int KT_SM_GetDevInfo(KT_SM_DEV_INFO_ST *pstDevInfo);

#ifdef  __cplusplus
}
#endif

#endif  /* end of __KT_SM_H__ */

