/*
 * Kedacom Hardware Abstract Level
 *
 * brd_led.h
 *
 * Copyright (C) 2013-2020, Kedacom, Inc.
 *
 * History:
 *   2013/09/22 - [xuliqin] Create
 *
 */

#ifndef _BRD_LED_H
#define _BRD_LED_H

#ifdef __cplusplus
extern "C" {
#endif

#include <drvlib_def.h>

#ifndef USE_KLSP_DEFINES

/*
 * Led identify defines for all boards
 * use u32 type variable
 *   D[31:16]: Led type
 *   D[ 7: 0]: index for each Led type
 */
#define LED_NO_SHIFT            0
#define LED_NO_MASK             0xff
#define LED_NO(v)               (((v) >> LED_NO_SHIFT) & LED_NO_MASK)

#define LED_TYPE_SHIFT          16
#define LED_TYPE_MASK           0xffff
#define LED_TYPE(v)             (((v) >> LED_TYPE_SHIFT) & LED_TYPE_MASK)

#define LED_ID(type, no) \
        (((type) << LED_TYPE_SHIFT) | \
         ((no)   << LED_NO_SHIFT))

/* one and only led defines */
#define LED_ID_RUN              LED_ID(0, 0)
#define LED_ID_ALARM            LED_ID(0, 1)
#define LED_ID_LINK             LED_ID(0, 2)
#define LED_ID_CHASSIS_NORM     LED_ID(0, 4)
#define LED_ID_CHASSIS_NALM     LED_ID(0, 5)
#define LED_ID_CHASSIS_SALM     LED_ID(0, 6)
#define LED_ID_GREEN            LED_ID(0, 7)
#define LED_ID_ORANGE           LED_ID(0, 8)
#define LED_ID_RED              LED_ID(0, 9)
#define LED_ID_IR               LED_ID(0, 10)
#define LED_ID_ENCODER          LED_ID(0, 11)
#define LED_ID_DECODER          LED_ID(0, 12)
#define LED_ID_SDI              LED_ID(0, 13)
#define LED_ID_CDMA             LED_ID(0, 14)
#define LED_ID_WLAN             LED_ID(0, 15)
#define LED_ID_DISK             LED_ID(0, 16)
#define LED_ID_MPC              LED_ID(0, 17)
#define LED_ID_USB              LED_ID(0, 18)

/* multi led defines */
#define LED_ID_ETH(n)           LED_ID(1, n)
#define LED_ID_E1(n)            LED_ID(2, n)
#define LED_ID_DSP(n)           LED_ID(3, n)
#define LED_ID_VIDEOIN(n)       LED_ID(4, n)
#define LED_ID_BATTER(n)        LED_ID(5, n)
#define LED_ID_HDD(n)           LED_ID(7, n)


/* Led state: D[31:0] = R[31:24] G[23:16] B[15:8] RSV[7:4] MODE[3:0] */
#define LED_COLOR_R(stat)       (((stat) >> 24) & 0xff)
#define LED_COLOR_G(stat)       (((stat) >> 16) & 0xff)
#define LED_COLOR_B(stat)       (((stat) >> 8) & 0xff)
#define LED_MODE(stat)          ((stat) & 0xf)

/*expand led define for led(like mbi5353) which support R16-G16-B16 48leds*/
/* Led state: CTRL[7:4]
 * LED_CTRL(stat) = 0, same with before
 * LED_CTRL(stat) = 1, set each of RED   16 led on/off, bit31-bit16 led15-led0
 * LED_CTRL(stat) = 2, set each of GREEN 16 led on/off, bit31-bit16 led15-led0
 * LED_CTRL(stat) = 3, set each of BLUE  16 led on/off, bit31-bit16 led15-led0
 * LED_CTRL(stat) = 14, sync R/G/B settings, setting will take effect
 * LED_CTRL(stat) = 15, LED_MODE(stat) = 15,
*/
#define LED_CTRL(stat)         (((stat) >> 4) & 0xf)
#define LED_CTRL_TYPE_LEDR         1   /*config red led*/
#define LED_CTRL_TYPE_LEDG         2   /*config green led*/
#define LED_CTRL_TYPE_LEDB         3   /*config blue led*/
#define LED_CTRL_TYPE_SYNC         14  /*config sync ,will be valid*/
#define LED_CTRL_TYPE_SMODE        15  /*config special led mode , eg: one bye one led lighten*/
/*LED_CTRL(stat) = 1 bit31-bit16 led15-led0 bit15-bit8 rsv bit4-bit0 dev_id*/
#define LED_R_STAT(stat)       (((stat) >> 16) & 0xffff)
#define LED_R_CFG(stat)        ((((stat)&0xffff)<<16)|0x10)
/*LED_CTRL(stat) = 2 bit31-bit16 led15-led0 bit15-bit8 rsv bit4-bit0 dev_id*/
#define LED_G_STAT(stat)       (((stat) >> 16) & 0xffff)
#define LED_G_CFG(stat)        ((((stat)&0xffff)<<16)|0x20)
/*LED_CTRL(stat) = 3 bit31-bit16 led15-led0 bit15-bit8 rsv bit4-bit0 dev_id*/
#define LED_B_STAT(stat)       (((stat) >> 16) & 0xffff)
#define LED_B_CFG(stat)        ((((stat)&0xffff)<<16)|0x30)
/*LED_CTRL(stat) = 14*/
#define LED_SYNC_CFG           0x000000e0
/*LED_CTRL(stat) = 15 bit23-bit16 speed(unit 250ms) bit15-bit8 circle cnt bit4-bit0 mode*/
#define LED_SPECIAL_MODE       0x000000f0
#define LED_SMODE_TYPE(stat)   ((stat)&0xf)
#define LED_ONE_BY_ONE 		   0x00000000
#define LED_ONE_STEP_ONE       0x00000001
/*0xff means never stop,0x0 means whatever stop, others means circle times*/
#define LED_SMODE_CIRCLE(stat)        (((stat)>>8)&0xff)
/* mseconds, unit 250ms ,eg: 1->0.25s 2->0.5s,10->2.5s*/
#define LED_SMODE_SPEED(stat)         (((stat)>>16)&0xff)
#define LED_SMODE_CFG(speed,cnt,mode) ((((speed)&0xff)<<16)|(((cnt)&0xff)<<8)|0xf0|((mode)&0xf))

#define LED_MODE_OFF               0
#define LED_MODE_ON                1
#define LED_MODE_FLICKER_FAST      2  /*250ms*/
#define LED_MODE_FLICKER           3  /*1s*/
#define LED_MODE_FLICKER_SLOW      4  /*2s*/
#define LED_MODE_FLICKER_USERDEF   15 /*expand led mode, maybe 1s on 3s off*/

/*LED_MODE(stat) = 15*/
#define LED_ON_TIME(stat)            (((stat) >> 16) & 0xff)  /*seconds*/
#define LED_OFF_TIME(stat)           (((stat) >> 24) & 0xff)  /*seconds*/
#define LED_FLICKER_TIME_CFG(on,off) ((((on)&0xff)<<16)|(((off)&0xff)<<24)|0xf)

#define LED_STAT(r, g, b, m)    ((((r) & 0xff) << 24) | (((g) & 0xff) << 16) | \
                                 (((b) & 0xff) <<  8) | ((m) & 0xf))

#endif /* USE_KLSP_DEFINES */

typedef struct
{
	u32   dwNo;          /* Input: 0 ~ led_registed_num-1 */
	u32   dwId;          /* see also: LED_ID_RUN */
	u32   dwCab;

	char achName[DRVLIB_NAME_MAX_LEN];
} TLedInfo;

typedef struct
{
	u32   dwId;          /* see also: LED_ID_RUN */
	u32   dwState;       /* see also: LED_STATE_OFF */
} TLedStatus;

int BrdLedQueryInfo(TLedInfo *ptInfo);
int BrdLedSetStatus(u32 dwId, u32 dwStatus);
int BrdLedGetStatus(u32 dwId, u32 *pdwStatus);

#ifdef __cplusplus
}
#endif

#endif
