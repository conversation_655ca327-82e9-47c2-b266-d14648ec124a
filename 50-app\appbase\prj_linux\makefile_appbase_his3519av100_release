###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ./

SRC_DIR := $(TOP)/src


## Name and type of the target for this Makefile

SO_TARGET      := appbase

## Define debugging symbols
DEBUG = 1
LINUX_COMPILER= _ARM_HIS3519AV100_
PWLIB_SUPPORT = 0
USE_OSP_TEL_CMD = 1
USE_OSP_MEMPOOL = 1


CFLAGS += -D_LINUX -Wall -fno-omit-frame-pointer -D_HIS3519AV100_

ifeq ($(USE_OSP_TEL_CMD),0)
CFLAGS += -DAPP_DISABLE_OSP_TEL_CMD
endif

ifeq ($(USE_OSP_MEMPOOL),1)
CFLAGS += -D_APP_MEMPOOL_USE_OSP
else
CFLAGS += -D_APP_MEMPOOL_USE_LIST
endif

OBJS :=                         	    \
		$(SRC_DIR)/appbase_utils\
		$(SRC_DIR)/appbase_utils_unicode\
		$(SRC_DIR)/appbase_mempool_implement\
		$(SRC_DIR)/appbase_mempool\
		$(SRC_DIR)/appbase_task\
		$(SRC_DIR)/appbase_cross_linux\
		$(SRC_DIR)/appbase_socket	\
		$(SRC_DIR)/appbase_xml	\
		$(SRC_DIR)/appbase_enum_str_conv \
		$(SRC_DIR)/appbase_log	\
		$(SRC_DIR)/appbase_gmssl
		

## Libraries to include in shared object file
        
#LIBS :=  

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += \
		../../../10-common/include/cbb/osp \
		../../../10-common/include/cbb/debuglog      \
		../../../10-common/include/cbb/mxml      \
		../../../10-common/include/cbb/protobuf       \
		../../../10-common/include/cbb/charconversion  \
            ../../../10-common/include/hal               \
            ../../../10-common/include/hal/drvlib        \
            ../../../10-common/include/hal/netcbb        \
            ../../../10-common/include/hal/ispctrl       \
            ../../../10-common/include/hal/mediactrl       \
            ../../../10-common/include/system            \
            ../../../10-common/include/service           \
            ../../../10-common/include/app               \
            ../include
            

LIB_PATH := ../../../10-common/lib/release/his3519av100
LIBS :=  debuglog pthread m stdc++
ifeq ($(USE_OSP_TEL_CMD),1)
LIBS += osp
endif

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3519av100

include $(COMM_DIR)/makelib.mk

