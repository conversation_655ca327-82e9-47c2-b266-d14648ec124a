/*
 * A simple helpful wrapper to include lots of agent specific include
 * * files for people wanting to embed and snmp agent into an external
 * * application
 */
#ifndef NET_SNMP_AGENT_INCLUDES_H
#define NET_SNMP_AGENT_INCLUDES_H

#include <net-snmp/agent/mib_module_config.h>
#include <net-snmp/agent/agent_module_config.h>

#include <net-snmp/agent/snmp_agent.h>
#include <net-snmp/agent/snmp_vars.h>
#include <net-snmp/agent/ds_agent.h>
#include <net-snmp/agent/agent_handler.h>
#include <net-snmp/agent/agent_read_config.h>
#include <net-snmp/agent/agent_trap.h>
#include <net-snmp/agent/agent_handler.h>
#include <net-snmp/agent/all_helpers.h>
#include <net-snmp/agent/var_struct.h>

#endif                          /* NET_SNMP_AGENT_INCLUDES_H */
