#ifndef _NETCBB_API_FIREWALL_H_
#define _NETCBB_API_FIREWALL_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

//自定义table
#define NETCBB_TABLE_WHITELIST "whitelist"
#define NETCBB_TABLE_BLACKLIST "blacklist"
#define NETCBB_TABLE_ICMPPACKET "icmppacket"

//netcbb-iptables-save-4 netcbb-iptables-save-6
#define NETCBB_DEFAULT_FIREWALL_SAVE_PATH "/etc/netcbb-iptables-save"

typedef enum{
    NETCBB_FILTER_LIST_CMD_DEF = 0,/*invalid*/
    NETCBB_FILTER_WHITE_LIST_ADD,
    NETCBB_FILTER_WHITE_LIST_DEL,
    NETCBB_FILTER_BLACK_LIST_ADD,
    NETCBB_FILTER_BLACK_LIST_DEL,
    NETCBB_FILTER_LIST_CMD_INVALID,/*invalid*/
}ENetcbbFilterCmd;

typedef enum{
    NETCBB_FILTER_DEF = 0,/*invalid*/
    NETCBB_FILTER_WHITE_LIST,
    NETCBB_FILTER_BLACK_LIST,
    NETCBB_FILTER_WHITE_LIST_IPV6,
    NETCBB_FILTER_BLACK_LIST_IPV6,
    NETCBB_FILTER_WHITE_LIST_ALL,//netfilter filter表过滤mac需要考虑ipv4&ipv6两个表，需要用这个枚举量
    NETCBB_FILTER_BLACK_LIST_ALL,
    NETCBB_FILTER_LIST_INVALID,/*invalid*/
}ENetcbbFilterList;

typedef enum{
    NETCBB_FILTER_ICMP_DEF = 0,/*invalid*/
    NETCBB_FILTER_ICMP_IPV4_ACCESS,
    NETCBB_FILTER_ICMP_IPV4_DENY,
    NETCBB_FILTER_ICMP_IPV6_ACCESS,
    NETCBB_FILTER_ICMP_IPV6_DENY,
    NETCBB_FILTER_ICMP_ALL_ACCESS,
    NETCBB_FILTER_ICMP_ALL_DENY,
    NETCBB_FILTER_ICMP_INVALID,/*invalid*/
}ENetcbbIcmpCmd;

typedef enum{
    NETCBB_FILTER_SOCKET_TYPE_DEF = 0,/*invalid*/
    NETCBB_FILTER_SOCKET_TYPE_TCP,//tcp
    NETCBB_FILTER_SOCKET_TYPE_UDP,//udp
    NETCBB_FILTER_SOCKET_TYPE_ALL,//tcp+udp
    NETCBB_FILTER_SOCKET_TYPE_INVALID,/*invalid*/
}ENetcbbSocketType;

typedef enum{
    NETCBB_FILTER_PORT_TYPE_DEF = 0,/*invalid*/
    NETCBB_FILTER_PORT_TYPE_SOURCE,//source
    NETCBB_FILTER_PORT_TYPE_DESTINATION,//dest
    NETCBB_FILTER_PORT_TYPE_INVALID,/*invalid*/
}ENetcbbPortType;

typedef enum{
    NETCBB_FILTER_IP_TYPE_DEF = 0,/*invalid*/
    NETCBB_FILTER_IP_TYPE_SOURCE,//source
    NETCBB_FILTER_IP_TYPE_DESTINATION,//dest
    NETCBB_FILTER_IP_TYPE_INVALID,/*invalid*/
}ENetcbbIpType;

typedef enum{
    NETCBB_FILTER_PORT_RULE_DEF = 0,/*invalid*/
    NETCBB_FILTER_PORT_RULE_INPUT_ACCESS,//入 允许
    NETCBB_FILTER_PORT_RULE_INPUT_DENY,//入 禁止
    NETCBB_FILTER_PORT_RULE_OUTPUT_ACCESS,//出 允许
    NETCBB_FILTER_PORT_RULE_OUTPUT_DENY,//出禁止
    NETCBB_FILTER_PORT_RULE_INVALID,/*invalid*/
}ENetcbbPortFilterRule;

typedef enum{
    NETCBB_FILTER_PORT_ACTION_DEF = 0,/*invalid*/
    NETCBB_FILTER_PORT_ACTION_ADD,
    NETCBB_FILTER_PORT_ACTION_DEL,
    NETCBB_FILTER_PORT_ACTION_INVALID,/*invalid*/
}ENetcbbPortFilterCmd;

//如修改ENetcbbFirewallLogLevel，请同步修改g_achLogLevel
typedef enum{
    NETCBB_FIREWALL_LEVEL_ERR = 0,
    NETCBB_FIREWALL_LEVEL_WARNING,
    NETCBB_FIREWALL_LEVEL_NOTICE,
    NETCBB_FIREWALL_LEVEL_INFO,
    NETCBB_FIREWALL_LEVEL_INVALID,
}ENetcbbFirewallLogLevel;
typedef struct{
    ENetcbbInetType eSinFamily;
    ENetcbbSocketType eSocketType;//tcp、udp
    ENetcbbPortType ePortType;//目的端口、源端口
    ENetcbbPortFilterRule eFilerRule;//出、入方向禁止、允许
    char achPort[NETCBB_BUFFERLENGTH_128];// "1"(单个的) "11:22"(端口区间) "1,3,7,20"(多个不连续端口15个以内，需要内核支持CONFIG_NETFILTER_XT_MATCH_MULTIPORT)
    int nLogFlag;//预留，暂不使用，非0打开，daemon处理时默认打开
    ENetcbbFirewallLogLevel eLevel;//预留，暂不使用，daemon处理时默认使用notice
}TNetcbbFilterPortParams;

typedef struct{
    TNetcbbIfaceIp tStartIp;//起始ip地址
    TNetcbbIfaceIp tEndIp;//结束ip地址，仅在ip段方式时需要配置,内核需要CONFIG_NETFILTER_XT_MATCH_IPRANGE
    ENetcbbIpType eType;//源地址、目的地址，暂不使用该参数
    int nLogFlag;//预留，暂不使用，非0打开，daemon处理时默认打开
    ENetcbbFirewallLogLevel eLevel;//预留，暂不使用，daemon处理时默认使用notice
}TNetcbbIpFilterParams;


typedef struct{
    char achInterface[NETCBB_IFACE_NAME_LEN];//接口地址，可留空，如果增加entry时配置了那么删除时也需配置
    char achMac[NETCBB_BUFFERLENGTH_32];//mac地址，删除时不必配置
    char achIp[NETCBB_BUFFERLENGTH_64];//ip地址，ipv6暂不支持
}TNetcbbArpEntry;


/************************************************************
 * 函数名 : NetcbbEnableFilter
 *
 * 功能 : 使能黑/白名单功能
 *
 * 描述 :
 *        使能黑/白名单功能
 *
 * 参数说明 :
 *        eFilterList ： 黑名单或者白名单
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbEnableFilter(ENetcbbFilterList eFilterList);

/************************************************************
 * 函数名 : NetcbbDisableFilter
 *
 * 功能 : 关闭黑/白名单功能
 *
 * 描述 :
 *        关闭黑/白名单功能
 *
 * 参数说明 :
 *        eFilterList ： 黑名单或者白名单
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbDisableFilter(ENetcbbFilterList eFilterList);

/************************************************************
 * 函数名 : NetcbbIpFilterManage
 *
 * 功能 : 管理黑/白 ip 名单
 *
 * 描述 :
 *        管理黑/白 ip 名单
 *
 * 参数说明 :
 *        eCmd ： 增加或者删除
 *        ptIpParams: ip地址信息
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbIpFilterManage(ENetcbbFilterCmd eCmd, TNetcbbIpFilterParams *ptIpParams);

/************************************************************
 * 函数名 : NetcbbMacFilterManage
 *
 * 功能 : 管理黑/白 mac 名单
 *
 * 描述 :
 *        管理黑/白     mac 名单
 *
 * 参数说明 :
 *        eCmd ： 增加或者删除
 *        pchMac: mac信息
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbMacFilterManage(ENetcbbFilterCmd eCmd, char * pchMac);

/************************************************************
 * 函数名 : NetcbbIpAndMacFilterManage
 *
 * 功能 : 管理黑/白 ip&mac 名单
 *
 * 描述 :
 *        管理黑/白     ip&mac 名单,同时满足ip&mac条件
 *
 * 参数说明 :
 *        eCmd ： 增加或者删除
 *        eCmd ： 增加或者删除
 *        pchMac: mac信息
 *        pchIp: ip信息
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbIpAndMacFilterManage(ENetcbbFilterCmd eCmd, char * pchMac, char * pchIp);

/************************************************************
 * 函数名 : NetcbbAddIptablesChain
 *
 * 功能 : 添加链表
 *
 * 描述 :
 *        添加链表
 *
 * 参数说明 :
 *        pchChain ： 链表名
 *        wSinFamily : ENetcbbInetType
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbAddIptablesChain(ENetcbbInetType wSinFamily, char * pchChain);

/************************************************************
 * 函数名 : NetcbbPingFilterManage
 *
 * 功能 : 禁止、允许ping
 *
 * 描述 :
 *        通过控制echo-request的方式控制ping访问
 *
 * 参数说明 :
 *        eCmd ：命令
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbPingFilterManage(ENetcbbIcmpCmd eCmd);

/************************************************************
 * 函数名 : NetcbbIpFilterManage
 *
 * 功能 : 管理端口过滤
 *
 * 描述 :
 *        管理端口过滤
 *
 * 参数说明 :
 *        eCmd ： 增加或者删除
 *        ptPortParams: 端口信息
 *
 * 返回值 : OK    - 成功
 *          ERROR - 错误
 ***********************************************************/
s32 NetcbbPortFilterManage(ENetcbbPortFilterCmd eCmd, TNetcbbFilterPortParams *ptPortParams);
#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _NETCBB_API_FIREWALL_H_ */
