/**
* @file     ais.proto
* @brief    ais proto
* <AUTHOR>
* @date     2019-06-11
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/
//省略package


//AIS智能配置参数
message TPbAisCfg
{
	repeated TPbAisDetCfg AisDetCfg = 1;				//通道检测算法配置信息
	repeated TPbAisAlgDetParamArray AisAlgDetParam = 2;			//每个通道的检测算法参数
	repeated TPbAisCmpRuleMgr  AisCmpRuleMgr = 3;			//智能比对规则相关配置信息
	repeated TPbAisDetProcessParamArray AisDetProcessParam = 4;		//每个通道的检测处理方式
	repeated TPbAisPersonDetectAlarmParam AisPersonDetectAlarmParam = 5; //通道行人检测告警参数
}
message  TPbAisAlgDetParamArray
{
	repeated TPbAisAlgDetParam AisAlgDetParam_ = 1;
}
message TPbAisDetProcessParamArray
{
	repeated TPbAisDetProcessParam AisDetProcessParam_ = 1;
}







//智能通道检测算法配置
message TPbAisDetCfg
{
	optional uint32 wChnNo = 1;                             //通道号,从1开始，0表示未配置智能检测通道
    optional uint32 wLoadedAlgNum = 2;                      //当前已经配置算法数
    repeated TPbAisDetCfgParam atLoadedAlgParam = 3;			//已经配置加载的算法参数
}
//检测算法部分配置参数
message TPbAisDetCfgParam
{
	optional uint32 bStart = 1;			//是否开启检测
	optional uint32 eDetectMode = 2;	//检测模式
	optional uint32 eLoadedType = 3;	//算法类型
}







//检测算法配置参数
message TPbAisAlgDetParam
{
    optional uint32 bStart = 1;                         //是否启动
    optional uint32 eAlgType = 2;             			//检测类型
    optional uint32 eDetectMode = 3;              		//检测模式
    optional string achEngineName = 4; 					//引擎名称
    optional TPbAisRectRegion tRectRegion = 5;          //长方形面积，10000*10000
    optional uint32 dwMinPD = 6;                        //最小瞳距,预留，暂时不用，内部使用
    optional uint32 dwSensitivity = 7;                  //灵敏度
    optional uint32 eRmRepeatMode = 8;    				//去重模式
    optional uint32 bOnlyDetectPic = 9;                 //仅检测抓拍图片,FALSE表示视频流和手动图片都检测，TRUE表示仅检测抓拍图片
    optional uint32 dwDetectInterval = 10;              //检测间隔时间
	optional uint32 eSnapPicQuailty = 11;				//抓拍图片质量
	optional string car_province    = 12;				//车牌省份
	optional uint32 replace_score   = 13;               //替换可信度分数
	optional int32 nMinFaceSize     = 14;               ///<最小人脸尺寸
    optional int32 nIsdeblur        = 15;               ///<是否去模糊 0：不去模糊 1：去模糊
	optional int32 dwMinFaceHeight  = 16;				///<最小人脸高度
	optional int32 dwMinFaceWidth   = 17;				///<最小人脸宽度
	optional int32 dwReSnapInterval = 18;				///<重复抓拍间隔
}
//长方形区域结构体
message TPbAisRectRegion
{
	optional uint32 wStartX = 1;   //起点x坐标
	optional uint32 wStartY = 2;   //起点y坐标
	optional uint32 wWidth = 3;    //宽
	optional uint32 wHeight = 4;   //高
}



//行人检测通道告警参数
message TPbAisPersonDetectAlarmParam
{
	repeated TPbAisGuardDayTime atAisGuardTimeOfDay = 3; 			//布防时间参数
	optional TPbAisLinkMode tAisLinkMode = 4;                      	//联动参数
}


//比对规则相关管理
message TPbAisCmpRuleMgr
{
	optional uint32 wRuleIndex = 1;                                 //比对规则索引，从1开始
	optional TPbAisCmpRuleParam tRuleParam = 2;                     //比对规则参数
	repeated TPbAisGuardDayTime atAisGuardTimeOfDay = 3; 			//布防时间参数
	optional TPbAisLinkMode tAisLinkMode = 4;                      	//联动参数
}
//比对规则参数
message TPbAisCmpRuleParam
{
    optional TPbAisUnicode64Str tRuleName = 1;   //规则名称
    optional TPbAisUnicode64Str tLibName = 2;    //布控库名称
    optional uint32 eMode = 3;           			//比对模式
    optional uint32 wSimilarity = 4;                //相似度
    optional uint32 wChnNum = 5;                    //参与比对的通道数量
    repeated uint32 achChn = 6;     				//对应通道号
    optional uint32 wAlarmDedupInterval = 7;		//告警去重时间间隔，时间间隔内相同的人员告警仅告警一次，当前仅黑名单告警有效
	
	optional uint32 eCarCule = 8;                //车辆匹配规则
	optional uint32 eTarget = 9;                //匹配规则目标物，人脸或车辆
}
//长度为64的unicode字符串
message TPbAisUnicode64Str
{
    optional uint32 dwLen = 1;           //unicode长度
    optional bytes abyUnicodeStr = 2;    //长度为64的unicode字符串
}
//一天布防时间
message TPbAisGuardDayTime
{
	optional uint32 wNum = 1;					            //有效时间段个数
	repeated TPbAisTimeSegment atGuardTimeOfDay = 2; 			//时间段
}
//时间段参数
message TPbAisTimeSegment
{
	optional uint32 dwStartTime = 1;			//时间段开始时间(0~86399, 00:00:00~23:59:59)
	optional uint32 dwEndTime = 2;				//时间段结束时间(0~86399, 00:00:00~23:59:59)
}
//告警联动方式
message TPbAisLinkMode
{
	optional TPbAisRoutineLink tRoutineLink = 1;		//常规联动参数
	optional TPbAisLinkOut tLinkOut = 2;				//联动输出参数
	optional TPbAisLinkRec tLinkRec = 3;				//联动到通道录像参数
	optional TPbAisLinkRec tLinkSnap = 4;			    //联动到通道抓拍参数
	optional TPbAisLinkPtz tLinkPtz = 5;				//联动到通道ptz参数
	optional TPbAisLinkSound tLinkSound = 6;			//联动声音参数
}
enum EPbAlarmLinkUploadMode
{
	ALARM_ONLY = 0;         //仅告警
	ALL        = 1;         //全部
}

enum EPbAlarmLinkUploadType
{
	FACE       = 0;			//人脸照
	BACKGROUND = 1;         //全景照
}
//告警联动上传视图库配置
message TPbAisAlarmLinkUploadCfg
{
    optional uint32 enable = 1;               //是否使能
	optional EPbAlarmLinkUploadMode mode = 2; //上传模式
	optional EPbAlarmLinkUploadType type = 3; //上传类型
}
//常规联动参数
message TPbAisRoutineLink
{
	optional uint32 bySound = 1;			//联动声音报警
	optional uint32 byMail = 2;			    //发送邮件
	optional uint32 byPostCenter = 3;       //上报中心
	optional uint32 byCloudSrv = 4;         //上报云服务
	repeated uint32 abyDspHDMI = 5;			//HDMI输出使能
	repeated uint32 abyDspVGA = 6;			//VGA输出使能
	repeated TPbAisAlarmLinkUploadCfg upload_cfg = 7;         //上传方式配置
}
//联动输出参数
message TPbAisLinkOut
{
	optional TPbAisLinkLocalOut tLinkLocalOut = 1;		//联动到本地输出参数
	optional uint32 wNum = 2;							//有效通道个数
	repeated TPbAisLinkChnOut atLinkChnOut = 3;			//联动到通道告警输出参数
}
//联动到nvr本地输出参数
message TPbAisLinkLocalOut
{
	optional uint32 wNum = 1;				//有效输出个数
	repeated uint32 abyOutList = 2;			//输出编号列表,从0开始计算
}
//联动到通道输出参数
message TPbAisLinkChnOut
{
	optional uint32 wChnId = 1;			//通道id
	optional uint32 wNum = 2;		   	//有效输出个数
	repeated uint32 abyOutList = 3;	   	//输出编号列表
}
//联动到通道录像参数
message TPbAisLinkRec
{
	optional uint32 wNum = 1;			//有效通道个数
	repeated uint32 awChnList = 2;		//通道列表
}
//联动到通道ptz参数
message TPbAisLinkPtz
{
	optional uint32 wNum = 1;					//有效通道个数
	repeated TPbAisLinkPtzUnit atPtzUnit = 2;	//联动到ptz参数
}
//联动到通道预置位参数
message TPbAisLinkPtzUnit
{
	optional uint32 wChnId = 1;				//通道id
	optional uint32 eType = 2;			    //联动ptz类型
	optional uint32 wNum = 3;				//编号
}

message TPbAisLinkSound
{
	optional uint32 byBeepEnable = 1;      //蜂鸣器使能
	optional uint32 bySoundOut = 2;		   //声音输出使能
	optional uint32 byTextId = 3;		   //文本数据库Id
}

//检测处理方式
message TPbAisDetProcessParam
{
    optional uint32 eAlgType = 1;           //检测类型
    repeated uint32 abUpload = 2;       	//是否上传
    repeated uint32 aeUploadMode = 3;		//上传方式
    optional uint32 bNotUseMobileNet = 4;   //是否不使用移动网络上传
}


///<Coi配置
message TPbAisCoiCfgParam
{
    optional TPbAisUnicode64Str black_lib = 1;   ///<黑名单
    optional TPbAisUnicode64Str white_lib = 2;   ///<白名单
}












