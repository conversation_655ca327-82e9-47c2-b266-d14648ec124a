#ifndef _KNET_CMD_ETH_H
#define _KNET_CMD_ETH_H

#define KNET_IP_MAX_NUM 16

typedef union {
    struct {
        char    mIpAdrs[IP_ADDR_SIZE];
        uint8_t mIpMask;
        uint8_t mMacAdrs[6];
    };
    uint32_t mMtu;
} eth_param_t;

typedef struct {
    /* sub IP addresses */
	int mIpNum;
	eth_param_t mNetEthParam[KNET_IP_MAX_NUM];

    /* interface current mtu */
    uint32_t mMtu;
} eth_param_multi_ip_t;



int32_t IfaceShutdown(uint32_t ethId);
int32_t IfaceStartup(uint32_t ethId);
bool IfaceIsStarted(uint32_t ethId);

/*
 * Get the first ip/mask on the interface refs 'ethId',
 * and its mac address
 */
int32_t EthGetParams(uint32_t ethId, eth_param_t *param);

/*
 * Get the all ip/mask on the interface refs 'ethId'
 * and its mac address
 */
int32_t EthGetParams(uint32_t ethId, eth_param_multi_ip_t *params);

/*
 * Del the all ip/mask on the interface refs 'ethId'
 */
int32_t EthDelParams(uint32_t ethId);

/*
 * Del the specific ip/mask on the interface refs 'ethId'
 */
int32_t EthDelParams(uint32_t ethId, eth_param_t *param);

/*
 * Set the specific param which represented by 'type' on the interface refs 'ethId'
 */

/*
 * Assign to 'type'
 */
#define KNET_SET_IP_AND_MASK 1
#define KNET_SET_MAC_ADDRESS 2
#define KNET_SET_ALL_PARAM   3
#define KNET_SET_SECOND_IP   4
#define KNET_SET_MTU         5

int32_t EthSetParams(uint32_t ethId, uint32_t type, eth_param_t *param);

/*
 * Set ethernet-phy duplex/speed, this implemented by drvlib
 * API: BrdEthSetNego, usage please refs drvlib/api
 *
 * And here for android which need gain the root perms
 * on IPC model.
 *
 */
int32_t EthSetNegos(uint32_t ethId, int32_t duplex, int32_t speed);

#endif
