#ifndef _MANDETECT_H_
#define _MANDETECT_H_

#include "VideoAlg.h"
#define MAX_DETECT_INFO 30



// typedef enum tagManDetectModule
// {             
// 	PROCESS_ONLY=0,处理单帧输入图像，并输出检测人脸数及对应坐标
// 	OP_FACE_OUT,/*跟踪视频序列，并在人脸丢失时输出最佳人脸和对应帧及坐标*/
// }EMManDetectModule;

typedef struct tagManDetectOpen
{
    EMImageFormat emFormat;
    char s8GPUBinPath[200];
    char s8ManDetectBinPath[3][200];
    // EMManDetectModule emMDModule;
  
}TKEDAManDetectOpen;


// roi info (input)
typedef struct tagManmodelROI	//人员检测的范围（是否需要使用待确认）
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
    // int timeStep;
    // int manualMode;
}TManDetectROI;



// man detect info (output)
typedef struct
{
	int up;				//输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
	int down;
	int left;
	int right;
    float score;
    // int manualMode;
	void* pvbuffertag;
}TRectMan;

typedef struct tagManDetectOutput
{
    unsigned int u32ManDetectNumber;			   // 输出检测出的人员数量
	TRectMan tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo;





#endif