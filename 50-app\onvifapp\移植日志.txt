# 需要做

* onvifapp_config.h中定义的宏，需要将无用的删除掉。
* 需要检查有没有多余的配置项。

	1. dwScopeMaxSize的作用使用宏替代。
	2. szListenIP不需要
	
* 废除s_awOnvifHomePos和s_nOnvifHomePos_Zoom，他们记录的数据放到配置结构里。
* 废除_OnvifAppReadHomePosFromCfgFile
* 废除_OnvifAppSaveHomePosToCfgFile，使用整体保存配置接口替代。
* g_ptOnvifAppConfig->dwScopeMaxSize 需要删除
* _OnvifAppLoadScope需要废除
* g_hOnvifScopeList需要替代掉
* 初始化模块失败，需要认为startapp失败。
* _OnvifAppInitConfigurations函数需要返回错误码，并且需要改名字。
* ONVIF_TOKEN_MAXSIZE和ONVIFAPP_TOKEN_BUF_LEN合并为同一个。

# 正在做

* 将device服务的GetServices接口开放出来。

# 已完成

1. onvifapp需要使用goaheadhelper中的接口。（2017-12-15）
2. 将所有文件编码格式统一为gbk，并使注释不再是乱码。（2017-12-4）
3. 将日志打印接口改用nvrv7的。（2017-12-4）
4. 优化激活状态的获取。（2017-12-15）
5. 激活邮箱写作“<EMAIL>”，是否可以？（2018-1-2）
  
    需求的意思，跟ipcv7统一，也写做"<EMAIL>"

6. 将配置接口改用nvrv7的。（done）