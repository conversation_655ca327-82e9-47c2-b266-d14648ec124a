path="../../10-common/version/compileinfo/nvrcustcap_qcom.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_custcap_linux for qcom           =
echo ==============================================

echo "============compile nvrcustcap qcom============">>../$path

make -e DEBUG=0 -f makefile_qcom clean
make -e DEBUG=0 -f makefile_qcom 2>>../$path

cp -L -r -f libnvrcustcap.so ../../../10-common/lib/release/qcom/speprolib
cd ..
