/*
 * Copyright (c) 2007 KEDACOM
 *
 * @file:	input.h
 * @desc:	usb keyboard/mouse api
 * @author:	<PERSON>
 * @version:	1.0.0
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */
#ifndef __INPUT_H__
#define __INPUT_H__

#ifdef __cplusplus
extern "C" { 
#endif // __cplusplus

struct input_ev {
	unsigned int  type;
	unsigned int  action;
	unsigned int  value;
};

// type:
#define MOUSE 	1
#define KEYBOARD	 2

	
// action:
#define KEYBOARD_PRESS 		1
#define KEYBOARD_RELEASE 	2
#define KEYBOARD_REPEAT	3

#define MOUSE_LEFT_PRESS	 4
#define MOUSE_LEFT_RELEASE		 5

#define MOUSE_RIGHT_PRESS 		7
#define MOUSE_RIGHT_RELEASE	 8

#define MOUSE_MIDDLE_PRESS 	10
#define MOUSE_MIDDLE_RELEASE 	11

#define MOUSE_MOVE 		13
#define MOUSE_WHEEL_MOVE	 14


// value:

// keyboard key value:
#define KEY_RESERVED		0
#define KEY_ESC			1
#define KEY_1			2
#define KEY_2			3
#define KEY_3			4
#define KEY_4			5
#define KEY_5			6
#define KEY_6			7
#define KEY_7			8
#define KEY_8			9
#define KEY_9			10
#define KEY_0			11
#define KEY_MINUS		12
#define KEY_EQUAL		13
#define KEY_BACKSPACE		14
#define KEY_TAB			15
#define KEY_Q			16
#define KEY_W			17
#define KEY_E			18
#define KEY_R			19
#define KEY_T			20
#define KEY_Y			21
#define KEY_U			22
#define KEY_I			23
#define KEY_O			24
#define KEY_P			25
#define KEY_LEFTBRACE		26
#define KEY_RIGHTBRACE		27
#define KEY_ENTER		28
#define KEY_LEFTCTRL		29
#define KEY_A			30
#define KEY_S			31
#define KEY_D			32
#define KEY_F			33
#define KEY_G			34
#define KEY_H			35
#define KEY_J			36
#define KEY_K			37
#define KEY_L			38
#define KEY_SEMICOLON		39
#define KEY_APOSTROPHE		40
#define KEY_GRAVE		41
#define KEY_LEFTSHIFT		42
#define KEY_BACKSLASH		43
#define KEY_Z			44
#define KEY_X			45
#define KEY_C			46
#define KEY_V			47
#define KEY_B			48
#define KEY_N			49
#define KEY_M			50
#define KEY_COMMA		51
#define KEY_DOT			52
#define KEY_SLASH		53
#define KEY_RIGHTSHIFT		54
#define KEY_KPASTERISK		55
#define KEY_LEFTALT		56
#define KEY_SPACE		57
#define KEY_CAPSLOCK		58
#define KEY_F1			59
#define KEY_F2			60
#define KEY_F3			61
#define KEY_F4			62
#define KEY_F5			63
#define KEY_F6			64
#define KEY_F7			65
#define KEY_F8			66
#define KEY_F9			67
#define KEY_F10			68
#define KEY_NUMLOCK		69
#define KEY_SCROLLLOCK		70
#define KEY_KP7			71
#define KEY_KP8			72
#define KEY_KP9			73
#define KEY_KPMINUS		74
#define KEY_KP4			75
#define KEY_KP5			76
#define KEY_KP6			77
#define KEY_KPPLUS		78
#define KEY_KP1			79
#define KEY_KP2			80
#define KEY_KP3			81
#define KEY_KP0			82
#define KEY_KPDOT		83

#define KEY_ZENKAKUHANKAKU	85
#define KEY_102ND		86
#define KEY_F11			87
#define KEY_F12			88
#define KEY_RO			89
#define KEY_KATAKANA		90
#define KEY_HIRAGANA		91
#define KEY_HENKAN		92
#define KEY_KATAKANAHIRAGANA	93
#define KEY_MUHENKAN		94
#define KEY_KPJPCOMMA		95
#define KEY_KPENTER		96
#define KEY_RIGHTCTRL		97
#define KEY_KPSLASH		98
#define KEY_SYSRQ		99
#define KEY_RIGHTALT		100
#define KEY_LINEFEED		101
#define KEY_HOME		102
#define KEY_UP			103
#define KEY_PAGEUP		104
#define KEY_LEFT		105
#define KEY_RIGHT		106
#define KEY_END			107
#define KEY_DOWN		108
#define KEY_PAGEDOWN		109
#define KEY_INSERT		110
#define KEY_DELETE		111
#define KEY_MACRO		112
#define KEY_MUTE		113
#define KEY_VOLUMEDOWN		114
#define KEY_VOLUMEUP		115
#define KEY_POWER		116
#define KEY_KPEQUAL		117
#define KEY_KPPLUSMINUS		118
#define KEY_PAUSE		119

// mouse value:
// 0x12345678 
// 5678:x, 1234:y 

// mouse wheel value
// 0x12340001: up 1234
// 0x12340002: down 1234



/*
 * input_init_monitor - init input monitor
 * @input - callback function
 * @width - screen width
 * @height - screen height
 * return - 0: success; -1: failure
 */
int input_init_monitor(int (*input)(struct input_ev *), unsigned int width, unsigned int height);


/*
 * input_set_mouse_speed - set mouse speed ratio
 * @ratio - speed ratio (must > 0)
 * return - 0: success; -1: failure
 */
int input_set_mouse_speed(unsigned char ratio);


/*
 * input_get_version - get module version
 * return - char pointer pointed to version string
 */
char * input_get_version(void);


/*
 * input_set_debug_level - set debug level
 * @ratio - level
 * return - no
 */
void input_set_debug_level(int level);


#ifdef __cplusplus 
} 
#endif // __cplusplus

#endif // __INPUT_H__
