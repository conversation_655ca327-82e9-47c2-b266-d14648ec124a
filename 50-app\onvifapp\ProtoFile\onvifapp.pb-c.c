/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: onvifapp.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "onvifapp.pb-c.h"
void   onvif_app__cfg__home_position__init
                     (OnvifApp__Cfg__HomePosition         *message)
{
  static OnvifApp__Cfg__HomePosition init_value = ONVIF_APP__CFG__HOME_POSITION__INIT;
  *message = init_value;
}
void   onvif_app__cfg__media_profile__init
                     (OnvifApp__Cfg__MediaProfile         *message)
{
  static OnvifApp__Cfg__MediaProfile init_value = ONVIF_APP__CFG__MEDIA_PROFILE__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_aud_cfg__init
                     (OnvifApp__Cfg__ChnMediaAudCfg         *message)
{
  static OnvifApp__Cfg__ChnMediaAudCfg init_value = ONVIF_APP__CFG__CHN_MEDIA_AUD_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_aud_out_cfg__init
                     (OnvifApp__Cfg__ChnMediaAudOutCfg         *message)
{
  static OnvifApp__Cfg__ChnMediaAudOutCfg init_value = ONVIF_APP__CFG__CHN_MEDIA_AUD_OUT_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_cfg__init
                     (OnvifApp__Cfg__ChnMediaCfg         *message)
{
  static OnvifApp__Cfg__ChnMediaCfg init_value = ONVIF_APP__CFG__CHN_MEDIA_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__multicast_cfg__init
                     (OnvifApp__Cfg__MulticastCfg         *message)
{
  static OnvifApp__Cfg__MulticastCfg init_value = ONVIF_APP__CFG__MULTICAST_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__event_subscription__init
                     (OnvifApp__Cfg__EventSubscription         *message)
{
  static OnvifApp__Cfg__EventSubscription init_value = ONVIF_APP__CFG__EVENT_SUBSCRIPTION__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_mdcfg_ext__init
                     (OnvifApp__Cfg__ChnMediaMDCfgExt         *message)
{
  static OnvifApp__Cfg__ChnMediaMDCfgExt init_value = ONVIF_APP__CFG__CHN_MEDIA_MDCFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_default_ptz_speed__init
                     (OnvifApp__Cfg__ChnDefaultPtzSpeed         *message)
{
  static OnvifApp__Cfg__ChnDefaultPtzSpeed init_value = ONVIF_APP__CFG__CHN_DEFAULT_PTZ_SPEED__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_ptz_cfg_ext__init
                     (OnvifApp__Cfg__ChnPtzCfgExt         *message)
{
  static OnvifApp__Cfg__ChnPtzCfgExt init_value = ONVIF_APP__CFG__CHN_PTZ_CFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_osd_cfg_ext__init
                     (OnvifApp__Cfg__ChnOsdCfgExt         *message)
{
  static OnvifApp__Cfg__ChnOsdCfgExt init_value = ONVIF_APP__CFG__CHN_OSD_CFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_vid_enc_cfg_ext__init
                     (OnvifApp__Cfg__ChnMediaVidEncCfgExt         *message)
{
  static OnvifApp__Cfg__ChnMediaVidEncCfgExt init_value = ONVIF_APP__CFG__CHN_MEDIA_VID_ENC_CFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_media_aud_enc_cfg_ext__init
                     (OnvifApp__Cfg__ChnMediaAudEncCfgExt         *message)
{
  static OnvifApp__Cfg__ChnMediaAudEncCfgExt init_value = ONVIF_APP__CFG__CHN_MEDIA_AUD_ENC_CFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_cfg_ext__init
                     (OnvifApp__Cfg__ChnCfgExt         *message)
{
  static OnvifApp__Cfg__ChnCfgExt init_value = ONVIF_APP__CFG__CHN_CFG_EXT__INIT;
  *message = init_value;
}
void   onvif_app__cfg__record_source_info__init
                     (OnvifApp__Cfg__RecordSourceInfo         *message)
{
  static OnvifApp__Cfg__RecordSourceInfo init_value = ONVIF_APP__CFG__RECORD_SOURCE_INFO__INIT;
  *message = init_value;
}
void   onvif_app__cfg__record_job_cfg__init
                     (OnvifApp__Cfg__RecordJobCfg         *message)
{
  static OnvifApp__Cfg__RecordJobCfg init_value = ONVIF_APP__CFG__RECORD_JOB_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__record_cfg__init
                     (OnvifApp__Cfg__RecordCfg         *message)
{
  static OnvifApp__Cfg__RecordCfg init_value = ONVIF_APP__CFG__RECORD_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__chn_record_cfg__init
                     (OnvifApp__Cfg__ChnRecordCfg         *message)
{
  static OnvifApp__Cfg__ChnRecordCfg init_value = ONVIF_APP__CFG__CHN_RECORD_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__preset_tour_cfg__init
                     (OnvifApp__Cfg__PresetTourCfg         *message)
{
  static OnvifApp__Cfg__PresetTourCfg init_value = ONVIF_APP__CFG__PRESET_TOUR_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__onvif_proto_only_cfg__init
                     (OnvifApp__Cfg__OnvifProtoOnlyCfg         *message)
{
  static OnvifApp__Cfg__OnvifProtoOnlyCfg init_value = ONVIF_APP__CFG__ONVIF_PROTO_ONLY_CFG__INIT;
  *message = init_value;
}
void   onvif_app__cfg__init
                     (OnvifApp__Cfg         *message)
{
  static OnvifApp__Cfg init_value = ONVIF_APP__CFG__INIT;
  *message = init_value;
}
size_t onvif_app__cfg__get_packed_size
                     (const OnvifApp__Cfg *message)
{
  assert(message->base.descriptor == &onvif_app__cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t onvif_app__cfg__pack
                     (const OnvifApp__Cfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &onvif_app__cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t onvif_app__cfg__pack_to_buffer
                     (const OnvifApp__Cfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &onvif_app__cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
OnvifApp__Cfg *
       onvif_app__cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (OnvifApp__Cfg *)
     protobuf_c_message_unpack (&onvif_app__cfg__descriptor,
                                allocator, len, data);
}
void   onvif_app__cfg__free_unpacked
                     (OnvifApp__Cfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &onvif_app__cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor onvif_app__cfg__home_position__field_descriptors[3] =
{
  {
    "P",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__HomePosition, p),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "T",
    2,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__HomePosition, t),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Z",
    3,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__HomePosition, z),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__home_position__field_indices_by_name[] = {
  0,   /* field[0] = P */
  1,   /* field[1] = T */
  2,   /* field[2] = Z */
};
static const ProtobufCIntRange onvif_app__cfg__home_position__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__home_position__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.HomePosition",
  "HomePosition",
  "OnvifApp__Cfg__HomePosition",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__HomePosition),
  3,
  onvif_app__cfg__home_position__field_descriptors,
  onvif_app__cfg__home_position__field_indices_by_name,
  1,  onvif_app__cfg__home_position__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__home_position__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__media_profile__field_descriptors[13] =
{
  {
    "State",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, state),
    &onvif_app__cfg__eprofile_state__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Token",
    2,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, token),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Name",
    3,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ChnID",
    4,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, chnid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HasVidSrcCfgID",
    5,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, hasvidsrccfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "VidEncCfgID",
    6,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, videnccfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HasPtzCfg",
    7,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MediaProfile, hasptzcfg),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudSrcCfgID",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_audsrccfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, audsrccfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudEncCfgID",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_audenccfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, audenccfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MetadataCfgID",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_metadatacfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, metadatacfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudOutCfgID",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_audoutcfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, audoutcfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudDecCfgID",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_auddeccfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, auddeccfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AnalyticsCfgID",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MediaProfile, has_analyticscfgid),
    offsetof(OnvifApp__Cfg__MediaProfile, analyticscfgid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__media_profile__field_indices_by_name[] = {
  12,   /* field[12] = AnalyticsCfgID */
  11,   /* field[11] = AudDecCfgID */
  8,   /* field[8] = AudEncCfgID */
  10,   /* field[10] = AudOutCfgID */
  7,   /* field[7] = AudSrcCfgID */
  3,   /* field[3] = ChnID */
  6,   /* field[6] = HasPtzCfg */
  4,   /* field[4] = HasVidSrcCfgID */
  9,   /* field[9] = MetadataCfgID */
  2,   /* field[2] = Name */
  0,   /* field[0] = State */
  1,   /* field[1] = Token */
  5,   /* field[5] = VidEncCfgID */
};
static const ProtobufCIntRange onvif_app__cfg__media_profile__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 13 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__media_profile__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.MediaProfile",
  "MediaProfile",
  "OnvifApp__Cfg__MediaProfile",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__MediaProfile),
  13,
  onvif_app__cfg__media_profile__field_descriptors,
  onvif_app__cfg__media_profile__field_indices_by_name,
  1,  onvif_app__cfg__media_profile__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__media_profile__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_aud_cfg__field_descriptors[2] =
{
  {
    "AudSrcCfgName",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaAudCfg, audsrccfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudEncCfgName",
    2,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaAudCfg, audenccfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_aud_cfg__field_indices_by_name[] = {
  1,   /* field[1] = AudEncCfgName */
  0,   /* field[0] = AudSrcCfgName */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_aud_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_aud_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaAudCfg",
  "ChnMediaAudCfg",
  "OnvifApp__Cfg__ChnMediaAudCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaAudCfg),
  2,
  onvif_app__cfg__chn_media_aud_cfg__field_descriptors,
  onvif_app__cfg__chn_media_aud_cfg__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_aud_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_aud_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_aud_out_cfg__field_descriptors[2] =
{
  {
    "AudOutCfgName",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaAudOutCfg, audoutcfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudDecCfgName",
    2,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaAudOutCfg, auddeccfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_aud_out_cfg__field_indices_by_name[] = {
  1,   /* field[1] = AudDecCfgName */
  0,   /* field[0] = AudOutCfgName */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_aud_out_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_aud_out_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaAudOutCfg",
  "ChnMediaAudOutCfg",
  "OnvifApp__Cfg__ChnMediaAudOutCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaAudOutCfg),
  2,
  onvif_app__cfg__chn_media_aud_out_cfg__field_descriptors,
  onvif_app__cfg__chn_media_aud_out_cfg__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_aud_out_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_aud_out_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_cfg__field_descriptors[6] =
{
  {
    "VidSrcCfgName",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaCfg, vidsrccfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "VidEncCfgName",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_STRING,
    offsetof(OnvifApp__Cfg__ChnMediaCfg, n_videnccfgname),
    offsetof(OnvifApp__Cfg__ChnMediaCfg, videnccfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudCfg",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__ChnMediaCfg, n_audcfg),
    offsetof(OnvifApp__Cfg__ChnMediaCfg, audcfg),
    &onvif_app__cfg__chn_media_aud_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PtzCfgName",
    4,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaCfg, ptzcfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MetadataCfgName",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaCfg, metadatacfgname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudOutCfg",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__ChnMediaCfg, n_audoutcfg),
    offsetof(OnvifApp__Cfg__ChnMediaCfg, audoutcfg),
    &onvif_app__cfg__chn_media_aud_out_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_cfg__field_indices_by_name[] = {
  2,   /* field[2] = AudCfg */
  5,   /* field[5] = AudOutCfg */
  4,   /* field[4] = MetadataCfgName */
  3,   /* field[3] = PtzCfgName */
  1,   /* field[1] = VidEncCfgName */
  0,   /* field[0] = VidSrcCfgName */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaCfg",
  "ChnMediaCfg",
  "OnvifApp__Cfg__ChnMediaCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaCfg),
  6,
  onvif_app__cfg__chn_media_cfg__field_descriptors,
  onvif_app__cfg__chn_media_cfg__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__multicast_cfg__field_descriptors[4] =
{
  {
    "IpType",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MulticastCfg, has_iptype),
    offsetof(OnvifApp__Cfg__MulticastCfg, iptype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "IpAddr",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__MulticastCfg, ipaddr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MulticastCfg, has_port),
    offsetof(OnvifApp__Cfg__MulticastCfg, port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "TTL",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__MulticastCfg, has_ttl),
    offsetof(OnvifApp__Cfg__MulticastCfg, ttl),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__multicast_cfg__field_indices_by_name[] = {
  1,   /* field[1] = IpAddr */
  0,   /* field[0] = IpType */
  2,   /* field[2] = Port */
  3,   /* field[3] = TTL */
};
static const ProtobufCIntRange onvif_app__cfg__multicast_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__multicast_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.MulticastCfg",
  "MulticastCfg",
  "OnvifApp__Cfg__MulticastCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__MulticastCfg),
  4,
  onvif_app__cfg__multicast_cfg__field_descriptors,
  onvif_app__cfg__multicast_cfg__field_indices_by_name,
  1,  onvif_app__cfg__multicast_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__multicast_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__event_subscription__field_descriptors[2] =
{
  {
    "Filter",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__EventSubscription, has_filter),
    offsetof(OnvifApp__Cfg__EventSubscription, filter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "TopicExpression",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__EventSubscription, topicexpression),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__event_subscription__field_indices_by_name[] = {
  0,   /* field[0] = Filter */
  1,   /* field[1] = TopicExpression */
};
static const ProtobufCIntRange onvif_app__cfg__event_subscription__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__event_subscription__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.EventSubscription",
  "EventSubscription",
  "OnvifApp__Cfg__EventSubscription",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__EventSubscription),
  2,
  onvif_app__cfg__event_subscription__field_descriptors,
  onvif_app__cfg__event_subscription__field_indices_by_name,
  1,  onvif_app__cfg__event_subscription__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__event_subscription__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_mdcfg_ext__field_descriptors[5] =
{
  {
    "MDMultiCfg",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, mdmulticfg),
    &onvif_app__cfg__multicast_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "SessionTimeout",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, has_sessiontimeout),
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, sessiontimeout),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bEvtSubscrip",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, has_bevtsubscrip),
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, bevtsubscrip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "EvtSubscrip",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, evtsubscrip),
    &onvif_app__cfg__event_subscription__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Analytics",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, has_analytics),
    offsetof(OnvifApp__Cfg__ChnMediaMDCfgExt, analytics),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_mdcfg_ext__field_indices_by_name[] = {
  4,   /* field[4] = Analytics */
  3,   /* field[3] = EvtSubscrip */
  0,   /* field[0] = MDMultiCfg */
  1,   /* field[1] = SessionTimeout */
  2,   /* field[2] = bEvtSubscrip */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_mdcfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_mdcfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaMDCfgExt",
  "ChnMediaMDCfgExt",
  "OnvifApp__Cfg__ChnMediaMDCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaMDCfgExt),
  5,
  onvif_app__cfg__chn_media_mdcfg_ext__field_descriptors,
  onvif_app__cfg__chn_media_mdcfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_mdcfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_mdcfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_default_ptz_speed__field_descriptors[3] =
{
  {
    "PanTilt_X",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, has_pantilt_x),
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, pantilt_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PanTilt_Y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, has_pantilt_y),
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, pantilt_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Zoom_X",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, has_zoom_x),
    offsetof(OnvifApp__Cfg__ChnDefaultPtzSpeed, zoom_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_default_ptz_speed__field_indices_by_name[] = {
  0,   /* field[0] = PanTilt_X */
  1,   /* field[1] = PanTilt_Y */
  2,   /* field[2] = Zoom_X */
};
static const ProtobufCIntRange onvif_app__cfg__chn_default_ptz_speed__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_default_ptz_speed__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnDefaultPtzSpeed",
  "ChnDefaultPtzSpeed",
  "OnvifApp__Cfg__ChnDefaultPtzSpeed",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnDefaultPtzSpeed),
  3,
  onvif_app__cfg__chn_default_ptz_speed__field_descriptors,
  onvif_app__cfg__chn_default_ptz_speed__field_indices_by_name,
  1,  onvif_app__cfg__chn_default_ptz_speed__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_default_ptz_speed__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_ptz_cfg_ext__field_descriptors[1] =
{
  {
    "DefPtzSpeed",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnPtzCfgExt, defptzspeed),
    &onvif_app__cfg__chn_default_ptz_speed__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_ptz_cfg_ext__field_indices_by_name[] = {
  0,   /* field[0] = DefPtzSpeed */
};
static const ProtobufCIntRange onvif_app__cfg__chn_ptz_cfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_ptz_cfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnPtzCfgExt",
  "ChnPtzCfgExt",
  "OnvifApp__Cfg__ChnPtzCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnPtzCfgExt),
  1,
  onvif_app__cfg__chn_ptz_cfg_ext__field_descriptors,
  onvif_app__cfg__chn_ptz_cfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_ptz_cfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_ptz_cfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_osd_cfg_ext__field_descriptors[3] =
{
  {
    "TimePosType",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnOsdCfgExt, timepostype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "LabelPosType",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnOsdCfgExt, labelpostype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "UserPosType",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_STRING,
    offsetof(OnvifApp__Cfg__ChnOsdCfgExt, n_userpostype),
    offsetof(OnvifApp__Cfg__ChnOsdCfgExt, userpostype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_osd_cfg_ext__field_indices_by_name[] = {
  1,   /* field[1] = LabelPosType */
  0,   /* field[0] = TimePosType */
  2,   /* field[2] = UserPosType */
};
static const ProtobufCIntRange onvif_app__cfg__chn_osd_cfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_osd_cfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnOsdCfgExt",
  "ChnOsdCfgExt",
  "OnvifApp__Cfg__ChnOsdCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnOsdCfgExt),
  3,
  onvif_app__cfg__chn_osd_cfg_ext__field_descriptors,
  onvif_app__cfg__chn_osd_cfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_osd_cfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_osd_cfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_vid_enc_cfg_ext__field_descriptors[2] =
{
  {
    "MDMultiCfg",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaVidEncCfgExt, mdmulticfg),
    &onvif_app__cfg__multicast_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "SessionTimeout",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnMediaVidEncCfgExt, has_sessiontimeout),
    offsetof(OnvifApp__Cfg__ChnMediaVidEncCfgExt, sessiontimeout),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_vid_enc_cfg_ext__field_indices_by_name[] = {
  0,   /* field[0] = MDMultiCfg */
  1,   /* field[1] = SessionTimeout */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_vid_enc_cfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_vid_enc_cfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaVidEncCfgExt",
  "ChnMediaVidEncCfgExt",
  "OnvifApp__Cfg__ChnMediaVidEncCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaVidEncCfgExt),
  2,
  onvif_app__cfg__chn_media_vid_enc_cfg_ext__field_descriptors,
  onvif_app__cfg__chn_media_vid_enc_cfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_vid_enc_cfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_vid_enc_cfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_media_aud_enc_cfg_ext__field_descriptors[2] =
{
  {
    "MDMultiCfg",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnMediaAudEncCfgExt, mdmulticfg),
    &onvif_app__cfg__multicast_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "SessionTimeout",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__ChnMediaAudEncCfgExt, has_sessiontimeout),
    offsetof(OnvifApp__Cfg__ChnMediaAudEncCfgExt, sessiontimeout),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_media_aud_enc_cfg_ext__field_indices_by_name[] = {
  0,   /* field[0] = MDMultiCfg */
  1,   /* field[1] = SessionTimeout */
};
static const ProtobufCIntRange onvif_app__cfg__chn_media_aud_enc_cfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_media_aud_enc_cfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnMediaAudEncCfgExt",
  "ChnMediaAudEncCfgExt",
  "OnvifApp__Cfg__ChnMediaAudEncCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnMediaAudEncCfgExt),
  2,
  onvif_app__cfg__chn_media_aud_enc_cfg_ext__field_descriptors,
  onvif_app__cfg__chn_media_aud_enc_cfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_media_aud_enc_cfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_media_aud_enc_cfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_cfg_ext__field_descriptors[5] =
{
  {
    "MetadataCfgExt",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnCfgExt, metadatacfgext),
    &onvif_app__cfg__chn_media_mdcfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PtzCfgExt",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnCfgExt, ptzcfgext),
    &onvif_app__cfg__chn_ptz_cfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "OsdCfgExt",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnCfgExt, osdcfgext),
    &onvif_app__cfg__chn_osd_cfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "VidEncCfgExt",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnCfgExt, videnccfgext),
    &onvif_app__cfg__chn_media_vid_enc_cfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AudEncCfgExt",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__ChnCfgExt, audenccfgext),
    &onvif_app__cfg__chn_media_aud_enc_cfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_cfg_ext__field_indices_by_name[] = {
  4,   /* field[4] = AudEncCfgExt */
  0,   /* field[0] = MetadataCfgExt */
  2,   /* field[2] = OsdCfgExt */
  1,   /* field[1] = PtzCfgExt */
  3,   /* field[3] = VidEncCfgExt */
};
static const ProtobufCIntRange onvif_app__cfg__chn_cfg_ext__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_cfg_ext__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnCfgExt",
  "ChnCfgExt",
  "OnvifApp__Cfg__ChnCfgExt",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnCfgExt),
  5,
  onvif_app__cfg__chn_cfg_ext__field_descriptors,
  onvif_app__cfg__chn_cfg_ext__field_indices_by_name,
  1,  onvif_app__cfg__chn_cfg_ext__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_cfg_ext__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__record_source_info__field_descriptors[5] =
{
  {
    "SourceId",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordSourceInfo, sourceid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordSourceInfo, name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Description",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordSourceInfo, description),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Address",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordSourceInfo, address),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Location",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordSourceInfo, location),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__record_source_info__field_indices_by_name[] = {
  3,   /* field[3] = Address */
  2,   /* field[2] = Description */
  4,   /* field[4] = Location */
  1,   /* field[1] = Name */
  0,   /* field[0] = SourceId */
};
static const ProtobufCIntRange onvif_app__cfg__record_source_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__record_source_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.RecordSourceInfo",
  "RecordSourceInfo",
  "OnvifApp__Cfg__RecordSourceInfo",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__RecordSourceInfo),
  5,
  onvif_app__cfg__record_source_info__field_descriptors,
  onvif_app__cfg__record_source_info__field_indices_by_name,
  1,  onvif_app__cfg__record_source_info__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__record_source_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__record_job_cfg__field_descriptors[2] =
{
  {
    "Priority",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg__RecordJobCfg, has_priority),
    offsetof(OnvifApp__Cfg__RecordJobCfg, priority),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "RecSrcToken",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordJobCfg, recsrctoken),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__record_job_cfg__field_indices_by_name[] = {
  0,   /* field[0] = Priority */
  1,   /* field[1] = RecSrcToken */
};
static const ProtobufCIntRange onvif_app__cfg__record_job_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__record_job_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.RecordJobCfg",
  "RecordJobCfg",
  "OnvifApp__Cfg__RecordJobCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__RecordJobCfg),
  2,
  onvif_app__cfg__record_job_cfg__field_descriptors,
  onvif_app__cfg__record_job_cfg__field_indices_by_name,
  1,  onvif_app__cfg__record_job_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__record_job_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__record_cfg__field_descriptors[6] =
{
  {
    "RecSrcCfg",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordCfg, recsrccfg),
    &onvif_app__cfg__record_source_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "RecContent",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordCfg, reccontent),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MaxRetentionTime",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT64,
    offsetof(OnvifApp__Cfg__RecordCfg, has_maxretentiontime),
    offsetof(OnvifApp__Cfg__RecordCfg, maxretentiontime),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "TrackCfgDescription",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_STRING,
    offsetof(OnvifApp__Cfg__RecordCfg, n_trackcfgdescription),
    offsetof(OnvifApp__Cfg__RecordCfg, trackcfgdescription),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "RecJobCfg",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__RecordCfg, recjobcfg),
    &onvif_app__cfg__record_job_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Enable",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__RecordCfg, has_enable),
    offsetof(OnvifApp__Cfg__RecordCfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__record_cfg__field_indices_by_name[] = {
  5,   /* field[5] = Enable */
  2,   /* field[2] = MaxRetentionTime */
  1,   /* field[1] = RecContent */
  4,   /* field[4] = RecJobCfg */
  0,   /* field[0] = RecSrcCfg */
  3,   /* field[3] = TrackCfgDescription */
};
static const ProtobufCIntRange onvif_app__cfg__record_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__record_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.RecordCfg",
  "RecordCfg",
  "OnvifApp__Cfg__RecordCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__RecordCfg),
  6,
  onvif_app__cfg__record_cfg__field_descriptors,
  onvif_app__cfg__record_cfg__field_indices_by_name,
  1,  onvif_app__cfg__record_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__record_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__chn_record_cfg__field_descriptors[1] =
{
  {
    "RecCfgs",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__ChnRecordCfg, n_reccfgs),
    offsetof(OnvifApp__Cfg__ChnRecordCfg, reccfgs),
    &onvif_app__cfg__record_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__chn_record_cfg__field_indices_by_name[] = {
  0,   /* field[0] = RecCfgs */
};
static const ProtobufCIntRange onvif_app__cfg__chn_record_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__chn_record_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.ChnRecordCfg",
  "ChnRecordCfg",
  "OnvifApp__Cfg__ChnRecordCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__ChnRecordCfg),
  1,
  onvif_app__cfg__chn_record_cfg__field_descriptors,
  onvif_app__cfg__chn_record_cfg__field_indices_by_name,
  1,  onvif_app__cfg__chn_record_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__chn_record_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__preset_tour_cfg__field_descriptors[3] =
{
  {
    "PresetTourName",
    1,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__PresetTourCfg, presettourname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Auto",
    2,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__PresetTourCfg, auto_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Enable",
    3,
    PROTOBUF_C_LABEL_REQUIRED,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__PresetTourCfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__preset_tour_cfg__field_indices_by_name[] = {
  1,   /* field[1] = Auto */
  2,   /* field[2] = Enable */
  0,   /* field[0] = PresetTourName */
};
static const ProtobufCIntRange onvif_app__cfg__preset_tour_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__preset_tour_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.PresetTourCfg",
  "PresetTourCfg",
  "OnvifApp__Cfg__PresetTourCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__PresetTourCfg),
  3,
  onvif_app__cfg__preset_tour_cfg__field_descriptors,
  onvif_app__cfg__preset_tour_cfg__field_indices_by_name,
  1,  onvif_app__cfg__preset_tour_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__preset_tour_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__onvif_proto_only_cfg__field_descriptors[16] =
{
  {
    "StdTzName",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, stdtzname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "DstTzName",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, dsttzname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HostNameFromDhcp",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, has_hostnamefromdhcp),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, hostnamefromdhcp),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HostName",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, hostname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "DnsSearchDomain",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, dnssearchdomain),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "NTPFromDHCP",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, has_ntpfromdhcp),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, ntpfromdhcp),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HttpEnable",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, has_httpenable),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, httpenable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HttpsEnable",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, has_httpsenable),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, httpsenable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "RtspEnable",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, has_rtspenable),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, rtspenable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Scopes",
    10,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_STRING,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_scopes),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, scopes),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "HomePos",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, homepos),
    &onvif_app__cfg__home_position__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ChnMediaCfgs",
    12,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_chnmediacfgs),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, chnmediacfgs),
    &onvif_app__cfg__chn_media_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "MediaProfiles",
    13,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_mediaprofiles),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, mediaprofiles),
    &onvif_app__cfg__media_profile__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ChnCfgsExt",
    14,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_chncfgsext),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, chncfgsext),
    &onvif_app__cfg__chn_cfg_ext__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ChnRecCfgs",
    15,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_chnreccfgs),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, chnreccfgs),
    &onvif_app__cfg__chn_record_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PresetTourCfgs",
    16,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, n_presettourcfgs),
    offsetof(OnvifApp__Cfg__OnvifProtoOnlyCfg, presettourcfgs),
    &onvif_app__cfg__preset_tour_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__onvif_proto_only_cfg__field_indices_by_name[] = {
  13,   /* field[13] = ChnCfgsExt */
  11,   /* field[11] = ChnMediaCfgs */
  14,   /* field[14] = ChnRecCfgs */
  4,   /* field[4] = DnsSearchDomain */
  1,   /* field[1] = DstTzName */
  10,   /* field[10] = HomePos */
  3,   /* field[3] = HostName */
  2,   /* field[2] = HostNameFromDhcp */
  6,   /* field[6] = HttpEnable */
  7,   /* field[7] = HttpsEnable */
  12,   /* field[12] = MediaProfiles */
  5,   /* field[5] = NTPFromDHCP */
  15,   /* field[15] = PresetTourCfgs */
  8,   /* field[8] = RtspEnable */
  9,   /* field[9] = Scopes */
  0,   /* field[0] = StdTzName */
};
static const ProtobufCIntRange onvif_app__cfg__onvif_proto_only_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 16 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__onvif_proto_only_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.OnvifProtoOnlyCfg",
  "OnvifProtoOnlyCfg",
  "OnvifApp__Cfg__OnvifProtoOnlyCfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg__OnvifProtoOnlyCfg),
  16,
  onvif_app__cfg__onvif_proto_only_cfg__field_descriptors,
  onvif_app__cfg__onvif_proto_only_cfg__field_indices_by_name,
  1,  onvif_app__cfg__onvif_proto_only_cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__onvif_proto_only_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue onvif_app__cfg__eauth_type__enum_values_by_number[2] =
{
  { "None", "ONVIF_APP__CFG__EAUTH_TYPE__None", 0 },
  { "Ws", "ONVIF_APP__CFG__EAUTH_TYPE__Ws", 1 },
};
static const ProtobufCIntRange onvif_app__cfg__eauth_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex onvif_app__cfg__eauth_type__enum_values_by_name[2] =
{
  { "None", 0 },
  { "Ws", 1 },
};
const ProtobufCEnumDescriptor onvif_app__cfg__eauth_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.EAuthType",
  "EAuthType",
  "OnvifApp__Cfg__EAuthType",
  "OnvifApp",
  2,
  onvif_app__cfg__eauth_type__enum_values_by_number,
  2,
  onvif_app__cfg__eauth_type__enum_values_by_name,
  1,
  onvif_app__cfg__eauth_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue onvif_app__cfg__eprofile_state__enum_values_by_number[4] =
{
  { "NotInit", "ONVIF_APP__CFG__EPROFILE_STATE__NotInit", 0 },
  { "Idle", "ONVIF_APP__CFG__EPROFILE_STATE__Idle", 1 },
  { "Fixed", "ONVIF_APP__CFG__EPROFILE_STATE__Fixed", 2 },
  { "NotFixed", "ONVIF_APP__CFG__EPROFILE_STATE__NotFixed", 3 },
};
static const ProtobufCIntRange onvif_app__cfg__eprofile_state__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex onvif_app__cfg__eprofile_state__enum_values_by_name[4] =
{
  { "Fixed", 2 },
  { "Idle", 1 },
  { "NotFixed", 3 },
  { "NotInit", 0 },
};
const ProtobufCEnumDescriptor onvif_app__cfg__eprofile_state__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg.EProfileState",
  "EProfileState",
  "OnvifApp__Cfg__EProfileState",
  "OnvifApp",
  4,
  onvif_app__cfg__eprofile_state__enum_values_by_number,
  4,
  onvif_app__cfg__eprofile_state__enum_values_by_name,
  1,
  onvif_app__cfg__eprofile_state__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCFieldDescriptor onvif_app__cfg__field_descriptors[5] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg, has_enable),
    offsetof(OnvifApp__Cfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "AuthType",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(OnvifApp__Cfg, has_authtype),
    offsetof(OnvifApp__Cfg, authtype),
    &onvif_app__cfg__eauth_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Discoverable",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(OnvifApp__Cfg, has_discoverable),
    offsetof(OnvifApp__Cfg, discoverable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "OnvifOnlyCfg",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(OnvifApp__Cfg, onvifonlycfg),
    &onvif_app__cfg__onvif_proto_only_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "CfgVer",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(OnvifApp__Cfg, has_cfgver),
    offsetof(OnvifApp__Cfg, cfgver),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned onvif_app__cfg__field_indices_by_name[] = {
  1,   /* field[1] = AuthType */
  4,   /* field[4] = CfgVer */
  2,   /* field[2] = Discoverable */
  0,   /* field[0] = Enable */
  3,   /* field[3] = OnvifOnlyCfg */
};
static const ProtobufCIntRange onvif_app__cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor onvif_app__cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "OnvifApp.Cfg",
  "Cfg",
  "OnvifApp__Cfg",
  "OnvifApp",
  sizeof(OnvifApp__Cfg),
  5,
  onvif_app__cfg__field_descriptors,
  onvif_app__cfg__field_indices_by_name,
  1,  onvif_app__cfg__number_ranges,
  (ProtobufCMessageInit) onvif_app__cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
