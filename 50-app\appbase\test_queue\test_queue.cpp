#include <pthread.h>
#include <appbase.h>
#include <stdio.h>
#include <stdlib.h>

HAppQueue g_hAppBaseTestQueue;

void* appbase_test_queue_thread(void *pvArg)
{
    EAppResult eAppRet = APP_OK;
    int nRand = 0;
    
    while(1)
    {
        nRand = rand();
        if(0 == nRand % 2)
        {
            eAppRet = AppQueueIn(g_hAppBaseTestQueue, (void*)(long)nRand, nRand);
            if(APP_OK != eAppRet)
            {
                printf("queue in failed %d, nRand %d\n", eAppRet, nRand);
            }
        }
        else
        {
            void *pvData = NULL;
            
            eAppRet = AppQueueOut(g_hAppBaseTestQueue, 1000, &pvData);
            if(APP_OK != eAppRet)
            {
                printf("queue out failed %d\n", eAppRet);
            }
        }
    }

    return NULL;
}

int appbase_test_queue()
{
    EAppResult eAppRet = APP_OK;
    pthread_t atThread[8] = {0};
    int nII = 0;

    eAppRet = AppQueueCreate(NULL, 0, TRUE, &g_hAppBaseTestQueue);
    if(APP_OK != eAppRet)
    {
        printf("create failed %d\n", eAppRet);
        return 1;
    }
    printf("queue %p\n", g_hAppBaseTestQueue);

    srand(time(NULL));

    for(nII = 0; nII < (sizeof(atThread)/sizeof(atThread[0])); nII++)
    {
        pthread_create(atThread + nII, NULL, appbase_test_queue_thread, NULL);
        printf("create thread, nII %d\n", nII);
    }
/*
    for(nII = 0; nII < (sizeof(atThread)/sizeof(atThread[0])); nII++)
    {
        pthread_join(atThread[nII], NULL);
    }
*/
    return 0;
}


