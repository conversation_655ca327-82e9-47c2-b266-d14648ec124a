#ifndef _KNET_CMD_E1_H
#define _KNET_CMD_E1_H

#define KNET_E1_CHAN_MAX 8

typedef struct {
	uint32_t mUsedFlag;
	uint32_t mSerialId;
	uint32_t mE1Id;
	uint32_t mE1TSMask;
	uint32_t mE1ChanGroupId;
	uint32_t mProtocalType;
	uint32_t mEchoInterval;
	uint32_t mEchoMaxRetry;
} e1_serial_info_t;

typedef struct {
    uint32_t mUsedFlag;
    char     mIpAdrs[IP_ADDR_SIZE];
    uint8_t  mIpMask;
	e1_serial_info_t mSerInfo;
} e1_single_link_info_t;

typedef struct {
	uint32_t mUsedFlag;
	char     mIpAdrs[IP_ADDR_SIZE];
	uint8_t  mIpMask;
	uint32_t mAuthType;
	uint32_t mFragMinPackLen;
	char     mSvrUsrName[KNET_STR_MAX];
	char     mSvrUsrPwd[KNET_STR_MAX];
	char     mSentUsrName[KNET_STR_MAX];
	char     mSentUsrPwd[KNET_STR_MAX];
    int32_t  mSerialNum;
	e1_serial_info_t mSerInfo[KNET_E1_CHAN_MAX];
} e1_multi_link_info_t;


int32_t E1OpenSingleChain(uint32_t channelId, e1_single_link_info_t *info);
int32_t E1CloseSingleChain(uint32_t channelId);
int32_t E1OpenMultiChain(uint32_t channelId, e1_multi_link_info_t *info);
int32_t E1CloseMultiChain(uint32_t channelId);
int32_t E1SetClk(uint32_t e1Id, char *clkType);

/*
 * The string is returned in a statically allocated buffer,
 * which subsequent calls will overwrite
 */
char* E1GetRemoteIp(char *ifaceType, uint32_t channelId);

uint32_t E1GetBandwidth(char *ifaceType, uint32_t channelId);

#endif
