﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{900681DF-CBFE-4840-B051-75B4FCF24608}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>appbase</RootNamespace>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>false</UseOfMfc>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_APP_MEMPOOL_USE_LIST;APP_DISABLE_OSP_TEL_CMD;_DEBUG;_WINDOWS;_USRDLL;APPBASE_EXPORTS;NO_SERVICE_LAYER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\include;..\..\..\..\10-common\include\cbb\debuglog;..\..\..\..\10-common\include\app;..\..\..\..\10-common\include\system;..\..\..\..\10-common\include\cbb\charconversion;..\..\..\..\10-common\include\cbb\mxml;..\..\..\..\10-common\include\service;..\..\..\..\10-common\include\cbb\protobuf;..\..\..\..\10-common\include\cbb\osp</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\..\10-common\lib\$(ConfigurationName)\win32\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>mxml_dll.lib;charconv.lib;debuglog.lib;osplib.lib;advapi32.lib;User32.lib;legacy_stdio_definitions.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy /Y $(OutDir)$(TargetName).dll ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\
copy /Y $(OutDir)$(TargetName).lib ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\$(TargetName)_dll.lib
copy /Y $(OutDir)$(TargetName).pdb ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\$(TargetName)_dll.pdb
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_APP_MEMPOOL_USE_LIST;APP_DISABLE_OSP_TEL_CMD;NDEBUG;_WINDOWS;_USRDLL;APPBASE_EXPORTS;NO_SERVICE_LAYER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\..\include;..\..\..\..\10-common\include\cbb\debuglog;..\..\..\..\10-common\include\app;..\..\..\..\10-common\include\system;..\..\..\..\10-common\include\cbb\charconversion;..\..\..\..\10-common\include\cbb\mxml;..\..\..\..\10-common\include\service;..\..\..\..\10-common\include\cbb\protobuf;..\..\..\..\10-common\include\cbb\osp</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\..\..\..\10-common\lib\$(ConfigurationName)\win32\;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>mxml_dll.lib;charconv.lib;debuglog.lib;osplib.lib;advapi32.lib;User32.lib;legacy_stdio_definitions.lib</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy /Y $(OutDir)$(TargetName).dll ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\
copy /Y $(OutDir)$(TargetName).lib ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\$(TargetName)_dll.lib
copy /Y $(OutDir)$(TargetName).pdb ..\..\..\..\10-common\lib\$(ConfigurationName)\win32\$(TargetName)_dll.pdb
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\include\appbase_cross.h" />
    <ClInclude Include="..\..\include\appbase_log.h" />
    <ClInclude Include="..\..\include\appbase_mempool_interface.h" />
    <ClInclude Include="..\..\include\appbase_utils.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\src\appbase_cross_win.c" />
    <ClCompile Include="..\..\src\appbase_enum_str_conv.c" />
    <ClCompile Include="..\..\src\appbase_mempool.c" />
    <ClCompile Include="..\..\src\appbase_mempool_implement.c" />
    <ClCompile Include="..\..\src\appbase_socket.c" />
    <ClCompile Include="..\..\src\appbase_task.c" />
    <ClCompile Include="..\..\src\appbase_utils.c" />
    <ClCompile Include="..\..\src\appbase_utils_unicode.c" />
    <ClCompile Include="..\..\src\appbase_xml.c" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>