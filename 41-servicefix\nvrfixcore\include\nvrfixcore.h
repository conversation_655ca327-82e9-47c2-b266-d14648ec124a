/**
* @file 	nvrfixcore.h
* @brief    nvr 枪机核心模块
* <AUTHOR>
* @date 	2019-05-9
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/

#ifndef _NVRFIXCORE_H_
#define _NVRFIXCORE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "nvrdef.h"
#include "nvrptzdef.h"
#include <stdio.h>
#include <string.h>
#include <math.h>
#include "debuglog.h"
#include "nvrcommon.h"
#include "lcamclt.h"
#include "nvrcap.h"
#include "nvrcapfix.h"
#include "charconv.h"
#include "nvrqueue.h"

#define NVR_FTPSERV_DATA_SRC_TYPE			"139"			//上传WIFI探针-数据采集系统类型
#define NVR_FTPSERV_DATA_SRC_IDE			"320000"		//上传WIFI探针-数据产生源标志
#define NVR_FTPSERV_DATA_FACTORY			"61504983A"		//上传WIFI探针-厂商组织机构代码
#define NVR_FTPSERV_DATA_TYPE				"001"			//上传WIFI探针-数据类型编码
#define NVR_FTPSERV_PDES_WLRZ          		"WLRZ"          //上传WIFI探针-特征采集日志目录缩写(存放探针数据目录)

#define NVR_PROBE_TMP_FTP_NAME  	"tmpwq.txt"		 ///<探针ftp文件保存路径，用于ftp上传

#define MAKE_COMPILER_HAPPY(x)       ((void)x)

#define PRINTERR(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_ERR, "%s "args, _NVRFUN_, ## __VA_ARGS__)///<错误打印
#define PRINTVIP(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_IMP, "%s "args, _NVRFUN_, ## __VA_ARGS__)///<重要打印
#define PRINTDBG(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, "%s "args, _NVRFUN_, ## __VA_ARGS__)///<调试打印
#define PRINTTMP(args, ...)    DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_TEMP, "%s "args, _NVRFUN_, ## __VA_ARGS__)///<临时打印

#define NvrFixLog(args...)     LogFlash(FLASH_LOG_NOTICE, "fixcore", ## args)
#define NvrFixMem(args, ...)   LogMem(MEM_LOG_NOTICE_FIXIPC, DEBUG_LOG_MOD_FIX_CORE, "FixCore", args, ##__VA_ARGS__)

#if defined(_WIN64) || defined(WIN64) ||defined (__LP64__) || defined (__64BIT__) || defined (_LP64) || (__WORDSIZE == 64)
    #define FIX_VAL_TO_PONINT(p)    (void *)(long)(p)
    #define FIX_POINT_TO_VAL32(p)   (u32 )(long)(p)
    #define FIX_POINT_SIZE(p)       (long)(p)
    #define FORMAT_HEX              "%x"
#else
    #define FIX_VAL_TO_PONINT(p)    (void *)(int)(p)
    #define FIX_POINT_TO_VAL32(p)   (u32 )(int)(p)
    #define FIX_POINT_SIZE(p)       (int)(p)
    #define FORMAT_HEX              "%lx"
#endif

typedef unsigned int                FixUint32;

#define FIX_ASSERT(p) \
if (NULL == p)			\
{	\
	PRINTERR("[%s]%s assert failed(line:%d)\n", __FILE__,__FUNCTION__, __LINE__);	\
	return NVR_ERR__ASSERT;												\
}	\

#define FIXASSERTSURE( p )  \
if( !(p) ) \
{ \
    PRINTERR("[%s]%s assert failed(L:%d)\n", __FILE__,__FUNCTION__, __LINE__ ); \
    return NVR_ERR__ASSERT; \
}

#define IPC_CORE_MAX_NFRARED_NUM                                    4
#define IPC_CORE_TRANS_DATA_BUF_MAX_NUM                             256
typedef struct eIpcTranDataMsg
{
	u16    wMsgType;    ///<透明通道消息类型
	u8     byChnId;     ///<通道Id号
	u16    wBufLen;     ///<透明数据结构长度
	u8     abyTransDataBuf[IPC_CORE_TRANS_DATA_BUF_MAX_NUM];   //透明数据结构
} TNvrOnvifTransDataMsg, *PTNvrOnvifTransDataMsg;

/**透明通道数据类型必须与ipcv7和nvrv7保持一致，不可冲突*/
#define NVR_ONVIFTRANS_TYPE_MSG_BGN               10000
/**主片发往从片*/
#define NVR_ONVIFTRANS_TYPE_SLAVE_MSG_BGN               218


typedef enum
{
  NVR_PTZ_ONVIFTRANS_TYPE_SLAVE_LDR = NVR_ONVIFTRANS_TYPE_SLAVE_MSG_BGN,	  ///<光敏发送到从片，发往从片的在此下面添加
  NVR_PTZ_ONVIFTRANS_TYPE_DEV_GET,///<获取设备参数
  NVR_PTZ_ONVIFTRANS_TYPE_DEV_SET,///<配置设备参数
  NVR_PTZ_ONVIFTRANS_TYPE_LDR_THR,///<光敏阀值设置
  NVR_PTZ_ONVIFTRANS_TYPE_MAX,				
}ENvrOnvifTransDataType;

void NvrFixPtzVer();
NVRSTATUS NvrSlaveToHostMessage(u8 byType,u8 byChnId,void *pbyBuf);

typedef struct tagNvrOnvifDevMsg
{
    u8     byDevId;    ///<设备id
    u16    wPangle;    ///<p坐标
    u16    wTangle;     ///<t坐标
    u32    dwZmPos;     ///<z坐标
    u32    dwFcsPos;   //聚焦
} TNvrOnvifDevMsg, *PTNvrOnvifDevMsg;

void NvrFixLifeStatTaskInit();

/****************************************************************************************
************************************ 事件告警COI上报定义 ********************************
****************************************************************************************/
/*==============================事件告警信息结构体定义===============================*/
#define NVR_SYNC_SNAPPIC_COI_PATH			"/tmp/snapcoi/"//编码图片路径
#define IT_PIC_SAVE_MAX_NUM  (256)                 //事件告警COI上报队列数目，处理抓拍

//事件告警信息
typedef struct
{
    u32 u32SnapIndex;//抓拍回调上下文
    ENvrAlarmType eEventType;   //通道告警类型
    ENvrAlarmSDType eAlmSDType; //通道智能侦测告警类型
    s32 nCapChn;                //视频源ID
    u32 dwRegionNum;            //告警，恢复状态改变的区域数
    u16 wTargetNum;             //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];        //异常运动目标
    TMediaCtrlRegionAlarmState atInvasionStat[MEDIACTRL_MAX_MD_REGION];
    TMediaCtrlTripLineAlarmState atTripLineStat[MEDIACTRL_MAX_TRIP_LINE_NUM];       //警戒线用到
	TNvrPloygon tRegion ;//警戒区域
	TNvrIntelCordon atNvrIntelCordon[NVR_MAX_CORDON_NUM];//警戒线的警戒区域
}TAlarmEventInfo;

/*********************************** 事件告警COI上报句柄定义 *****************************/
//事件告警文件维护结构体
typedef struct
{
    u64 dwEventTime; //文件生成时间
    s8 achPicPath[NVR_MAX_STR256_LEN]; //文件名
}TPicPathParam;

//事件告警COI上报句柄参数
typedef struct
{
    TASKHANDLE u32EventInfoQueueTask;   //事件告警信息队列线程句柄
    PTNvrDoubleList ptEventInfoQueue;         //事件告警信息队列
    pthread_mutex_t nPicLock;           //图片编码锁
    TPicPathParam tEventPicPathParam[IT_PIC_SAVE_MAX_NUM];  //事件告警文件维护结构体
    s32 dwCurPicNum;                //图片存储管理（超过一定时间删除，最多IT_PIC_SAVE_MAX_NUM张）
    u32 dwLastMotionTime[2];        //移动侦测事件时间，用于去重
    HTIMERHANDLE hDelPicTimer;      //文件删除定时器
}TItEventCoiHandle;

NVRSTATUS NvrFixCoiEventNotify(TAlarmEventInfo *ptEventInfoCbData);


#ifdef __cplusplus
}
#endif

#endif

