/**
* @file 	NvrFixDev.c
* @brief    本地cameral dev模块
* <AUTHOR>
* @date 	2019-1-3
* @version  1.0
*/

#include "nvrcommon.h"
#include "lcamclt.h"
#include "lcamcltapi.h"
#include "lcammc.h"
#include "lcamisp.h"
///#include "lcamosd.h"
#include "nvrcap.h"
#include "nvrispdef.h"
#include "nvrosd.h"
#include "nvrsys.h"
#include "nvrbrdapi.h"
#include "nvrdev.h"
#include "nvrqueue.h"
#include "nvrcap_in.h"
#include "nvrfixdev.h"
#include "nvrfixcore.h"
#include "math.h"
#include "nvrfixcfg.h"
#include "nvrfixisp.h"
#include "nvrfixmc.h"
#include "nvrlist.h"
#include "nvrpui.h"
#include "nvrnetwork.h"
#include "nvrgeo.h"
#include "nvrfixprofeintelli.h"
#include "nvrsys_in.h"


//云台宏定义
#define MAX_EASTERN_TIME_ZONE_SEC		-12*3600	//转换后-东12区时间秒数
#define MAX_WESTERN_TIME_ZONE_SEC		36*3600		//转换后-西12区时间秒数
#define ALL_DAY_SEC						24*3600		//全天秒数
#define NVR_MAX_TIME_SCALE_DOUBLE		10*2   		//检测时间段(双倍)
#define NVR_REC_TIME_DOUBLE				4*2			//定时录像每天时间段个数(双倍)
#define NVR_HOUR_SEC					3600		//一个小时公共多少秒
#define NVR_MIN_SEC						60			//一分钟公共多少秒


//UTC时间段分布位置-枚举值
typedef enum eNvrCfgTimeSegment
{
	IPC_CFG_IN_LAST_DAY = 1,			//该时间段,在上一天内
	IPC_CFG_CROSS_LAST_THIS_DAY = 2,	//该时间段,跨了上一天到这一天
	IPC_CFG_IN_THIS_DAY = 3,			//该时间段,在这一天内
	IPC_CFG_CROSS_THIS_NEXT_DAY = 4,	//该时间段,跨了这一天到下一天
	IPC_CFG_IN_NEXT_DAY = 5,			//该时间段,在下一天内

	IPC_CFG_NOT_IN_ANY_DAY				//该时间段,不在任一天内
}ENvrCfgTimeSegment;


typedef enum eNvrTimeconvType
{
	IPC_TIMECONV_MD = 1,
	IPC_TIMECONV_OD = 2,
	IPC_TIMECONV_PIN = 3,
	IPC_TIMECONV_REC = 4,
	IPC_TIMECONV_TMINGTASK = 5,
	IPC_TIMECONV_ISP = 6,
    IPC_TIMECONV_TRIP_LINE = 7,
    IPC_TIMECONV_SCENE_CHG = 8,
    IPC_TIMECONV_REGION_INVASION = 9,
    IPC_TIMECONV_REGION_LEAVING = 10,
    IPC_TIMECONV_OBJECT_TAKEN = 11,
    IPC_TIMECONV_OBJECT_LEFT = 12,
    IPC_TIMECONV_ISP_SCENE_CHG = 13,
    IPC_TIMECONV_PEOPLE_GATHER = 14,
    IPC_TIMECONV_CORDON = 15,
    IPC_TIMECONV_AUDIO_ABNORMAL = 16,
    IPC_TIMECONV_LUMA_CHANGE = 17,
	IPC_TIMECONV_REGION_ENTER = 18,
	IPC_TIMECONV_SHADE = 19,
}ENvrTimeconvType;


//云台结构体
//定时任务时间参数转换结构
typedef struct tagNvrTimeSec
{
	s32 nHour;
	s32 nMin;
	s32 nSec;
}TNvrTimeSec;

typedef struct tagTmingInfoEx
{
	u8    byTaskType;			//任务类型
	u32   byTaskParam;			//任务参数
}TTmingInfoEx;

typedef struct tagEvdayTmingInfoEx
{
	TTmingInfoEx atTmingInfoEx[NVR_MAX_DISARMING_PERIOD];
}TEvdayTmingInfoEx;

typedef struct tagTmingTaskParamEx
{
	TEvdayTmingInfoEx atWeekScale[NVR_WEEK_DAY];
}TTmingTaskParamEx;


///<检测线程设置的定时任务参数
typedef struct tagPtzTmingTaskSetParam
{
	BOOL32 bIsSet;			///<是否是设置定时任务(如果为FALSE说明为清除任务)
	u8 byTaskType;			///<任务类型
	u8 byTaskParam;			///<任务参数
}TPtzTmingTaskSetParam, *PTPtzTmingTaskSetParam;

/**lcam与appclt 守望任务类型转换map表*/
typedef struct tLcamWatchOnTypeMap
{
	ENvrPtzTaskType 	eNvrType;
	EAppCltPtzTaskType	eApCltType;
}TLcamWatchOnTypeMap;


/**lcam与appclt ptz控制指令转换map表*/
typedef struct tLcamPtzCmdMap
{
	ENvrPtzCtrlType 	eNvrType;
	EAppCltPtzCtrlType	eApCltType;
}TLcamPtzCmdMap;

typedef struct tagSyncScan
{
	u8  byOptType;			//操作类型
	u8  byParam;			//每个操作参数
	u32 dwTimesTamp;		//停留时间
	u32 dwStepNum;			//当前操作需要走的步数(目标位置)
}TSyncScan, *PTSyncScan;

///<花样扫描中每个操作为一个链表节点
typedef struct tagSyncScanNode
{
	TSyncScan tSycnScan;
	struct tagSyncScanNode* pNext;
}TSyncScanNode,*PTSyncScanNode;


///<花样扫描路径参数
typedef struct tagScanPath
{
	PTSyncScanNode pHead;		//每条路径头节点
	u32 dwOptNum;				//当前路径中的操作个数
	u32 dwTimesTamp;			//开始时间戳
	u32 dwEndTimesTamp;			//结束时间戳
	u16 wVStartPos;				//起始位置垂直角度
	u16 wHStartPos;				//起始位置水平角度
	s32 nZoomStartPos;			//起始位置zoom位置
}TScanPath,*PTScanPath;



///云台升级相关参数
typedef struct tagNvrNvrFixDevUpgradeParam
{    
    PFCltProtoDevPtzUpgradeStateReport pfPtzUpgradeStateReport; ///云台升级任务状态回调
    TAppCltDevPtzUpState tState;//云台升级状态
    u32 dwDevID;//设备id
    u32 dwOpeque;//绑定的appcltid
    u32 dwPkgTotalSize;//升级文件总大小
    s8 achUpgradeFilePath[128];//升级文件路径
    u8 *pPkgBuf;
    u32 dwWriteSize;
    u8 byUpdateType;
}TNvrFixDevUpgradeParam, *PTNvrFixDevUpgradeParam;

static TNvrFixDevUpgradeParam g_tNvrFixDevUpgradeParam;//云台升级参数
static TNvrCapHwCapInfo g_tLcHwCapInfo;			///<硬件能力集
static PTNvrDoubleList g_ptLcDevQueue = NULL;					///<设备处理队列
TNvrFixPowerOffMemCfg g_tPowerOffMemCfg[NVR_DEV_SERIAL_MAX_NUM];	//断电记忆参数
static HTIMERHANDLE g_hGetHvAngleTimer = NULL;				//获取水平俯仰角度定时器
static HTIMERHANDLE g_hPowerOffMemTimer = NULL;				//断电记忆定时器
static s32 g_nPowerOffInterval = 60;                        //断电记忆定时间隔
static HTIMERHANDLE g_hPtzUpgradeTimer = NULL;				//云台升级状态更新定时器
static HTIMERHANDLE g_hGetFocusTimer = NULL;			        //获取聚焦定时器
static HTIMERHANDLE g_hGetTempatureTimer = NULL;               //获取温度定时器
static HTIMERHANDLE g_hHeaterTimer = NULL;                      //加热定时器


static HTIMERHANDLE g_hWatchOnTimer    = NULL;				///<守望定时器
static HTIMERHANDLE g_hTimingTaskTimer = NULL; 				///<定时任务定时器。
static TNvrCapPTZCap  g_tPtzCap = {0}; 				   ///<ptz能力集
static TNvrCapPTZCapInfo g_tPtzCapInfo = {0};			///<ptz能力集信息


pthread_mutex_t g_hSerialExeMutex = PTHREAD_MUTEX_INITIALIZER;
//static struct list_head g_SerialExeList;
pthread_cond_t exeover = PTHREAD_COND_INITIALIZER;

pthread_mutex_t g_hSerialQueryMutex = PTHREAD_MUTEX_INITIALIZER;
//static struct list_head g_SerialQueryList;
pthread_cond_t querywait = PTHREAD_COND_INITIALIZER;
pthread_cond_t queryReswait = PTHREAD_COND_INITIALIZER;
BOOL32 g_bWait = FALSE;

static SEMHANDLE    g_hSemExeWait=(SEMHANDLE)NULL; //等待指令
static SEMHANDLE    g_hSemExeOver=(SEMHANDLE)NULL; //等待指令执行完
static SEMHANDLE    g_hSemQueryRES=(SEMHANDLE)NULL; //等待查询完成
static SEMHANDLE    g_hSemQueryPTZ=(SEMHANDLE)NULL; //查询保护
static SEMHANDLE    g_hSemQueryThread=(SEMHANDLE)NULL; //等待写完成
static TASKHANDLE   g_hThreadQueryPTZ=(TASKHANDLE)NULL; //查询PTZ线程句柄

static SEMHANDLE g_hPtzSeiSem = NULL;					//云台状态扩展sei信号量
static void* g_pPtzSeiHandle = NULL;					//上报AR云台状态sei操作句柄
static u8 g_byVidEncNum = 0;                               //总的视频编码通道数

static TASKHANDLE   g_hPtzTimeingTask = (TASKHANDLE)NULL;	///<云台定时任务处理线程句柄
static TASKHANDLE   g_hXcoreQueryTask = (TASKHANDLE)NULL; ///热成像定时查询线程句柄

static u32  g_dwPreSetSpd = 40;                 	///<预置点间切换速度

static s32 g_nZoomSpeed = NVR_DEV_MAX_ZOOM_SPEED;	///<默认zoom最大速度，内部和isp交互使用的全局变量，对应af模块的速度等级（范围是1--6），和eZoomSpeedValue之间需要进行转换（枚举值，范围是0--2）

static u8 g_byPtzType = NVR_CAP_PTZ_TYPE_NONE;
static u8 g_byHotImageType = NVR_HOTIMAGE_TYPE_IRAYFT2;

static u16  g_wFindPos = 0;							///<路径巡航中下一个需要查找的位置
static u8   g_byPathCrsNum = 0;						///<路径巡航路径编号
static u16  g_wCurPos = 0;							///<路径巡航中当前需设置的预置位编号
BOOL32 g_byPathCrsState = FALSE;					///<当前巡航状态
static BOOL32 g_bWiperStat = FALSE;					///<当前雨刷状态，0关闭，1开启
BOOL32 g_byPathCruising = FALSE;					///<当前云台是否在巡航中
static BOOL32 g_bStoping = FALSE;

//利用宏定义的方式初始化全局的互斥锁和条件变量
pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t actviemsg = PTHREAD_COND_INITIALIZER;

static struct list_head g_task_list;
static 	BOOL g_bTempCheck = TRUE;     //是否温度检查

extern s32 g_nInfraredVal[IPC_CORE_MAX_NFRARED_NUM];
//串口执行指令历史列表，用于指令被冲掉时重发
pthread_mutex_t g_hSerialHistoryListMutex = PTHREAD_MUTEX_INITIALIZER;
static struct list_head g_SerialHistoryList;
pthread_cond_t hasmsg = PTHREAD_COND_INITIALIZER;

///<历史指令数据
typedef struct tagHistoryCmd
{
	u32 dwMsgCnt;//发送次数
	ENvrPtzCtrlType eMsgCmd;//发送命令
	u32 dwCmdId;//每条指令递增
	u8  bySerialID;//串口
	u8  byProtocal;//FF EE AA
	u8  byFrom;//1,来自PTZ，2来自热成像
	u16 dwCurrentZOOM;
    u16 dwCurrentHang;
    u16 dwCurrentVang;
    ENvrPtzCtrlType ePreCmd;
    u8  abyCmd[64];
    u8  byCmdLen;
	u8  byNeedRes;//是否需要回复
	u32 dwSendTime;
    u32 dwDelay;//多久后开始检测
	u32 dwTimeOut;//超时时间
	u32 dwRecvNum;//接收消息号
	BOOL32 bDone;
}THistoryCmd, *PTHistoryCmd;


typedef struct tagTHistoryCmdListNode
{
    THistoryCmd tCmdNode; //数据区
	struct list_head listnode;
}THistoryCmdNode;


#define IPC_DEV_PT_GENERAL_PKG       	   0        //general pt pkg 
#define IPC_DEV_PT_FIX_MAINBOARD_PKG       1        //fixgun, pt main board pkg 
#define IPC_DEV_PT_FIX_CAMBOARD_PKG        2      	//fixgun, pt camera board pkg
#define IPC_DEV_PT_FIX_FOCUSBOARD_PKG      3      	//fixgun, pt focus board pkg



//云台代码添加
static u16	g_dwCurrentHAngle = 0;		///<用于记录当前云台的角度
static u16	g_dwCurrentVAngle = 0;		///<用于记录当前云台的角度
static u16	g_dwCurrentZoom = 0;		///<用于记录当前云台的角度

static s32 lastPtzAngle_H = 0;			///<用于记录云台操作前的角度
static s32 lastPtzAngle_V = 0;			///<用于记录云台操作前的角度
static s32 lastPtzZoom = 0;			///<用于记录云台操作前的角度

#ifdef XCORE
static s32 lastXcoreZoom = 0;
#endif

static s32 g_dwCurrenXcoreZoom = 0;


//static s32 g_dwCheckPTZMoveCount = 0; 	///<云台运动次数

static u32 g_dwMinZoom = 430;			///< 记录最小zoom值
static u32 g_dwMaxZoom = 2742; 		///< 记录最大zoom值



static BOOL32 g_bPathCrsMode = FALSE;				///<路径巡航方式，FALSE从头巡航，TRUE为接上个预置位巡航
static HTIMERHANDLE g_hPathCrsTimer    = NULL;				///<路径巡航定时器
static HTIMERHANDLE g_hCheckPtzIsMoveTimer    = NULL;		///<用于定时任务或者巡航操作后，启动该定时器查询pt坐标判断任务是否生效
//static BOOL32       g_bIsReadPtzAngle = FALSE;			///<是否需要查询云台角度标志
static TNvrPtzCtrlInfo g_tAutoRestoreTask = {0};         ///<云台自动维护前的非定时/守望任务
BOOL32 g_byAutoRestoreState = FALSE;					///<云台自动维护状态


static u16 g_byPtzPresetId = 0;				   	 	///<当前位置所处预置位编号，0是未知位置(对接交通卡口)
TPtzTaskState g_tTmingTaskState = {0};				///<定时任务实际状态
TPtzTaskState g_tWatchOnState = {0};				///<守望任务实际状态
TPtzTmingTaskSetParam g_tTmingTaskSetParam = {0};	///<检测线程设置的定时任务参数
static BOOL32 g_bMotorMorving = FALSE;				///<客户端主动发起的云台转动操作，标志电机持续运动
static BOOL32 g_bHorizonScan = FALSE;				///<当前客户端操作水平扫描状态
static u32 g_dwMoving=0;                      ///<云台当前是否在移动
static s32 g_nQuerying=1;//未查询

//ptz定时任务(时间与参数)
TNvrDisarmingTime g_tFixPtzTime;
TTmingTaskParamEx g_tFixTimingTaskEx;

#define NVR_PTZ_DEV_MAX_BUF_LEN				255		///<读串口缓存大小
#define NVR_PTZ_DEV_GETHVPOS_DELAY			600		///<云台转动停止后获取当前角度延时时间 ms


#define LCAM_DEV_MAX_READBUF_LEN			256		///<读串口buf长度

s8 g_achPtzVertmp[NVR_DEV_MAX_SOFTVER_LEN + 1];///<云台版本号
s8 g_achPtzVer[NVR_FIX_DEV_MAX_SOFTVER_LEN + 1];//云台版本号
s8 g_achFocusVer[NVR_FIX_DEV_MAX_SOFTVER_LEN + 1];//聚焦板版本号
s8 g_achLensVer[NVR_FIX_DEV_MAX_SOFTVER_LEN + 1];//镜头版本号

#define NVR_DEV_LOW_TEMP_LIMIT_VALUE_MINI   -10 ///< 2241Mini半球加热开启,-10度

u16 g_wPtzCtrlChnId = 0;	///<云台控制通道号，对于普通球机来说，默认都是0
static TNvrBasicPosInfo g_tBasicPos = {0};         //方位角基准位置


/************************云台升级相关************************/
#define NVR_FIX_DEV_UPTPTZ_PATH  			""NVR_ROOT_PATH"/ptzupdate.bin"
#define NVR_FIX_DEV_UPTPTZ_SUCCESS			1		///<云台升级成功
#define NVR_FIX_DEV_UPTPTZ_FAILED			2		///<云台升级失败
#define NVR_FIX_DEV_UPTPTZ_TIMEOUT			150		///<云台升级超时时间(S)
#define NVR_FIX_DEV_MAX_UPT_FRAME_LEN		1024	///<云台升级帧数据最大长度

#define NVR_FIX_DEV_PTZREBOOT_DELAY		60			///<云台重启恢复时间(S)
#define NVR_FIX_DEV_UPT_WAIT_RSP_TIME	20			///<云台升级发送开始指令后，等待回复超时时间(s)
#define NVR_FIX_DEV_UPT_WAIT_C_TIME		20			///<云台升级等待云台回复'c'超时时间(s)
#define NVR_FIX_DEV_UPT_TIMEOUT			20000		///<升级定时器延时时间(ms)
#define NVR_FIX_DEV_UPTFRAME_KEDA_LEN	128			///<科达云台升级每一帧长度
#define NVR_FIX_DEV_UPTFRAME_WEIDUO_LEN	45			///<科达云台升级每一帧长度

///<云台升级过程中交互消息
#define NVR_FIX_DEV_UPTMSG_ACK			0x00		///<云台升级中ACK消息
#define NVR_FIX_DEV_UPTMSG_KEDA_ACK		0x06		///<科达云台升级ACK消息
#define NVR_FIX_DEV_UPTMSG_CAN			0x18		///<数据发生严重错误，要求从第一帧重新发送消息
#define NVR_FIX_DEV_UPTMSG_NAK			0x15		///<当前数据有误,要求从发当前帧消息
#define NVR_FIX_DEV_UPTMSG_EOT			0x04		///<传输结束消息

///<云台升级过程中网络模块状态(业务状态)
#define NVR_FIX_DEV_UPTSTAT_RCV_RSP		0x20		///<云台回复开始升级指令FD状态
#define NVR_FIX_DEV_UPTSTAT_RCV_C		0x21		///<云台回复'c'状态
#define NVR_FIX_DEV_UPTSTAT_SEND_EOT	0x22		///<发送传输结束状态
#define NVR_FIX_DEV_UPTSTAT_RCV_ACK		0x23		///<云台回复传输ACK状态
#define NVR_FIX_DEV_UPTSTAT_RCV_NAK		0x24		///<云台回复NAK状态
#define NVR_FIX_DEV_UPTSTAT_RCV_CAN		0x25		///<云台回复CAN状态
#define NVR_FIX_DEV_UPTSTAT_RCV_P		0x26		///<云台回复P状态
#define NVR_FIX_DEV_UPTSTAT_RCV_E		0x27		///<云台回复E状态
#define NVR_FIX_DEV_UPTSTAT_RCV_W		0x28		///<云台回复W状态
#define NVR_FIX_DEV_UPTSTAT_ERROR		0xFC		///<接收错误信息状态
#define NVR_FIX_DEV_UPTSTAT_IDLE		0xFF		///<空闲状态
#define NVR_FIX_DEV_UPTSTAT_RCV_1		0x31		///<云台回复'c'状态
/****************end****************/

/************************镜头升级相关************************/
#define NVR_FIX_UPTLENS_PATH  ""NVR_ROOT_PATH"/lensupdate.bin"
#define NVR_FIX_MAX_LENSVER_LEN			32		///<镜头版本号最大长度
/****************end****************/

static u8 byPtzUptStat = NVR_FIX_DEV_UPTSTAT_IDLE;	///<云台升级过程中网络模块状态(业务状态)
static BOOL32 g_bRecevieC = FALSE;					///<升级过程中是否接收到'C'(防止升级过程中出现死循环)
static BOOL32 g_bPtzUpdating = FALSE;				///<云台升级标志，TRUE为正在升级
static u8 g_byPtzUpdateSuc = 0;						///<云台升级成功状态
static u8 g_byPtzCtrlSerialId = 0;					///<云台控制串口id号
static BOOL32 g_bPtzGetVerSuc = FALSE;			    ///<云台版本号获取标志，TRUE为获取成功

static HTIMERHANDLE g_hPtzUpdateTimer = NULL;				///<云台升级定时器
static TASKHANDLE   g_hPtzUpdateTask = (TASKHANDLE)NULL;	///<云台升级线程句柄

static u8 g_byLensCtrlSerialId = 0;					            ///<镜头控制串口id号
static ENvrDevUpgradeState g_byLensUpdateState = 0;			///<镜头升级状态
static ENvrDevUpgradeErrReason g_byLensUpdateErr = 0;			///<镜头升级状态

static BOOL32 g_bLensUpdating = FALSE;			///<镜头xmodem升级标志，TRUE为正在升级
static u8 byLensUptStat = NVR_FIX_DEV_UPTSTAT_IDLE;	///<镜头升级过程中网络模块状态(业务状态)
static u8 byLensUptStartStat = NVR_FIX_DEV_UPTSTAT_IDLE;	///<镜头升级过程中网络模块开始状态
static BOOL32 g_bLensRecevieC = FALSE;			///<镜头升级过程中是否接收到'C'(防止升级过程中出现死循环)
static BOOL32 g_bLensVerGeting = FALSE;			///<镜头xmodem获取标志，TRUE为正在获取

static HTIMERHANDLE g_hLensUpgradeTimer = NULL;			    ///<镜头升级状态更新定时器
static HTIMERHANDLE g_hLensUpdateTimer = NULL;				///<镜头升级定时器
static TASKHANDLE   g_hLensUpdateTask = (TASKHANDLE)NULL;	///<镜头升级线程句柄

static TNvrSyncScanInfo g_tSyncScanStateInfo;		///<花样扫描状态信息
static TScanPath g_Path[NVR_MAX_SYNCSCAN_PATH_NUM];	///<花样扫描路径信息

static HTIMERHANDLE g_hFocusCtrlTimer = NULL;				///<爱芯聚焦控制定时器
static int  g_eVidLensCmd = LENS_CTRL_FOCUS_STOP;                       ///<爱芯聚焦控制命令

static int g_dwFileTotalSize = 0;						///<镜头升级文件总大小
static int g_dwFileCurSaveSize = 0;						///<镜头升级文件当前保存大小

static TNvrCapFixInernalCapInfo g_tFixInterCapInfo;

static u8 g_byHeatS = 3;
static u8 g_byHeatE = 3;

void *NvrFixDevCoretaskCheckThread();

static s32 NvrDevGetTempatureTimerCB(HTIMERHANDLE dwTimerId, void* param);
static s32 NvrDevHeaterTimerCB(HTIMERHANDLE dwTimerId, void* param);

static NVRSTATUS NvrFixDevGetTemperature(s32 *Temperature);
static BOOL NvrFixDevLensInit(EIpcCamLensType byLenType);
static BOOL NvrFixDevLensIrayInit(EIpcCamLensType byLenType);
static void* NvrFixDevCmdHistoryCheckThread();
NVRSTATUS NvrFixHistoryCmdInsert(u8 bySerialID,u8*buf,u32 len,BOOL bNeedRes,ENvrPtzCtrlType eCmd);
NVRSTATUS NvrFixHistoryCmdInsert2(u8 bySerialID,u8*buf,u32 len,BOOL bNeedRes,ENvrPtzCtrlType eCmd,u32 dwDelay,u32 dwTimeOut);
NVRSTATUS NvrFixHistoryCmdClear(u8 bySerialID,ENvrPtzCtrlType eCmd);
NVRSTATUS NvrFixDevGetCurrentPTZHV(u32 *pdwPan, u32 *pdwTite,u32*pdwZoom,u32*pdwH,u32*pdwV,u32*pdwRatio);


/******ring buff define**************/

typedef struct cycle_buffer 
{  
    u8* pbyBuf;
    u32   dwSize;
    u32   dwIn;
    u32   dwOut;
}RingBuffer;  

RingBuffer *RingBuffer_create(u32 nLength);
void RingBuffer_destroy(RingBuffer *pRingBuffer);

s32 RingBuffer_read(RingBuffer *pRingBuffer, u8 *pbyBuf, u32 dwAmount);
s32 RingBuffer_write(RingBuffer *pRingBuffer, u8 *pbyBuf, u32 dwLength);

s32 RingBuffer_empty(RingBuffer *pRingBuffer);
s32 RingBuffer_Reset(RingBuffer *pRingBuffer);
s32 RingBuffer_remain(RingBuffer *pRingBuffer);
u8 RingBuffer_CheckSum(RingBuffer *pRingBuffer,u32 dwOut, u32 byBufLen);
s32 RingBuffer_Copy(RingBuffer *pRingBuffer,u32 dwOut,u8 *pbyBuf, u32 byBufLen);
s32 RingBuffer_Getcmd(RingBuffer *pRingBuffer, u8 *pbyBuf, u32* pdwLen);



//预置位转动不同速度等级Cnt值
static const u32 adwPersetSpeedCnt[41] =
{
	  0, 786, 785, 732, 683, 651, 606, 572, 516, 492,
	469, 463, 393, 349, 315, 286, 262, 242, 228, 207,
	192, 178, 167, 148, 140, 125, 112, 102,  93,  80,
	 66,  60,  54,  50,  43,  38,  36,  35,  34,  33, 23
};



static u8 g_bySerialID[NVR_MAX_CHN_NUM][NVR_DEV_SERIAL_MAX_NUM] = {{0}};
extern TNvrLifeStat g_aLifeStat[NVR_MAX_LCAM_CHN_NUM];
static u32 g_dwEESendCount=0;
static u32 g_dwEERecvCount=0;

static u16 g_wPtzSotfVer = 0;		///云台软件版本号

u8 g_dwMinZoomRatio = 0; 			///< 用于记录所用镜头最小ZOOM倍率
u8 g_dwMaxZoomRatio = 0; 			///< 用于记录所用镜头最大ZOOM倍率
u32 dwIrRealState = 100;				///<当前红外灯开关状态.100为初始状态




static TLcamPtzCmdMap g_atPtzCmdMap[NVR_PTZCTRL_TYPE_MAX-1] = {

{NVR_PTZCTRL_TYPE_MOVEUP, APP_CLT_PTZ_CTRL_MOVE_UP},
{NVR_PTZCTRL_TYPE_MOVEDOWN, APP_CLT_PTZ_CTRL_MOVE_DOWN},
{NVR_PTZCTRL_TYPE_MOVELEFT, APP_CLT_PTZ_CTRL_MOVE_LEFT},
{NVR_PTZCTRL_TYPE_MOVERIGHT, APP_CLT_PTZ_CTRL_MOVE_RIGHT},
{NVR_PTZCTRL_TYPE_MOVELEFTUP, APP_CLT_PTZ_CTRL_MOVE_LEFTUP},
{NVR_PTZCTRL_TYPE_MOVELEFTDOWN, APP_CLT_PTZ_CTRL_MOVE_LEFTDOWN},
{NVR_PTZCTRL_TYPE_MOVERIGHTUP, APP_CLT_PTZ_CTRL_MOVE_RIGHTUP},
{NVR_PTZCTRL_TYPE_MOVERIGHTDOWN, APP_CLT_PTZ_CTRL_MOVE_RIGHTDOWN},
{NVR_PTZCTRL_TYPE_MOVESTOP, APP_CLT_PTZ_CTRL_MOVE_STOP},

{NVR_PTZCTRL_TYPE_ZOOMTELE, APP_CLT_PTZ_CTRL_ZOOM_TELE},
{NVR_PTZCTRL_TYPE_ZOOMWIDE, APP_CLT_PTZ_CTRL_ZOOM_WIDE},
{NVR_PTZCTRL_TYPE_ZOOMSTOP, APP_CLT_PTZ_CTRL_ZOOM_STOP},

{NVR_PTZCTRL_TYPE_FOCUSFAR, APP_CLT_PTZ_CTRL_FOCUS_FAR},
{NVR_PTZCTRL_TYPE_FOCUSNEAR, APP_CLT_PTZ_CTRL_FOCUS_NEAR},
{NVR_PTZCTRL_TYPE_FOCUSAUTO, APP_CLT_PTZ_CTRL_FOCUS_AUTO},
{NVR_PTZCTRL_TYPE_FOCUSSTOP, APP_CLT_PTZ_CTRL_FOCUS_STOP},

{NVR_PTZCTRL_TYPE_IRIS_PLUS, APP_CLT_PTZ_CTRL_IRIS_PLUS},
{NVR_PTZCTRL_TYPE_IRIS_MINUS, APP_CLT_PTZ_CTRL_IRIS_MINUS},
{NVR_PTZCTRL_TYPE_IRIS_AUTO, APP_CLT_PTZ_CTRL_IRIS_AUTO},
{NVR_PTZCTRL_TYPE_IRIS_STOP, APP_CLT_PTZ_CTRL_IRIS_STOP},

{NVR_PTZCTRL_TYPE_RESET, APP_CLT_PTZ_CTRL_RESET},

{NVR_PTZCTRL_TYPE_PRESET_SAVE, APP_CLT_PTZ_CTRL_PRESET_SET},
{NVR_PTZCTRL_TYPE_PRESET_DEL, APP_CLT_PTZ_CTRL_PRESET_REMOVE},
{NVR_PTZCTRL_TYPE_PRESET_LOAD, APP_CLT_PTZ_CTRL_PRESET_LOAD},

{NVR_PTZCTRL_TYPE_LIGHT_OPEN, APP_CLT_PTZ_CTRL_LIGHT_OPEN},
{NVR_PTZCTRL_TYPE_LIGHT_CLOSE, APP_CLT_PTZ_CTRL_LIGHT_CLOSE},

{NVR_PTZCTRL_TYPE_WIPER_OPEN, APP_CLT_PTZ_CTRL_WIPER_OPEN},
{NVR_PTZCTRL_TYPE_WIPER_CLOSE, APP_CLT_PTZ_CTRL_WIPER_CLOSE},

{NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT, APP_CLT_PTZ_CTRL_HORIZONSCAN_START},
{NVR_PTZCTRL_TYPE_HORIZONSCAN_STOP, APP_CLT_PTZ_CTRL_HORIZONSCAN_STOP},

{NVR_PTZCTRL_TYPE_PATH_CRUISE_START, APP_CLT_PTZ_CTRL_PATHCRUISE_START},
{NVR_PTZCTRL_TYPE_PATH_CRUISE_STOP, APP_CLT_PTZ_CTRL_PATHCRUISE_STOP},

{NVR_PTZCTRL_TYPE_GOTOPOINT, APP_CLT_PTZ_CTRL_GOTOPOINT},
{NVR_PTZCTRL_TYPE_ZOOMPART, APP_CLT_PTZ_CTRL_ZOOMPART},
{NVR_PTZCTRL_TYPE_ZOOMWHOLE, APP_CLT_PTZ_CTRL_ZOOMWHOLE},

{NVR_PTZCTRL_TYPE_PTZSTOP, APP_CLT_PTZ_CTRL_PTZSTOP},
{NVR_PTZCTRL_TYPE_CRUISE_SCAN_STOP, APP_CLT_PTZ_CTRL_CRUISE_SCAN_STOP},
{NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET, APP_CLT_PTZ_CTRL_HORIZONSCAN_SPEEDSET},

{NVR_PTZCTRL_PRESET_CREATE, APP_CLT_PTZ_CTRL_PRESET_CREATE},

{NVR_PTZCTRL_LIMIT_HORIZONALLEFT, APP_CLT_PTZ_LIMIT_HORIZONALLEFT },
{NVR_PTZCTRL_LIMIT_HORIZONALRIGHT, APP_CLT_PTZ_LIMIT_HORIZONALRIGHT },
{NVR_PTZCTRL_LIMIT_VERTICALUP, APP_CLT_PTZ_LIMIT_VERTICALUP},
{NVR_PTZCTRL_LIMIT_VERTICALDOWN, APP_CLT_PTZ_LIMIT_VERTICALDOWN},
{NVR_PTZCTRL_LIMIT_HORIZONAL_REMOVE, APP_CLT_PTZ_LIMIT_HORIZONAL_REMOVE},
{NVR_PTZCTRL_LIMIT_VERTICAL_REMOVE, APP_CLT_PTZ_LIMIT_VERTICAL_REMOVE},
{NVR_PTZCTRL_LIMIT_CLEAR, APP_CLT_PTZ_LIMIT_CLEAR},
{NVR_PTZCTRL_MANUALLIMITSWITCH_SET, APP_CLT_PTZ_MANUALLIMITSWITCH_SET},
{NVR_PTZCTRL_SCANLIMITSWITCH_SET, APP_CLT_PTZ_SCANLIMITSWITCH_SET},
{NVR_PTZCTRL_SYNCSCAN_REC, APP_CLT_PTZ_SYNCSCAN_REC},
{NVR_PTZCTRL_SYNCSCAN_STOPREC, APP_CLT_PTZ_SYNCSCAN_STOPREC},
{NVR_PTZCTRL_SYNCSCAN_PREVIEW, APP_CLT_PTZ_SYNCSCAN_PREVIEW},
{NVR_PTZCTRL_SYNCSCAN_STOPPREVIEW, APP_CLT_PTZ_SYNCSCAN_STOPPREVIEW},
{NVR_PTZCTRL_SYNCSCAN_DELETE, APP_CLT_PTZ_SYNCSCAN_DELETE},
{NVR_PTZCTRL_PRESET_REMOVE_ALL, APP_CLT_PTZ_SYNCSCAN_DELETE_ALL},
{NVR_PTZCTRL_PATH_CRUISE_REMOVE_ALL, APP_CLT_PTZ_CTRL_PATHCRUISE_REMOVE_ALL},
{NVR_PTZCTRL_SYNCSCAN_DELETE_ALL, APP_CLT_PTZ_SYNCSCAN_DELETE_ALL},
{NVR_PTZCTRL_PANPOSION_SET, APP_CLT_PTZ_PANPOSION_SET},
{NVR_PTZCTRL_TILTPOSION_SET, APP_CLT_PTZ_TILTPOSION_SET },
{NVR_PTZCTRL_TURNTO_MACHINEZERO, APP_CLT_PTZ_TURNTO_MACHINEZERO},
{NVR_PTZCTRL_PTZ_CALIBRATE, APP_CLT_PTZ_CALIBRATE},

{NVR_PTZCTRL_RESTORE_FACTORY, APP_CLT_PTZ_RESTORE_FACTORY},
{NVR_PTZCTRL_PRESET_REMOVE_ALL, APP_CLT_PTZ_CTRL_PRESET_REMOVE_ALL},
{NVR_PTZCTRL_TYPE_LASER_MOVEUP, APP_CLT_PTZ_LASER_MOVEUP},
{NVR_PTZCTRL_TYPE_LASER_MOVEDOWN, APP_CLT_PTZ_LASER_MOVEDOWN},
{NVR_PTZCTRL_TYPE_LASER_MOVELEFT, APP_CLT_PTZ_LASER_MOVELEFT},
{NVR_PTZCTRL_TYPE_LASER_MOVERIGHT, APP_CLT_PTZ_LASER_MOVERIGHT},
{NVR_PTZCTRL_TYPE_LASER_MOVESTOP, APP_CLT_PTZ_LASER_MOVESTOP},
{NVR_PTZCTRL_PTZ_LASER_ANGLE_INCRE,APP_CLT_PTZ_LASER_ANGLE_INCRE},
{NVR_PTZCTRL_PTZ_LASER_ANGLE_DECRE,APP_CLT_PTZ_LASER_ANGLE_DECRE},
{NVR_PTZCTRL_TYPE_BF_NEAR,APP_CLT_PTZ_BF_NEAR},
{NVR_PTZCTRL_TYPE_BF_FAR,APP_CLT_PTZ_BF_FAR},
{NVR_PTZCTRL_TYPE_BF_AUTO,APP_CLT_PTZ_BF_AUTO},

};



static TLcamWatchOnTypeMap g_atWatchOnTypeMap[NVR_PTZCTRL_PTZ_TASK_COUNT - 1] = {
{NVR_PTZCTRL_HORIZON_SCAN_TASK, APP_CLT_HORIZON_SCAN_TASK},
{NVR_PTZCTRL_VERTICAL_SCAN_TASK, APP_CLT_VERTICAL_SCAN_TASK},
{NVR_PTZCTRL_PRESET_LOAD_TASK, APP_CLT_PRESET_LOAD_TASK},
{NVR_PTZCTRL_PATH_CRUISE_TASK, APP_CLT_PATH_CRUISE_TASK},
{NVR_PTZCTRL_FRAME_SCAN_TASK, APP_CLT_FRAME_SCAN_TASK},
{NVR_PTZCTRL_RAND_SCAN_TASK, APP_CLT_RAND_SCAN_TASK},
{NVR_PTZCTRL_FULLVIEW_SCAN_TASK, APP_CLT_FULLVIEW_SCAN_TASK},
{NVR_PTZCTRL_SYNC_SCAN_TASK, APP_CLT_SYNC_SCAN_TASK},
};
static void NvrFixDevStartPTZQuery(u8 chnid);
//static void NvrFixDevStopPTZQuery();

static void* NvrFixDevPTZQueryThread();

#ifdef XCORE
static void* NvrFixDevXCoreQueryThread();
#endif
static void* NvrFixDevDealCamCmdThread();//设备控制处理线程

static void NrvFixDevWaitPTZQueryOverBlock();
static void NrvFixDevWaitPTZQueryOverUnBlock();




static u8 NvrFixDevCalcCheckSum(u8 *pbybuf, u8 byBufLen);
static s32 NvrFixDevGetHvAngleTimerCB( HTIMERHANDLE dwTimerId, void* param );
void NvrFixDevParseSerialReadDateCB(u8 bySerialId,u8 *pbyBuf, s32 nLen);
static NVRSTATUS NvrFixDevParseSerialReadDate(u8 bySerialId,u8 *pbyBuf, s32 nLen);
static s32 NvrFixDevPowerOffMemTimerCB(HTIMERHANDLE dwTimerId, void* param);
NVRSTATUS NvrFixDevSetFocus(u8 bySerialId, u8 byOptType, u8 bySpeed);
NVRSTATUS NvrFixDevSetFocusZoomXcoreFTII(u8 bySerialId, u8 byOptType, u8 bySpeed);
NVRSTATUS NvrFixDevSetFocusZoomMINJIA2(u8 bySerialId, u8 byOptType, u8 bySpeed);
NVRSTATUS NvrFixDevSetIris(u8 bySerialId, u8 byOptType, u8 bySpeed);
static s32 NvrFixDevHandleZoomOperation(ENvrPtzCtrlType eCtrlType, BOOL32 bPre);


static NVRSTATUS NvrFixDevStartPtzUpdateTask();

static void NvrFixDevLensUpgradeDateCB(TNvrSysDevUpgradeDateInfo *ptLensUpgradeDateInfo, ENvrDevUpgradeStateIn eUpgradeStateIn);
static NVRSTATUS NvrFixDevStartLensUpdateTask();
static void* NvrFixDevLensUpdateThread();
static void* NvrFixDevLensXmodemUpdateThread();
static s32 NvrFixDevLensUpgradeTimerCB(HTIMERHANDLE dwTimerId, void* param);
static NVRSTATUS NvrFixDevSetLensVer();
static NVRSTATUS NvrFixDevSetLensInfo();
static s32 NvrFixDevGetFocusTimerCB( HTIMERHANDLE dwTimerId, void* param);
static s32 NvrFixDevSetFocusTimerCB( HTIMERHANDLE dwTimerId, void* param);

/************************通过磁感应和加速度贴方位角osd************************/
#define NVR_FIX_MAG_FILE        "/usr/config/nvrcfg_magnetic.dat"   //磁感应偏差配
static s32 NvrFixDevPtzUpdatePtzOsdPositionCB(HTIMERHANDLE dwTimerId, void* param);  //更新ptz osd回调函数
NVRSTATUS NvrFixDevMagneticCalibration();                               //调试命令，出厂设置调试偏差值
NVRSTATUS NvrFixSetMagneticCfg(TNvrFixMagneticInfo *ptMagInfo);      //设置设备磁感应在偏差值
NVRSTATUS NvrFixGetMagneticCfg(TNvrFixMagneticInfo *ptMagInfo);//设置设备磁感应在偏差值
static HTIMERHANDLE g_hUpdateOsdTimer = NULL;               //刷新ptz图标定时器
static s16 g_wLpf_x = 0, g_wLpf_y = 0, g_wLpf_z = 0;
static s16 g_wAtantab[] = {11520, 6801, 3593, 1824, 916, 458, 229, 115, 57, 29, 14, 7, 4, 2, 1};
static s16 g_wXmax = -32768, g_wXmin = 32767, g_wYmax = -32768, g_wYmin = 32767, g_wZmax = -32768, g_wZmin = 32767;
static s16 g_wXoff = -1666, g_wYoff = 1442, g_wZoff = 0;
static u8 byUpdatePtzOsd = 0; 
/******************************************end*********************************/

///< -----------------------  smoke fire  --------------------------
typedef struct tagSmokeFireBuffer
{
    u16 wChnId;
    u16 wAlgType;
    s32 nPresentId;
}TNvrFixDevSmokeFireTimerParam;

typedef enum
{
    NVR_DEV_SMOKE_FIREE_PTZ_START = 0,      ///< ptz操作开启算法
    NVR_DEV_SMOKE_FIREE_START,              ///< 检测到pt位置停止开启算法

    NVR_DEV_SMOKE_FIREE_COUNT,
}ENvrFixDevSmokeFireOpType;

static TNvrFixDevSmokeFireTimerParam g_tTimerParam = {0};                       ///< 烟火识别定时器参数
static HTIMERHANDLE g_hSmokeFireStartTimer = NULL;                              ///< 烟火识别启用定时器

static NVRSTATUS NvrFixDevSmokeFireTrigger(ENvrFixDevSmokeFireOpType eType, u16 wChnId, s32 nPresetId);
static s32 NvrFixDevSomkeFireTimer( HTIMERHANDLE dwTimerId, void* param);       ///< 烟火识别启用定时器处理函数
static void NvrFixDevAlarmStatusChangeCB(TNvrAlarmSrc tAlarmSrc, u8 byAlarmStat, TNvrBrokenDownTime tAlarmTime, void* pParam);
///< -----------------------smoke fire end--------------------------

static NVRSTATUS NvrFixDevGetEnvInfo(PTNvrSysEnvInfo ptNvrSysEnvInfo);

//读取串口数据
NVRSTATUS NvrFixDevReadSerial(u8 bySerialId,u8 *pbyReadBuf, u16 wReadLen, u16 *pwRealLen, u32 dwTimeOut)
{
    u32 i = 0;
    static u8 abyReadOnceBuf[NVR_FIX_DEV_MAX_BUF_LEN];      //每次读串口缓存buf
    static u8 abyStoreBuf[NVR_FIX_DEV_MAX_BUF_LEN];         //将每次读串口数据组装buf
    u32 dwReadOnceLen = 0;
    u32 dwStoreBufLen = 0;
    u32 dwNeedReadLen = wReadLen;

    memset(abyStoreBuf, 0, sizeof(abyStoreBuf));
    memset(abyReadOnceBuf, 0, sizeof(abyReadOnceBuf));

    FIX_ASSERT(pbyReadBuf);
    FIX_ASSERT(pwRealLen);

    for (i = 0; i < 5; i++)
    {
        if(0 == dwNeedReadLen)
        {
            break;
        }

        memset(abyReadOnceBuf, 0, sizeof(abyReadOnceBuf));
        dwReadOnceLen = 0;
        NvrDevSerialReadOnce(bySerialId, abyReadOnceBuf, dwNeedReadLen, &dwReadOnceLen, dwTimeOut);

        if(dwReadOnceLen < dwNeedReadLen)            //当前还未读出指定大小
        {
            memcpy(abyStoreBuf+dwStoreBufLen, abyReadOnceBuf, dwReadOnceLen);
            dwStoreBufLen += (u8)dwReadOnceLen;
        }
        else
        {
            memcpy(abyStoreBuf+dwStoreBufLen, abyReadOnceBuf, dwNeedReadLen);
            dwReadOnceLen = dwNeedReadLen;
            dwStoreBufLen += dwNeedReadLen;
        }
        dwNeedReadLen -= (u8)dwReadOnceLen;

        if (dwTimeOut > 10)
        {
            ///<增加延时，防止数据没有收齐
            usleep(10 * 1000);
        }
    }

    memcpy(pbyReadBuf, abyStoreBuf, dwStoreBufLen);
    *pwRealLen = dwStoreBufLen;

    return NVR_ERR__OK;

}

NVRSTATUS NvrFixDevEnvInfoGetBySerial(u8 bySerialId, u8 *pbyBuff, u32 dwLen, u32* pdwRealLen)
{
     NVRSTATUS eRet = NVR_ERR__OK;

     NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMTAKE);
     eRet = NvrDevWriteSerial(bySerialId, pbyBuff, dwLen, pdwRealLen);
     usleep(100 * 1000);
     NvrFixDevReadSerial(bySerialId, pbyBuff, dwLen, pdwRealLen, 10);
     NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMGIVE);

     return eRet;
}

NVRSTATUS NvrFixDevWriteSerial(u8 bySerialId, u8 *pbyBuff, u32 dwLen, u32* pdwRealLen)
{
     NVRSTATUS eRet = NVR_ERR__OK;

     NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMTAKE);
     eRet = NvrDevWriteSerial(bySerialId, pbyBuff, dwLen, pdwRealLen);
     NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMGIVE);

     return eRet;
}


//向串口写数据 --- 对端来不及接收数据，发送的时候加延时
NVRSTATUS NvrDevWriteSerial2(u8 *pbyWrBuf, u16 wWrLen, u32 *pwRealLen, u8 bySerialId)
{

	u32 i = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
    //3519平台的高空?望机型，云台升级时，字节间的延时要加到3.5ms才能升级成功

	u32 realCount = 0;
	u32 realLen = 0;
	for( i = 0; i < wWrLen; i++ )
	{
		//realLen = NvrBrdApiSerialWrite(g_tSerialHandle[bySerialId].hSerialHandle, &pbyWrBuf[i], 1);
        NvrDevWriteSerial(bySerialId, &pbyWrBuf[i], 1, &realLen);
		realCount = realCount + realLen;
		usleep(3500);
	}
	return eRet;
}


NVRSTATUS NvrFixDevWriteSerial2(u8 bySerialId,u8 *pbyBuff, u32 dwLen, u32* pdwRealLen)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMTAKE);
    if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
    {
        eRet = NvrDevWriteSerial2(pbyBuff, dwLen, pdwRealLen,bySerialId);
    }
    else
    {
        eRet = NvrDevWriteSerial(bySerialId, pbyBuff, dwLen, pdwRealLen);
    }
    NvrDevSemOpt(bySerialId,NVR_DEV_SERIAL_SEMGIVE);

    return eRet;
}


NVRSTATUS NvrFixDevModifyBaudrate(u8 bySerialId,ENvrDevSerialBaudrate eBaudrate)
{
	TNvrDevSerialParam tParam;
	NVRSTATUS eNvrRet;
	
	NvrDevGetSerialParam(bySerialId, &tParam);

	tParam.eBaudrate = eBaudrate;

    eNvrRet = NvrDevSetSerialParam(bySerialId, &tParam);
    if(NVR_ERR__OK != eNvrRet)
    {
        PRINTDBG("NvrDevSetSerialParam failed %d, id %u\n", eNvrRet, bySerialId);
    }

	return eNvrRet;
}

s32 NvrFixDevUpgradeTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
    if(g_tNvrFixDevUpgradeParam.tState.eState != APP_CLT_DEV_PTZ_UP_STATE_DONE)
    {
        PRINTDBG("NvrFixDevGetHvAngleTimerCB APP_CLT_DEV_PTZ_UP_STATE_SENDING\n");
        g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_SENDING;
        g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);
    }
    else
    {
        PRINTDBG("NvrFixDevGetHvAngleTimerCB start reboot\n");
        NvrSysReboot(NULL);
    }
    
    return 0;
}
#define IPC_DEV_PT_GENERAL_PKG       	   0        //general pt pkg 
#define IPC_DEV_PT_FIX_MAINBOARD_PKG       1        //fixgun, pt main board pkg 
#define IPC_DEV_PT_FIX_CAMBOARD_PKG        2      	//fixgun, pt camera board pkg
#define IPC_DEV_PT_FIX_FOCUSBOARD_PKG      3      	//fixgun, pt focus board pkg

NVRSTATUS NvrFixDevUpgradeStart(IN const u32 dwDevID, IN const u32 dwPkgTotalSize,IN const u8 * pbyPkgHeadBuf,IN const u32 dwPkgHeadBufSize,IN PFCltProtoDevPtzUpgradeStateReport pfStateReport,u32 dwOpeque)
{
    PRINTDBG("File head:%s HeadLen:%d\n", pbyPkgHeadBuf, dwPkgHeadBufSize);

    memset(&g_tNvrFixDevUpgradeParam,0,sizeof(g_tNvrFixDevUpgradeParam));
    g_tNvrFixDevUpgradeParam.byUpdateType = 0xFF;

    if(0 == strncmp((s8*)pbyPkgHeadBuf, "MAIN",4))
	{
		PRINTDBG("[IPCDEV]IpcDevPtzUpdateHeadCheck, main board bin !\n");	
		g_tNvrFixDevUpgradeParam.byUpdateType = IPC_DEV_PT_FIX_MAINBOARD_PKG;
 	}
    else if(0 == strncmp((s8*)pbyPkgHeadBuf, "CAMB",4))
	{	
		PRINTDBG("[IPCDEV]IpcDevPtzUpdateHeadCheck, cammera board bin !\n");	
		g_tNvrFixDevUpgradeParam.byUpdateType = IPC_DEV_PT_FIX_CAMBOARD_PKG;
	}
    else if(0 == strncmp((s8*)pbyPkgHeadBuf, "AFBOARD",7))
	{	
		PRINTDBG("[IPCDEV]IpcDevPtzUpdateHeadCheck, focus board bin !\n");	
		g_tNvrFixDevUpgradeParam.byUpdateType = IPC_DEV_PT_FIX_FOCUSBOARD_PKG;
	}
    else if(0 == strncmp((s8 *)pbyPkgHeadBuf, "stm32f051", strlen("stm32f051")))
    {
        PRINTDBG("File head check stm32f051\n");
        g_tNvrFixDevUpgradeParam.byUpdateType = IPC_DEV_PT_GENERAL_PKG;
    }
    
    if (g_tNvrFixDevUpgradeParam.byUpdateType == 0xFF)
    {
        PRINTERR("File head check fail\n");
        return NVR_ERR__ERROR;
    }
    
#ifdef _CV2X_
    strncpy(g_tNvrFixDevUpgradeParam.achUpgradeFilePath, NVR_FIX_DEV_UPTPTZ_PATH, sizeof(g_tNvrFixDevUpgradeParam.achUpgradeFilePath));
#else
    strcpy(g_tNvrFixDevUpgradeParam.achUpgradeFilePath,"/mnt/sdcard/ptzupgrade.bin");
#endif
    g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport = pfStateReport;
    g_tNvrFixDevUpgradeParam.dwDevID = dwDevID;
    g_tNvrFixDevUpgradeParam.dwPkgTotalSize = dwPkgTotalSize;
    g_tNvrFixDevUpgradeParam.dwOpeque = dwOpeque;
    g_tNvrFixDevUpgradeParam.pPkgBuf = (u8 *)malloc(dwPkgTotalSize);

    remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);

	///<nvrpui里调用appclt接口时，nvrpui的回调函数指针赋值给appclt里的函数指针操作在当前的回调函数调用之后，所以必须要用定时器等一段时间后在上报，否则appclt回调nvrpui里的函数指针时会为空，后续的状态上报不需要再用定时器
    OsApi_TimerSet( g_hPtzUpgradeTimer, 1000, NvrFixDevUpgradeTimerCB, NULL);//停止1s后上报云台状态

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevUpgradeSendPkgData(IN const u32 dwDevID,IN const u32 dwDataBeginPos,IN const u8 * pbyDataBuf,IN const u32 dwDataBufSize)
{
    FILE* pFile = NULL;

    NVRSTATUS eRet = NVR_ERR__OK;


#ifdef _CV2X_
#else
    s32 nRet = 0;
    u8 i = 0;
#endif


    memcpy(g_tNvrFixDevUpgradeParam.pPkgBuf+dwDataBeginPos,pbyDataBuf,dwDataBufSize);
    g_tNvrFixDevUpgradeParam.dwWriteSize += dwDataBufSize;

    if(g_tNvrFixDevUpgradeParam.dwWriteSize >= g_tNvrFixDevUpgradeParam.dwPkgTotalSize)
    {
        PRINTDBG("NvrFixDevUpgradeSendPkgData download finish %s\n",g_tNvrFixDevUpgradeParam.achUpgradeFilePath);

        pFile = fopen(g_tNvrFixDevUpgradeParam.achUpgradeFilePath,"w+");
        if(pFile == NULL)
        {
            PRINTERR("NvrFixDevUpgradeSendPkgData fopen %s fail\n", g_tNvrFixDevUpgradeParam.achUpgradeFilePath);

            g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
            g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_UPLOAD;
            remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
            g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

            free(g_tNvrFixDevUpgradeParam.pPkgBuf);
            return NVR_ERR__ERROR;
        }
        if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
        {
            fwrite(g_tNvrFixDevUpgradeParam.pPkgBuf,1,g_tNvrFixDevUpgradeParam.dwPkgTotalSize,pFile);
        }
        else
        {
            fwrite(g_tNvrFixDevUpgradeParam.pPkgBuf+32,1,g_tNvrFixDevUpgradeParam.dwPkgTotalSize-32,pFile);
        }
        
        free(g_tNvrFixDevUpgradeParam.pPkgBuf);
        fclose(pFile);

#ifdef _CV2X_
        g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_CHECKING;
        g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);
        
        eRet = NvrFixDevStartPtzUpdateTask();
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("NvrPtzDevUpgradeSendPkgData, NvrPtzDevStartPtzUpdateTask fail\n");
            g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
            g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
            remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
            g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

            return NVR_ERR__ERROR;
        }
#else
        g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_DONE;        
        g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);
        
        //先关闭所有串口,再串口升级
        for(i = 0;i < 2;i++)
        {
		   NvrDevSemOpt(i,NVR_DEV_SERIAL_SEMTAKE);
           NvrDevCloseSerial(i);           
           nRet = SysUpgradeScm(i,g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
		   NvrDevSemOpt(i,NVR_DEV_SERIAL_SEMGIVE);
           PRINTDBG("NvrFixDevUpgradeThread No:%u,NvrBrdApiSerialClose eRet %u,SysUpgradeScm nRet %u\n",i,eRet,nRet);
        }

        remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);

        OsApi_TimerSet( g_hPtzUpgradeTimer, 1000, NvrFixDevUpgradeTimerCB, NULL);//1s后重启设备
#endif
        MAKE_COMPILER_HAPPY(NvrFixDevStartPtzUpdateTask);
    }

    return NVR_ERR__OK;
}

void NvrFixDevPtzUpdateQuit(FILE *pFile, BOOL32 bUpdateSuccess)
{
	TNvrPtzCtrlInfo tCmd;
    u8 byGetVerCount = 0;
	g_bPtzUpdating = FALSE;
	g_hPtzUpdateTask = (TASKHANDLE)NULL;

	if (NULL != pFile)
	{
		fclose(pFile);
	}

	if (NULL != g_hPtzUpdateTimer)
	{
		OsApi_TimerStop(g_hPtzUpdateTimer);
		OsApi_TimerDelete(g_hPtzUpdateTimer);
		g_hPtzUpdateTimer = NULL;
		PRINTDBG("delete timer\n");
	}

	if (bUpdateSuccess)
	{
	    g_bPtzGetVerSuc = FALSE;
		g_byPtzUpdateSuc = NVR_FIX_DEV_UPTPTZ_SUCCESS;
		PRINTDBG("ptz update success!!!\n");
		sleep(NVR_FIX_DEV_PTZREBOOT_DELAY);
		//升级成功后开启停止的任务
		//NvrFixDevStartAllPtzTask();

        ///<升级成功后查询云台版本号，如获取多次失败重启设备
		tCmd.eCtrlType = NVR_PTZCTRL_SOFTVER_QUERY;
        while(!g_bPtzGetVerSuc)
        {
            if(byGetVerCount > 5)
            {
                g_byPtzUpdateSuc = NVR_FIX_DEV_UPTPTZ_FAILED;
                break;
            }
            NvrFixDevPtzCtrl(0, tCmd);
            byGetVerCount++;
            OsApi_TaskDelay( 1000 );
            PRINTDBG("byGetVerCount : %u\n",byGetVerCount);
        }

		///<升级成功后查询云台角度同时可以刷新云台坐标
		mzero(tCmd);
		tCmd.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;
		NvrFixDevPtzCtrl(0, tCmd);

	}
	else
	{
#if 0	///<科达云台升级时波特率不会修改，还是9600，其他外厂商云台升级时波特率可能会修改，波特率需要再设置回来
		u32 dwBitRate = 9600;
		///<设置波特率
		IpcHalApiSerialIoctl(g_hSerialHandle[1], SIO_SET_BAUDRATE, (void *)&dwBitRate);
#endif
		g_byPtzUpdateSuc = NVR_FIX_DEV_UPTPTZ_FAILED;
		PRINTVIP("ptz update failed!!!\n");
	}

}

s32 NvrFixDevPtzUpdateTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
	g_bPtzUpdating = FALSE;
	PRINTVIP("update time out!\n");

	return 0;
}

u16	calcrc(u8 *pData, u16 len)
{
	u16 crc = 0;
	char i;
	while (0 < len--)
	{
		crc = crc ^ (u16) *pData++ << 8;
	    i = 8;
	    do
	    {
	    	if (crc & 0x8000)
	        		crc = crc << 1 ^ 0x1021;
	    	else
	        		crc = crc << 1;
	    }while(--i);
	}
	return (crc);
}

NVRSTATUS NvrFixDevConvertSerialCodeForLaser(TNvrPtzCtrlInfo tNvrPtzCmdInfo, u8 *pbyBuf, u32 *pbyLen)
{

	u8 byCmdLen = 0;
	u8 byAddress = 0;

	FIX_ASSERT(pbyBuf);
	FIX_ASSERT(pbyLen);

	///<TODO，ptz变化时需要对移动侦测做特殊处理，待处理
    TNvrDevSerialParam tSerialParam;
    mzero(tSerialParam);

    NvrDevGetSerialParam(SERIAL_TYPE_PTZCTRL, &tSerialParam);
    printf("Laser type:%d\n",tSerialParam.eProtoType);
	///<TODO，支持的云台控制协议、云台地址码需要从TIpcPtzCtrlPrm配置中获取，暂时云台协议只处理PELCO-D，地址码固定为1，待处理
	if (NVR_PTZPROTO_TYPE_PELCO_D_LFEB == tSerialParam.eProtoType)
    {   
	byCmdLen = 7;
	byAddress = 0x02;	///<地址码暂时固定为1，后续再根据TIpcPtzCtrlPrm中的配置来，tNvrPtzCmdInfo中也有个地址码，待弄清楚这两个地址码有什么区别，到底应该用哪个
	pbyBuf[0] = 0xff;
	pbyBuf[1] = byAddress;
	switch (tNvrPtzCmdInfo.eCtrlType)
	{
		case NVR_PTZCTRL_TYPE_LASER_MOVEUP:
		{
			pbyBuf[3] = 0x08;
			///<云台速度等级范围是0-63，需要做一下转换
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;	
		}
		break;

		case NVR_PTZCTRL_TYPE_LASER_MOVEDOWN:
		{
			pbyBuf[3] = 0x10;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
		}
		break;

		case NVR_PTZCTRL_TYPE_LASER_MOVELEFT:
		{
			pbyBuf[3] = 0x04;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
		}
		break;

		case NVR_PTZCTRL_TYPE_LASER_MOVERIGHT:
		{   
			pbyBuf[3] = 0x02;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
		}
		break;
        case NVR_PTZCTRL_TYPE_LASER_MOVESTOP:
        {
        }
        break;
        case NVR_PTZCTRL_PTZ_LASER_ANGLE_INCRE:
        {   
            pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x02;
			pbyBuf[3] = 0x00;
			pbyBuf[4] = 0x00;
			pbyBuf[5] = 0x00; //每次增大或减小步数为16步，对应角度0.1左右
        }
        break;
        case NVR_PTZCTRL_PTZ_LASER_ANGLE_DECRE:
        {   
            pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x04;
			pbyBuf[3] = 0x00;
			pbyBuf[4] = 0x00;
			pbyBuf[5] = 0x00; //每次增大或减小步数为16步，对应角度0.1左右
        }
        break;

		default:
		PRINTERR("NvrPtzDevConvertSerialCode ________ laser unknow command (%d) _______\n", tNvrPtzCmdInfo.eCtrlType);

	}

	if (byCmdLen > 0)
	{
		pbyBuf[byCmdLen - 1] = NvrFixDevCalcCheckSum(pbyBuf, byCmdLen);
	}

   }


    //if(NVR_PTZPROTO_TYPE_SONY == tSerialParam.eProtoType){;}
   
	///<TODO，end

	*pbyLen = byCmdLen;

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevConvertSerialCode(TNvrPtzCtrlInfo tNvrPtzCmdInfo, u8 *pbyBuf, u32 *pbyLen)
{
	u8 i = 0;
	u8 byCmdLen = 0;
	u8 byAddress = 0;

	FIX_ASSERT(pbyBuf);
	FIX_ASSERT(pbyLen);

	///<TODO，ptz变化时需要对移动侦测做特殊处理，待处理
    TNvrDevSerialParam tSerialParam;
    mzero(tSerialParam);

    NvrDevGetSerialParam(g_byPtzCtrlSerialId, &tSerialParam);
    printf("ptz type:%d\n",tSerialParam.eProtoType);
	///<TODO，支持的云台控制协议、云台地址码需要从TIpcPtzCtrlPrm配置中获取，暂时云台协议只处理PELCO-D，地址码固定为1，待处理
	if (NVR_PTZPROTO_TYPE_PELCO_D_LFEB == tSerialParam.eProtoType)
    {   
	byCmdLen = 7;
	byAddress = 0x01;	///<地址码暂时固定为1，后续再根据TIpcPtzCtrlPrm中的配置来，tNvrPtzCmdInfo中也有个地址码，待弄清楚这两个地址码有什么区别，到底应该用哪个
	pbyBuf[0] = 0xff;
	pbyBuf[1] = byAddress;
	switch (tNvrPtzCmdInfo.eCtrlType)
	{
		case NVR_PTZCTRL_AQUILLA_SET_PTZAngle:
		{
			byCmdLen = 11;
			pbyBuf[0] = 0x09;
			pbyBuf[1] = 0xF6;
			pbyBuf[2] = byAddress;
			pbyBuf[3] = 0x09;
			pbyBuf[4] = (u8)(tNvrPtzCmdInfo.wXposition)&0xFF;
			pbyBuf[5] = (u8)(tNvrPtzCmdInfo.wXposition >> 8) & 0xFF;
			pbyBuf[6] = (u8)(tNvrPtzCmdInfo.wYposition)&0xFF;
			pbyBuf[7] = (u8)(tNvrPtzCmdInfo.wYposition >> 8) & 0xFF;
			pbyBuf[8] = (u8)(tNvrPtzCmdInfo.dwRes)&0xFF;
			pbyBuf[9] = (u8)(tNvrPtzCmdInfo.dwRes >> 8) & 0xFF;
		}
		break;
		case NVR_PTZCTRL_AQUILLA_SET_PTMOVE:
		{
			byCmdLen = 16;
			pbyBuf[0] = 0x0E;
			pbyBuf[1] = 0xF1;
			pbyBuf[2] = byAddress;
			pbyBuf[3] = 0x19;
			pbyBuf[4] = (u8)(tNvrPtzCmdInfo.wXposition)&0xFF;
			pbyBuf[5] = (u8)(tNvrPtzCmdInfo.wXposition >> 8) & 0xFF;
			pbyBuf[6] = (u8)(tNvrPtzCmdInfo.wYposition)&0xff;
			pbyBuf[7] = (u8)(tNvrPtzCmdInfo.wYposition >> 8) & 0xFF;
			pbyBuf[8] = (u8)(tNvrPtzCmdInfo.dwRes) & 0xFF;
			pbyBuf[9] = (u8)(tNvrPtzCmdInfo.dwRes >> 8 ) & 0xFF;
			pbyBuf[10] = (u8)(tNvrPtzCmdInfo.dwRes >> 16) & 0xFF;
			pbyBuf[11] = (u8)(tNvrPtzCmdInfo.wPanSpeed) & 0xFF;
			pbyBuf[12] = (u8)(tNvrPtzCmdInfo.wPanSpeed >> 8) & 0xFF;
			pbyBuf[13] = (u8)(tNvrPtzCmdInfo.wTilSpeed) & 0xFF;
			pbyBuf[14] = (u8)(tNvrPtzCmdInfo.wTilSpeed >> 8) & 0xFF;
		}
		break;

		case NVR_PTZCTRL_AQUILLA_SET_SPEED:
		{
			byCmdLen = 11;
			pbyBuf[0] = 0x09;
			pbyBuf[1] = 0xF6;
			pbyBuf[2] = byAddress;
			pbyBuf[3] = 0x07;
			pbyBuf[4] = (u8)(tNvrPtzCmdInfo.wPanSpeed) & 0xFF;
			pbyBuf[5] = (u8)(tNvrPtzCmdInfo.wPanSpeed >> 8) & 0xFF;
			pbyBuf[6] = (u8)(tNvrPtzCmdInfo.dwRes >> 8 ) & 0xFF;
			pbyBuf[7] = (u8)(tNvrPtzCmdInfo.wTilSpeed) & 0xFF;
			pbyBuf[8] = (u8)(tNvrPtzCmdInfo.wTilSpeed >> 8) & 0xFF;
			pbyBuf[9] = (u8)(tNvrPtzCmdInfo.dwRes) & 0xFF;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVEUP:
		{
			pbyBuf[3] = 0x08;
			///<云台速度等级范围是0-63，需要做一下转换
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;	
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVEDOWN:
		{
			pbyBuf[3] = 0x10;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVELEFT:
		{
			pbyBuf[3] = 0x04;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVERIGHT:
		{
			pbyBuf[3] = 0x02;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVELEFTUP:
		{
			pbyBuf[3] = 0x0c;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVELEFTDOWN:
		{
			pbyBuf[3] = 0x14;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVERIGHTUP:
		{
			pbyBuf[3] = 0x0A;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVERIGHTDOWN:
		{
			pbyBuf[3] = 0x12;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed * NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_MOVESTOP:
		{
		    OsApi_TimerSet( g_hGetHvAngleTimer, 600, NvrFixDevGetHvAngleTimerCB, NULL);//停止600ms后获取坐标
		}
		break;
        case NVR_PTZCTRL_TYPE_ZOOMTELE:
        {
            pbyBuf[3] = 0x20;
            pbyBuf[4] = tNvrPtzCmdInfo.wIspSpeed;
            g_byPtzPresetId = 0;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_ZOOMWIDE:
        {
            pbyBuf[3] = 0x40;
            pbyBuf[4] = tNvrPtzCmdInfo.wIspSpeed;
            g_byPtzPresetId = 0;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_FOCUSFAR:
        {
            pbyBuf[3] = 0x80;
            pbyBuf[4] = tNvrPtzCmdInfo.wIspSpeed;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_FOCUSNEAR:
        {
            pbyBuf[2] = 0x01;
            pbyBuf[4] = tNvrPtzCmdInfo.wIspSpeed;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_FOCUSAUTO:
        {
            pbyBuf[2] = 0xA1;
            pbyBuf[3] = 0x00;
        }
        break;
        
        
        case NVR_PTZCTRL_TYPE_IRIS_PLUS:
        {
            pbyBuf[2] = 0x02;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_IRIS_MINUS:
        {
            pbyBuf[2] = 0x04;
        }
        break;
        
        case NVR_PTZCTRL_TYPE_IRIS_AUTO:
        {
            pbyBuf[2] = 0xB2;            
        }
        break;
        case NVR_PTZCTRL_TYPE_IRIS_STOP:
        case NVR_PTZCTRL_TYPE_FOCUSSTOP:
        case NVR_PTZCTRL_TYPE_ZOOMSTOP:
        case NVR_PTZCTRL_TYPE_HORIZONSCAN_STOP:
        case NVR_PTZCTRL_TYPE_PATH_CRUISE_STOP:
		    OsApi_TimerSet( g_hGetHvAngleTimer, 600, NvrFixDevGetHvAngleTimerCB, NULL);//停止600ms后获取坐标
            break;

		case NVR_PTZCTRL_TYPE_GOTOPOINT:
		case NVR_PTZCTRL_TYPE_ZOOMPART:
		case NVR_PTZCTRL_TYPE_ZOOMWHOLE:
		{
			pbyBuf[0] = 0x02;
			if(NVR_PTZCTRL_TYPE_ZOOMWHOLE == tNvrPtzCmdInfo.eCtrlType)
				pbyBuf[2] = 137;
			else
				pbyBuf[2] = 136;
			///<正常框选缩放功能，此处四个值赋给pbyBuf后，因为pbyBuf是u8类型，所以传过来的框信息如果是基于10000*10000的，会溢出，后面具体计算框选pt角度时不用此处转换的值（框信息基于255*255画布，且画布中心为原点，x/y坐标为框中心点坐标），只有标准机芯云台类型时才用。从NvrPtzDevPtzCtrl传过来的框信息原点为画布左上角，x/y坐标为框中心点坐标
			pbyBuf[3] = tNvrPtzCmdInfo.wXposition < tNvrPtzCmdInfo.wWinWide/2 ? (tNvrPtzCmdInfo.wWinWide/2+tNvrPtzCmdInfo.wXposition)*255/tNvrPtzCmdInfo.wWinWide : (tNvrPtzCmdInfo.wXposition-tNvrPtzCmdInfo.wWinWide/2)*255/tNvrPtzCmdInfo.wWinWide;
			pbyBuf[4] = tNvrPtzCmdInfo.wYposition < tNvrPtzCmdInfo.wWinHeight/2 ? (tNvrPtzCmdInfo.wWinHeight/2-tNvrPtzCmdInfo.wYposition)*255/tNvrPtzCmdInfo.wWinHeight : ((tNvrPtzCmdInfo.wWinHeight-tNvrPtzCmdInfo.wYposition)+tNvrPtzCmdInfo.wWinHeight/2)*255/tNvrPtzCmdInfo.wWinHeight;
			pbyBuf[5] = tNvrPtzCmdInfo.wWide * 255/tNvrPtzCmdInfo.wWinWide;
			pbyBuf[6] = tNvrPtzCmdInfo.wHeight * 255/tNvrPtzCmdInfo.wWinHeight;
			byCmdLen = 10;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_PRESET_SAVE:
		{
			pbyBuf[2] = (tNvrPtzCmdInfo.wNumber + 1) >> 8;
			pbyBuf[3] = 0x03;
			pbyBuf[5] = tNvrPtzCmdInfo.wNumber + 1;
			if(tNvrPtzCmdInfo.wNumber + 1 == g_tPtzCapInfo.tPresetNum.dwMaxValue)
			{
				pbyBuf[2] = 0;
				pbyBuf[5] = 0;
			}
			g_byPtzPresetId = tNvrPtzCmdInfo.wNumber + 1;
			//IpcDevSetCurrentPresetPlanId(g_byPtzPresetId);
		}
		break;

		case NVR_PTZCTRL_TYPE_PRESET_LOAD:
		{
			if(g_dwPreSetSpd != 40)
			{
				pbyBuf[4] = g_dwPreSetSpd;
			}
			pbyBuf[2] = (tNvrPtzCmdInfo.wNumber + 1) >> 8;
			pbyBuf[3] = 0x07;
			pbyBuf[5] = tNvrPtzCmdInfo.wNumber + 1;
			if(tNvrPtzCmdInfo.wNumber + 1 == g_tPtzCapInfo.tPresetNum.dwMaxValue)
			{
				pbyBuf[2] = 0;
				pbyBuf[5] = 0;
			}
			//加载预置位两s后将预置位id设置成当前id，预估位置，仅用作卡口
			//OsApi_TimerSet(g_hSetPresetIdTimer, 2000, IpcDevSetCurrentPresetIdTimerCB, &pbyBuf[5]);
			//g_byPtzPresetId = tNvrPtzCmdInfo.byNumber + 1;
			//IpcDevSetCurrentPresetPlanId(tNvrPtzCmdInfo.byNumber+1);
		}
		break;

		case NVR_PTZCTRL_TYPE_PRESET_DEL:
		{
			pbyBuf[2] = (tNvrPtzCmdInfo.wNumber + 1) >> 8;
			pbyBuf[3] = 0x05;
			pbyBuf[5] = tNvrPtzCmdInfo.wNumber + 1;
			if(tNvrPtzCmdInfo.wNumber + 1 == g_tPtzCapInfo.tPresetNum.dwMaxValue)
			{
				pbyBuf[2] = 0;
				pbyBuf[5] = 0;
			}
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_HORIZONTAL_TURN:
		{
			pbyBuf[3] = 0x01;
			pbyBuf[5] = 0x01;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TURNTO_MACHINEZERO:
		{
			pbyBuf[3] = 0x01;
			pbyBuf[5] = 0x02;
			g_byPtzPresetId = 0;

		}
		break;

		case NVR_PTZCTRL_LIMIT_HORIZONALLEFT:		//设置水平左限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x03;
		}
		break;

		case NVR_PTZCTRL_LIMIT_HORIZONALRIGHT:		//设置水平右限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x04;
		}
		break;

		case NVR_PTZCTRL_LIMIT_VERTICALUP:		//设置垂直上限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x06;
		}
		break;

		case NVR_PTZCTRL_LIMIT_VERTICALDOWN:		//设置垂直下限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x09;
		}
		break;

		case NVR_PTZCTRL_LIMIT_HORIZONAL_REMOVE:         // 清除水平限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x0e;
		}
		break;

		case NVR_PTZCTRL_LIMIT_VERTICAL_REMOVE:          // 清除垂直限位
		{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x0f;
		}
		break;

		case NVR_PTZCTRL_VERTICA_RANGE:          // 设置垂直角度范围
		{
			if(NVR_CAP_SUPPORT == g_tPtzCap.byVerticaRangeSupport)
			{
				pbyBuf[3] = 0x75;
				pbyBuf[5] = tNvrPtzCmdInfo.dwRes;
			}
			
		}
		break;

		case NVR_PTZCTRL_MANUALLIMITSWITCH_SET:		//手动限位(记录开关状态)
		{
			pbyBuf[3] = 0x2d;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
			else
			{
				pbyBuf[5] = 0x00;
			}
		}
		break;

		case NVR_PTZCTRL_SCANLIMITSWITCH_SET:		//扫描限位(记录开关状态)
		{
			
			pbyBuf[3] = 0x2f;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
			else
			{
				pbyBuf[5] = 0x00;
			}
		}
		break;

		case NVR_PTZCTRL_AUTOFLIP_SET:				//自动翻转(记录开关状态)
		{
			pbyBuf[3] = 0x25;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				//90度与180度球自动翻转指令
				if (9000 == g_tPtzCapInfo.tTAngleRange.dwMaxValue && NVR_CAP_PTZ_TYPE_CHENAN != g_tPtzCap.byPtzType)
				{
					pbyBuf[5] = 0x02;
				}
				else
				{
					pbyBuf[5] = 0x01;
				}
			}
			else
			{
				pbyBuf[5] = 0x00;
			}

		}
		break;

		case NVR_PTZCTRL_DEPTHRATESPEED_SET:		//景深比例(记录开关状态)
		{
			pbyBuf[3] = 0x27;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
			else
			{
				pbyBuf[5] = 0x00;
			}
		}
		break;

		case NVR_PTZCTRL_TYPE_WIPER_OPEN:					//雨刷开启
		{
			pbyBuf[5] = 0x01;
			pbyBuf[3] = 0x09;
			g_bWiperStat = TRUE;
		}
		break;

		case NVR_PTZCTRL_TYPE_WIPER_CLOSE:					//雨刷关闭
		{
			pbyBuf[5] = 0x01;
			pbyBuf[3] = 0x0B;
			g_bWiperStat = FALSE;
		}
		break;

		case NVR_PTZCTRL_IRCTRL_OPEN:
		{
			pbyBuf[3] = 0x09;
			pbyBuf[5] = 0x02;
		}
		break;

		case NVR_PTZCTRL_IRCTRL_CLOSE:
		{
			pbyBuf[3] = 0x0B;
			pbyBuf[5] = 0x02;
		}
		break;

		case NVR_PTZCTRL_LASERCTRL_OPEN:
		{
			
			if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x03;
				pbyBuf[3] = 0x01;
				pbyBuf[4] = 0x00;
				
			 }
			 else
			 {
				//佶达德激光器指令(通过云台单片机透传)
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x01;
				pbyBuf[4] = 0x01;

			}
		}
		break;

		case NVR_PTZCTRL_LASERCTRL_CLOSE:
		{
			if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x03;
				pbyBuf[3] = 0x00;
				pbyBuf[4] = 0x00;
			 }
			 else
			 {
				//佶达德激光器指令(通过云台单片机透传)
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x01;
				pbyBuf[4] = 0x00;

			 }
		}
		break;

		case NVR_PTZCTRL_LASERCTRL_RESET:
		{
			if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x02;
				pbyBuf[3] = 0x00;
				pbyBuf[4] = 0x00;
			 }
			 else
			 {
				//佶达德激光器指令(通过云台单片机透传)
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x06;
				pbyBuf[4] = 0x00;
			 }
		}
		break;
		case NVR_PTZCTRL_LASERCTRL_ANGLE:
		{
			 if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x08;
				pbyBuf[3] = (u8)tNvrPtzCmdInfo.wXposition;
				pbyBuf[4] = (u8)tNvrPtzCmdInfo.wYposition;
			 }
			 else
			 {
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x05;
				pbyBuf[4] = (u8)tNvrPtzCmdInfo.wXposition;
				pbyBuf[5] = (u8)tNvrPtzCmdInfo.wYposition;
			 }
			
		}
		break;

		case NVR_PTZCTRL_PTZ_LASER_ANGLE_INCRE:
		{
			 if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x06;
				pbyBuf[3] = 0x00;
				pbyBuf[4] = 0x0a; //每次增大或减小的角度为0.1度左右
			 }
			 else
			 {
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x04;
				pbyBuf[4] = 0x01;
				pbyBuf[5] = 0x10; //每次增大或减小步数为16步，对应角度0.1左右
			 }
			
		}
		break;
		case NVR_PTZCTRL_PTZ_LASER_ANGLE_DECRE:
		{
			 if(NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType)
			 {
				byCmdLen = 6;
				pbyBuf[0] = 0xff;
				pbyBuf[1] = 0x06;
				pbyBuf[2] = 0x07;
				pbyBuf[3] = 0x00;
				pbyBuf[4] = 0x0a; //每次增大或减小的角度为0.1度左右
			 }
			 else
			 {
				pbyBuf[2] = 0x01;
				pbyBuf[3] = 0x04;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0x10; //每次增大或减小步数为16步，对应角度0.1左右
			 }
			
		}
		break;
		
		case NVR_PTZCTRL_DEFROST_SET:				//除霜(记录开关状态)
		{
			pbyBuf[5] = 0x00;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x09;
			}
			else
			{
				pbyBuf[3] = 0x0B;
			}
		}
		break;
		case NVR_PTZCTRL_DEMIST_SET:				//除雾(记录开关状态)
		{
			pbyBuf[5] = 0x0c;

			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x09;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x0B;
			}
		}
		break;
		
		/*上电启动模式设置*/
		case NVR_PTZCTRL_POWERON_MODE_SET:
		{
			pbyBuf[3] = 0x8b;	
			///<插电上电启动
			if (NVR_PTZCTRL_MODE_POWERON_PLUGIN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			///<按钮上电启动
			else if(NVR_PTZCTRL_MODE_POWERON_BUTTON == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		/*断电关机模式设置*/
		case NVR_PTZCTRL_POWEROFF_MODE_SET:
		{
			pbyBuf[3] = 0x8f;		
			///<拔电关机
			if (NVR_PTZCTRL_MODE_POWEROFF_PLUGIN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			///<按钮关机
			else if(NVR_PTZCTRL_MODE_POWEROFF_BUTTON == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		/*LED指示灯总开关设置*/
		case NVR_PTZCTRL_LEDMAINSWITCH:
		{
			pbyBuf[3] = 0x9F;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		/*外置扩展WIFI开关设置*/
		case NVR_PTZCTRL_EXT_WIFI_SWITCH:
		{
			pbyBuf[3] = 0x8d;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		case NVR_PTZCTRL_REPORT_PT_REAL_TIME:
		{
			pbyBuf[3] = 0x99;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		case NVR_PTZCTRL_PTMOVE_STATE_REPORT:
		{
			pbyBuf[3] = 0x11;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;

		case NVR_PTZCTRL_VIRTUALZERO_SET:			//虚拟零位设置
		{
			pbyBuf[3] = 0x31;
			pbyBuf[5] = 0x01;
		}break;
		case NVR_PTZCTRL_VIRTUALZERO_REMOVE:		//虚拟零位清除
		{
			pbyBuf[3] = 0x33;
			pbyBuf[5] = 0x01;
		}
		break;

		case NVR_PTZCTRL_RESTORE_FACTORY:	//ptz相关内容恢复出厂设置
		{
			pbyBuf[3] = 0x35;
			pbyBuf[5] = 0x01;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_TYPE_RESET:					//云台复位
		{
			pbyBuf[3] = 0x35;
			pbyBuf[5] = 0x00;
			g_byPtzPresetId = 0;
			OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
		}
		break;

        case NVR_PTZCTRL_PTZ_RESTORE:               //云台单片机恢复出厂(清除预置位等信息)
        {
            pbyBuf[3] = 0x35;
			pbyBuf[5] = 0x01;
			g_byPtzPresetId = 0;
        }
        break;

		case NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET:			//设置水平扫描速度（影响自动扫描速度）bySpeed(1-40)
		{
			pbyBuf[3] = 0x2b;
			pbyBuf[5] = tNvrPtzCmdInfo.wPanSpeed;
			if (0 == pbyBuf[5])		//速度设置为0时操作水平扫描云台复位
			{
				pbyBuf[5] = 1;
			}
		}
		break;

		case NVR_PTZCTRL_PANPOSION_SET:			//设置水平位置
		{
			if(g_tPtzCap.byPtzPAngleTransformate == NVR_CAP_SUPPORT)
			{
				if(!tNvrPtzCmdInfo.bTaskTrg)
				{
					if(tNvrPtzCmdInfo.wXposition >= g_tPtzCapInfo.tStrucPAngleRange.dwMaxValue)
					{
						tNvrPtzCmdInfo.wXposition = g_tPtzCapInfo.tStrucPAngleRange.dwMaxValue;
					}
				}
			}
			pbyBuf[3] = 0x4b;
			//维多云台，敏佳，晨安云台角度增大方向与科达云台相反，为功能统一此处转化
			if(NVR_CAP_PTZ_TYPE_MINJIA2 == g_tPtzCap.byPtzType)
			{
			    PRINTDBG("=======>x:%d,basicpos:%d",tNvrPtzCmdInfo.wXposition,g_tBasicPos.wBasicHPos);
			    tNvrPtzCmdInfo.wXposition = tNvrPtzCmdInfo.wXposition + g_tBasicPos.wBasicHPos;
				tNvrPtzCmdInfo.wXposition = tNvrPtzCmdInfo.wXposition > 36000 ?tNvrPtzCmdInfo.wXposition - 36000:tNvrPtzCmdInfo.wXposition  ;
                tNvrPtzCmdInfo.wXposition = 36000-tNvrPtzCmdInfo.wXposition;
			}
			pbyBuf[4] = (tNvrPtzCmdInfo.wXposition) >> 8;
			pbyBuf[5] = (tNvrPtzCmdInfo.wXposition) & 0xFF;
			g_byPtzPresetId = 0;
			OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
		}
		break;

		case NVR_PTZCTRL_TILTPOSION_SET:		//设置俯仰位置
		{
			pbyBuf[3] = 0x4d;
			pbyBuf[4] = (tNvrPtzCmdInfo.wYposition) >> 8;
			pbyBuf[5] = (tNvrPtzCmdInfo.wYposition) & 0xFF;
			g_byPtzPresetId = 0;
			OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
		}
		break;

		case NVR_PTZCTRL_PTPOSION_SET:			//同时设置水平俯仰位置
		{
			//维多云台，敏佳，晨安云台角度增大方向与科达云台相反，为功能统一此处转化
			if(NVR_CAP_PTZ_TYPE_MINJIA == g_tPtzCap.byPtzType )
			{
				tNvrPtzCmdInfo.wXposition = tNvrPtzCmdInfo.wXposition == 0 ? 0 : 36000 - tNvrPtzCmdInfo.wXposition;
			}
			// 同pelco-d协议不同，keda云台pelco-k协议(以0xAA开头)使用的地址码固定是1，不会使用用户设置的地址码
			pbyBuf[0] = 0xAA;
			pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x03;
			pbyBuf[3] = 0x00;
			if(NVR_CAP_SUPPORT == g_tPtzCap.byLocSpeedSupport)
			{
				pbyBuf[3] = tNvrPtzCmdInfo.dwRes;//复用指令，当值不为零时候，新指令用于实现根据景深比例调整点击缩放定位速度，指令采用pelco-k协议
			} 
			pbyBuf[4] = (tNvrPtzCmdInfo.wXposition) >> 8;
			pbyBuf[5] = (tNvrPtzCmdInfo.wXposition) & 0xFF;
			pbyBuf[6] = (tNvrPtzCmdInfo.wYposition) >> 8;
			pbyBuf[7] = (tNvrPtzCmdInfo.wYposition) & 0xFF;
			byCmdLen = 9;
			g_byPtzPresetId = 0;

		}
		break;

		case NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT:			//水平扫描
		{
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x07;
			}
			else
			{
				pbyBuf[3] = 0x00;
				pbyBuf[5] = 0x00;
			}
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_SAVE_LONGRUNTEST_PRE:
		{
			pbyBuf[3] = 0x01;
			pbyBuf[5] = 0x08;
		}
		break;

		case NVR_PTZCTRL_VERTICAL_SCAN:			//垂直扫描
		{
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x0a;
			}
			else
			{
				pbyBuf[3] = 0x00;
				pbyBuf[5] = 0x00;
			}
		
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_FRAME_SCAN:			//帧扫描(每隔3S向左转90度)
		{
			// 同pelco-d协议不同，keda云台pelco-k协议(以0xAA开头)使用的地址码固定是1，不会使用用户设置的地址码
			pbyBuf[0] = 0xAA;
			pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x02;
			pbyBuf[3] = 0x04;
			pbyBuf[4] = 0x1E;					//30度每秒
			pbyBuf[5] = 0x00;
			pbyBuf[6] = 9000 >> 8;
			pbyBuf[7] = 9000 & 0xFF;
			byCmdLen = 9;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_RAND_SCAN:				//随机扫描
		{
			// 同pelco-d协议不同，keda云台pelco-k协议(以0xAA开头)使用的地址码固定是1，不会使用用户设置的地址码
			pbyBuf[0] = 0xAA;
			pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x02;
			pbyBuf[3] = tNvrPtzCmdInfo.eMode;
			pbyBuf[4] = 0x1E;					//30度每秒
			pbyBuf[5] = 0x00;
			pbyBuf[6] = tNvrPtzCmdInfo.wXposition >> 8;
			pbyBuf[7] = tNvrPtzCmdInfo.wXposition & 0xFF;
			byCmdLen = 9;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_FULLVIEW_SCAN:			//全景扫描
		{
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x01;
				pbyBuf[5] = 0x0c;
			}
			else							//停止扫描
			{
				pbyBuf[3] = 0x00;
				pbyBuf[5] = 0x00;
			}
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_REC:		//花样扫描记录
		{
			pbyBuf[3] = 0xB5;
			pbyBuf[4] = tNvrPtzCmdInfo.wNumber; 	//扫描路径0~3
			pbyBuf[5] = 0x00;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_STOPREC:	//花样扫描停止记录
		{
			pbyBuf[3] = 0xB5;
			pbyBuf[4] = tNvrPtzCmdInfo.wNumber;
			pbyBuf[5] = 0x01;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_PREVIEW:	//花样扫描预览
		{
			pbyBuf[3] = 0xB5;
			pbyBuf[4] = tNvrPtzCmdInfo.wNumber;
			pbyBuf[5] = 0x03;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_STOPPREVIEW:	//花样扫描停止预览
		{
			pbyBuf[3] = 0xB5;
			pbyBuf[4] = tNvrPtzCmdInfo.wNumber;
			pbyBuf[5] = 0x04;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_DELETE:	//花样扫描删除路径
		{
			pbyBuf[3] = 0xB5;
			pbyBuf[4] = tNvrPtzCmdInfo.wNumber;
			pbyBuf[5] = 0x02;
		}
		break;

		case NVR_PTZCTRL_SYNCSCAN_EXEC:			//花样扫描预览执行时发送的串口指令
		{
			// 同pelco-d协议不同，keda云台pelco-k协议(以0xAA开头)使用的地址码固定是1，不会使用用户设置的地址码
			pbyBuf[0] = 0xAA;
			pbyBuf[1] = 0x01;
			pbyBuf[2] = 0x01;
			///<TODO， 此处只是复用eMode值，后面eMode改成u8之后，需要在修改下，此处eMode值需要根据花样扫描要执行的是往什么方向转动赋不同的值，待处理
			pbyBuf[3] = tNvrPtzCmdInfo.eMode;
			pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed;
			pbyBuf[5] = tNvrPtzCmdInfo.wXposition & 0xFF;		//圈数
			pbyBuf[6] = tNvrPtzCmdInfo.wYposition >> 8;
			pbyBuf[7] = tNvrPtzCmdInfo.wYposition & 0xFF;
			byCmdLen = 9;

		}
		break;

		case NVR_PTZCTRL_ZOOMINFO_SEND:
		{
			if(NVR_CAP_PTZ_TYPE_WEIDUO == g_tPtzCap.byPtzType)
			{
				//当前倍率，不是zoom位置
				pbyBuf[2] = 0x00;
				pbyBuf[3] = 0x63;
				pbyBuf[4] = ((tNvrPtzCmdInfo.dwRes*100) >> 8) & 0xff;
				pbyBuf[5] = (tNvrPtzCmdInfo.dwRes*100) & 0xff;
			}
			else
			{
				pbyBuf[3] = 0x37;
				pbyBuf[4] = (tNvrPtzCmdInfo.dwRes >> 8) & 0xff;
				pbyBuf[5] = tNvrPtzCmdInfo.dwRes & 0xff;
			}
		}
		break;

		case NVR_PTZCTRL_SET_PINOUT_ALARM:
		{
			//枪机并口扩展卡与球机设置并口告警输出指令不相同	
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[3] = 0x09;
			}
			else
			{
				pbyBuf[3] = 0x0B;
			}

			pbyBuf[5] = 0x03 + tNvrPtzCmdInfo.wNumber;
		}
		break;

		case NVR_PTZCTRL_SOFTVER_QUERY:
		{
			pbyBuf[3] = 0x73;
		}
		break;

		case NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY:
		{
			pbyBuf[2] = 0x10;
			pbyBuf[3] = 0x73;
		}
		break;

		case NVR_PTZCTRL_CAMERA_SOFTVER_QUERY:
		{
			pbyBuf[2] = 0x20;
			pbyBuf[3] = 0x73;
		}
		break;

        case NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY:
		{
			pbyBuf[2] = 0x30;
			pbyBuf[3] = 0x73;
		}
        break ;

		case NVR_PTZCTRL_ALARMINPUT_QUERY:
		{
			pbyBuf[3] = 0x0d;
		}
		break;

		case NVR_PTZCTRL_PANPOSION_QUERY:
		{
			pbyBuf[3] = 0x51;
		}
		break;

		case NVR_PTZCTRL_TILTPOSION_QUERY:
		{
			pbyBuf[3] = 0x53;
		}
		break;

		case NVR_PTZCTRL_PTPOSION_QUERY:
		{
			pbyBuf[3] = 0x55;
		}
		break;

		case NVR_PTZCTRL_LDR_QUERY:
		{
			pbyBuf[3] = 0x67;
		}
		break;

		case NVR_PTZCTRL_DAYNIGHTINFO_SEND:
		{
			///<TODO, 获取机芯当前实际日夜模式状态接口待修改，暂时注掉，pbyBuf[5]先写死为0x01(日模式)，后面再修改，待处理
			//IpcIspSetKeyParam(0,IPC_ISP_CMD_GET_DAYNIGHT_STATUS,&nCurDayNight);			//查询日夜模式
			
			pbyBuf[2] = 0x00;
			pbyBuf[3] = 0xa1;
			pbyBuf[5] = 0x01;
		}
		break;

		case NVR_PTZCTRL_TEST_PTZ_DEVIATION:// 云台误差测试(Kate测试)
		{
			pbyBuf[3] = 0x01;
			pbyBuf[5] = 0x0b;
			g_byPtzPresetId = 0;
		}
		break;

		case NVR_PTZCTRL_QUERY_PTZ_DEVIATION:// 查询云台误差测试结果
		{
			pbyBuf[3] = 0xa7;
		}
		break;

		case NVR_PTZCTRL_TEST_IRGROUPCTRL: // 红外灯组亮度控制(Kate测试)
		{
			pbyBuf[2] = 0x00;
			pbyBuf[3] = 0x39;
			pbyBuf[4] = tNvrPtzCmdInfo.dwRes >> 8;					// 红外灯通道号
			pbyBuf[5] = (tNvrPtzCmdInfo.dwRes & 0x000000ff);		// 红外灯亮度等级
		}
		break;

		//激光强度 对应 电流大小
		case NVR_PTZCTRL_LASER_INTENSITY:
		{
			pbyBuf[2] = 0x01;
			pbyBuf[3] = 0x03;
		    //0~100映射到 0~255
		    pbyBuf[4] = ((tNvrPtzCmdInfo.dwRes) * 254 / 100 + 1)&0xFF;
		}
		break;

		case NVR_PTZCTRL_LASER_INTENSITY_QUERY:
		{
			pbyBuf[2] = 0x02;
			pbyBuf[3] = 0x03;
		}
		break;

		case NVR_PTZCTRL_WIPER_RESET:
		{
			if(NVR_CAP_PTZ_TYPE_WEIDUO == g_tPtzCap.byPtzType)
			{
				pbyBuf[2] = 0x20;
				pbyBuf[3] = 0x82;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0x01;
			}
			else
			{
				pbyBuf[2] = 0x00;
				pbyBuf[3] = 0x0d;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0x01;
			}
		}
		break;
		
		case NVR_PTZCTRL_SET_PTZ_ADDRESS_ID:
		{
			pbyBuf[1] = 0x01; //keda云台，将地址码0x01作为广播地址，设置云台地址码指令中使用0x01广播地址码，可以保证新设置的地址码一定能设到云台
			pbyBuf[2] = 0x00;
			pbyBuf[3] = 0x6b;
			pbyBuf[4] = 0x00;
			pbyBuf[5] = tNvrPtzCmdInfo.dwRes;
		}
		break;
		
		case NVR_PTZCTRL_SET_MBLED_STATE:
		{
			pbyBuf[3] = 0x85;
			pbyBuf[5] = tNvrPtzCmdInfo.eMode;		
		}
		break;
		case NVR_PTZCTRL_STEPOUT_CORRECTION:
		{
			pbyBuf[3] = 0x8B;
			if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{	
				pbyBuf[4] = 0x00;
			}
			else
			{
				pbyBuf[4] = 0x01;
			}
		}
		break;
		case NVR_PTZCTRL_TRACK_UP:
		{
			pbyBuf[3] = 0x91;
            pbyBuf[4] = (tNvrPtzCmdInfo.wTilSpeed >> 8) & 0xff;  
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed  & 0xff;
		}
		break;
		case NVR_PTZCTRL_TRACK_DOWN:
		{
			pbyBuf[3] = 0x93;
            pbyBuf[4] = (tNvrPtzCmdInfo.wTilSpeed >> 8) & 0xff;	
			pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed  & 0xff;

		}
		break;
		case NVR_PTZCTRL_TRACK_LEFT:
		{
			pbyBuf[3] = 0x95;
            pbyBuf[4] = (tNvrPtzCmdInfo.wPanSpeed >> 8) & 0xff;	
			pbyBuf[5] = tNvrPtzCmdInfo.wPanSpeed  & 0xff;           
		}
		break;
		case NVR_PTZCTRL_TRACK_RIGHT:
		{
			pbyBuf[3] = 0x97;
            pbyBuf[4] = (tNvrPtzCmdInfo.wPanSpeed >> 8) & 0xff;	
			pbyBuf[5] = tNvrPtzCmdInfo.wPanSpeed  & 0xff;
		}
		break;
		case NVR_PTZCTRL_FAN_SCAN:
		{
			pbyBuf[3] = 0x9B;
			pbyBuf[4] = 0x00;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;
		case NVR_PTZCTRL_PTZ_MCU_HEATER:
		{
			pbyBuf[3] = 0x9D;
			pbyBuf[4] = 0x00;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x00;
			}
			else if (NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0x01;
			}
		}
		break;
		case NVR_PTZCTRL_SET_SHAKE_A_THR:
		{
			pbyBuf[3] = 0x89;
			///<TODO，此处复用eMode值，实际设置振动加速度阈值时eMode应该是一个阈值，u8的值，后面需要重新处理，待处理
			pbyBuf[5] = tNvrPtzCmdInfo.eMode;
		}
		break;
		case NVR_PTZCTRL_CAR_MODE_SET:
		{
			pbyBuf[3] = 0x19;
			if (NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[5] = 0;
			}
			else
			{
				pbyBuf[5] = 100;
			}
		}
		break;
		case NVR_PTZCTRL_SDI_FRAMERATE_CTRL:
		{
			pbyBuf[3] = 0xA3;
			pbyBuf[5] = tNvrPtzCmdInfo.eMode;
		}
		break;		
		case NVR_PTZCTRL_SET_MAGNETIC:
		{
			pbyBuf[3] = 0x13;
		}
		break;
		case NVR_PTZCTRL_SET_PTZ_RECOVERY:
		{
			pbyBuf[3] = 0x17;
            pbyBuf[5] = tNvrPtzCmdInfo.dwRes;//1 开启该功能 0 关闭该功能
		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_WIFI_STA_INFO:
		{
			
			pbyBuf[2] = 0x01;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}
			pbyBuf[5] = tNvrPtzCmdInfo.dwRes; ///信号强度范围0-4
		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_WIFI_AP_INFO:
		{
			pbyBuf[2] = 0x02;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}

		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_MBNET1_INFO:
		{
			pbyBuf[2] = 0x03;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 128+tNvrPtzCmdInfo.wNumber;///<number为当前制式 开启最高位为1
				pbyBuf[5] = tNvrPtzCmdInfo.dwRes;	///<deres标识信号强度
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = tNvrPtzCmdInfo.wNumber;//<number为当前制式 关闭时最高位为0
			}
		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_MBNET2_INFO:
		{
			pbyBuf[2] = 0x04;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 128+tNvrPtzCmdInfo.wNumber;///<number为当前制式 开启最高位为1
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = tNvrPtzCmdInfo.wNumber;//<number为当前制式 关闭时最高位为0
			}
			pbyBuf[5] = tNvrPtzCmdInfo.dwRes;	///<deres标识信号强度
		}
		break;
			
		case NVR_PTZCTRL_REPORT_PTZ_TF1_INFO:
		{
			pbyBuf[2] = 0x06;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 128+((tNvrPtzCmdInfo.dwRes>>8)&0xff);///<number为当前制式 开启最高位为1
				
				pbyBuf[5] = tNvrPtzCmdInfo.dwRes&0xff;	///<deres标识剩余容量
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] =0;
			}
		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_TF2_INFO:
		{
			pbyBuf[2] = 0x07;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 128+((tNvrPtzCmdInfo.dwRes>>8)&0xff);///<number为当前制式 开启最高位为1
				pbyBuf[5] = tNvrPtzCmdInfo.dwRes&0xff;	///<deres标识剩余容量
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] =0;
			}

		}
		break;	
		case NVR_PTZCTRL_REPORT_PTZ_BT_STATE:
		{
			pbyBuf[2] = 0x08;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}

		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_LOCATION_STATE:
		{
			pbyBuf[2] = 0x09;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}
			pbyBuf[5] = tNvrPtzCmdInfo.dwRes;///<定位状态

		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_PUBSEC_LINK_STATE:
		{
			pbyBuf[2] = 0x0a;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}

		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_TF_REC_STATE:
		{
			pbyBuf[2] = 0x0b;
			pbyBuf[3] = 0xa5;
			if(NVR_PTZCTRL_MODE_OPEN == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x01;
			}
			else if(NVR_PTZCTRL_MODE_CLOSE == tNvrPtzCmdInfo.eMode)
			{
				pbyBuf[4] = 0x00;
			}
		}
		break;
		case NVR_PTZCTRL_REPORT_PTZ_BAT_QUERY:
		{
			pbyBuf[2] = 0x00;
			pbyBuf[3] = 0x71;
		}
		break;

		default:
		PRINTERR("NvrPtzDevConvertSerialCode ________ unknow command (%d) _______\n", tNvrPtzCmdInfo.eCtrlType);

	}

	if (byCmdLen > 0)
	{
		u8 sum = 0;
		
		if(NVR_CAP_SUPPORT == g_tPtzCap.byLaserSupport && 
			NVR_LASER_TYPE_SWGD == g_tPtzCap.byLaserType && 
			(NVR_PTZCTRL_LASERCTRL_OPEN == tNvrPtzCmdInfo.eCtrlType || NVR_PTZCTRL_LASERCTRL_CLOSE == tNvrPtzCmdInfo.eCtrlType || NVR_PTZCTRL_LASERCTRL_RESET == tNvrPtzCmdInfo.eCtrlType || NVR_PTZCTRL_LASERCTRL_ANGLE == tNvrPtzCmdInfo.eCtrlType))
		 {		
			for (i = 1; i < byCmdLen - 1; i++)
			{
				sum +=pbyBuf[i];
			}

			pbyBuf[byCmdLen - 1] = sum - 1;
		 }
		 else if(tNvrPtzCmdInfo.eCtrlType == NVR_PTZCTRL_AQUILLA_SET_PTMOVE
		 	||tNvrPtzCmdInfo.eCtrlType == NVR_PTZCTRL_AQUILLA_SET_SPEED
		 	||tNvrPtzCmdInfo.eCtrlType == NVR_PTZCTRL_AQUILLA_SET_PTZAngle)
		 {
			
			for (i = 0; i < byCmdLen - 1; i++)
			{
				sum +=pbyBuf[i];
			}

			pbyBuf[byCmdLen - 1] = sum;
		 }
		 else
		 {
			pbyBuf[byCmdLen - 1] = NvrFixDevCalcCheckSum(pbyBuf, byCmdLen);
		}
	}

       }


    if(NVR_PTZPROTO_TYPE_SONY == tSerialParam.eProtoType)
    {
		byCmdLen = 9;

		pbyBuf[0] = 0x81;
		pbyBuf[1] = 0x01;
		pbyBuf[2] = 0x06;
		pbyBuf[3] = 0x01;
		pbyBuf[4] = tNvrPtzCmdInfo.wPanSpeed* NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
		pbyBuf[5] = tNvrPtzCmdInfo.wTilSpeed* NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
		pbyBuf[8] = 0xff;

		switch (tNvrPtzCmdInfo.eCtrlType)
		{
			case NVR_PTZCTRL_TYPE_MOVEUP:
			{
				pbyBuf[6] = 0x03;
				pbyBuf[7] = 0x01;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVEDOWN:
			{
				pbyBuf[6] = 0x03;
				pbyBuf[7] = 0x02;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVELEFT:
			{
				pbyBuf[6] = 0x01;
				pbyBuf[7] = 0x03;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVERIGHT:
			{
				pbyBuf[6] = 0x02;
				pbyBuf[7] = 0x03;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVELEFTUP:
			{
				pbyBuf[6] = 0x01;
				pbyBuf[7] = 0x01;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVELEFTDOWN:
			{
				pbyBuf[6] = 0x01;
				pbyBuf[7] = 0x02;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVERIGHTUP:
			{
				pbyBuf[6] = 0x02;
				pbyBuf[7] = 0x01;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVERIGHTDOWN:
			{
				pbyBuf[6] = 0x02;
				pbyBuf[7] = 0x02;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_MOVESTOP:
			{
				pbyBuf[6] = 0x03;
				pbyBuf[7] = 0x03;
                g_byPtzPresetId = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_ZOOMTELE:
			{
				tNvrPtzCmdInfo.wIspSpeed = tNvrPtzCmdInfo.wIspSpeed/20;
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x07;
				pbyBuf[4] = 0x20+(tNvrPtzCmdInfo.wIspSpeed&0xf);
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_ZOOMWIDE:
			{
				tNvrPtzCmdInfo.wIspSpeed = tNvrPtzCmdInfo.wIspSpeed/20;
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x07;
				pbyBuf[4] = 0x30+(tNvrPtzCmdInfo.wIspSpeed&0xf);
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_ZOOMSTOP:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x07;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_FOCUSFAR:
			{

				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x08;
				pbyBuf[4] = 0x02;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_FOCUSNEAR:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x08;
				pbyBuf[4] = 0x03;
				pbyBuf[5] = 0xff;

			}
			break;

			case NVR_PTZCTRL_TYPE_FOCUSSTOP:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x08;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_FOCUSAUTO:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x38;
				pbyBuf[4] = 0x02;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_IRIS_PLUS:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x0b;
				pbyBuf[4] = 0x02;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_IRIS_MINUS:
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x0b;
				pbyBuf[4] = 0x03;
				pbyBuf[5] = 0xff;
			}
			break;

			case NVR_PTZCTRL_TYPE_IRIS_STOP:			//sony云台没有光圈停止(步进式)
			{
				byCmdLen = 0;
			}
			break;

			case NVR_PTZCTRL_TYPE_IRIS_AUTO:			//设置为全自动曝光
			{
				byCmdLen = 6;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x39;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = 0xff;

			}
			break;


			case NVR_PTZCTRL_TYPE_PRESET_SAVE:
			{
 				byCmdLen = 7;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x3f;
				pbyBuf[4] = 0x01;
				pbyBuf[5] = tNvrPtzCmdInfo.wNumber+1;
				pbyBuf[6] = 0xff;
                g_byPtzPresetId = tNvrPtzCmdInfo.wNumber + 1;
 			}
			break;

			case NVR_PTZCTRL_TYPE_PRESET_LOAD:
			{
 				byCmdLen = 7;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x3f;
				pbyBuf[4] = 0x02;
				pbyBuf[5] = tNvrPtzCmdInfo.wNumber+1;
				pbyBuf[6] = 0xff;
                g_byPtzPresetId = tNvrPtzCmdInfo.wNumber + 1;
			}
			break;

			case NVR_PTZCTRL_TYPE_PRESET_DEL:
			{
			 	byCmdLen = 7;
				pbyBuf[0] = 0x81;
				pbyBuf[1] = 0x01;
				pbyBuf[2] = 0x04;
				pbyBuf[3] = 0x3f;
				pbyBuf[4] = 0x00;
				pbyBuf[5] = tNvrPtzCmdInfo.wNumber+1;
				pbyBuf[6] = 0xff;
                g_byPtzPresetId = tNvrPtzCmdInfo.wNumber + 1;
			}
			break;

			default:
			PRINTERR("[IPCDEV]IpcDevConvertSerialCode sony protocal error!\n");
			break;
		}
	}

	///<TODO，end

    ///< 云台操作进行烟火识别联动
    {
        if ( (NVR_PTZCTRL_TYPE_MOVEUP <= tNvrPtzCmdInfo.eCtrlType && NVR_PTZCTRL_TYPE_MOVERIGHTDOWN >= tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_RESET == tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_PRESET_LOAD== tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT == tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_PATH_CRUISE_START== tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_GOTOPOINT <= tNvrPtzCmdInfo.eCtrlType && NVR_PTZCTRL_TYPE_ZOOMWHOLE >= tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_PANPOSION_SET <= tNvrPtzCmdInfo.eCtrlType && NVR_PTZCTRL_PTPOSION_SET >= tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT == tNvrPtzCmdInfo.eCtrlType)
            || (NVR_PTZCTRL_TYPE_PATH_CRUISE_START== tNvrPtzCmdInfo.eCtrlType))
        {
            NvrFixDevSmokeFireTrigger(NVR_DEV_SMOKE_FIREE_PTZ_START, tNvrPtzCmdInfo.wChnId, g_byPtzPresetId);
        }
    }

	*pbyLen = byCmdLen;

	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevSendCmd(TNvrPtzCtrlInfo tCtrlInfo)
{
	u8 abyCmd[256];
	u32 byRealLen = 0;
	u32 byCmdLen = 0;
#ifdef _AX603A_
	///<其他平台镜头是通过单片机控制，爱芯平台695是直接通过驱动控制
	PRINTDBG("eCtrlType = %d\n",tCtrlInfo.eCtrlType);
    if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSSTOP)
    {
    	 g_eVidLensCmd =  LENS_CTRL_FOCUS_STOP;
		 OsApi_TimerStop(g_hFocusCtrlTimer);  	 
    }
	else if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSNEAR || tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSFAR)
	{
		if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSNEAR)
		{
			g_eVidLensCmd = LENS_CTRL_FOCUS_NEAR;
		}
		if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSFAR)
		{
			g_eVidLensCmd = LENS_CTRL_FOCUS_FAR;
		}
		///<先触发一次
		NvrFixDevSetFocusTimerCB(g_hFocusCtrlTimer, NULL);
	}

    MAKE_COMPILER_HAPPY(abyCmd);
    MAKE_COMPILER_HAPPY(byRealLen);
    MAKE_COMPILER_HAPPY(byCmdLen);
#else
	mzero(abyCmd);
	abyCmd[0] = 1;
	NvrFixDevConvertSerialCode(tCtrlInfo, abyCmd, &byCmdLen);
	//printf("point x:%d,y:%d,w:%d,h:%d\n",abyCmd[3],abyCmd[4],abyCmd[5],abyCmd[6]);
	//先执行一次，有可能冲掉，但是速度快
	//NvrFixDevWriteSerial(g_byPtzCtrlSerialId,abyCmd, byCmdLen, &byRealLen);

    //有保障，可能延迟
    if(tCtrlInfo.eCtrlType==NVR_PTZCTRL_TYPE_MOVESTOP || tCtrlInfo.eCtrlType==NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY  || tCtrlInfo.eCtrlType==NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY)
    {
        NvrFixHistoryCmdInsert(g_byPtzCtrlSerialId,abyCmd,byCmdLen,FALSE,tCtrlInfo.eCtrlType);
    }
	NrvFixDevWaitPTZQueryOverBlock();
	NvrFixDevWriteSerial(g_byPtzCtrlSerialId,abyCmd, byCmdLen, &byRealLen);
    NrvFixDevWaitPTZQueryOverUnBlock();


#endif

    MAKE_COMPILER_HAPPY(NvrFixDevSetFocusTimerCB);
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevSendLasersCmd(TNvrPtzCtrlInfo tCtrlInfo)
{
	u8 abyCmd[256];
	u32 byRealLen = 0;
	u32 byCmdLen = 0;
	mzero(abyCmd);
	abyCmd[0] = 1;
	NvrFixDevConvertSerialCodeForLaser(tCtrlInfo, abyCmd, &byCmdLen);
	//printf("point x:%d,y:%d,w:%d,h:%d\n",abyCmd[3],abyCmd[4],abyCmd[5],abyCmd[6]);
	NvrFixDevWriteSerial(SERIAL_TYPE_PTZCTRL,abyCmd, byCmdLen, &byRealLen);
	return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevSetUptStat(u8 byUptStat)
{
	byPtzUptStat = byUptStat;

	return NVR_ERR__OK;
}

u8 NvrFixDevGetUptStat(void)
{
	return byPtzUptStat;
}

void* NvrFixDevPtzUpdateThread()
{
    s32 i;
	FILE *pfUpdate = NULL;
	u32 dwRealWrLen = 0;
	u16 wTimerNewRet = 0;
	u32 dwCount = 0;
	s32 nFileOffset = 0;
	BOOL32 bOffset = FALSE;
	u8 abyBuff[NVR_FIX_DEV_MAX_UPT_FRAME_LEN];
	TThreadInfoRecord tThreadInfo;

    u32 dwFileLength = 0;
    u8 byFileType = 0;
	u8 byPacknum = 0;						///<升级包序号
	u16 wCrc = 0;							///<crc校验码
	u16	wFrameLen = 0;						///<升级包每一帧长度
	BOOL32 bIsLastPacketWritten = FALSE;	///<标志最后一包数据是否发送完成
	BOOL32 bIsEOTWritten = FALSE;			///<标志传输结束消息是否发送到完成
	u8 byCmdd = 'd';
	u8 abyStartUpdateCmd[7] = {0xFF, 0x01, 0x00, 0x57, 0x00, 0x00, 0x58};

	mzero(tThreadInfo);
	memset(abyBuff, 0, sizeof(abyBuff));

	//添加线程信息
	tThreadInfo.m_dwThreadId = getpid();
	mcopy(tThreadInfo.m_strThreadName, "NvrFixDevPtzUpdateThread");
	OsApi_AddThreadInfo( &tThreadInfo );

	//设置线程为分离状态，防止内存泄露
	pthread_detach(pthread_self());

	PRINTVIP("Thread %d created!\n", getpid());

#ifndef WIN32
	prctl(PR_SET_NAME, "NvrPtzDevPtzUpdateThread", 0, 0, 0);
#endif

	//先校验文件，再启动升级
	pfUpdate = fopen(NVR_FIX_DEV_UPTPTZ_PATH, "rb");
	if(NULL == pfUpdate)
	{
		PRINTDBG("Open update file failed\n");

		NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);
		OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
		OsApi_TaskExit();

		///<回复状态给web
		g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
		g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
		remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
		g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);
		
		return NULL;
	}
	PRINTDBG("Open update file success\n");

	abyStartUpdateCmd[2] = 0x00;
	abyStartUpdateCmd[6] = 0x58;

    byFileType  =g_tNvrFixDevUpgradeParam.byUpdateType;
    dwFileLength = g_tNvrFixDevUpgradeParam.dwPkgTotalSize;
    PRINTDBG("dwFileLength: %d,filetype:%d\n",dwFileLength,byFileType);
    dwFileLength = dwFileLength/128;

	//fixgun_yzd
	if(byFileType == IPC_DEV_PT_FIX_MAINBOARD_PKG)
	{
		
		//01 DD
		abyStartUpdateCmd[2] = 0x10;
		abyStartUpdateCmd[4] = (dwFileLength >>8) & 0xFF;
		abyStartUpdateCmd[5] = dwFileLength & 0xFF;
		abyStartUpdateCmd[6] = 0;
		for(i=1;i<6;i++)
			abyStartUpdateCmd[6] = abyStartUpdateCmd[6] + abyStartUpdateCmd[i];
	}
	else if(byFileType == IPC_DEV_PT_FIX_CAMBOARD_PKG)
	{
		abyStartUpdateCmd[2] = 0x20;
		abyStartUpdateCmd[4] = (dwFileLength >>8) & 0xFF;
		abyStartUpdateCmd[5] = dwFileLength & 0xFF;
		abyStartUpdateCmd[6] = 0;
		for(i=1;i<6;i++)
			abyStartUpdateCmd[6] = abyStartUpdateCmd[6] + abyStartUpdateCmd[i];		
	}
    else if(byFileType == IPC_DEV_PT_FIX_FOCUSBOARD_PKG)
	{
		abyStartUpdateCmd[2] = 0x30;
		abyStartUpdateCmd[4] = (dwFileLength >>8) & 0xFF;
		abyStartUpdateCmd[5] = dwFileLength & 0xFF;
		abyStartUpdateCmd[6] = 0;
		for(i=1;i<6;i++)
			abyStartUpdateCmd[6] = abyStartUpdateCmd[6] + abyStartUpdateCmd[i];		
	}

	OsApi_Delay(500);

	g_bPtzUpdating = TRUE;		///<读串口线程进入升级状态
	g_bRecevieC = FALSE;
	wFrameLen = NVR_FIX_DEV_UPTFRAME_KEDA_LEN; ///<设置帧长度。

	NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

	//发送开始升级指令
	PRINTDBG("send start cmd\n");
	NvrFixDevWriteSerial(g_byPtzCtrlSerialId, abyStartUpdateCmd, sizeof(abyStartUpdateCmd), &dwRealWrLen);

	///<发送开始指令后，等待云台回复FD
	dwCount = 0;
	while(NVR_FIX_DEV_UPTSTAT_RCV_RSP != NvrFixDevGetUptStat() && NVR_FIX_DEV_UPT_WAIT_RSP_TIME > dwCount)
	{
		sleep(1);
		dwCount++;
	}
	///<云台回复超时
	if(NVR_FIX_DEV_UPT_WAIT_RSP_TIME <= dwCount)
	{
		PRINTERR("No response FD from mcu\n");

		NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);		
		PRINTERR("NvrPtzDevUpgradeSendPkgData, NvrPtzDevStartPtzUpdateTask fail\n");

		///<回复状态给web
		g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
		g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
		remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
		g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

		OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
		OsApi_TaskExit();
		return NULL;
	}
	OsApi_Delay(500);

	///<发送指令'd'
	PRINTDBG("Write 'd' to mcu\n");
	NvrFixDevWriteSerial(g_byPtzCtrlSerialId, &byCmdd, sizeof(byCmdd), &dwRealWrLen);

	//等待云台回复'C'
	dwCount = 0;
	while(NVR_FIX_DEV_UPTSTAT_RCV_C != NvrFixDevGetUptStat() && NVR_FIX_DEV_UPT_WAIT_C_TIME > dwCount)
	{
		sleep(1);
		dwCount++;
	}
	if(NVR_FIX_DEV_UPT_WAIT_C_TIME <= dwCount)
	{
		PRINTERR("No respond 'C' from mcu\n");

		NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);

		///<回复状态给web
		g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
		g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
		remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
		g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

		OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
		OsApi_TaskExit();
		return NULL;
	}

	byPacknum = 0;
	NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);

	if (NULL == g_hPtzUpdateTimer)
	{
		wTimerNewRet = OsApi_TimerNew( &g_hPtzUpdateTimer );
		if(0 != wTimerNewRet)
		{
			PRINTERR("create g_hPtzUpdateTimer failed(%d)\n", wTimerNewRet );

			NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);

			///<回复状态给web
			g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
			g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
			remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
			g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

			OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
			OsApi_TaskExit();
			return NULL;
		}
	}

	while(g_bPtzUpdating)
	{
		if(NVR_FIX_DEV_UPTSTAT_RCV_ACK == NvrFixDevGetUptStat())
		{

			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hPtzUpdateTimer);
			OsApi_TimerSet(g_hPtzUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevPtzUpdateTimerCB, NULL);

			if(bIsEOTWritten)		///<发送EOT消息后，升级成功
			{
				PRINTDBG("Update finished!\n" );

				NvrFixDevPtzUpdateQuit(pfUpdate, TRUE);
				
				///<回复状态给web
				g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_DONE;
				g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

                if(g_byPtzUpdateSuc != NVR_FIX_DEV_UPTPTZ_SUCCESS)
                {
                    PRINTDBG("ptz update done, get ver failed, start reboot\n");
                    NvrSysReboot(NULL);
                }

				OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
				OsApi_TaskExit();
				return NULL;
			}

			if(bIsLastPacketWritten)  ///<发送完最后一包数据后，发送EOT消息
			{
				NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_SEND_EOT);
				continue;
			}

			abyBuff[0] = 0x01;
			byPacknum++;
			abyBuff[1] = byPacknum;
			abyBuff[2] = ~abyBuff[1];
			dwCount = fread(&abyBuff[3], 1, wFrameLen, pfUpdate);

			if(wFrameLen == dwCount) 		///<正常数据发送
			{
				wCrc = calcrc(&abyBuff[3], wFrameLen);
				abyBuff[wFrameLen+3] = (wCrc>>8) & 0xFF;
				abyBuff[wFrameLen+4] = wCrc & 0xFF;

				PRINTDBG("write packet %d to mcu\n", abyBuff[1]);
				NvrFixDevWriteSerial2(g_byPtzCtrlSerialId, abyBuff, (wFrameLen+5), &dwCount);
			}
			else if(wFrameLen > dwCount && dwCount != 0) 	///<最后一包数据，不够一帧数据0x1A填充
			{

				memset(&abyBuff[3+dwCount], 0x1A, (wFrameLen-dwCount));
				wCrc = calcrc(&abyBuff[3], wFrameLen);
				abyBuff[wFrameLen+3] = (wCrc>>8) & 0xFF;
				abyBuff[wFrameLen+4] = wCrc & 0xFF;

				NvrFixDevWriteSerial2(g_byPtzCtrlSerialId, abyBuff, (wFrameLen+5), &dwCount);

				PRINTDBG("write last packet %d to mcu\n", abyBuff[1]);
				bIsLastPacketWritten = TRUE;
			}
			else		///<dwCount为0,刚好最后一包数据与帧长度相同
			{
				bIsLastPacketWritten = TRUE;
				OsApi_Delay(50);
				NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_RCV_NAK == NvrFixDevGetUptStat())		//数据有误，云台要求重发上一帧
		{
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hPtzUpdateTimer);
			OsApi_TimerSet(g_hPtzUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevPtzUpdateTimerCB, NULL);

			PRINTERR("EXCEPTION ____________ resend previous frame data ____________\n");

			if(bIsEOTWritten)		///<重发EOT标志
			{
				NvrFixDevWriteSerial2(g_byPtzCtrlSerialId, abyBuff, 1, &dwCount);
			}
			else					///<重发数据包
			{
				NvrFixDevWriteSerial2(g_byPtzCtrlSerialId, abyBuff, (wFrameLen+5), &dwCount);
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_RCV_CAN == NvrFixDevGetUptStat())	//发生重大错误，云台要求从第一帧开始重发
		{
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hPtzUpdateTimer);
			OsApi_TimerSet(g_hPtzUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevPtzUpdateTimerCB, NULL);

			PRINTERR("EXCEPTION ____________ resend all frame data ____________\n");

			///<从数据部分开始读
			nFileOffset = fseek(pfUpdate, 0, SEEK_SET);
			if (0 == nFileOffset)
			{
				bOffset = TRUE;
			}

			if(!bOffset)	///<偏移失败
			{
				PRINTDBG("can't reach the beginning pos of the file\n");

				NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);

				///<回复状态给web
				g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
				g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
				remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
				g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);
				
				OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
				OsApi_TaskExit();
				return NULL;
			}
			else
			{
				byPacknum = 0;
				NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);

				OsApi_Delay(50);
				continue;
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_SEND_EOT == NvrFixDevGetUptStat())		//发送EOT结束传输标志
		{
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hPtzUpdateTimer);
			OsApi_TimerSet(g_hPtzUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevPtzUpdateTimerCB, NULL);

			PRINTDBG("send EOT flag to mcu\n");

			abyBuff[0] = NVR_FIX_DEV_UPTMSG_EOT;
			NvrFixDevWriteSerial2(g_byPtzCtrlSerialId, abyBuff, 1, &dwCount);
			bIsEOTWritten = TRUE;
		}
		else
		{
			OsApi_Delay(50);
		}
	}

	NvrFixDevPtzUpdateQuit(pfUpdate, FALSE);
	
	///<回复状态给web
	g_tNvrFixDevUpgradeParam.tState.eState = APP_CLT_DEV_PTZ_UP_STATE_ERR;
	g_tNvrFixDevUpgradeParam.tState.eReason = APP_CLT_DEV_PTZ_UP_ERR_CHECK;
	remove(g_tNvrFixDevUpgradeParam.achUpgradeFilePath);
	g_tNvrFixDevUpgradeParam.pfPtzUpgradeStateReport(APP_CLT_PROTO_LCAM, g_tNvrFixDevUpgradeParam.dwDevID, g_tNvrFixDevUpgradeParam.dwOpeque, &g_tNvrFixDevUpgradeParam.tState);

	OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
	OsApi_TaskExit();

	return NULL;
}

NVRSTATUS NvrFixDevStartPtzUpdateTask()
{
	///<创建线程准备升级
	if((TASKHANDLE)NULL == g_hPtzUpdateTask)
	{

		g_hPtzUpdateTask = OsApi_TaskCreate((LINUXFUNC)NvrFixDevPtzUpdateThread, "NvrFixDevPtzUpdate", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL);

		if ((TASKHANDLE)NULL == g_hPtzUpdateTask)
		{
			PRINTERR("Create NvrPtzDevPtzUpdate Failed\n");
			return NVR_ERR__ERROR;
		}
	}

	return NVR_ERR__OK;
}

/************************************ 云台升级结束 ********************************/
void NvrFixDevLensUpgradeDateCB(TNvrSysDevUpgradeDateInfo *ptLensUpgradeDateInfo, ENvrDevUpgradeStateIn eUpgradeStateIn)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    FILE* pFile = NULL;
    u8 * pbyLensDataBuf = NULL;
    u16 wTimerNewRet = 0;
	TNvrCapSysInfo tSysCap;
	mzero(tSysCap);
	u32 u32WriteLen = 0;
	u32 u32SkipHeadLen = 0;
	TNvrCapLcam tCapLcam;
	mzero(tCapLcam);
	char achCommand[64];
	NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);

	if (NULL == g_hLensUpgradeTimer)
	{
		wTimerNewRet = OsApi_TimerNew( &g_hLensUpgradeTimer );
		if(0 != wTimerNewRet)
		{
		 	PRINTERR("create g_hLensUpgradeTimer failed(%d)\n", wTimerNewRet );
			g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
	 		g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_CHECK;
			return;
		}
	}

	///<第一次标记
	static u32 u32CbFirst = 1;
    if(NVR_DEV_UPGRADE_CHECK_SUCC == eUpgradeStateIn)
    {
    	u32CbFirst = 1;
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_SENDING;
		///<存在则删除
		if(0 == access(NVR_FIX_UPTLENS_PATH, 0))
    	{
			remove(NVR_FIX_UPTLENS_PATH);
		}
		//设置错误回调
		OsApi_TimerSet(g_hLensUpgradeTimer, 100*1000, NvrFixDevLensUpgradeTimerCB, NULL);
		g_dwFileCurSaveSize = 0;
		g_dwFileTotalSize = ptLensUpgradeDateInfo->dwPkgTotalSize;
        return;
    }
	if(u32CbFirst)
    {
    	eRet = NvrCapGetCapParam(NVR_CAP_ID_SYS, &tSysCap);
		if(NVR_ERR__OK != eRet)
		{
			g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
			g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_CHECK;
			PRINTERR("NvrLenUpgradeStart Get SysCap Failed !!\n");
			return ;
		}	
		u32CbFirst = 0;
		if (0 == tSysCap.tUpgradeLens.wPkgHeadSize)
		{
		    u32SkipHeadLen = strlen(tSysCap.tUpgradeLens.achPkgHead);
		}
		else
		{
		    u32SkipHeadLen = tSysCap.tUpgradeLens.wPkgHeadSize;
		}

		u32WriteLen = ptLensUpgradeDateInfo->dwDataBufSize - u32SkipHeadLen;
		///<去掉头部
		PRINTDBG("pkghead len = %d\n", u32SkipHeadLen);
		pbyLensDataBuf = (u8 *)malloc(u32WriteLen);
    	memcpy(pbyLensDataBuf, ptLensUpgradeDateInfo->pbyDataBuf + u32SkipHeadLen, u32WriteLen);
	}
	else
	{
		u32WriteLen = ptLensUpgradeDateInfo->dwDataBufSize;
    	pbyLensDataBuf = (u8 *)malloc(ptLensUpgradeDateInfo->dwDataBufSize);
    	memcpy(pbyLensDataBuf, ptLensUpgradeDateInfo->pbyDataBuf, ptLensUpgradeDateInfo->dwDataBufSize);
	}
	g_dwFileCurSaveSize = g_dwFileCurSaveSize + ptLensUpgradeDateInfo->dwDataBufSize;
    

    pFile = fopen(NVR_FIX_UPTLENS_PATH,"a+");
    if(pFile == NULL)
    {
        PRINTERR("fopen %s fail\n", NVR_FIX_UPTLENS_PATH);
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
        g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_UPLOAD;
        remove(NVR_FIX_UPTLENS_PATH);
        free(pbyLensDataBuf);
        return;
    }
    fwrite(pbyLensDataBuf,1,u32WriteLen,pFile);
    free(pbyLensDataBuf);
    fclose(pFile);
	if(g_dwFileCurSaveSize < g_dwFileTotalSize)
	{	
		PRINTDBG("lens pkg download curszie = %d, total size = %d\n", g_dwFileCurSaveSize, g_dwFileTotalSize);
		return;
	}
	OsApi_TimerStop(g_hLensUpgradeTimer);
	PRINTDBG("lens pkg download finish\n");
	
    g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_CHECKING;

    eRet = NvrFixDevStartLensUpdateTask();
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("NvrFixDevStartLensUpdateTask fail\n");
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
        g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_CHECK;
        remove(NVR_FIX_UPTLENS_PATH);

        return;
    }


   
    return;
}

NVRSTATUS NvrFixDevStartLensUpdateTask()
{
    TEdgeOsInterCapSysInfo tInterSysCap;

    mzero(tInterSysCap);
    NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &tInterSysCap);

    ///<创建线程准备升级
    if((TASKHANDLE)NULL == g_hLensUpdateTask)
    {
        if(tInterSysCap.tNvrMicroControllersInfo.byUse == TRUE)
        {
            g_hLensUpdateTask = OsApi_TaskCreate((LINUXFUNC)NvrFixDevLensXmodemUpdateThread, "NvrFixDevLensXmodemUpdateThread", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL);
        }
        else
        {
            g_hLensUpdateTask = OsApi_TaskCreate((LINUXFUNC)NvrFixDevLensUpdateThread, "NvrFixDevLensUpdateThread", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL);

            if ((TASKHANDLE)NULL == g_hLensUpdateTask)
            {
                PRINTERR("Create Failed\n");
                return NVR_ERR__ERROR;
            }
        }
    }

    return NVR_ERR__OK;
}

void* NvrFixDevLensUpdateThread()
{
    TThreadInfoRecord tThreadInfo;
    s32 dwRet  = 0;

    ///<添加线程信息
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "NvrFixDevLensUpdate");
    OsApi_AddThreadInfo( &tThreadInfo );

    ///<设置线程为分离状态，防止内存泄露
    pthread_detach(pthread_self());

    PRINTVIP("Thread %d created!\n", getpid());

#ifndef WIN32
    prctl(PR_SET_NAME, "NvrFixDevLensUpdateThread", 0, 0, 0);
#endif

    OsApi_TimerSet(g_hLensUpgradeTimer, 100*1000, NvrFixDevLensUpgradeTimerCB, NULL);
#if (!defined _QCOM_) && (!defined _HIS3559A_)
    dwRet = SysProgramMcu(MCU_TYPE_LPC17XX,"/dev/ttyAMA5",NVR_FIX_UPTLENS_PATH);
#endif
    PRINTVIP("SysProgramMcu Done! Ret %d!\n", dwRet);
    if(dwRet != 0)
    {
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
        g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_CHECK;
        PRINTERR("Lens Update Failed!\n");
    }
    else
    {
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_DONE;
    }
    OsApi_TimerStop(g_hLensUpgradeTimer);

    OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
    OsApi_TaskExit();

    return 0;

}

NVRSTATUS NvrFixDevSetLensUptStat(u8 byUptStat)
{
    byLensUptStat = byUptStat;

    return NVR_ERR__OK;
}

u8 NvrFixDevGetLensUptStat(void)
{
    return byLensUptStat;
}


s32 NvrFixDevLensUpdateTimerCB(HTIMERHANDLE dwTimerId, void* param)
{

    g_bLensUpdating = FALSE;
    PRINTVIP("Lens update time out!\n");

    return 0;
}

void NvrFixDevLensXmodemUpdateQuit(FILE *pFile, BOOL32 bUpdateSuccess)
{
    g_bLensUpdating = FALSE;
    g_hLensUpdateTask = (TASKHANDLE)NULL;

    OsApi_TimerStop(g_hLensUpgradeTimer);

    if (NULL != pFile)
    {
        fclose(pFile);
    }

    if (NULL != g_hLensUpdateTimer)
    {
        OsApi_TimerStop(g_hLensUpdateTimer);
        OsApi_TimerDelete(g_hLensUpdateTimer);
        g_hLensUpdateTimer = NULL;
        PRINTVIP("delete timer\n");
    }

    if (bUpdateSuccess)
    {
        //<升级成功，等待单片机重启
        sleep(NVR_FIX_DEV_PTZREBOOT_DELAY);
        NvrFixDevSetLensVer();
        g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_DONE;
    }
    else
    {
        if(g_byLensUpdateState == NVR_DEV_UPGRADE_STATE_ERR && g_byLensUpdateErr == NVR_DEV_UPGRADE_ERR_TIMEOUT)
        {
           PRINTVIP("Lens update timeout!!!\n");
        }
        else
        {   g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
            g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_CHECK;
            PRINTVIP("Lens update failed!!!\n");
        }
    }
    remove(NVR_FIX_UPTLENS_PATH);

    return;
}

void* NvrFixDevLensXmodemUpdateThread()
{
    FILE *pfUpdate = NULL;
    u32 wRealWrLen = 0;
    u16 wTimerNewRet = 0;
    u32 dwCount = 0;
    s32 nFileOffset = 0;
    BOOL32 bOffset = FALSE;
    u8 abyBuff[NVR_FIX_DEV_MAX_UPT_FRAME_LEN+20] = {0};
    TThreadInfoRecord tThreadInfo;
    u8 byPacknum = 0;                       ///<升级包序号
    u16 wFrameLen = 0;                      ///<升级包每一帧长度
    BOOL32 bIsLastPacketWritten = FALSE;    ///<标志最后一包数据是否发送完成
    BOOL32 bIsEOTWritten = FALSE;           ///<标志传输结束消息是否发送到完成    
    u8 abyStartUpdateCmd[7] = {0xFF, 0x01, 0x00, 0x57, 0x00, 0x00, 0x58};

    mzero(tThreadInfo);
    memset(abyBuff, 0, sizeof(abyBuff));

    ///<添加线程信息
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "NvrFixDevLensXmodemUpdate");
    OsApi_AddThreadInfo( &tThreadInfo );

    ///<设置线程为分离状态，防止内存泄露
    pthread_detach(pthread_self());

    PRINTVIP("Thread %d created!\n", getpid());

#ifndef WIN32
    prctl(PR_SET_NAME, "NvrFixDevLensXmodemUpdate", 0, 0, 0);
#endif

    OsApi_TimerSet(g_hLensUpgradeTimer, 200*1000, NvrFixDevLensUpgradeTimerCB, NULL);

    ///<先校验文件，再启动升级
    pfUpdate = fopen(NVR_FIX_UPTLENS_PATH, "rb");
    if(NULL == pfUpdate)
    {
        PRINTVIP("Open update file failed\n");

        NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);
        OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
        OsApi_TaskExit();
        return NULL;
    }
    fseek(pfUpdate, 0, SEEK_SET);
    PRINTVIP("Open update file success\n");


    g_bLensUpdating = TRUE;      ///<读串口线程进入升级状态
    g_bLensRecevieC = FALSE;
    byLensUptStartStat = NVR_FIX_DEV_UPTSTAT_IDLE;
    wFrameLen = NVR_FIX_DEV_UPTFRAME_KEDA_LEN; ///<设置帧长度。

    NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

    ///<确保加热膜，AF动作完成
    OsApi_Delay(200);

    ///<发送开始升级指令
    PRINTVIP("send start cmd\n");
    NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyStartUpdateCmd, sizeof(abyStartUpdateCmd), &wRealWrLen);

    ///<发送开始指令后，等待镜头回复FD
    dwCount = 0;
    while(NVR_FIX_DEV_UPTSTAT_RCV_RSP != byLensUptStartStat && NVR_FIX_DEV_UPT_WAIT_RSP_TIME > dwCount)
    {
        sleep(1);
        dwCount++;
    }

    ///<回复超时
    if(NVR_FIX_DEV_UPT_WAIT_RSP_TIME <= dwCount)
    {
        PRINTVIP("No response FD from mcu\n");

        NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);
        OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
        OsApi_TaskExit();
        return NULL;
    }

    ///<等待回复'C'
    dwCount = 0;
    while(NVR_FIX_DEV_UPTSTAT_RCV_C != NvrFixDevGetLensUptStat() && NVR_FIX_DEV_UPT_WAIT_C_TIME > dwCount)
    {
        sleep(1);
        dwCount++;
    }
    if(NVR_FIX_DEV_UPT_WAIT_C_TIME <= dwCount)
    {
        PRINTERR("No respond 'C' from mcu\n");

		NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);
		OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
		OsApi_TaskExit();
		return NULL;
	}

	byPacknum = 0;
	NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);

    if (NULL == g_hLensUpdateTimer)
    {
        wTimerNewRet = OsApi_TimerNew( &g_hLensUpdateTimer );
        if(0 != wTimerNewRet)
        {
            PRINTVIP("create g_hLensUpdateTimer failed(%d)\n", wTimerNewRet );
            NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);
            OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
            OsApi_TaskExit();
            return NULL;
        }
    }
    while(g_bLensUpdating)
	{
		if(NVR_FIX_DEV_UPTSTAT_RCV_ACK == NvrFixDevGetLensUptStat())
		{

			NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hLensUpdateTimer);
			OsApi_TimerSet(g_hLensUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevLensUpdateTimerCB, NULL);

			if(bIsEOTWritten)		///<发送EOT消息后，升级成功
			{
				PRINTVIP("Update finished!\n" );

				NvrFixDevLensXmodemUpdateQuit(pfUpdate, TRUE);
				OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
				OsApi_TaskExit();
				return NULL;
			}

			if(bIsLastPacketWritten)  ///<发送完最后一包数据后，发送EOT消息
			{
				NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_SEND_EOT);
				continue;
			}

			abyBuff[0] = 0x01;
			byPacknum++;
			abyBuff[1] = byPacknum;
			abyBuff[2] = ~abyBuff[1];
			dwCount = fread(&abyBuff[3], 1, wFrameLen, pfUpdate);

			if(wFrameLen == dwCount) 		///<正常数据发送
			{
				abyBuff[wFrameLen+3] = NvrFixDevCalcCheckSum(abyBuff+2, 130);

				PRINTVIP("write packet %d to mcu\n", abyBuff[1]);

				NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyBuff, (wFrameLen+4), &dwCount);

			}
			else if(wFrameLen > dwCount && dwCount != 0) 	///<最后一包数据，不够一帧数据0x1A填充
			{

				memset(&abyBuff[3+dwCount], 0x1A, (wFrameLen-dwCount));
                abyBuff[wFrameLen+3] = NvrFixDevCalcCheckSum(abyBuff+2, 130);

				NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyBuff, (wFrameLen+4), &dwCount);

				PRINTVIP("write last packet %d to mcu\n", abyBuff[1]);
				bIsLastPacketWritten = TRUE;
			}
			else		///<dwCount为0,刚好最后一包数据与帧长度相同
			{
				bIsLastPacketWritten = TRUE;
				OsApi_Delay(50);
				NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_RCV_NAK == NvrFixDevGetLensUptStat())		///<数据有误，要求重发上一帧
		{
			NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hLensUpdateTimer);
			OsApi_TimerSet(g_hLensUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevLensUpdateTimerCB, NULL);

			PRINTERR("EXCEPTION ____________ resend previous frame data ____________\n");

			if(bIsEOTWritten)		///<重发EOT标志
			{
				NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyBuff, 1, &dwCount);
			}
			else					///<重发数据包
			{
				NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyBuff, (wFrameLen+5), &dwCount);
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_RCV_CAN == NvrFixDevGetLensUptStat())	///<发生重大错误，从第一帧开始重发
		{
			NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hLensUpdateTimer);
			OsApi_TimerSet(g_hLensUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevLensUpdateTimerCB, NULL);

			PRINTERR("EXCEPTION ____________ resend all frame data ____________\n");

			///<从数据部分开始读
			nFileOffset = fseek(pfUpdate, 0, SEEK_SET);
			if (0 == nFileOffset)
			{
				bOffset = TRUE;
			}


			if(!bOffset)	///<偏移失败
			{
				PRINTVIP("can't reach the beginning pos of the file\n");

				NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);
				OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
				OsApi_TaskExit();
				return NULL;

			}
			else
			{
				byPacknum = 0;
				NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);

				OsApi_Delay(50);
				continue;
			}

		}
		else if(NVR_FIX_DEV_UPTSTAT_SEND_EOT == NvrFixDevGetLensUptStat())		///<发送EOT结束传输标志
		{
			NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_IDLE);

			OsApi_TimerStop(g_hLensUpdateTimer);
			OsApi_TimerSet(g_hLensUpdateTimer, NVR_FIX_DEV_UPT_TIMEOUT, NvrFixDevLensUpdateTimerCB, NULL);

			PRINTVIP("send EOT flag to mcu\n");

			abyBuff[0] = NVR_FIX_DEV_UPTMSG_EOT;
			NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyBuff, 1, &dwCount);
			bIsEOTWritten = TRUE;
		}
		else
		{
			OsApi_Delay(50);
		}
	}

    NvrFixDevLensXmodemUpdateQuit(pfUpdate, FALSE);

    OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
    OsApi_TaskExit();

    return NULL;
}

void NvrFixDevLensUpgradeStatusCB(TNvrLensUpgradeState *ptLensUpgradeState)
{
    ptLensUpgradeState->eState = g_byLensUpdateState;
    ptLensUpgradeState->eReason = g_byLensUpdateErr;

    PRINTVIP("LensUpdateState = %d ! LensUpdateErr = %d!\n", g_byLensUpdateState, g_byLensUpdateErr);
    return;
}


NVRSTATUS NvrFixDevSetLensVer()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u8 byGetLensVerCount = 0;
    s8 chBuffer[NVR_FIX_MAX_LENSVER_LEN] = "0";
    TEdgeOsInterCapSysInfo tInterSysCap;

    mzero(tInterSysCap);
    NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &tInterSysCap);

    if(tInterSysCap.tNvrMicroControllersInfo.byUse == TRUE)
    {
        if(g_bLensUpdating)
        {
            PRINTVIP("LensVer not get, updating \n");
            return NVR_ERR__OK;
        }
        else
        {
            g_bLensVerGeting = TRUE;
        }

        u8 abyCmdLensVer[7] = {0xFF, 0x01, 0x11, 0x00, 0x00, 0x00, 0x12};  ///<获取镜头版本号
        if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
        {
            //NVR_PTZCTRL_CAMERA_SOFTVER_QUERY
            abyCmdLensVer[0] = 0xff;
            abyCmdLensVer[1] = 0x01;
            abyCmdLensVer[2] = 0x20;
            abyCmdLensVer[3] = 0x73;
            abyCmdLensVer[4] = 0x00;
            abyCmdLensVer[5] = 0x00;
            abyCmdLensVer[6] = 0x94;
        }
        u32 wRealLen = 0;

        while(g_bLensVerGeting)
        {
            if(byGetLensVerCount > 5)
            {
                PRINTERR("NvrFixDevSetLensVer failed, get over 5 times\n");
                break;
            }
            NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyCmdLensVer, 7, &wRealLen);
            byGetLensVerCount++;
            OsApi_TaskDelay( 1000 );
            PRINTDBG("byGetLensVerCount : %u\n",byGetLensVerCount);
        }

    }
    else
    {
        TLensParams tLensParams = {0};
        tLensParams.adwVal32[0] = 19;
        VidLensCtrl(0,LENS_CTRL_RD_MOTOR_REG,&tLensParams);
        snprintf(chBuffer,NVR_FIX_MAX_LENSVER_LEN,""FORMAT_U32"."FORMAT_U32"",(tLensParams.adwVal32[1]>>8), (tLensParams.adwVal32[1] & 0xFF));

        PRINTVIP("LensVer %s !\n", chBuffer); 
        eRet = NvrSysSetCameraLensVer(chBuffer);
    }
    
    return eRet;
}

static s32 NvrFixDevLensUpgradeTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
    ///<镜头升级防止超时不回收
    g_byLensUpdateState = NVR_DEV_UPGRADE_STATE_ERR;
    g_byLensUpdateErr = NVR_DEV_UPGRADE_ERR_TIMEOUT;

    g_bLensUpdating = FALSE;

    return 0;
}




//ptz定时任务(时间与参数)
//TNvrDisarmingTime g_tFixPtzTime;
//TTmingTaskParamEx g_tFixTimingTaskEx;
NVRSTATUS NvrPtzDevSetBasicPos()
{

	TNvrCooParam       tCooParam;	///<方位角参数配置
	mzero(tCooParam);

	
	if(g_dwCurrentHAngle != g_tBasicPos.wBasicHPos||g_dwCurrentVAngle != g_tBasicPos.wBasicVPos)
	{
		g_tBasicPos.wBasicHPos = g_dwCurrentHAngle;
		g_tBasicPos.wBasicVPos = g_dwCurrentVAngle;
		tCooParam.nX = g_tBasicPos.wBasicHPos;
		tCooParam.nY = g_tBasicPos.wBasicHPos;

		NvrFixCfgSetParamByFlag(&tCooParam, sizeof(tCooParam), NVR_PTZ_MODULE_COOR_PARAM);
	    //更新osd
		NvrFixDevNotifyUpdatePtzOsd();
	}
	PRINTDBG("NvrPtzDevSetBasicPos:save basic pos succ(%d,%d)!\n", g_tBasicPos.wBasicHPos, g_tBasicPos.wBasicVPos);
    return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevGetHvPos(u32 dwDelayTime)
{
	OsApi_TimerSet( g_hGetHvAngleTimer, dwDelayTime, NvrFixDevGetHvAngleTimerCB, NULL);
	PRINTDBG("NvrPtzDevGetHvPos delay time:%u ms\n", dwDelayTime);
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixIspEventNotify(s32 byEvent)
{
	switch(byEvent)
	{
		case NVR_FIX_ISP_EVENT_KEY_FIX_START:
		{
			NvrFixIspSetKeyParam(0, NVR_FIX_ISP_EVENT_KEY_FIX_START,NULL);
		}
		break;
		case NVR_FIX_ISP_EVENT_KEY_FIX_STOP:
		{
			NvrFixIspSetKeyParam(0, NVR_FIX_ISP_EVENT_KEY_FIX_STOP,NULL);
		}
		break;
		default:
			break;
	}
	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevSetWatchOnState(u8 byTaskType, u8 byTaskParam)
{
	g_tWatchOnState.byTaskType = byTaskType;
	g_tWatchOnState.byTaskParam = byTaskParam;
	return NVR_ERR__OK;

}

NVRSTATUS NvrFixDevGetWatchOnState(u8 *pbyTaskType, u8 *pbyTaskParam)
{
	FIX_ASSERT(pbyTaskType);
	FIX_ASSERT(pbyTaskParam);

	*pbyTaskType = g_tWatchOnState.byTaskType;
	*pbyTaskParam = g_tWatchOnState.byTaskParam;
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevStopWatchOn()
{
	u8 byTaskType = 0;
	u8 byTaskParam = 0;
	BOOL32 bStop = TRUE;
	TNvrPtzCtrlInfo tCmdInfo = {0};

	NvrFixDevGetWatchOnState(&byTaskType, &byTaskParam);
	OsApi_TimerStop(g_hWatchOnTimer);


	tCmdInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
	tCmdInfo.wNumber = byTaskParam;
	tCmdInfo.bTaskTrg = TRUE;			//由守望任务触发的操作

	switch(byTaskType)
	{
		case NVR_PTZCTRL_HORIZON_SCAN_TASK:
        {
            OsApi_TimerStop(g_hUpdateOsdTimer);
		}
        break;
		case NVR_PTZCTRL_VERTICAL_SCAN_TASK:
		case NVR_PTZCTRL_FULLVIEW_SCAN_TASK:
		case NVR_PTZCTRL_PRESET_LOAD_TASK:
		{
			//OsApi_TimerStop(g_hPresetOsdTimer);
		}
		break;
		
		case NVR_PTZCTRL_PATH_CRUISE_TASK:
		{
			OsApi_TimerStop(g_hPathCrsTimer);          
            g_byPathCrsState = FALSE;
		}
		break;

		case NVR_PTZCTRL_SYNC_SCAN_TASK:
		{
			//NvrPtzDevSyncScanEndPreview();
		}
		break;
		default:
		{
			bStop = FALSE;
			PRINTERR( "NvrPtzDevStopWatchOn unknow watchon type:%d\n", byTaskType);
		}
		break;
	}

	if (bStop)
	{
		tCmdInfo.eCtrlType= NVR_PTZCTRL_TYPE_MOVESTOP;///<原来
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);

		NvrFixIspEventNotify(NVR_FIX_ISP_EVENT_KEY_FIX_STOP); //停止后进行一次聚焦动作

		PRINTDBG( "NvrPtzDevStopWatchOn stop task:%d\n", byTaskType);
		NvrFixDevGetHvPos(NVR_PTZ_DEV_GETHVPOS_DELAY);

		///<更新守望状态
		NvrFixDevSetWatchOnState(NVR_PTZCTRL_TASK_MODE_NONE,0);
	}

	return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevStopMove(void)
{
	TNvrPtzCtrlInfo tCmdInfo;
	mzero(tCmdInfo);
	tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_MOVESTOP;
	NvrFixDevPtzCtrl(0, tCmdInfo);
	PRINTDBG("NvrFixDevStopMove\n");
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevPreset(u16 wNum)
{
	TNvrPtzCtrlInfo tCmdInfo;
	TNvrPresetInfo tPreInfo;
	mzero(tCmdInfo);
	tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_LOAD;
	tCmdInfo.wNumber = wNum;
	
	NvrFixCfgGetPresetInfo(tCmdInfo.wNumber, &tPreInfo);
	if(tPreInfo.bIsSet)
	{
		NvrFixDevPtzCtrl(0, tCmdInfo);
	}
	PRINTDBG("NvrFixDevPreset %d\n",wNum);
	return NVR_ERR__OK;
}




void NvrFixDevPtztaskCheck(u32 dwCurTime, s32 nWday)
{
	u8 byTimeId = 0;//布防时间段索引
	TNvrTmingTaskParam tPtzParam;//PTZ定时任务参数
	static TNvrPtzCtrlInfo tPtzMsg = {.eCtrlType = NVR_PTZCTRL_TIMINGTASK_CLEAR};//PTZ控制消息结构
	static TNvrTmingInfo tTmingInfo = {0};
	//TIpcAudParam tIpcAudParam;
	TNvrPtzCtrlInfo tCmdInfoMicOpenFan;

	//memset(&tIpcAudParam, 0, sizeof(tIpcAudParam));
	memset(&tCmdInfoMicOpenFan, 0, sizeof(tCmdInfoMicOpenFan));
	static u16 dwCount =0;
	memset(&tPtzParam, 0, sizeof(tPtzParam));//lint !e419
	if(dwCount<2)
	{
		dwCount++;
	}

	//获取PTZ定时任务参数
	NvrFixCfgGetParamByFlag(&tPtzParam, sizeof(tPtzParam), NVR_PTZ_MODULE_TIMING_TASK_PARAM);
	//IpcCfgGetParam(&tIpcAudParam , sizeof(tIpcAudParam), IPC_AUD_FLAG);
	//PRINTDBG("tTmingTaskHead enable %d\n",tPtzParam.tTmingTaskHead.bEnable);

	//判断定时任务使能
	if (tPtzParam.tTmingTaskHead.bEnable)
	{
	
		//敏佳云台ipc在重启时定时任务会概率性不生效，第一次重启时加500ms延时
		//if(NVR_CAP_PTZ_TYPE_MINJIA == g_tPtzCap.byPtzType&&1==dwCount)
		//{
		//	OsApi_Delay(500);
		//}
		//布防时间判断
		for(byTimeId = 0; byTimeId < NVR_MAX_DISARMING_PERIOD; byTimeId++)
		{

			
			PRINTDBG("scale enable %d,start:%u end:%u\n",g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].bEnable,g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nStartTime
				,g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nEndTime);
			//布防时间段内则开启PTZ任务
			if (g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].bEnable
				&& dwCurTime >= g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nStartTime
				&& dwCurTime <= g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nEndTime)
			{

				PRINTDBG("tming tasktype %d,param:%d g_tasktype:%d,g_taskparam:%d\n",tTmingInfo.eTaskType,tTmingInfo.byTaskParam
					,g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskType,
					g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskParam);

			
				//避免重复开启设置
				if (tTmingInfo.eTaskType != g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskType
					|| tTmingInfo.byTaskParam != g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskParam)
				{
					tPtzMsg.eCtrlType = NVR_PTZCTRL_TIMINGTASK_SET;
					tPtzMsg.wNumber = g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskType;			//预置点、巡航路径、扫描路径
					tPtzMsg.eMode = g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskParam; 			//模式命令，如打开，关闭，自动
					NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzMsg);	//发送定时任务消息给PTZ处理模块

					//tTmingInfo = tPtzParam.atIpcEvdayParam[nWday].atIpcTmingInfo[byTimeId];
					tTmingInfo.bIsEnable = g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].bEnable;
					tTmingInfo.nStartTime = g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nStartTime;
					tTmingInfo.nEndTime = g_tFixPtzTime.atWeekScale[nWday].atTimeScale[byTimeId].nEndTime;
					tTmingInfo.eTaskType = g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskType;
					tTmingInfo.byTaskParam = g_tFixTimingTaskEx.atWeekScale[nWday].atTmingInfoEx[byTimeId].byTaskParam;
				}			
				break;
			}
		}

		//不在布防时间段内，则需要停止PTZ任务
		if (NVR_MAX_DISARMING_PERIOD == byTimeId)
		{
			//避免重复关闭设置
			if (NVR_PTZCTRL_TIMINGTASK_CLEAR != tPtzMsg.eCtrlType)
			{
				//清除定时任务
				tPtzMsg.eCtrlType = NVR_PTZCTRL_TIMINGTASK_CLEAR;
				NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzMsg);	//发送定时任务消息给PTZ处理模块
				memset(&tTmingInfo, 0, sizeof(tTmingInfo));
			}
		}
	}
	else		//未开启或开启后关闭
	{
		if (NVR_PTZCTRL_TIMINGTASK_CLEAR != tPtzMsg.eCtrlType)
		{
			//清除定时任务
			tPtzMsg.eCtrlType = NVR_PTZCTRL_TIMINGTASK_CLEAR;
			NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzMsg);	//发送定时任务消息给PTZ处理模块
			memset(&tTmingInfo, 0, sizeof(tTmingInfo));
		}
	}
	return;
}


void *NvrFixDevPtztaskCheckThread()
{
	u32 dwCurTime = 0;
	TNvrBrokenDownTime tLocalTime;
	
	while(TRUE)
	{
		memset(&tLocalTime, 0, sizeof(TNvrBrokenDownTime));
		//获取当前时间，转换为s
		NvrSysGetSystemLocalTime(&tLocalTime);
		dwCurTime = tLocalTime.byHour*3600 + tLocalTime.byMinute*60 + tLocalTime.bySecond;

		NvrFixDevPtztaskCheck(dwCurTime, tLocalTime.byWeek);
		OsApi_TaskDelay(1000);
	}
}

static void *NvrFixDevDealLinkLedThread()
{
    u32 dwStatus = 0;
    TNvrNetEthParam tEthParam;
    TThreadInfoRecord tThreadInfo;
	mzero(tThreadInfo);

    //添加线程信息
	tThreadInfo.m_dwThreadId = getpid();
	mcopy(tThreadInfo.m_strThreadName, "NvrFixDevLinkLed");
	OsApi_AddThreadInfo( &tThreadInfo);

	PRINTDBG("[IPCDEV]NvrFixDevDealLinkLedThread Thread %d created!\n", getpid());

	while(TRUE)
    {
        mzero(tEthParam);
        NvrNetworkGetEthParam(0, &tEthParam);

        //获取网络连接状态，连接正常link灯长亮
        if(NVR_NET_STATE_LINK == tEthParam.eNetLinkStatus)
        {
            dwStatus = 1;
        }
        else
        {
            dwStatus = 0;
        }

        //link灯id为LED_ID_LINK
        NvrBrdApiBrdSetLedStatus(LED_ID_LINK, dwStatus);

        OsApi_TaskDelay(1000);
    }

    PRINTERR("NvrFixDevDealLinkLedThread Task Quit...\n");
	OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
	OsApi_TaskExit();
	return NULL;

}

NVRSTATUS NvrFixDevNextValidPreset(u8 byPathNum, u16 wFindPos, u16 *pwPreNum, u32 *pdwStayTime)
{
	u32 i = 0;
	u16 wIndex = wFindPos - 1;
	u16 wPreCnt = 0;		//当前路径中添加的预置位个数
	u16 wPreNum = 0;
	u32 dwStayTime = 0;
	TNvrPathCrsInfo tPathCrsInfo;
	TNvrPresetInfo tIpcPresetInfo;

	mzero(tPathCrsInfo);
	NvrFixCfgGetPathCrsInfo(byPathNum, &tPathCrsInfo);
	wPreCnt = tPathCrsInfo.wPresetCnt;
	for (i = 0; i < wPreCnt; i++)
	{
		if (wIndex >= wPreCnt)		//找到最后一个位置时再从头开始遍历
		{
			wIndex = 0;
		}

		mzero(tIpcPresetInfo);
		wPreNum = tPathCrsInfo.tPresetInfo[wIndex].wPrenum;
		dwStayTime = tPathCrsInfo.tPresetInfo[wIndex].wStaytime;
		
		PRINTDBG("NvrPtzDevNextValidPreset seek：%d\n",wPreNum);
		NvrFixCfgGetPresetInfo(wPreNum, &tIpcPresetInfo);
		wIndex++;

		if (tIpcPresetInfo.bIsSet)
		{
			*pwPreNum = wPreNum;
			*pdwStayTime = dwStayTime;
			g_wFindPos = wIndex;
			return NVR_ERR__OK;
		}
	}

	PRINTDBG("NvrPtzDevNextValidPreset No Valid Preset Num,total：%d\n",tPathCrsInfo.wPresetCnt);
	

	return NVR_ERR__ERROR;

}


s32 NvrFixDevWatchOnTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
	BOOL32 bSendCmd = TRUE;
	TNvrPtzCtrlInfo tCmdInfo = {0};
	TNvrWatchOnParam tWatchOnPrm = {0};
	NvrFixCfgGetParamByFlag(&tWatchOnPrm,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);
	PRINTDBG( "NvrPtzDevWatchOnTimerCB \n");

	if (tWatchOnPrm.bEnable)
	{
		tCmdInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
		tCmdInfo.bTaskTrg = TRUE;			//由守望任务触发的操作
		tCmdInfo.wNumber = 0;
		switch(tWatchOnPrm.byTaskType)
		{
			case NVR_PTZCTRL_HORIZON_SCAN_TASK:
			{
				tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT;
			}
			break;

			case NVR_PTZCTRL_VERTICAL_SCAN_TASK:
			{
				tCmdInfo.eCtrlType = NVR_PTZCTRL_VERTICAL_SCAN;
			}
			break;

			case NVR_PTZCTRL_PRESET_LOAD_TASK:
			{
				tCmdInfo.wNumber = tWatchOnPrm.dwPresetID;
				tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_LOAD;
				NvrFixDevGetHvPos(2000);
			}
			break;

			case NVR_PTZCTRL_PATH_CRUISE_TASK:
			{
				///<cgiapp传过来的路径巡航号从0开始，业务保存的配置也是从0开始，但是实际执行操作时是从1开始，需要做+1做转换
				tCmdInfo.wNumber = tWatchOnPrm.dwPathCruiseID + 1;
				tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_PATH_CRUISE_START;
			}
			break;

			case NVR_PTZCTRL_FULLVIEW_SCAN_TASK:
			{
				tCmdInfo.eCtrlType = NVR_PTZCTRL_FULLVIEW_SCAN;
			}
			break;

			case NVR_PTZCTRL_SYNC_SCAN_TASK:
			{
				tCmdInfo.wNumber = tWatchOnPrm.dwSyncScanID;
				tCmdInfo.eCtrlType = NVR_PTZCTRL_SYNCSCAN_PREVIEW;
			}
			break;

			default:
			PRINTDBG( "NvrPtzDevWatchOnTimerCB unknow watchon type:%d\n", tWatchOnPrm.byTaskType);
			return NVR_ERR__ERROR;
		}

		PRINTDBG("NvrPtzDevWatchOnTimerCB start task:%d\n", tWatchOnPrm.byTaskType);

		NvrFixDevSetWatchOnState(tWatchOnPrm.byTaskType, tCmdInfo.wNumber);

		if (bSendCmd)
		{
			NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
		}
	}

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevStartWatchOn()
{
	TNvrWatchOnParam tWatchOnPrm = {0};

	NvrFixCfgGetParamByFlag(&tWatchOnPrm,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);
	if (tWatchOnPrm.bEnable)
	{
		OsApi_TimerSet(g_hWatchOnTimer, (tWatchOnPrm.dwWaitTimeSec)*1000, NvrFixDevWatchOnTimerCB, NULL);
		PRINTDBG( "NvrFixDevStartWatchOn dealy (%d)min\n", tWatchOnPrm.dwWaitTimeSec);
	}
	return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevSetTmingTaskState(u8 byTaskType, u8 byTaskParam)
{
	g_tTmingTaskState.byTaskType = byTaskType;
	g_tTmingTaskState.byTaskParam = byTaskParam;
	return NVR_ERR__OK;

}

NVRSTATUS NvrFixDevGetTmingTaskState(u8 *pbyTaskType, u8 *pbyTaskParam)
{
	FIX_ASSERT(pbyTaskType);
	FIX_ASSERT(pbyTaskParam);

	*pbyTaskType = g_tTmingTaskState.byTaskType;
	*pbyTaskParam = g_tTmingTaskState.byTaskParam;
	return NVR_ERR__OK;
}



BOOL32 NvrFixDevGetMotorMovingState(void)
{
	return g_bMotorMorving;
}


NVRSTATUS NvrFixDevSetMotorMovingState(BOOL32 bState)
{

    TMediaCtrlDisParam	tDisParam;
    TLcamIspParam tIspParam;
    u32 dwParam = 0;

    mzero(tDisParam);
    mzero(tIspParam);

    LcamIspGetParam(0, &tIspParam);
    if (!bState && NVR_ISP_VIDEO_STABLIZER_GYRO_ENABLE == tIspParam.tStablizer.eVideoStablizerMode)
    {
        NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACT_GET_FOCAL_LENGTH, &dwParam);
        tDisParam.bDisEnable = TRUE;
        tDisParam.dwDisLevel = tIspParam.tStablizer.dwVideoStablizerLevel;
        tDisParam.bDisStillEnable = FALSE;
        tDisParam.fHorFov = (float)dwParam;

        PRINTDBG("NvrPtzDevSetMotorMovingState Stablizer StablizerMode:%u, StablizerLevel:%u, FocalLength:%f\n",tIspParam.tStablizer.eVideoStablizerMode, tIspParam.tStablizer.dwVideoStablizerLevel, tDisParam.fHorFov);
        MediaCtrlSetDisParam(0, &tDisParam);
    }
    else if (bState && NVR_ISP_VIDEO_STABLIZER_GYRO_ENABLE == tIspParam.tStablizer.eVideoStablizerMode)
    {
        NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACT_GET_FOCAL_LENGTH, &dwParam);
        tDisParam.bDisEnable = TRUE;
        tDisParam.dwDisLevel = tIspParam.tStablizer.dwVideoStablizerLevel;
        tDisParam.bDisStillEnable = TRUE;
        tDisParam.fHorFov = (float)dwParam;

        PRINTDBG("NvrPtzDevSetMotorMovingState Stablizer StablizerMode:%u, StablizerLevel:%u, FocalLength:%f\n",tIspParam.tStablizer.eVideoStablizerMode, tIspParam.tStablizer.dwVideoStablizerLevel, tDisParam.fHorFov);
        MediaCtrlSetDisParam(0, &tDisParam);
    }

    g_bMotorMorving = bState;

    return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevClrTimingTask(BOOL32 bStartWatchOnTask)
{
	u8 byCurTaskType = 0;
	u8 byCurParam = 0;
	static TNvrPtzCtrlInfo tCmdInfo = {0};
	TNvrPtzBasicState tPtzState = {0};
	u8 abyCmd[NVR_PTZ_DEV_MAX_BUF_LEN];
	TNvrPtzCtrlInfo tCmd;
	u32 byCmdLen = 0;
	u32 wRealLen = 0;
	mzero(tCmd);
	NvrFixCfgGetParamByFlag(&tPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
	NvrFixDevGetTmingTaskState(&byCurTaskType, &byCurParam);

	OsApi_TimerStop(g_hTimingTaskTimer);

	///<当前没有正在执行的定时任务
	if (NVR_PTZCTRL_TASK_MODE_NONE == byCurTaskType)
	{
		PRINTDBG( "NvrPtzDevClrTimingTask Current Task NULL\n");
		return NVR_ERR__OK;
	}


	tCmdInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
	tCmdInfo.wNumber = byCurParam;
	tCmdInfo.bTaskTrg = TRUE;			//由任务触发的操作

	switch(byCurTaskType)
	{
		case NVR_PTZCTRL_HORIZON_SCAN_TASK:
        {
            OsApi_TimerStop(g_hUpdateOsdTimer);
		}
        break;
		case NVR_PTZCTRL_VERTICAL_SCAN_TASK:
		case NVR_PTZCTRL_FULLVIEW_SCAN_TASK:
		case NVR_PTZCTRL_PRESET_LOAD_TASK:
		{
			//OsApi_TimerStop(g_hPresetOsdTimer);
		}
		break;

		case NVR_PTZCTRL_PATH_CRUISE_TASK:
		{
			OsApi_TimerStop(g_hPathCrsTimer);
            g_byPathCrsState = FALSE;
		}
		break;

		case NVR_PTZCTRL_FAN_SCAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_FAN_SCAN;
			tCmdInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
			NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
		}
		break;

		case NVR_PTZCTRL_OPEN_FAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_FAN_SCAN;
			tCmdInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
			if (NVR_FAN_DEMISTER_OPEN != tPtzState.eFanDemisterMode)
			{
				NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
			}
		}
		break;
		
		default:
		PRINTERR( "NvrPtzDevClrTimingTask unknow timing task:%d\n", byCurTaskType);
		return NVR_ERR__ERROR;
	}

	///<云台停止运动

	tCmd.eCtrlType = NVR_PTZCTRL_TYPE_MOVESTOP;
	NvrFixDevConvertSerialCode(tCmd, abyCmd, &byCmdLen);
	NvrFixDevWriteSerial(g_byPtzCtrlSerialId,abyCmd, byCmdLen, &wRealLen);

	//球机会概率性存在，触发的定时任务因为串口冲突导致失效，这里启动定时器检测触发的任务是否生效
	//没有生效再次发送同样的命令
	//OsApi_TimerSet(g_hCheckPtzIsMoveTimer, 800, IpcDevCheckPtzIsStopTimerCB, &tCmdInfo);

	//水平巡行，垂直巡航，花样扫描，帧扫描，全景扫描这几个都是在定时任务开始的时候到发送了
	//NvrPtzIspEventNotify(NVR_PTZ_ISP_EVENT_KEY_PTZ_START)操作，所以在定时时间结束或者，定时任务关闭
	//需要在触发stop操作，使其进行聚焦。
	//加载预值位和水平巡航这两个不进行聚焦，是因为在加载预值位后。会过800ms等待后自动触发聚焦。
	if(NVR_PTZCTRL_HORIZON_SCAN_TASK == byCurTaskType ||
		NVR_PTZCTRL_VERTICAL_SCAN_TASK == byCurTaskType ||
		NVR_PTZCTRL_SYNC_SCAN_TASK == byCurTaskType ||
		NVR_PTZCTRL_FULLVIEW_SCAN_TASK == byCurTaskType ||
		NVR_PTZCTRL_RAND_SCAN_TASK ==  byCurTaskType)
	{
		NvrFixIspEventNotify(NVR_FIX_ISP_EVENT_KEY_FIX_STOP);
	}


	//NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
	PRINTDBG("NvrPtzDevClrTimingTask stop task:%d\n", byCurTaskType);

	NvrFixDevGetHvPos(NVR_PTZ_DEV_GETHVPOS_DELAY);

	NvrFixDevSetTmingTaskState(0, 0);

	///<重启守望任务
	if (bStartWatchOnTask)
	{
		NvrFixDevStartWatchOn();
	}
	return NVR_ERR__OK;

}

#if 0 //此前从球机移植过来，查询策略有别与球机，暂时不要
static s32 NvrFixDevCheckPtzIsMoveTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
	u8 byCmdLen = 0;
	u8 abyCmd[NVR_PTZ_DEV_MAX_BUF_LEN] = {0};		//生成串口指令buf
	u16 dwCurrentPtzAngle_H = 0;
	u16 dwCurrentPtzAngle_V = 0;
	TNvrPtzCtrlInfo tCmd = {0};
	static TNvrPtzCtrlInfo tTmpCmd = {0};
	NVRSTATUS eRet = NVR_ERR__ERROR;

	///<查询pt角度
	tCmd.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;
	NvrFixDevConvertSerialCode(tCmd, abyCmd, (u32*)&byCmdLen);
	
	///<NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmd);
	dwCurrentPtzAngle_H = g_dwCurrentHAngle;
	dwCurrentPtzAngle_V = g_dwCurrentVAngle;
	///<如果角度相等则继续重复,
	PRINTDBG("NvrPtzDevCheckPtzIsMoveTimerCB last:(%d, %d), current:(%d, %d),(%d,%d),count:%d\n",
	lastPtzAngle_H, lastPtzAngle_V,dwCurrentPtzAngle_H, dwCurrentPtzAngle_V,g_dwCurrentHAngle, g_dwCurrentVAngle,g_dwCheckPTZMoveCount);
	memcpy(&tTmpCmd, (TNvrPtzCtrlInfo *)param, sizeof(TNvrPtzCtrlInfo));
	PRINTDBG("NvrPtzDevCheckPtzIsMoveTimerCB copy test, deal cmd:%d, number:%d\n", tTmpCmd.eCtrlType,tTmpCmd.wNumber);

	if(lastPtzAngle_H == dwCurrentPtzAngle_H &&  lastPtzAngle_V == dwCurrentPtzAngle_V && g_dwCheckPTZMoveCount < 3)
	{

		g_dwCheckPTZMoveCount++;
		memcpy(&tTmpCmd, (TNvrPtzCtrlInfo *)param, sizeof(TNvrPtzCtrlInfo));
		if (g_dwCheckPTZMoveCount != 1)
		{
			NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tTmpCmd);
		}
		else
		{
			PRINTDBG( "NvrPtzDevCheckPtzIsMoveTimerCB need to read ptz angle firstly\n");
		}
		PRINTDBG( "NvrPtzDevCheckPtzIsMoveTimerCB again:%d\n", tTmpCmd.eCtrlType);

		g_bIsReadPtzAngle = TRUE;
		OsApi_TimerSet(g_hCheckPtzIsMoveTimer, 3000, NvrFixDevCheckPtzIsMoveTimerCB, &tTmpCmd);
	}
	else
	{
		//如果角度发送改变表示云台转动指令成功接收到，停止定时器
		g_dwCheckPTZMoveCount = 0;
		OsApi_TimerStop(g_hCheckPtzIsMoveTimer);
		PRINTDBG("NvrPtzDevCheckPtzIsMoveTimerCB stop\n");

	}
	return eRet;
}
#endif 
u8 NvrFixDevGetSyncScanState(void)
{
	return g_tSyncScanStateInfo.eState;
}
NVRSTATUS NvrFixDevSetSyncScanState(u8 bySyncScanState)
{
	g_tSyncScanStateInfo.eState = bySyncScanState;
	return NVR_ERR__OK;
}
NVRSTATUS NvrFixDevGetSyncScanParam(PTScanPath *pptScanPath)
{
	*pptScanPath = g_Path;
	return NVR_ERR__OK;
}

#if 0 //此前从球机移植过来，查询策略有别与球机，暂时不要

/************************* 云台操作相关定时器回调 ***********************/
static s32 NvrFixDevCheckPtzIsStopTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
///<TODO
	u8 byCmdLen = 0;
	u8 abyCmd[NVR_PTZ_DEV_MAX_BUF_LEN] = {0};		//生成串口指令buf
	u16 dwCurrentPtzAngle_H = 0xFFFF;
	u16 dwCurrentPtzAngle_V = 0xFFFF;
	TNvrPtzCtrlInfo tCmd = {0};
	static TNvrPtzCtrlInfo tTmpCmd = {0};
	static int  dwCount = 0;
	NVRSTATUS eRet = NVR_ERR__ERROR;

	
	///查询pt角度
	tCmd.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;
	NvrFixDevConvertSerialCode(tCmd, abyCmd, (u32*)&byCmdLen);
	///<NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmd);
	dwCurrentPtzAngle_H = g_dwCurrentHAngle;
	dwCurrentPtzAngle_V = g_dwCurrentVAngle;
	///<如果角度相等则继续重复,
	PRINTDBG( "NvrPtzDevCheckPtzIsStopTimerCB last:(%d, %d), current:(%d, %d),count:%d\n",
	lastPtzAngle_H, lastPtzAngle_V,dwCurrentPtzAngle_H, dwCurrentPtzAngle_V,dwCount);
	memcpy(&tTmpCmd, (TNvrPtzCtrlInfo *)param, sizeof(TNvrPtzCtrlInfo));
	PRINTDBG( "NvrPtzDevCheckPtzIsStopTimerCB copy test deal cmd:%d\n", tTmpCmd.eCtrlType);
	if((lastPtzAngle_H != dwCurrentPtzAngle_H ||  lastPtzAngle_V != dwCurrentPtzAngle_V ) && dwCount < 3)
	{

		dwCount++;
		memcpy(&tTmpCmd, (TNvrPtzCtrlInfo *)param, sizeof(TNvrPtzCtrlInfo));
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tTmpCmd);
		PRINTDBG("NvrPtzDevCheckPtzIsStopTimerCB again:%d\n", tTmpCmd.eCtrlType);
		lastPtzAngle_H = g_dwCurrentHAngle;
		lastPtzAngle_V = g_dwCurrentVAngle;
		g_bIsReadPtzAngle = TRUE;
		OsApi_TimerSet(g_hCheckPtzIsMoveTimer, 3000, NvrFixDevCheckPtzIsStopTimerCB, &tTmpCmd);
	}
	else
	{
		//如果角度发送改变表示云台转动指令成功接收到，停止定时器
		dwCount = 0;
		OsApi_TimerStop(g_hCheckPtzIsMoveTimer);
		PRINTDBG( "NvrPtzDevCheckPtzIsStopTimerCB stop\n");

	}
	return eRet;
}
#endif 

NVRSTATUS NvrFixDevSetTimingTask(u8 byTaskType, u8 byTaskParam)
{
	u8 byCurTaskType = 0;
	u8 byCurParam = 0;
	BOOL32 bSendCmd = TRUE;
	static TNvrPtzCtrlInfo tCmdInfo = {0};
	TNvrWatchOnParam tWatchOnPrm = {0};


	NvrFixCfgGetParamByFlag(&tWatchOnPrm,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);
		
	NvrFixDevGetTmingTaskState(&byCurTaskType, &byCurParam);

	PRINTDBG( "NvrPtzDevSetTimingTask Type:%d, Param:%d\n", byTaskType, byTaskParam);

	///<界面中持续运动的操作时，不启动任务(客户端操作优先级最高)
	if (NvrFixDevGetMotorMovingState())
	{
		PRINTDBG( "NvrPtzDevSetTimingTask Motor Moving State:%d, SyncState:%d \n", NvrFixDevGetMotorMovingState(), NvrFixDevGetSyncScanState());
		return NVR_ERR__OK;
	}

	///<任务正在执行，无需重新开启
	if (byCurTaskType == byTaskType && byCurParam == byTaskParam)
	{
		PRINTDBG("NvrPtzDevSetTimingTask Execute task (%d) param(%d) not need restart\n", byCurTaskType, byCurParam);

		return NVR_ERR__OK;
	}

	///<开启新任务之前清除原来任务
	NvrFixDevClrTimingTask(FALSE);

    #if 0
	///<球机会概率性存在，触发的定时任务因为串口冲突导致失效，停止任务实效
	///<如果是停止任务则查询角度判断任务是否停止
	if(byTaskType == NVR_PTZCTRL_TASK_MODE_NONE)
	{
		lastPtzAngle_H = g_dwCurrentHAngle;
		lastPtzAngle_V = g_dwCurrentVAngle;
		g_bIsReadPtzAngle = TRUE;
		tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_MOVESTOP;
		OsApi_TimerSet(g_hCheckPtzIsMoveTimer, 3000, NvrFixDevCheckPtzIsStopTimerCB, &tCmdInfo);

	}
    #endif
	///<关闭优先级更低的守望任务
	if (tWatchOnPrm.bEnable)
	{
		NvrFixDevStopWatchOn();
	}
	tCmdInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
	tCmdInfo.wNumber = byTaskParam;
	tCmdInfo.bTaskTrg = TRUE;			//由定时任务触发的操作
	switch(byTaskType)
	{
		case NVR_PTZCTRL_HORIZON_SCAN_TASK:
		{
			tCmdInfo.eCtrlType= NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT;
		}
		break;

		case NVR_PTZCTRL_VERTICAL_SCAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_VERTICAL_SCAN;

		}
		break;

		case NVR_PTZCTRL_PRESET_LOAD_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_LOAD;
		}
		break;

		case NVR_PTZCTRL_PATH_CRUISE_TASK:
		{	
			///<cgiapp传过来的路径巡航号从0开始，业务保存的配置也是从0开始，但是实际执行操作时是从1开始，需要做+1做转换
			tCmdInfo.wNumber = byTaskParam + 1;
			tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_PATH_CRUISE_START;
		}
		break;
		case NVR_PTZCTRL_FULLVIEW_SCAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_FULLVIEW_SCAN;
		}
		break;

		case NVR_PTZCTRL_SYNC_SCAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_SYNCSCAN_PREVIEW;
		}
		break;

		case NVR_PTZCTRL_FAN_SCAN_TASK:
		{
				tCmdInfo.eCtrlType = NVR_PTZCTRL_FAN_SCAN;
				tCmdInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
		}
		break;

		case NVR_PTZCTRL_OPEN_FAN_TASK:
		{
			tCmdInfo.eCtrlType = NVR_PTZCTRL_FAN_SCAN;
			tCmdInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
			#if 0 ///<暂不处理，后续添加TEMFLAG
			if(NVR_MEDIA_AUD_INPUT_MIC_IN == tIpcAudParam.aEAudInputMode[0])
			{
				tCmdInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;		//业务要求即便在风扇开启时间，如果在使用mic，则关闭风扇
			}
			#endif
		}
		break;

		default:
		PRINTERR( "byTaskType unknow task type:%d\n", byTaskType);
		return NVR_ERR__ERROR;
	}
	lastPtzAngle_H = g_dwCurrentHAngle;
	lastPtzAngle_V = g_dwCurrentVAngle;
	if (bSendCmd)
	{
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
	}

	NvrFixDevSetTmingTaskState(byTaskType, byTaskParam);
	///<球机会概率性存在，触发的定时任务因为串口冲突导致失效，这里启动定时器检测触发的任务是否生效
	///<没有生效再次发送同样的命令
	#if 0
	if(byTaskType == NVR_PTZCTRL_HORIZON_SCAN_TASK
		|| byTaskType == NVR_PTZCTRL_VERTICAL_SCAN_TASK
		|| byTaskType == NVR_PTZCTRL_PRESET_LOAD_TASK)
	{
		g_bIsReadPtzAngle = TRUE;
		OsApi_TimerSet(g_hCheckPtzIsMoveTimer, 3000, NvrFixDevCheckPtzIsMoveTimerCB, &tCmdInfo);
	}
    #endif
	return NVR_ERR__OK;
}


s32 NvrFixDevCheckTimingTaskCB(HTIMERHANDLE dwTimerId, void* param)
{
	PRINTDBG( "NvrPtzDevCheckTimingTaskCB Timing Task Resume\n");

	NvrFixDevSetTimingTask(g_tTmingTaskSetParam.byTaskType, g_tTmingTaskSetParam.byTaskParam);

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevSetHorizonState(BOOL32 bState)
{
	g_bHorizonScan = bState;
	return NVR_ERR__OK;
}


BOOL32 NvrFixDevGetHorizonState(void)
{
	return g_bHorizonScan;
}


NVRSTATUS NvrFixDevStopHorizonScan(void)
{
	TNvrPtzCtrlInfo tCmdInfo;

	mzero(tCmdInfo);

	//如果正在水平扫描则停止
	if (NvrFixDevGetHorizonState())
	{
		tCmdInfo.eCtrlType = NVR_PTZCTRL_TYPE_MOVESTOP;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
		NvrFixDevSetHorizonState(FALSE);//停止后将水平扫描标志清为0
		//ptz方位角图标停止刷新(linan 2015.6.25)
        OsApi_TimerStop(g_hUpdateOsdTimer);
		PRINTDBG("NvrPtzDevStopHorizonScan stop horizon scan\n");
	}

	return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevStopAllPtzTask()
{
	//停止水平扫描
	NvrFixDevStopHorizonScan();

	//停止路径巡航
	OsApi_TimerStop(g_hPathCrsTimer);
        g_byPathCrsState = FALSE;

	//停止守望任务
	NvrFixDevStopWatchOn();

	//清除定时任务
	NvrFixDevClrTimingTask(FALSE);
	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevStartAllPtzTask()
{
	TNvrTmingTaskParam tTimingTaskParam;
	mzero(tTimingTaskParam);

	NvrFixCfgGetParamByFlag(&tTimingTaskParam,sizeof(TNvrTmingTaskParam),NVR_PTZ_MODULE_TIMING_TASK_PARAM);

	NvrFixDevStartWatchOn();

	if (g_tTmingTaskSetParam.bIsSet)
	{
		OsApi_TimerStop(g_hWatchOnTimer);
		OsApi_TimerSet( g_hTimingTaskTimer, (tTimingTaskParam.tTmingTaskHead.dwResumeTime)*1000, NvrFixDevCheckTimingTaskCB, NULL);
		PRINTDBG( "NvrFixDevStartAllPtzTask set timing task delay (%u) S\n", tTimingTaskParam.tTmingTaskHead.dwResumeTime);		
	}
	return NVR_ERR__OK;
}

s32 NvrFixDevGetHvAngleTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
    TNvrPtzCtrlInfo tPtzCtrlInfo;
	NVRSTATUS eNvrRet = NVR_ERR__OK;
    s32 nRet = 0;
    return 0;
    mzero(tPtzCtrlInfo);
    //ee 06 02 09 00 ff
    tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;

	PRINTDBG("NvrPtzDevGetHvAngleTimerCB get HV position\n");

    eNvrRet = NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
    if (NVR_ERR__OK != eNvrRet)
    {
        PRINTERR("NvrPtzDevGetHvAngleTimerCB failed, ret:%d\n", eNvrRet);
        nRet = -1;
    }

	return nRet;
}



u32 NvrFixDevCalcPtzUsedTime(u16 byPreNum)
{
    static BOOL bPtzVerUseful = FALSE;
	u32 dwTiltClkFactor = 0;
	u32 dwPanTime = 0,dwTiltTime = 0;
	TNvrPresetInfo tIpcPresetInfo = {0};
	NvrFixCfgGetPresetInfo(byPreNum, &tIpcPresetInfo);
	PRINTDBG("NvrPtzDevCalcPtzUsedTime PtzSoftVer: %u\n",g_wPtzSotfVer);

	if(NVR_CAP_PTZ_TYPE_KEDA_METAL == g_tPtzCap.byPtzType)
	{
		dwTiltClkFactor = 1;
		bPtzVerUseful = 	g_wPtzSotfVer < 314 ? FALSE : TRUE;
	}
	else if(NVR_CAP_PTZ_TYPE_KEDA_PLASTIC == g_tPtzCap.byPtzType || NVR_CAP_PTZ_TYPE_KEDA_427 == g_tPtzCap.byPtzType || NVR_CAP_PTZ_TYPE_KEDA_522 == g_tPtzCap.byPtzType 
		|| NVR_CAP_PTZ_TYPE_KEDA_422 == g_tPtzCap.byPtzType || NVR_CAP_PTZ_TYPE_KEDA_980 == g_tPtzCap.byPtzType || NVR_CAP_PTZ_TYPE_KEDA_PLASTIC_GD == g_tPtzCap.byPtzType)
	{
		dwTiltClkFactor = 2;
		bPtzVerUseful = g_wPtzSotfVer < 59 ? FALSE : TRUE;
	}

	if(!bPtzVerUseful || g_dwPreSetSpd == 0 || g_dwPreSetSpd == 40)
	{
		return 0;
	}

	u32 dwCalcPan = abs(lastPtzAngle_H - tIpcPresetInfo.wHPos);
	dwPanTime = (dwCalcPan > 18000 ? 36000 - dwCalcPan: dwCalcPan)/((pow(10,6) * 0.9) / (adwPersetSpeedCnt[g_dwPreSetSpd] * 5 * 32)* 100);

	s32 nLastHVPos =lastPtzAngle_V > 18000 ? lastPtzAngle_V - 36000 : lastPtzAngle_V;
	s32 nNextHVPos = tIpcPresetInfo.wVPos > 18000 ? tIpcPresetInfo.wVPos - 36000 : tIpcPresetInfo.wVPos;

	dwTiltTime = abs(nLastHVPos - nNextHVPos)
		/((pow(10,6) * 0.9) / (adwPersetSpeedCnt[g_dwPreSetSpd] * 5 * 32 * dwTiltClkFactor) * 100);

	return dwPanTime > dwTiltTime ? dwPanTime : dwTiltTime;
	
}


s32 NvrFixDevPathCrsTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
	u8  byPathNum = g_byPathCrsNum;
	u16  wPreNum = 0;
	u32 dwCurStayTime = *(u32 *)param;
	static u32 dwStayTime = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
	static u16 byLastPreNum = 0;
	static TNvrPtzCtrlInfo tCmd = {0};
	TNvrPresetInfo tIpcPresetInfo = {0};	

	PRINTDBG("NvrPtzDevPathCrsTimerCB path num:%d\n", byPathNum);

	NvrFixCfgGetPresetInfo(byLastPreNum, &tIpcPresetInfo);
	lastPtzAngle_H = tIpcPresetInfo.wHPos;
	lastPtzAngle_V = tIpcPresetInfo.wVPos;
	tCmd.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_LOAD;
	tCmd.wNumber = g_wCurPos;
	tCmd.bTaskTrg = TRUE;		//路径巡航触发的预置位操作，所有任务不重启
	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmd);
	byLastPreNum = g_wCurPos;

    g_byPathCrsState = TRUE;

	memset(&tIpcPresetInfo, 0 , sizeof(tIpcPresetInfo));
	NvrFixCfgGetPresetInfo(g_wCurPos, &tIpcPresetInfo);

	PRINTDBG("NvrPtzDevPathCrsTimerCB last:(%d, %d), cur:(%d, %d)\n",
		lastPtzAngle_H, lastPtzAngle_V,tIpcPresetInfo.wHPos, tIpcPresetInfo.wVPos);

	eRet = NvrFixDevNextValidPreset(byPathNum, g_wFindPos + 1, &wPreNum, &dwStayTime);
	if (NVR_ERR__OK == eRet)
	{
		u32 dwUsedTime = NvrFixDevCalcPtzUsedTime(g_wCurPos);
		PRINTDBG("NvrPtzDevPathCrsTimerCB next pos:%d, curstaytime:%u(s), usedtime:%u(s) \n", wPreNum, dwCurStayTime, dwUsedTime);
		g_wCurPos = wPreNum;
		OsApi_TimerSet(g_hPathCrsTimer, (dwUsedTime + dwCurStayTime)*1000, NvrFixDevPathCrsTimerCB, &dwStayTime);
	}

	return eRet;

}


NVRSTATUS NvrFixDevStartPathCrs(u8 byPathNum)
{
	NVRSTATUS eRet = NVR_ERR__ERROR;
	u16  wPreNum = 0;
	static u32 dwStayTime = 0;

	if (byPathNum >= NVR_MAX_PATHCRUISE_NUM)
	{
		PRINTERR("NvrPtzDevStartPathCrs path num invalid %d", byPathNum);
		return NVR_ERR__ERROR;
	}
	g_byPathCrsNum = byPathNum;
	if(TRUE==g_bPathCrsMode)
	{
		eRet = NvrFixDevNextValidPreset(byPathNum, g_wFindPos, &wPreNum, &dwStayTime);
	}
	else
	{
		eRet = NvrFixDevNextValidPreset(byPathNum, 1, &wPreNum, &dwStayTime);
	}
	if (NVR_ERR__OK == eRet)
	{
		g_wCurPos = wPreNum;
		OsApi_TimerSet(g_hPathCrsTimer, 0, NvrFixDevPathCrsTimerCB, &dwStayTime);
	}

	return eRet;
}

void *alarm1(u8 state, u8 chnid)
{
    TNvrAlarmSrc tAlmNftType;
    BOOL32 bStartOrStop = state;
    void* pvContext = NULL;


    mzero(tAlmNftType);
    tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
    tAlmNftType.byAlarmNum = NVR_ALARM_SD_THERMEASTEMP;
    LcamCltAlmNotify(tAlmNftType,chnid,bStartOrStop,pvContext);
    return NULL;
}

void* sendcmd(u8 id,s8*cmd)
{
	u32 res = 0;
    u32 i=0,j=0,k=0;
    u8  buf[1024] = {0xAA,0x05,0x08,0x2f,0x01,0x00,0xE7,0xEB,0xAA};
    u8 cBuf[1024];
	u8 tmp=0,tmp1=0;

	
	PRINTDBG("\ninput:");
	i = strlen(cmd);
	memcpy(cBuf,cmd,strlen(cmd)+1);

	for(j=0;j<i;j++)
	{
		tmp1=0;
	  if(cBuf[j] == ' '||cBuf[j]=='	')
			continue;
	  
	  if('0'<=cBuf[j]&&cBuf[j]<='9')
		  tmp1=cBuf[j]-'0';
	  if('a'<=cBuf[j]&&cBuf[j]<='f')
		  tmp1=cBuf[j]-'a'+10;
	  if('A'<=cBuf[j]&&cBuf[j]<='F')
		  tmp1=cBuf[j]-'A'+10;
	
	 if(res++%2==0) 
	 {
	    tmp=tmp1<<4;
	 }
	 else
	 {
	   tmp=(tmp1&0x0f)|tmp;
	   buf[k++]= tmp;
	 }
	}
	PRINTDBG("\n");

	for(i=0;i<k;i++)
		PRINTDBG("0x%x ",buf[i]);

	NvrFixDevWriteSerial(id, buf, k,&res);
    PRINTDBG("write=%d\n", res);

	return 0;

}


void* NvrHeatSet(u8 byHeatSec,u8 byNoHeatSec)
{

    g_byHeatS = byHeatSec;
    g_byHeatE = byNoHeatSec;
    PRINTDBG("heatset=start:%d,close:%d\n", g_byHeatS,g_byHeatE);

	return 0;

}


void NvrFixGetvh()
{
    TNvrPtzCtrlInfo tCmdInfo;

    mzero(tCmdInfo);
    tCmdInfo.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;
    NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tCmdInfo);
    //NvrFixDevStartPTZQuery();
    return;
    
}

void NvrFixSetBaseTime()
{
    NVRSTATUS eNvrRet = NVR_ERR__OK;
    TNvrBrokenDownTime tBdTime = {0};

    tBdTime.wYear = 2000;

    tBdTime.byMonth = 1;
    tBdTime.byDay = 1;

    eNvrRet = NvrSysSetSystemTime(&tBdTime, NVR_SYS_SYNCTIME_MANUAL);

    PRINTDBG("set default time ret :%d \n", eNvrRet);
}


NVRSTATUS NvrFixPTZHVReport()
{
	s32 nRet = 0;
    u32 dwEncId = 0;
	s8 achData[NVR_MAX_STR128_LEN]={0};
    u32 Wangle;
    u32 Xangle;
    u32 Hangle;
    u32 Yangle;
    u32 Zoom;
    u32 MinZoomRatio = g_dwMinZoomRatio;
    u32 MaxZoomRatio = g_dwMaxZoomRatio;
    u32 CurZoomRatio = 1;
    
	TMediaCtrlSeiInfo tSeiInfo;

    OsApi_SemTake(&g_hPtzSeiSem);
	if(g_pPtzSeiHandle)
	{
		mzero(achData);
		mzero(tSeiInfo);

        NvrFixDevGetCurrentPTZHV(&Xangle, &Yangle,&Zoom, &Wangle, &Hangle, &CurZoomRatio);

		sprintf(achData,"{\"x\":%.2f,\"y\":%.2f,\"w\":%.2f,\"h\":%.2f,\"z\":%.2f,\"minZ\":"FORMAT_U32",\"maxZ\":"FORMAT_U32"}",
		        Xangle*1.0/100,(s32)Yangle*1.0/100,Wangle*1.0/100,Hangle*1.0/100,CurZoomRatio*1.0/100,MinZoomRatio,MaxZoomRatio);
		tSeiInfo.dwType = NVR_VID_SNTT_PTZ;
		tSeiInfo.dwLen = strlen(achData);
		tSeiInfo.pvData = (void*)achData;
		for(dwEncId = 0; dwEncId < g_byVidEncNum; dwEncId++)
		{
		    PRINTDBG("encnum:%d,txt:%s\n",g_byVidEncNum,achData);
			nRet = MediaCtrlPutSeiInfo(g_pPtzSeiHandle, (s32)dwEncId,  &tSeiInfo, 1);
			if (0 != nRet)
			{
				PRINTERR("MediaCtrlPutSeiInfo failed ret:%d\n", nRet);
			}
		}
	}
	OsApi_SemGive(&g_hPtzSeiSem);
    return nRet;
}

NVRSTATUS NvrFixDevModuleInit()
{
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrCapLcam tCapLcamInfo;
    ENvrCapClassId eCapClassId = NVR_CAP_ID_DEF_CFG;  ///<能力集id
    TNvrCapDefaultCfg tNvrCapDefCfg;      ///<默认配置能力集
    TEdgeOsInterCapSysInfo tInterSysCap;
    TNvrCapFixInernalCapInfo tFixInterCapInfo;
    BOOL32 bSemRet=TRUE;
    u16 wChnId = 0;
	INIT_LIST_HEAD(&g_task_list);	
    INIT_LIST_HEAD(&g_SerialHistoryList);

	mzero(tCapLcamInfo);
    mzero(tInterSysCap);
    mzero(tNvrCapDefCfg);
    mzero(tFixInterCapInfo);
   

    bSemRet = OspSemBCreate(&g_hSemQueryPTZ);
    if(TRUE != bSemRet)
    {
        PRINTERR("NvrFixDevModuleInit g_hSemQueryPTZ failed, ret:%d\n",bSemRet);
    	return eRet;
    }

    bSemRet = OspSemBCreate(&g_hSemQueryRES);
    if(TRUE != bSemRet)
    {
        PRINTERR("NvrFixDevModuleInit g_hSemQueryRES failed, ret:%d\n",bSemRet);
    	return eRet;
    }

    bSemRet = OspSemBCreate(&g_hSemQueryThread);
    if(TRUE != bSemRet)
    {
        PRINTERR("NvrFixDevModuleInit g_hSemQueryThread failed, ret:%d\n",bSemRet);
    	return eRet;
    }

    bSemRet = OspSemBCreate(&g_hSemExeOver);
    if(TRUE != bSemRet)
    {
        PRINTERR("NvrFixDevModuleInit g_hSemExeOver failed, ret:%d\n",bSemRet);
    	return eRet;
    }

    bSemRet = OspSemBCreate(&g_hSemExeWait);
    if(TRUE != bSemRet)
    {
        PRINTERR("NvrFixDevModuleInit g_hSemExeWait failed, ret:%d\n",bSemRet);
    	return eRet;
    }

   
    PRINTDBG("==================== local cameral dev start: %s %s ====================\n", __DATE__, __TIME__);
    NvrCapGetFixInterCapParam(&tFixInterCapInfo);
	memcpy(&g_tFixInterCapInfo,&tFixInterCapInfo,sizeof(g_tFixInterCapInfo));
	g_byPtzType = tFixInterCapInfo.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPtzType;

	NvrFixDevLowTempLimitAf();

    NvrFixDevOsdInfoAdjust();

	eRet = NvrCapGetCapParam(eCapClassId, (void*)&tNvrCapDefCfg);
    if(NVR_ERR__OK != eRet)
    {
		PRINTERR("NvrFixDevModuleInit get defcap failed, ret:%d\n",eRet);
    	return eRet;
    }

    eCapClassId = NVR_CAP_ID_LCAM;

    eRet = NvrCapGetCapParam(eCapClassId, (void*)&tCapLcamInfo);
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("NvrFixDevModuleInit get softcap failed, ret:%d\n",eRet);
    	return eRet;
    }

    eRet = NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, (void*)&tInterSysCap); 
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("NvrFixDevModuleInit get EDGEOS_IN_CAP_ID_SYS cap failed, ret:%d\n",eRet);
        return eRet;
    }

    if (g_tFixInterCapInfo.tFixHwInternalCap.byMicroControlEnv)
    {
        ///< 注册环境信息获取回调
        NvrSysGetEnvInfoByMcuRegistCB(NvrFixDevGetEnvInfo);
    }

	g_bySerialID[0][SERIAL_TYPE_PTZCTRL] = SERIAL_TYPE_PTZCTRL;
	g_bySerialID[1][SERIAL_TYPE_PTZCTRL] = SERIAL_TYPE_PTZCTRL;
	g_bySerialID[2][SERIAL_TYPE_PTZCTRL] = SERIAL_TYPE_PTZCTRL;
	g_bySerialID[2][SERIAL_TYPE_RECHENGXIANG] = SERIAL_TYPE_RECHENGXIANG;

	memcpy(&g_tPtzCap, &tFixInterCapInfo.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam, sizeof(g_tPtzCap));
	memcpy(&g_tPtzCapInfo, &tFixInterCapInfo.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo, sizeof(g_tPtzCapInfo));

    eRet = NvrQueueCreate(&g_ptLcDevQueue);
    if (NVR_ERR__OK != eRet)
    {
        PRINTERR("NvrFixDevModuleInit NvrQueueCreate failed ret:%d\n", eRet);
        return eRet;
    }

	NvrDevRegistReadSerialDataCallBack(NvrFixDevParseSerialReadDateCB);

    if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrFixDevDealCamCmdThread, "NvrFixDevCamCmd", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL))
    {
        PRINTERR("NvrFixDevModuleInit NvrFixDevDealCamCmdThread create failed\n");
        eRet = NVR_ERR__ERROR;
    }

     
	PRINTERR("NvrFixDevModuleInit support ptz\n");

	if(0 != OsApi_TimerNew( &g_hCheckPtzIsMoveTimer ))
	{
		PRINTERR( "NvrPtzDevTimerInit: create g_hCheckPtzIsMoveTimer failed\n" );
		return NVR_ERR__ERROR;
	}

   if(0 != OsApi_TimerNew( &g_hPathCrsTimer ))
   {
	   PRINTERR( "NvrFixDevModuleInit: create g_hPathCrsTimer failed\n" );
	   return NVR_ERR__ERROR;
   }

	if(0 != OsApi_TimerNew( &g_hWatchOnTimer ))
	{
		PRINTERR( "NvrPtzDevTimerInit: create g_hWatchOnTimer failed\n" );
		return NVR_ERR__ERROR;
	}

	
   if(0 != OsApi_TimerNew( &g_hGetHvAngleTimer ))
   {
	   PRINTERR("NvrFixDevModuleInit g_hGetHvAngleTimer create failed\n");
	   eRet = NVR_ERR__ERROR;
   }

   if(0 != OsApi_TimerNew( &g_hPowerOffMemTimer ))
   {
	   PRINTERR("NvrFixDevModuleInit g_hPowerOffMemTimer create failed\n");
	   eRet = NVR_ERR__ERROR;
   }
   g_nPowerOffInterval = tFixInterCapInfo.tFixHwInternalCap.byElectricLens ? 3 : 60;

   if(0 != OsApi_TimerNew( &g_hPtzUpgradeTimer ))
   {
	   PRINTERR("NvrFixDevModuleInit g_hPtzUpgradeTimer create failed\n");
	   eRet = NVR_ERR__ERROR;
   }

   if(0 != OsApi_TimerNew( &g_hGetFocusTimer ))
   {
	   PRINTERR("NvrFixDevModuleInit g_hGetFocusTimer create failed\n");
	   eRet = NVR_ERR__ERROR;
   }

   if(0 != OsApi_TimerNew( &g_hFocusCtrlTimer ))
	{
		 PRINTERR("NvrFixDevModuleInit g_hFocusCtrlTimer create failed\n");
		  eRet = NVR_ERR__ERROR;
	}

   NvrFixDevTcConvertPtzTime();

    //创建link灯状态处理线程
    if(NVR_CAP_SUPPORT == tFixInterCapInfo.tFixHwInternalCap.bySupportLinkLed)
    {
        if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrFixDevDealLinkLedThread, "NvrFixDevLinkLed", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL))
        {
            PRINTERR("NvrFixDevModuleInit NvrFixDevDealLinkLedThread create failed\n");
            eRet = NVR_ERR__ERROR;
        }
    }

  if((TASKHANDLE)NULL == g_hXcoreQueryTask && g_tFixInterCapInfo.tFixHwInternalCap.byHotImageType == NVR_HOTIMAGE_TYPE_IRAYFT2)
   {
	  g_hXcoreQueryTask = OsApi_TaskCreate((LINUXFUNC)NvrFixDevCoretaskCheckThread, "NvrFixDevCoretaskCheckThread", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL );
	  if ((TASKHANDLE)NULL == g_hXcoreQueryTask)
	  {
		  PRINTERR("NvrPtzDevModuleInit: Create NvrFixDevCoretaskCheckThread Failed\n" );
		  return NVR_ERR__ERROR;
	  }
   }

  if(g_tPtzCap.byReportAR == TRUE)
   {
        if(NULL == g_hPtzSeiSem)
		{
			if(!OsApi_SemBCreate(&g_hPtzSeiSem))
			{
				return NVR_ERR__ERROR;
			}
		}
		OsApi_SemTake(&g_hPtzSeiSem);
		g_pPtzSeiHandle = MediaCtrlRegisterSeiWriter();
		if (NULL == g_pPtzSeiHandle)
		{
			PRINTERR("create PTZ sei handle failed\n");
			return NVR_ERR__ERROR;
		}
		else
		{
            u32 dwEncNum = 0,i=0,j=0;
             for (i = 0; i < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; i++)
             {
                  LcamMcGetVidEncChn(i,&dwEncNum);
                  for (j = 0; j < dwEncNum; j++)
                  {
                      MediaCtrlDisableSei(g_pPtzSeiHandle, g_byVidEncNum, NVR_VID_SNTT_PTZ);
                      MediaCtrlSetSeiAddMode(g_pPtzSeiHandle, g_byVidEncNum, NVR_VID_SNTT_PTZ, MEDIACTRL_SEI_ADD_MODE_FOLLOW_EVERY_FRM);        //叠加每一帧
                      MediaCtrlEnableSei(g_pPtzSeiHandle, g_byVidEncNum, NVR_VID_SNTT_PTZ);
                      g_byVidEncNum++;
                  }
             }

		}
		OsApi_SemGive(&g_hPtzSeiSem);

   }

  

   OsApi_TaskDelay(500);

   NvrFixDevSetLensInfo();

   //镜头视场角初始化,暂时固定死for ipc528,后面ipc546需要调整
    NvrFixDevLensInit(CamType_CBC_775);

    NvrFixDevLensIrayInit(0);

    // ptz osd方位角更新
    if (tInterSysCap.byMagneticSensorCap == NVR_CAP_SUPPORT)
    {
       if(0 != OsApi_TimerNew( &g_hUpdateOsdTimer))
       {
           PRINTERR( "NvrPtzDevTimerInit: create g_hUpdateOsdTimer failed(%d)\n");
           return NVR_ERR__ERROR;
       }
       // 更新方位定时器
       OsApi_TimerSet(g_hUpdateOsdTimer, 500, NvrFixDevPtzUpdatePtzOsdPositionCB, NULL);
       OsApi_RegCommand( "nvrmagcal", (void*)NvrFixDevMagneticCalibration, "show isp info on osd" );
    }

    // 视窗加热(能力支持加热且不支持温度获取，则一直加热)
    if ((NVR_CAP_SUPPORT == tFixInterCapInfo.tFixHwInternalCap.byHeaterSupport) && (NVR_CAP_SUPPORT != tFixInterCapInfo.tFixHwInternalCap.byTemperatureSupport))
    {
        ///< 优先判断是否有加热策略
        if (tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy)
        {
            if (NULL == g_hHeaterTimer)
            {
                if(0 != OsApi_TimerNew( &g_hHeaterTimer ))
                {
                    PRINTERR("NvrFixDevModuleInit g_hHeaterTimer create failed\n");
                    eRet = NVR_ERR__ERROR;
                }
                else
                {
                    OsApi_TimerSet(g_hHeaterTimer, 10 * 1000, NvrDevHeaterTimerCB, NULL);
                }
            }
        }
        else
        {
            THwmonStat tHeaterStat;
            mzero(tHeaterStat);

            tHeaterStat.dwId = HWMON_ID(HWMON_TYPE_HEATER, 0);
            tHeaterStat.tEntry.tHeater.on_off = 1;

            eRet = NvrBrdApiHwmonSetStatus(&tHeaterStat);
            if (NVR_ERR__OK != eRet)
            {
                PRINTERR("NvrFixDevModuleInit set heater failed, ret:%d\n", eRet);
            }
        }
    }
    else if(NVR_CAP_SUPPORT == tFixInterCapInfo.tFixHwInternalCap.byTemperatureSupport)
    {
        if(0 != OsApi_TimerNew( &g_hGetTempatureTimer ))
        {
            PRINTERR("NvrFixDevModuleInit g_hGetTempatureTimer create failed\n");
            eRet = NVR_ERR__ERROR;
        }
        else
        {
            OsApi_TimerSet(g_hGetTempatureTimer, 10*1000, NvrDevGetTempatureTimerCB, NULL);
        }
    }

    if (g_tFixInterCapInfo.tFixHwInternalCap.bySupportFan)
    {
        //默认风扇80
        NvrBrdApiSetFanSpeed(80);
    }

    if (g_tFixInterCapInfo.tFixHwInternalCap.byNoRtc)
    {
        NvrFixMem("no rtc to set default time \n");
        NvrFixSetBaseTime();
    }

    //加载PTZ OSD
    NvrFixDevNotifyUpdatePtzOsd();

   if((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrFixDevPTZQueryThread, "NvrFixDevPTZQueryThread", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL))
   {
        PRINTERR(" NvrFixDevPTZQueryThread create failed\n");
   }

   if((TASKHANDLE)NULL == OsApi_TaskCreate((LINUXFUNC)NvrFixDevCmdHistoryCheckThread, "NvrFixDevCmdHistoryCheckThread", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL ))
   {
		  PRINTERR("Create NvrFixDevCmdHistoryCheckThread Failed\n" );
		  return NVR_ERR__ERROR;
   }
   
   for (wChnId = 0; wChnId < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; wChnId++)
   {
       ///<启用烟火识别相关定时器
       if(NVR_CAP_SUPPORT == tFixInterCapInfo.tFixProIntelCap.bySupSmokeFire[wChnId])
       {
            eRet = NvrAlarmSetStatusChangeCallBack(NvrFixDevAlarmStatusChangeCB, NVR_APP_PROTO_OTHERAPP);
            if (NVR_ERR__OK != eRet)
            {
                PRINTERR("NvrFixDevModuleInit NvrAlarmSetStatusChangeCallBack regist failed\n");
            }

            if(0 != OsApi_TimerNew( &g_hSmokeFireStartTimer ))
            {
                PRINTERR("NvrFixDevModuleInit g_hSmokeFireStartTimer create failed\n");
            }

            break;
       }
   }

   if((TASKHANDLE)NULL == g_hPtzTimeingTask)
   {
	  g_hPtzTimeingTask = OsApi_TaskCreate((LINUXFUNC)NvrFixDevPtztaskCheckThread, "NvrPtzDevPtztaskCheckThread", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL );
	  if ((TASKHANDLE)NULL == g_hPtzTimeingTask)
	  {
		  PRINTERR("NvrPtzDevModuleInit: Create NvrPtzDevPtztaskCheckThread Failed\n" );
		  return NVR_ERR__ERROR;
	  }
   }

      OsApi_TaskDelay(500);

      NvrFixDevPtzExtraOperate();

    OsApi_RegCommand( "send", (void *)sendcmd, "sendcmd" );
    OsApi_RegCommand("alarm",(void*)alarm1,"alarm test");
    OsApi_RegCommand("getvh",(void*)NvrFixGetvh,"get ptz vh");
    OsApi_RegCommand( "ssf", (void *)NvrFixDevSmokeFireTrigger, "start smoke fire" );
    OsApi_RegCommand( "heat", (void *)NvrHeatSet, "set heat param" );

   return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevSetDefog(u32 dwOnOff)
{
	NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0;
    static TEdgeOsInterCapSysInfo s_tInterSysCap;
    u8 abyCmdHeat1[7] = {0xFF, 0x01, 0x0F, 0x01, 0x00, 0x64, 0x75};		//加热1
    u8 abyCmdHeat2[7] = {0xFF, 0x01, 0x0F, 0x02, 0x00, 0x64, 0x76};		//加热2
    u8 abyCmdStopHeat[7] = {0xFF, 0x01, 0x0F, 0x01, 0x00, 0x00, 0x11};  //停止加热
    u32 wRealLen = 0;
    u8 bySerialID = 0;
    THwmonStat tHwmonStat;
   	static u32 dwLastOnOff = 0xFFFF;
	
    memset(&tHwmonStat, 0, sizeof(tHwmonStat));

    if(g_bLensUpdating)
    {
        PRINTDBG("NvrFixDevSetDefog Lens updating, skip\n");
        return eRet;
    }

    if (0xFFFF == dwLastOnOff)
    {
        mzero(s_tInterSysCap);
        eRet = NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &s_tInterSysCap);
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("get in_sys failed, ret:%d\n",eRet);
            return eRet;
        }
    }

    bySerialID = s_tInterSysCap.tNvrMicroControllersInfo.bySerialID;
    
    if(s_tInterSysCap.tNvrMicroControllersInfo.byUse == TRUE)
    {
        if(dwOnOff == 0)
        {
            NvrFixDevWriteSerial(bySerialID, abyCmdStopHeat, 7, &wRealLen);
            if(wRealLen != 7)
            {
                PRINTERR("send serial failed,wRealLen :%d\n",wRealLen);
                return NVR_ERR__ERROR;
            }
        }
        else
        {
            NvrFixDevWriteSerial(bySerialID, abyCmdHeat1, 7, &wRealLen);
            if(wRealLen != 7)
            {
                PRINTERR("abyCmdHeat1 send serial failed,wRealLen :%d\n",wRealLen);
                return NVR_ERR__ERROR;
            }
            OsApi_Delay(200);
            NvrFixDevWriteSerial(bySerialID, abyCmdHeat2, 7, &wRealLen);
            if(wRealLen != 7)
            {
                PRINTERR("abyCmdHeat2 send serial failed,wRealLen :%d\n",wRealLen);
                return NVR_ERR__ERROR;
            }
        }
    }
    else
    {
        //和上一次配置相同则返回
		if(dwLastOnOff == dwOnOff)
		{
		    PRINTTMP("NvrFixDevSetDefog:BrdHwmonSetStatus Same,dwOnOff=%d\n", dwOnOff);
			return eRet;
		}
	  
        // no为0时，对镜头前方玻璃加热，防止起雾
        tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_HEATER, 0);
        tHwmonStat.tEntry.tHeater.on_off = (dwOnOff == 0) ? 0 : 1;
        tHwmonStat.tEntry.tHeater.temper = dwOnOff;

#ifdef _SSC339G_
        ///< sigmastar 平台使用占空比方式，兼容之前代码
        tHwmonStat.tEntry.tHeater.on_off = dwOnOff;
#endif
        nRet = BrdHwmonSetStatus(&tHwmonStat);
        if (0 != nRet)
        {
            PRINTERR("NvrFixDevSetDefog:BrdHwmonSetStatus failed,errno=%d\n", nRet);
            return NVR_ERR__ERROR;
        }
        else
        {
        	dwLastOnOff = dwOnOff;
            PRINTDBG("NvrFixDevSetDefog:BrdHwmonSetStatus success,dwOnOff = %d\n", dwOnOff);
        }
    }

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevSetLensInfo()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrCapSysInfo tCapSys;
    
    mzero(tCapSys);

    eRet = NvrCapGetCapParam(NVR_CAP_ID_SYS, &tCapSys);
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("get sys failed, ret:%d\n",eRet);
        return eRet;
    }

    ///<注册镜头升级回调    
    if(NVR_CAP_SUPPORT == tCapSys.tUpgradeLens.bSupUpgrade)
    {
         NvrSysLensUpgradeDateTransCB(NvrFixDevLensUpgradeDateCB);
         NvrSysLensUpgradeStatusTransCB(NvrFixDevLensUpgradeStatusCB);
    }

    ///<获取镜头版本号
    if(NVR_CAP_SUPPORT == tCapSys.tNvrCapSysBasic.bySupptCameraLensVer)
    {
         NvrFixDevSetLensVer();
    }

    ///<开启镜头加热 
    NvrFixDevSetDefog(100);

    return NVR_ERR__OK;
}


u8 NvrFixDevCalcCheckSum(u8 *pbybuf, u8 byBufLen)
{
	u8 i = 0;

	u8 sum = 0;
	for (i = 1; i < byBufLen - 1; i++)
	{
		sum +=pbybuf[i];
	}
	return sum;
}

//pelco-d
NVRSTATUS NvrFixDevDealPtzCtrl(u16 wChnId,TNvrPtzCtrlInfo tCtrlInfo,u8 byPTZType)
{     
    u8 achCmd[7];
    u32 dwLen = 7;
	u32 dwReLen = 0;
    
    u8 abyCmdQueryEE[6] = {0xee, 0x06, 0x02, 0x09, 0x00, 0xff};//查询PTZ指令
	NVRSTATUS eRet = NVR_ERR__OK;
    u8 bySerialId;
	
    memset(achCmd,0,sizeof(achCmd));

    achCmd[0] = 0xff;
    achCmd[1] = 0x01;
     
    switch(tCtrlInfo.eCtrlType)
    {
    case NVR_PTZCTRL_TYPE_MOVEUP:
         {
            achCmd[3] = 0x08;
            achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVEDOWN:
         {
             achCmd[3] = 0x10;
             achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVELEFT:
         {
             achCmd[3] = 0x04;
             achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVERIGHT:
         {
             achCmd[3] = 0x02;
             achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVELEFTUP:
         {
            achCmd[3] = 0x0c;
            achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
            achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVELEFTDOWN:
         {
            achCmd[3] = 0x14;
            achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
            achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVERIGHTUP:
         {
            achCmd[3] = 0x0A;
            achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
            achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVERIGHTDOWN:
         {
            achCmd[3] = 0x12;
            achCmd[4] = tCtrlInfo.wPanSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
            achCmd[5] = tCtrlInfo.wTilSpeed*NVR_FIX_DEV_PTZ_MAX_MOVE_SPEED/100;
         }
         break;
    case NVR_PTZCTRL_TYPE_MOVESTOP:
        {
		     //OsApi_TimerSet( g_hGetHvAngleTimer, 600, NvrFixDevGetHvAngleTimerCB, NULL);//停止600ms后获取坐标
        }
        break;
    case NVR_PTZCTRL_PANPOSION_SET:
        {
             achCmd[3] = 0x4b;
             u32 dwTmp = 0;
             //tCtrlInfo.wXposition = tCtrlInfo.wXposition>0?(360-tCtrlInfo.wXposition):tCtrlInfo.wXposition;
             //维多云台，敏佳，晨安云台角度增大方向与科达云台相反，为功能统一此处转化
			if(NVR_CAP_PTZ_TYPE_MINJIA2 == byPTZType)
			{
			    PRINTDBG("=======>x:%d,basicpos:%d",tCtrlInfo.wXposition,g_tBasicPos.wBasicHPos);
			    dwTmp =  (36000 - g_tBasicPos.wBasicHPos) + tCtrlInfo.wXposition;
                PRINTDBG("=======>x:%d,delt:%d",tCtrlInfo.wXposition,36000 - g_tBasicPos.wBasicHPos);
                //tCtrlInfo.wXposition = tCtrlInfo.wXposition < 0 ?tCtrlInfo.wXposition + 36000:tCtrlInfo.wXposition  ;
                tCtrlInfo.wXposition = 36000-dwTmp%36000;
                PRINTDBG("=======>x:%d,basicpos:%d",tCtrlInfo.wXposition,g_tBasicPos.wBasicHPos);
			}


             achCmd[4] = (tCtrlInfo.wXposition) >> 8;
             achCmd[5] = (tCtrlInfo.wXposition) & 0xFF;
            // OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
        }
        break;
    case NVR_PTZCTRL_TILTPOSION_SET:
        {
             achCmd[3] = 0x4d;
             achCmd[4] = (tCtrlInfo.wYposition) >> 8;
             achCmd[5] = (tCtrlInfo.wYposition) & 0xFF;
		    // OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
        }
        break;
	case NVR_PTZCTRL_PTPOSION_QUERY:
        {
             achCmd[3] = 0x55;
        }
        break; 
    case NVR_PTZCTRL_TYPE_RESET:
        {
            achCmd[3] = 0x35;
            achCmd[5] = 0x00;
            //OsApi_TimerSet( g_hGetHvAngleTimer, 1800, NvrFixDevGetHvAngleTimerCB, NULL);//停止1.8s后获取坐标
        }
        break;
    case NVR_PTZCTRL_SOFTVER_QUERY:
        {
            achCmd[3] = 0x73;
        }
        break;
    case NVR_PTZCTRL_RESTORE_FACTORY:
        {
            achCmd[3] = 0x35;
            achCmd[5] = 0x01;
        }
        break;
    default:
        break;
    }

    achCmd[dwLen-1] = NvrFixDevCalcCheckSum(achCmd,dwLen);

	bySerialId = g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL];

    if(NVR_CAP_PTZ_TYPE_MINJIA2 == byPTZType && NVR_PTZCTRL_PTPOSION_QUERY == tCtrlInfo.eCtrlType )
    {
        dwLen=6;
        g_dwEESendCount++;
        eRet = NvrFixDevWriteSerial(bySerialId, abyCmdQueryEE, dwLen, &dwReLen);
    }
    else
    {
        eRet = NvrFixDevWriteSerial(bySerialId, achCmd, dwLen, &dwReLen);
    }

	
    if(eRet != NVR_ERR__OK)
    {
        PRINTERR("NvrFixDevWriteSerial error ,ret %u\n",eRet);
        return NVR_ERR__ERROR;
    }
    
    PRINTTMP("NvrFixDevDealPtzCtrl %u-%lu-%lu-%x-%x-%x\n",bySerialId,dwLen,dwReLen,achCmd[3],achCmd[4],achCmd[5]);
    return eRet;
}

ENvrPtzCtrlType NvrFixDevTypeConvert(EAppCltPtzCtrlType eCtrlType)
{
	ENvrPtzCtrlType eNvrType;
    u16 i = 0;
    
    for (i = 0; i < NVR_PTZCTRL_TYPE_MAX - 1; i++)
    {
        if (g_atPtzCmdMap[i].eApCltType == eCtrlType)
        {
            eNvrType = g_atPtzCmdMap[i].eNvrType;
            PRINTDBG("NvrFixDevTypeConvert ctrl type %d\n",eNvrType);
            return eNvrType;
        }
    }
    
    PRINTDBG("NvrFixDevTypeConvert not match pui ctrltype %d\n",eCtrlType);

    return 255;
    
}


NVRSTATUS NvrDevBackFocusCtrlCmd(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

    if(NVR_PTZCTRL_TYPE_BF_NEAR == eType)
    {
        NvrFixIspSetKeyParam(wChnId,NVR_FIX_ISP_ACTION_KEY_ABF_FOCUS_NEAR,(void*)&pParam->wIspSpeed);
    }

    if(NVR_PTZCTRL_TYPE_BF_FAR == eType)
    {
        NvrFixIspSetKeyParam(wChnId,NVR_FIX_ISP_ACTION_KEY_ABF_FOCUS_FAR,(void*)&pParam->wIspSpeed);
    }

    if(NVR_PTZCTRL_TYPE_BF_AUTO == eType)
    {
        NvrFixIspSetKeyParam(wChnId,NVR_FIX_ISP_ACTION_KEY_ABF_FOCUS_AUTO,(void*)&pParam->wIspSpeed);
    }

    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP)
    {
        //OsApi_TaskDelay(100);
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }

	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlPtzOther(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
	{
		NvrFixDevDealPtzCtrl(wChnId,*pParam,NVR_CAP_PTZ_TYPE_MINJIA2);//暂时差异性不大，使用同一接口，如果差异性加大，需要使用新接口区分
	}
	else if(g_byPtzType == NVR_CAP_PTZ_TYPE_ZHONGYOU)
	{
	}
	else //普通相机使用标准指令
	{
		NvrFixDevDealPtzCtrl(wChnId,*pParam,NVR_CAP_PTZ_TYPE_NONE);
	}

	if(NVR_PTZCTRL_RESTORE_FACTORY == eType)
	{
	    //ptz恢复出厂需要删除断电记忆文件
		remove(LCAM_DEV_POWEROFFRSM_CFG_PATH);
	}

    if(eType != NVR_PTZCTRL_PTPOSION_QUERY && eType != NVR_PTZCTRL_PANPOSION_QUERY && eType !=  NVR_PTZCTRL_TILTPOSION_QUERY)
    {
            if(eType != NVR_PTZCTRL_TYPE_MOVESTOP)
            {
                g_bStoping =TRUE;
                NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
            }
    }

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevLaserCtrlMoveCmd(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

    NvrFixDevSendLasersCmd(*pParam);

	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlMoveCmd(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	//pt操作优先级最高，清除任务

	if(eType != NVR_PTZCTRL_TYPE_MOVESTOP)
	{
	    g_bStoping=FALSE;
		NvrFixDevStopAllPtzTask();
	}
	else
	{
	    //如果是停止，做个记录，在检测PT值还有变化时，再次发送停止
	    g_bStoping=TRUE;
		NvrFixDevStartAllPtzTask();
	}

	if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
	{

	    NvrFixDevSendCmd(*pParam);
		//NvrFixDevDealPtzCtrl(wChnId,*pParam);
	}
	else if(g_byPtzType == NVR_CAP_PTZ_TYPE_ZHONGYOU)
	{
	}
	else //普通相机使用标准指令
	{
		//NvrFixDevDealPtzCtrl(wChnId,*pParam);
        NvrFixDevSendCmd(*pParam);
	}

    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP)
    {
        NvrFixHistoryCmdClear(SERIAL_TYPE_PTZCTRL,NVR_PTZCTRL_TYPE_MOVESTOP);
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlZoomFocus(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	if(eType == NVR_PTZCTRL_TYPE_IRIS_STOP || eType == NVR_PTZCTRL_TYPE_FOCUSSTOP || eType == NVR_PTZCTRL_TYPE_ZOOMSTOP)
	{
	    g_bStoping=TRUE;
		NvrFixDevStartAllPtzTask();
	}
	else
	{
	    g_bStoping=FALSE;
		if(eType != NVR_PTZCTRL_TYPE_FOCUSAUTO || eType != NVR_PTZCTRL_TYPE_IRIS_AUTO)
		{
			NvrFixDevStopAllPtzTask();
		}
	}


	if(g_bySerialID[wChnId][SERIAL_TYPE_RECHENGXIANG] == SERIAL_TYPE_RECHENGXIANG)
	{	
		if(g_byHotImageType == NVR_HOTIMAGE_TYPE_IRAYFT2)
		{
			NvrFixDevSetFocusZoomXcoreFTII(SERIAL_TYPE_RECHENGXIANG, pParam->eCtrlType, pParam->wIspSpeed);
		}
	}
	else
	{
		if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
		{
		    NvrFixDevSendCmd(*pParam);
			//NvrFixDevSetFocusZoomMINJIA2(g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL], pParam->eCtrlType, pParam->wIspSpeed);
		}
		else if(g_byPtzType == NVR_CAP_PTZ_TYPE_ZHONGYOU)
		{
		}
		else //普通相机使用标准指令
		{
			//NvrFixDevSetFocus(g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL], pParam->eCtrlType, pParam->wIspSpeed);
			NvrFixDevSendCmd(*pParam);
		}
	}

    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP && eType != NVR_PTZCTRL_TYPE_IRIS_STOP && eType != NVR_PTZCTRL_TYPE_FOCUSSTOP && eType != NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        
        if(g_bySerialID[wChnId][SERIAL_TYPE_RECHENGXIANG] == SERIAL_TYPE_RECHENGXIANG)
        {
            NvrFixDevStartPTZQuery(SERIAL_TYPE_RECHENGXIANG);
        }
        else
        {
            NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
        }
    }

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlIris(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	if(eType == NVR_PTZCTRL_TYPE_IRIS_STOP || eType == NVR_PTZCTRL_TYPE_FOCUSSTOP || eType == NVR_PTZCTRL_TYPE_ZOOMSTOP)
	{
	    g_bStoping =TRUE;
		NvrFixDevStartAllPtzTask();
	}
	else
	{
	    g_bStoping =FALSE;
		NvrFixDevStopAllPtzTask();
	}


	if(g_bySerialID[wChnId][SERIAL_TYPE_RECHENGXIANG] == SERIAL_TYPE_RECHENGXIANG)
	{	
		if(g_byHotImageType == NVR_HOTIMAGE_TYPE_IRAYFT2)
		{
			NvrFixDevSetFocusZoomXcoreFTII(SERIAL_TYPE_RECHENGXIANG, pParam->eCtrlType, pParam->wIspSpeed);
		}
	}
	else
	{
		if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
		{
			NvrFixDevSendCmd(*pParam);
			//NvrFixDevSetFocusZoomMINJIA2(g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL], pParam->eCtrlType, pParam->wIspSpeed);
		}
		else if(g_byPtzType == NVR_CAP_PTZ_TYPE_ZHONGYOU)
		{
		}
		else //普通相机使用标准指令
		{
			//NvrFixDevSetIris(g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL], pParam->eCtrlType, pParam->wIspSpeed);
			NvrFixDevSendCmd(*pParam);
		}
	}


	return NVR_ERR__OK;

}

NVRSTATUS NvrDevPtzCtrlVerQuery(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
    
	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlReset(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

    if (240425 == g_tFixInterCapInfo.tFixSoftInternalCap.dwCustomOperation)
    {
        //俄罗斯定制，ptz复位按键临时执行电动镜头复位操作
        u32 dwIspParam = 0;
        dwIspParam= 1;
        IspActionParam(0, ISP_ACT_SF_CALIBRATE, &dwIspParam);
        dwIspParam= 0;
        IspActionParam(0, ISP_ACT_SF_CALIBRATE, &dwIspParam);
        dwIspParam= 12803;
        IspActionParam(0, ISP_ACT_SF_CALIBRATE, &dwIspParam);
    }

	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}
	NvrFixDevSendCmd(*pParam);
    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP && eType != NVR_PTZCTRL_TYPE_IRIS_STOP && eType != NVR_PTZCTRL_TYPE_FOCUSSTOP && eType != NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlPresetSave(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	s32 nZmPos = 0;
	s32 nFcsPos = 0;
	TNvrPresetInfo tIpcPresetInfo = {0};
	NvrFixCfgGetPresetInfo(pParam->wNumber, &tIpcPresetInfo);
	NvrFixDevGetZoomPosition(&nZmPos);
	NvrFixDevGetFocusPosition(&nFcsPos);
	tIpcPresetInfo.bIsSet = TRUE;
	tIpcPresetInfo.nZoomPos = nZmPos;
	tIpcPresetInfo.nFoucsPos = nFcsPos;
	tIpcPresetInfo.wVPos = g_tPowerOffMemCfg[0].wVerticalPos;
	tIpcPresetInfo.wHPos = g_tPowerOffMemCfg[0].wHorizonPos;
	NvrFixCfgSetPresetInfo(pParam->wNumber, &tIpcPresetInfo);


	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}
	NvrFixDevSendCmd(*pParam);
	//NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzPresetRemoveAll(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	u16 i = 0;
	TNvrPresetInfo atIpcPresetInfo[NVR_PUI_PRESET_MAX_NUM];
	memset(atIpcPresetInfo, 0, sizeof(atIpcPresetInfo));
	NvrFixDevStopAllPtzTask();
	//清除(非特殊)预置位信息
	NvrFixCfgGetParamByFlag(atIpcPresetInfo, sizeof(atIpcPresetInfo), NVR_PTZ_MODULE_PRESET_PARAM);
	for (i = 0; i < g_tPtzCapInfo.tPresetNum.dwMaxValue; i++)
	{
		if (FALSE == atIpcPresetInfo[i].bSpecial)
		{
			atIpcPresetInfo[i].bIsSet = FALSE;
			atIpcPresetInfo[i].wVPos = 0;
			atIpcPresetInfo[i].wHPos = 0;
			snprintf((s8*)atIpcPresetInfo[i].abyAlias, sizeof(atIpcPresetInfo[i].abyAlias), "preset-%u", i+1);
			atIpcPresetInfo[i].dwAliasLen = strlen((s8*)atIpcPresetInfo[i].abyAlias);
		}
	}
	NvrFixCfgSetParamByFlag(atIpcPresetInfo, sizeof(atIpcPresetInfo), NVR_PTZ_MODULE_PRESET_PARAM);
	NvrFixDevStartAllPtzTask();

	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlPresetDel(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	//该操作由客户端直接操作，重启任务
	TNvrPresetInfo tIpcPresetInfo = {0};

	if (!pParam->bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}


	NvrFixCfgGetPresetInfo(pParam->wNumber, &tIpcPresetInfo);


	//过滤特殊预置位,不可删除及关联其他
	if (TRUE == tIpcPresetInfo.bSpecial)
	{
		PRINTDBG("%s can't remove special preset-%u\n", __FUNCTION__, pParam->wNumber+1);
		return NVR_ERR__ERROR;
	}	

	///<TODO，预置位和移动侦测关联操作待处理
	tIpcPresetInfo.bIsSet = FALSE;
	tIpcPresetInfo.dwAliasLen = NVR_PUI_PRESET_NAME_BUF_LEN;
	sprintf((s8 *)tIpcPresetInfo.abyAlias, "preset-%u", pParam->wNumber+1);
	tIpcPresetInfo.wVPos = 0;
	tIpcPresetInfo.wHPos = 0;
	NvrFixCfgSetPresetInfo(pParam->wNumber, &tIpcPresetInfo);


	PRINTDBG("NvrDevPtzCtrlPresetDel %d\n", pParam->wNumber-1);
	NvrFixDevSendCmd(*pParam);

	
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlPresetLoad(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{		


	TNvrPresetInfo tIpcPresetInfo = {0};
	TNvrPtzBasicState tPtzState;

	mzero(tPtzState);	
	NvrFixCfgGetPresetInfo((*pParam).wNumber, &tIpcPresetInfo);

	
	//预置位不存在则返回
	if(FALSE == tIpcPresetInfo.bIsSet)
	{
		return NVR_ERR__OK;
	}

	g_byPtzPresetId = (*pParam).wNumber + 1;

	NvrFixIspEventNotify(NVR_FIX_ISP_EVENT_KEY_FIX_START);

	//该操作由客户端直接操作，重启任务
	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
		
	}

	NvrFixDevSendCmd(*pParam);
	//NvrFixDevPreset((*pParam).wNumber);

    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP && eType != NVR_PTZCTRL_TYPE_IRIS_STOP && eType != NVR_PTZCTRL_TYPE_FOCUSSTOP && eType != NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }
	return NVR_ERR__OK;

}


NVRSTATUS NvrDevPtzCtrlLightOpen(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzCtrlLightClose(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlWiperOpen(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzCtrlWiperClose(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzCtrlHScanStart(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	//该操作由客户端直接操作，重启任务
	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}
	
	NvrFixDevSetHorizonState(TRUE);
	NvrFixDevSendCmd(*pParam);
    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP && eType != NVR_PTZCTRL_TYPE_IRIS_STOP && eType != NVR_PTZCTRL_TYPE_FOCUSSTOP && eType != NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzCtrlHScanStop(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	//该操作由客户端直接操作，重启任务
	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}
	NvrFixDevSetHorizonState(FALSE);
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

TNvrPathCrsInfo atPathCrsInfo[NVR_MAX_PATHCRUISE_NUM] = {0};

NVRSTATUS NvrDevPtzCtrlPathCruiseStart(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	
	TNvrDevSmartTransParam tCtrlSourceParam;
	NvrFixCfgGetParamByFlag(&atPathCrsInfo,NVR_MAX_PATHCRUISE_NUM*sizeof(TNvrPathCrsInfo),NVR_PTZ_MODULE_PATH_CRS_PARAM);
	
	NvrPuiSetCurrentSourceType(&tCtrlSourceParam);

	//当前路径存在已设置的预置位
	if (atPathCrsInfo[(*pParam).wNumber - 1].wPresetCnt > 0)
	{
		OsApi_TimerStop(g_hPathCrsTimer);
	
		//该操作由客户端直接操作
		if (!(*pParam).bTaskTrg)
		{
			NvrFixDevSetMotorMovingState(TRUE);
			NvrFixDevStopAllPtzTask();
			//记录客户端设置的巡航用于自动维护时恢复
			memcpy(&g_tAutoRestoreTask,pParam, sizeof(TNvrPtzCtrlInfo));
		}
		NvrFixDevStartPathCrs((*pParam).wNumber - 1);
	}
	else
	{
		PRINTDBG("NvrPtzDevDealCamCmd path cruise, Current path no valid preset.\n");
	}

    //会触发预置位，所以此处不用调用
    //NvrFixDevStartPTZQuery();

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlPathCruiseStop(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	if (!(*pParam).bTaskTrg)
	{
		NvrFixDevSetMotorMovingState(FALSE);
		NvrFixDevStopAllPtzTask();//防止本应成对的操作,单独停止，造成无法恢复定时任务
		NvrFixDevStartAllPtzTask();
		//非自动维护的原因停止巡航则不恢复
		if (g_byAutoRestoreState == FALSE)
		{
			mzero(g_tAutoRestoreTask);
		}
	}
	OsApi_TimerStop(g_hPathCrsTimer);
	
	g_byPathCrsState = FALSE;
	g_byPathCruising = FALSE;
	NvrFixDevGetHvPos(NVR_PTZ_DEV_GETHVPOS_DELAY);

	return NVR_ERR__OK;
}


///<平面坐标映射成球面坐标
BOOL32 NvrFixPixelConvertToAngularCoordinate(double pan, double tilt,float fHangle,float fVangle,PixelCoordinate pc, AngularCoordinate *ptAnglec)
{
	double Vpixels = 0.0;
	double Hpixels = 0.0;
	double angle_tilt;//相对于中心倾斜角的增量角
	double angle_pan; //相对于中心的摇射角的增量角
	double temp_angle = 0;
	double temp = 3.14159/180.0;//用于将角度值转换为弧度值
	double Hfov = 0.0;
	double Vfov = 0.0;

	int xpixel  = 0;
	int ypixel = 0;

	Vpixels = 10000/2; //v7画布为10000*10000,不是根据具体的像素来确定的。
	Hpixels = 10000/2;

    Hfov = fHangle/2;
	Vfov = fVangle/2;
  
	xpixel = Hpixels - pc.x;
	ypixel = pc.y - Vpixels;

	angle_tilt = atan(tan(Vfov*temp)* ypixel/Vpixels);
	angle_pan = atan(tan(Hfov*temp)*xpixel/Hpixels);
	
	temp_angle = atan(cos(angle_tilt) *tan(angle_pan));
	ptAnglec->titl = asin(cos(temp_angle) *sin((tilt*temp+angle_tilt)));
	ptAnglec->pan =  pan*temp + asin(sin(temp_angle)/cos(ptAnglec->titl));

	PRINTDBG("ac: (%f,%f)\n",((ptAnglec->pan)*180)/3.14159, ((ptAnglec->titl)*180)/3.14159);
	return TRUE;
}

#if 0

NVRSTATUS NvrDevPtzCtrlGoPoint(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{	
		PRINTDBG("eType:%d,x:%d,y:%d,w:%d,h:%d,winw:%d,winh:%d.\n",eType,(*pParam).wXposition,(*pParam).wYposition,(*pParam).wWide,(*pParam).wHeight,(*pParam).wWinWide,(*pParam).wWinHeight);


	double dPan = 0.0;
	double dTilt = 0.0;
	float fHfov = 0.0;
	s32 nZoomPos;
	u32 dwTarZoomPos = 0;
	u32 dwSrcTarZoomPos = 0;
	u32 dwHVPos = 0;
	u16 wReportVPos = 0;
	u16 wTarHPos = 0;
	u16 wTarVpos = 0;

	u8 abyCmd[12] = {0};
	u32 byCmdLen,wRealLen;
	TNvrPowerOffRsmCfg tPowerOffRsmCfg;
	BOOL32 bDealPt = TRUE;
	BOOL32 bDealZoom = FALSE;

	TNvrPtzCtrlInfo tPtzCtrlInfo;
	PixelCoordinate tPoint_xy;
	AngularCoordinate tMaskAngleCoord; 	

	NvrPtzDevGetZoomPosition(&nZoomPos);
	//NvrPuiSetCurrentSourceType(&tCtrlSourceParam);
	

	///<根据云台特性、机芯是否反装以及画面是否翻转等，对框选信息进行转换
	///<achCmd[3]/achCmd[4]（坐标原点是画面中心点，x和y坐标是框的中心点）以及tCtrlInfo.wXposition/tCtrlInfo.wYposition（坐标原点是画面左上顶点，x和y坐标是框的中心点）都需要转换，前者用于机芯（框选缩放由云台实现，机芯传框选区域信息给云台），后者用于框选缩放由机芯实现的整机


	dPan = (double)g_dwCurrentHAngle/100;
	dTilt = (double)g_dwCurrentVAngle>9000 ? ((double)g_dwCurrentVAngle-36000)/100:(double)g_dwCurrentVAngle/100;
	NvrFixGetZoomHAngle(nZoomPos, &fHfov);
	mzero(tPoint_xy);

	///<此处坐标原点是画面左上顶点，x和y坐标是框的中心点，另外NvrPtzDevConvertSerialCode()中计算出来的achCmd[]对应的坐标和宽高因为只是u8类型，画布大小超过255*255时会溢出，所以此处不用achCmd[]的值
	tPoint_xy.x = (*pParam).wXposition*10000/(*pParam).wWinWide;
	tPoint_xy.y = (*pParam).wYposition*10000/(*pParam).wWinHeight;


	mzero(tMaskAngleCoord);
	
	NvrFixPixelConvertToAngularCoordinate(dPan, dTilt, fHfov, tPoint_xy, &tMaskAngleCoord);
	wTarHPos = tMaskAngleCoord.pan<0 ? 36000+tMaskAngleCoord.pan*180*100/3.14159:tMaskAngleCoord.pan*180*100/3.14159;
	wTarHPos = wTarHPos%36000;
	wTarVpos = tMaskAngleCoord.titl<0 ? 36000+tMaskAngleCoord.titl*180*100/3.14159:tMaskAngleCoord.titl*180*100/3.14159;
	wTarVpos = wTarVpos%36000;

	//根据云台结构上的范围修改垂直角度
	if(wTarVpos>=27000)
	{
		if((g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue >= 27000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	else 
	{
		if((wTarVpos>g_tPtzCapInfo.tTAngleRange.dwMaxValue) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tTAngleRange.dwMaxValue;
		}
		if(g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos)
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	PRINTDBG("*********** g_dwCurrentHAngle: %d, g_dwCurrentVAngle: %d, pan:%f, tilt:%f, x:%d, y:%d, zmposition: %d, Hfov:%f, tarhpos:%d, tarvpos:%d ***********\n",
		g_dwCurrentHAngle, g_dwCurrentVAngle, dPan, dTilt, tPoint_xy.x, tPoint_xy.y, nZoomPos, fHfov, wTarHPos, wTarVpos);

	///<计算tarzoom
	if((*pParam).wWide != 0)
	{
		NvrFixDevGetZoomPosition(&nZoomPos);
		nZoomPos = ((s32)(float)nZoomPos/(10000/(float)(*pParam).wWide));

		nZoomPos = nZoomPos<=100?100:nZoomPos;
		nZoomPos = nZoomPos>g_dwMaxZoom?g_dwMaxZoom:nZoomPos;
		dwTarZoomPos = nZoomPos;
		PRINTDBG("real tarzoom is %d\n", dwTarZoomPos);
		bDealZoom = TRUE;
	}

	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();

	PRINTDBG("DealCmd dealpt %u,dealzoom %u\n",bDealPt,bDealZoom);
	PRINTDBG("DealCmd byPtzType %u\n", g_tPtzCap.byPtzType);


	//敏佳云台
	{
		abyCmd[0] = 0xEE;
		abyCmd[1] = 0xC;
		abyCmd[2] = 0x2;
		abyCmd[3] = 0x8;
		abyCmd[4] = 0x6;
		
		if (wTarHPos == g_dwCurrentHAngle)
		{
			abyCmd[5] = 0xFF;
			abyCmd[6] = 0xFF;
		}
		else
		{
			abyCmd[6] = (wTarHPos & 0xFF);
			abyCmd[5] = (wTarHPos & 0xFF00)>> 8;
		}
		if (wTarVpos == g_dwCurrentVAngle)
		{
			abyCmd[7] = 0xFF;
			abyCmd[8] = 0xFF;
		}
		else
		{
			abyCmd[8] = (wTarVpos & 0xFF);
			abyCmd[7] = (wTarVpos & 0xFF00)>> 8;
		}
		if (!bDealZoom)
		{ 
			abyCmd[10] = 0xFF;
			abyCmd[9] = 0xFF; 
		}
		else
		{
            abyCmd[10] = dwTarZoomPos & 0xFF;
            abyCmd[9] = (dwTarZoomPos & 0xFF00)>>8; 
		}
		abyCmd[11] = (NvrFixDevCalcCheckSum(abyCmd,12) + 0xEE);
		byCmdLen = 12;
		NvrDevWriteSerial(abyCmd, byCmdLen, &wRealLen, g_byPtzCtrlSerialId);
	}


	mzero(tPowerOffRsmCfg);
	NvrFixCfgGetParamByFlag(&tPowerOffRsmCfg, sizeof(TNvrPowerOffRsmCfg), NVR_PTZ_MODULE_POWE_RSM_PARAM);		
	//记录断电记忆值
	if (tPowerOffRsmCfg.bEnable && NVR_DEV_POWEROFF_MEMORY == tPowerOffRsmCfg.eResumeMode)
	{
		if(bDealZoom)
		{
			//记录断电记忆zoom值
			g_tPowerOffMemCfg[0].nZoomPos = dwTarZoomPos;
		}
		//记录断电记忆pt值
		if((*pParam).dwRes != 1 && bDealPt)
		{
			g_tPowerOffMemCfg[0].wHorizonPos = g_dwCurrentHAngle;
			g_tPowerOffMemCfg[0].wVerticalPos = g_dwCurrentVAngle;
		}
		OsApi_TimerSet(g_hPowerOffMemTimer, tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_MEMORY] * 1000, NvrFixDevPowerOffMemTimerCB, NULL);
	}

	//3d缩放后通知聚焦
	//NvrPtzIspEventNotify(NVR_PTZ_ISP_EVENT_KEY_PTZ_STOP);
	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzCtrlZoomPart(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{				
	double dPan = 0.0;
	double dTilt = 0.0;
	float fHfov = 0.0;
	s32 nZoomPos;
	u32 dwTarZoomPos = 0;
	u32 dwSrcTarZoomPos = 0;
	u32 dwHVPos = 0;
	u16 wReportVPos = 0;
	u16 wTarHPos = 0;
	u16 wTarVpos = 0;

	u8 abyCmd[12] = {0};
	u32 byCmdLen,wRealLen;
	TNvrPowerOffRsmCfg tPowerOffRsmCfg;
	BOOL32 bDealPt = TRUE;
	BOOL32 bDealZoom = FALSE;

	TNvrPtzCtrlInfo tPtzCtrlInfo;
	PixelCoordinate tPoint_xy;
	AngularCoordinate tMaskAngleCoord; 	

	NvrFixDevGetZoomPosition(&nZoomPos);
	//NvrPuiSetCurrentSourceType(&tCtrlSourceParam);

	
	PRINTDBG("eType:%d,x:%d,y:%d,w:%d,h:%d,winw:%d,winh:%d.\n",eType,(*pParam).wXposition,(*pParam).wYposition,(*pParam).wWide,(*pParam).wHeight,(*pParam).wWinWide,(*pParam).wWinHeight);

	///<根据云台特性、机芯是否反装以及画面是否翻转等，对框选信息进行转换
	///<achCmd[3]/achCmd[4]（坐标原点是画面中心点，x和y坐标是框的中心点）以及tCtrlInfo.wXposition/tCtrlInfo.wYposition（坐标原点是画面左上顶点，x和y坐标是框的中心点）都需要转换，前者用于机芯（框选缩放由云台实现，机芯传框选区域信息给云台），后者用于框选缩放由机芯实现的整机


	dPan = (double)g_dwCurrentHAngle/100;
	dTilt = (double)g_dwCurrentVAngle>9000 ? ((double)g_dwCurrentVAngle-36000)/100:(double)g_dwCurrentVAngle/100;
	NvrFixGetZoomHAngle(nZoomPos, &fHfov);
	mzero(tPoint_xy);

	///<此处坐标原点是画面左上顶点，x和y坐标是框的中心点，另外NvrPtzDevConvertSerialCode()中计算出来的achCmd[]对应的坐标和宽高因为只是u8类型，画布大小超过255*255时会溢出，所以此处不用achCmd[]的值
	tPoint_xy.x = (*pParam).wXposition*10000/(*pParam).wWinWide;
	tPoint_xy.y = (*pParam).wYposition*10000/(*pParam).wWinHeight;


	mzero(tMaskAngleCoord);
	
	NvrFixPixelConvertToAngularCoordinate(dPan, dTilt, fHfov, tPoint_xy, &tMaskAngleCoord);
	wTarHPos = tMaskAngleCoord.pan<0 ? 36000+tMaskAngleCoord.pan*180*100/3.14159:tMaskAngleCoord.pan*180*100/3.14159;
	wTarHPos = wTarHPos%36000;
	wTarVpos = tMaskAngleCoord.titl<0 ? 36000+tMaskAngleCoord.titl*180*100/3.14159:tMaskAngleCoord.titl*180*100/3.14159;
	wTarVpos = wTarVpos%36000;

	//根据云台结构上的范围修改垂直角度
	if(wTarVpos>=27000)
	{
		if((g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue >= 27000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	else 
	{
		if((wTarVpos>g_tPtzCapInfo.tTAngleRange.dwMaxValue) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tTAngleRange.dwMaxValue;
		}
		if(g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos)
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	PRINTDBG("*********** g_dwCurrentHAngle: %d, g_dwCurrentVAngle: %d, pan:%f, tilt:%f, x:%d, y:%d, zmposition: %d, Hfov:%f, tarhpos:%d, tarvpos:%d ***********\n",
		g_dwCurrentHAngle, g_dwCurrentVAngle, dPan, dTilt, tPoint_xy.x, tPoint_xy.y, nZoomPos, fHfov, wTarHPos, wTarVpos);

	///<计算tarzoom
	if((*pParam).wWide != 0)
	{
		NvrFixDevGetZoomPosition(&nZoomPos);
		nZoomPos = ((s32)(float)nZoomPos/(10000/(float)(*pParam).wWide));

		nZoomPos = nZoomPos<=100?100:nZoomPos;
		nZoomPos = nZoomPos>g_dwMaxZoom?g_dwMaxZoom:nZoomPos;
		dwTarZoomPos = nZoomPos;
		PRINTDBG("real tarzoom is %d\n", dwTarZoomPos);
		bDealZoom = TRUE;
	}

	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();

	PRINTDBG("DealCmd dealpt %u,dealzoom %u\n",bDealPt,bDealZoom);
	PRINTDBG("DealCmd byPtzType %u\n", g_tPtzCap.byPtzType);


	//敏佳云台
	{
		abyCmd[0] = 0xEE;
		abyCmd[1] = 0xC;
		abyCmd[2] = 0x2;
		abyCmd[3] = 0x8;
		abyCmd[4] = 0x6;
		
		if (wTarHPos == g_dwCurrentHAngle)
		{
			abyCmd[5] = 0xFF;
			abyCmd[6] = 0xFF;
		}
		else
		{
			abyCmd[6] = (wTarHPos & 0xFF);
			abyCmd[5] = (wTarHPos & 0xFF00)>> 8;
		}
		if (wTarVpos == g_dwCurrentVAngle)
		{
			abyCmd[7] = 0xFF;
			abyCmd[8] = 0xFF;
		}
		else
		{
			abyCmd[8] = (wTarVpos & 0xFF);
			abyCmd[7] = (wTarVpos & 0xFF00)>> 8;
		}
		if (!bDealZoom)
		{ 
			abyCmd[10] = 0xFF;
			abyCmd[9] = 0xFF; 
		}
		else
		{
            abyCmd[10] = dwTarZoomPos & 0xFF;
            abyCmd[9] = (dwTarZoomPos & 0xFF00)>>8; 
		}
		abyCmd[11] = (NvrFixDevCalcCheckSum(abyCmd,12) + 0xEE);
		byCmdLen = 12;
		NvrDevWriteSerial(abyCmd, byCmdLen, &wRealLen, g_byPtzCtrlSerialId);
	}


	mzero(tPowerOffRsmCfg);
	NvrFixCfgGetParamByFlag(&tPowerOffRsmCfg, sizeof(TNvrPowerOffRsmCfg), NVR_PTZ_MODULE_POWE_RSM_PARAM);		
	//记录断电记忆值
	if (tPowerOffRsmCfg.bEnable && NVR_DEV_POWEROFF_MEMORY == tPowerOffRsmCfg.eResumeMode)
	{
		if(bDealZoom)
		{
			//记录断电记忆zoom值
			g_tPowerOffMemCfg[0].nZoomPos = dwTarZoomPos;
		}
		//记录断电记忆pt值
		if((*pParam).dwRes != 1 && bDealPt)
		{
			g_tPowerOffMemCfg[0].wHorizonPos = g_dwCurrentHAngle;
			g_tPowerOffMemCfg[0].wVerticalPos = g_dwCurrentVAngle;
		}
		OsApi_TimerSet(g_hPowerOffMemTimer, tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_MEMORY] * 1000, NvrFixDevPowerOffMemTimerCB, NULL);
	}


	//3d缩放后通知聚焦
	//NvrPtzIspEventNotify(NVR_PTZ_ISP_EVENT_KEY_PTZ_STOP);


	return NVR_ERR__OK;

}

#endif


NVRSTATUS NvrDevPtzCtrl3DCtrl(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{				
	double dPan = 0.0;
	double dTilt = 0.0;
	float fHfov = 0.0;
    float fVfov = 0.0;
	s32 nZoomPos;
	u32 dwTarZoomPos = 0;
	u16 wTarHPos = 0;
	u16 wTarVpos = 0;

	u8 abyCmd[12] = {0};
	u32 byCmdLen = 0;
	u32 wRealLen = 0;

	BOOL32 bDealPt = TRUE;
	BOOL32 bDealZoom = FALSE;

	PixelCoordinate tPoint_xy;
	AngularCoordinate tMaskAngleCoord; 	

	//NvrFixDevGetZoomPosition(&nZoomPos);
	if(wChnId<=1)
    {
        nZoomPos = g_dwCurrentZoom;
    }
    else
    {
        nZoomPos = g_dwCurrenXcoreZoom;
    }
	//NvrPuiSetCurrentSourceType(&tCtrlSourceParam);
	
	PRINTDBG("eType:%d,x:%d,y:%d,w:%d,h:%d,winw:%d,winh:%d.\n",eType,(*pParam).wXposition,(*pParam).wYposition,(*pParam).wWide,(*pParam).wHeight,(*pParam).wWinWide,(*pParam).wWinHeight);

	///<根据云台特性、机芯是否反装以及画面是否翻转等，对框选信息进行转换
	///<achCmd[3]/achCmd[4]（坐标原点是画面中心点，x和y坐标是框的中心点）以及tCtrlInfo.wXposition/tCtrlInfo.wYposition（坐标原点是画面左上顶点，x和y坐标是框的中心点）都需要转换，前者用于机芯（框选缩放由云台实现，机芯传框选区域信息给云台），后者用于框选缩放由机芯实现的整机

    
	dPan = (double)g_dwCurrentHAngle/100;
	dTilt = (double)g_dwCurrentVAngle>9000 ? ((double)g_dwCurrentVAngle-36000)/100:(double)g_dwCurrentVAngle/100;

    if(wChnId<=1)
    {
	    NvrFixGetZoomHAngle(nZoomPos, &fHfov);
        NvrFixGetZoomVAngle(nZoomPos, &fVfov);
        
    }
    else
    {
        NvrFixIrayGetZoomHAngle(nZoomPos, &fHfov);
        NvrFixIrayGetZoomVAngle(nZoomPos, &fVfov);
    }
	mzero(tPoint_xy);

	///<此处坐标原点是画面左上顶点，x和y坐标是框的中心点，另外NvrPtzDevConvertSerialCode()中计算出来的achCmd[]对应的坐标和宽高因为只是u8类型，画布大小超过255*255时会溢出，所以此处不用achCmd[]的值
	tPoint_xy.x = (*pParam).wXposition*10000/(*pParam).wWinWide;
	tPoint_xy.y = (*pParam).wYposition*10000/(*pParam).wWinHeight;


	mzero(tMaskAngleCoord);
	
	NvrFixPixelConvertToAngularCoordinate(dPan, dTilt,fHfov,fVfov, tPoint_xy, &tMaskAngleCoord);
	wTarHPos = tMaskAngleCoord.pan<0 ? (36000+tMaskAngleCoord.pan*180*100/3.14159):tMaskAngleCoord.pan*180*100/3.14159;
	wTarHPos = wTarHPos%36000;

	wTarVpos = tMaskAngleCoord.titl<0 ? 36000+tMaskAngleCoord.titl*180*100/3.14159:tMaskAngleCoord.titl*180*100/3.14159;
	wTarVpos = wTarVpos%36000;

	//根据云台结构上的范围修改垂直角度
	if(wTarVpos>=27000)
	{
		if((g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue >= 27000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	else 
	{
		if((wTarVpos>g_tPtzCapInfo.tTAngleRange.dwMaxValue) || (g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos))
		{
			wTarVpos = g_tPtzCapInfo.tTAngleRange.dwMaxValue;
		}
		if(g_tPtzCapInfo.tStrucTAngleRange.dwMinValue <= 9000 && g_tPtzCapInfo.tStrucTAngleRange.dwMinValue > wTarVpos)
		{
			wTarVpos = g_tPtzCapInfo.tStrucTAngleRange.dwMinValue;
		}
	}
	PRINTDBG("*********** g_dwCurrentHAngle: %d, g_dwCurrentVAngle: %d, pan:%f, tilt:%f, x:%d, y:%d, zmposition: %d, Hfov:%f, tarhpos:%d, tarvpos:%d ***********\n",
		g_dwCurrentHAngle, g_dwCurrentVAngle, dPan, dTilt, tPoint_xy.x, tPoint_xy.y, nZoomPos, fHfov, wTarHPos, wTarVpos);

	///<计算tarzoom
	if((*pParam).wWide != 0)
	{
        float fZoom;
		if(eType == NVR_PTZCTRL_TYPE_ZOOMWHOLE)
		{
		    if(wChnId<=1)
            {
		        NvrFixGetZoomByRatio(10000*1.0/(*pParam).wWide,nZoomPos,&fZoom);
            }
            else
            {
                NvrFixIrayGetZoomByRatio(10000*1.0/(*pParam).wWide,nZoomPos,&fZoom);
            }
			nZoomPos = fZoom;
		}

		if(NVR_PTZCTRL_TYPE_ZOOMPART == eType)
		{
			if(10000/(float)(*pParam).wWide > 4)
			{
				PRINTDBG("The magnification is more than 5 times\n");
                if(wChnId<=1)
                {
                    NvrFixGetZoomByRatio(1.0/3,nZoomPos,&fZoom);
                }
                else
                {
                    NvrFixIrayGetZoomByRatio(1.0/3,nZoomPos,&fZoom);
                }
				nZoomPos = fZoom;
			}
			else
			{
			    if(wChnId<=1)
                {
			        NvrFixGetZoomByRatio((float)(*pParam).wWide/10000,nZoomPos,&fZoom);
                }
                else
                { 
                    NvrFixIrayGetZoomByRatio((float)(*pParam).wWide/10000,nZoomPos,&fZoom);
                }
                
				nZoomPos = fZoom;
			}
		}

		nZoomPos = nZoomPos<=g_dwMinZoom?g_dwMinZoom:nZoomPos;
		nZoomPos = nZoomPos>g_dwMaxZoom?g_dwMaxZoom:nZoomPos;
		dwTarZoomPos = nZoomPos;
		PRINTDBG("real tarzoom is %d\n", dwTarZoomPos);
		bDealZoom = TRUE;
	}

	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();

	PRINTDBG("DealCmd dealpt %u,dealzoom %u\n",bDealPt,bDealZoom);
	PRINTDBG("DealCmd byPtzType %u\n", g_tPtzCap.byPtzType);


	if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)//敏佳云台
	{
		abyCmd[0] = 0xEE;
		abyCmd[1] = 0xC;
		abyCmd[2] = 0x2;
		abyCmd[3] = 0x8;
		abyCmd[4] = 0x6;
		
		if (wTarHPos == g_dwCurrentHAngle)
		{
			abyCmd[5] = 0xFF;
			abyCmd[6] = 0xFF;
		}
		else
		{
			abyCmd[6] = (wTarHPos & 0xFF);
			abyCmd[5] = (wTarHPos & 0xFF00)>> 8;
		}
		if (wTarVpos == g_dwCurrentVAngle)
		{
			abyCmd[7] = 0xFF;
			abyCmd[8] = 0xFF;
		}
		else
		{
			abyCmd[8] = (wTarVpos & 0xFF);
			abyCmd[7] = (wTarVpos & 0xFF00)>> 8;
		}
		if (!bDealZoom)
		{ 
			abyCmd[10] = 0xFF;
			abyCmd[9] = 0xFF; 
		}
		else
		{
            abyCmd[10] = dwTarZoomPos & 0xFF;
            abyCmd[9] = (dwTarZoomPos & 0xFF00)>>8; 
		}
		abyCmd[11] = (NvrFixDevCalcCheckSum(abyCmd,12) + 0xEE);
		byCmdLen = 12;
        NrvFixDevWaitPTZQueryOverBlock();
		NvrFixDevWriteSerial(g_bySerialID[wChnId][SERIAL_TYPE_PTZCTRL],abyCmd,byCmdLen,&wRealLen);
        NrvFixDevWaitPTZQueryOverUnBlock();
		
	}


	//3d缩放后通知聚焦
	//NvrFixIspEventNotify(NVR_PTZ_ISP_EVENT_KEY_PTZ_STOP);
    if(eType != NVR_PTZCTRL_TYPE_MOVESTOP && eType != NVR_PTZCTRL_TYPE_IRIS_STOP && eType != NVR_PTZCTRL_TYPE_FOCUSSTOP && eType != NVR_PTZCTRL_TYPE_ZOOMSTOP)
    {
        NvrFixDevStartPTZQuery(SERIAL_TYPE_PTZCTRL);
    }

	return NVR_ERR__OK;

}


NVRSTATUS NvrDevPtzCtrlPtzStop(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	/// vsip 2.0平台操作zoom focus iris 后通过此信令停止，由于无法区分具体操作内容，增加对应停止处理
    s32 nRet = 0;
    TNvrDoubleListPushAttr tCmdAttr;
    TNvrPtzCtrlInfo tPtzCtrlInfo;

    mzero(tPtzCtrlInfo);
    tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_ZOOMSTOP;
    NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);

    mzero(tCmdAttr);
    tCmdAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_HIGH;
    tCmdAttr.byMergerType = NVR_QUEUE_NODE_MERGER_NONE;
    tCmdAttr.dwType = (u32)wChnId;//用来传设备id
    tCmdAttr.dwDataLen = sizeof(tPtzCtrlInfo);
    tCmdAttr.pchDataBuf = (s8*)(&tPtzCtrlInfo);
    nRet = NvrQueuePush(g_ptLcDevQueue, &tCmdAttr);
    if (NVR_ERR__OK != nRet)
    {
        PRINTERR( "queue push NVR_PTZCTRL_TYPE_ZOOMSTOP failed, ret:%d\n",nRet);
        return NVR_ERR__ERROR;
    }

    tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_FOCUSSTOP;
    nRet = NvrQueuePush(g_ptLcDevQueue, &tCmdAttr);
    if (NVR_ERR__OK != nRet)
    {
        PRINTERR( "queue push NVR_PTZCTRL_TYPE_FOCUSSTOP failed, ret:%d\n",nRet);
        return NVR_ERR__ERROR;
    }

    tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_IRIS_STOP;
    nRet = NvrQueuePush(g_ptLcDevQueue, &tCmdAttr);
    if (NVR_ERR__OK != nRet)
    {
        PRINTERR( "queue push NVR_PTZCTRL_TYPE_IRIS_STOP failed, ret:%d\n",nRet);
        return NVR_ERR__ERROR;
    }

	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzCruiseScanStop(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzHScanSpeedSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzPresetCreate(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzLimitHLeft(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzLimitHRight(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{

	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzLimitVUp(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzLimitVDown(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzLimitVRemove(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzLimitHRemove(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzLimitHVRemove(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzManualLimitSwithch(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevSavePtzState(*pParam);
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);

	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzScanLimitSwithch(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	NvrFixDevStopAllPtzTask();
	NvrFixDevSavePtzState(*pParam);
	NvrFixDevStartAllPtzTask();
	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;
}

TNvrPathCrsInfo atIpcPathCrsInfo[NVR_MAX_PATHCRUISE_NUM];
NVRSTATUS NvrDevPtzPathCruiseRemoveAll(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	u32 i = 0,j = 0;
	memset(atIpcPathCrsInfo, 0, sizeof(atIpcPathCrsInfo));
	NvrFixDevStopAllPtzTask();
	
	for (i = 0; i < NVR_MAX_PATHCRUISE_NUM; i++)
	{
		atIpcPathCrsInfo[i].wPresetCnt = 0;
	
		for (j = 0; j < g_tPtzCapInfo.tPresetNum.dwMaxValue; j++)
		{
			///<TODO，暂时固定为15
			atIpcPathCrsInfo[i].tPresetInfo[j].wStaytime = 15;
		}
	}
	
	NvrFixCfgSetParamByFlag(atIpcPathCrsInfo, sizeof(atIpcPathCrsInfo), NVR_PTZ_MODULE_PATH_CRS_PARAM);
	
	NvrFixDevStartAllPtzTask();
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzPanPosionSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevTiltPosionSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevTiltPanZero(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevHVPosionSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevHVPosionQuery(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevHPosionQuery(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevVPosionQuery(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzSoftVerQuery(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzRestoreFactory(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzLaserOpen(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzLaserClose(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}
NVRSTATUS NvrDevPtzLaserSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevPtzTimingTaskSet(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	g_tTmingTaskSetParam.bIsSet = TRUE;
	g_tTmingTaskSetParam.byTaskType = pParam->wNumber;
	g_tTmingTaskSetParam.byTaskParam = pParam->eMode;
	NvrFixDevSetTimingTask(pParam->wNumber, pParam->eMode);

    //NvrFixDevStartPTZQuery();水平巡航、预置位时会触发
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzTimingTaskClear(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	g_tTmingTaskSetParam.bIsSet= FALSE;
	NvrFixDevClrTimingTask(TRUE);
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzSetZMRatio(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzSetPTZMRatio(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	/*
	if(PTZTYPE == MINGJIA2)
	{
	}
	if(PTZTYPE == ZHONGYOU)
	{
	}
	*/
	return NVR_ERR__OK;
}

NVRSTATUS NvrDevPtzSetPresetSpeed(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	//该操作由客户端直接操作，重启任务
	
	NvrFixDevSavePtzState(*pParam);
	if (!pParam->bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}

	
	 g_dwPreSetSpd = pParam->dwRes;
	
	NvrFixDevSendCmd(*pParam);
	
	return NVR_ERR__OK;

}


NVRSTATUS NvrDevPtzSetZoomSpeed(const u16 wChnId,ENvrPtzCtrlType eType, TNvrPtzCtrlInfo* pParam)
{
	//该操作由客户端直接操作，重启任务
	
	NvrFixDevSavePtzState(*pParam);
	if (!pParam->bTaskTrg)
	{
		NvrFixDevStopAllPtzTask();
		NvrFixDevStartAllPtzTask();
	}
	g_nZoomSpeed = pParam->dwRes;
	if(NVR_PTZ_ZOOM_SPEED_LOW == pParam->dwRes)
		g_nZoomSpeed = 1;
	else if(NVR_PTZ_ZOOM_SPEED_MID == pParam->dwRes)
		g_nZoomSpeed = 3;
	else
		g_nZoomSpeed = 5;
	//NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACTION_KEY_ZOOM_SPEED, &g_nZoomSpeed);
	
	NvrFixDevSendCmd(*pParam);
	return NVR_ERR__OK;

}


TPtzCmdMap g_tPtzCmdMap[NVR_PTZCTRL_TYPE_MAX] = 
{
	{NVR_PTZCTRL_TYPE_MAX,NULL},											///< 0  与MOVEUP为1对齐
	{NVR_PTZCTRL_TYPE_MOVEUP,  NvrDevPtzCtrlMoveCmd}, 						///< 1	向上移动(初始值禁止修改)
	{NVR_PTZCTRL_TYPE_MOVEDOWN,NvrDevPtzCtrlMoveCmd},						///< 2	向下移动
	{NVR_PTZCTRL_TYPE_MOVELEFT,NvrDevPtzCtrlMoveCmd},						///< 3	向左移动
	{NVR_PTZCTRL_TYPE_MOVERIGHT,NvrDevPtzCtrlMoveCmd},						///< 4	向右移动
	{NVR_PTZCTRL_TYPE_MOVELEFTUP,NvrDevPtzCtrlMoveCmd}, 					///< 5	向左上移动
	{NVR_PTZCTRL_TYPE_MOVELEFTDOWN,NvrDevPtzCtrlMoveCmd},					///< 6	向左下移动
	{NVR_PTZCTRL_TYPE_MOVERIGHTUP,NvrDevPtzCtrlMoveCmd},					///< 7	向右上移动
	{NVR_PTZCTRL_TYPE_MOVERIGHTDOWN,NvrDevPtzCtrlMoveCmd},					///< 8	向右下移动
	{NVR_PTZCTRL_TYPE_MOVESTOP,NvrDevPtzCtrlMoveCmd},						///< 9	停止移动
	{NVR_PTZCTRL_TYPE_ZOOMTELE,NvrDevPtzCtrlZoomFocus},						///< 10 拉近摄像头
	{NVR_PTZCTRL_TYPE_ZOOMWIDE,NvrDevPtzCtrlZoomFocus},						///< 11 拉远摄像头
	{NVR_PTZCTRL_TYPE_ZOOMSTOP,NvrDevPtzCtrlZoomFocus},						///< 12 视野调节停止
	{NVR_PTZCTRL_TYPE_FOCUSFAR,NvrDevPtzCtrlZoomFocus},						///< 13 将焦距调远
	{NVR_PTZCTRL_TYPE_FOCUSNEAR,NvrDevPtzCtrlZoomFocus},						///< 14 将焦距调近
	{NVR_PTZCTRL_TYPE_FOCUSAUTO,NvrDevPtzCtrlZoomFocus},						///< 15 自动调焦
	{NVR_PTZCTRL_TYPE_FOCUSSTOP,NvrDevPtzCtrlZoomFocus},						///< 16 调焦停止
	{NVR_PTZCTRL_TYPE_IRIS_PLUS,NvrDevPtzCtrlIris},						///< 17 光圈调大
	{NVR_PTZCTRL_TYPE_IRIS_MINUS,NvrDevPtzCtrlIris}, 					///< 18 光圈调小
	{NVR_PTZCTRL_TYPE_IRIS_AUTO,NvrDevPtzCtrlIris},						///< 19 光圈自动
	{NVR_PTZCTRL_TYPE_IRIS_STOP,NvrDevPtzCtrlIris},						///< 20 光圈调节停止

	{NVR_PTZCTRL_TYPE_RESET,NvrDevPtzCtrlReset},							///< 21 云台复位
	{NVR_PTZCTRL_TYPE_PRESET_SAVE,NvrDevPtzCtrlPresetSave},					///< 22 保存预置位
	{NVR_PTZCTRL_TYPE_PRESET_DEL,NvrDevPtzCtrlPresetDel}, 					///< 23 删除预置位
	{NVR_PTZCTRL_TYPE_PRESET_LOAD,NvrDevPtzCtrlPresetLoad},					///< 24 加载预置位
	{NVR_PTZCTRL_TYPE_LIGHT_OPEN,NvrDevPtzCtrlLightOpen}, 					///< 25 开灯
	{NVR_PTZCTRL_TYPE_LIGHT_CLOSE,NvrDevPtzCtrlLightClose},					///< 26 关灯
	{NVR_PTZCTRL_TYPE_WIPER_OPEN,NvrDevPtzCtrlWiperOpen}, 					///< 27 雨刷开启
	{NVR_PTZCTRL_TYPE_WIPER_CLOSE,NvrDevPtzCtrlWiperClose},					///< 28 雨刷关闭
	{NVR_PTZCTRL_TYPE_HORIZONSCAN_STATRT,NvrDevPtzCtrlHScanStart}, 			///< 29 开始水平巡航
	{NVR_PTZCTRL_TYPE_HORIZONSCAN_STOP,NvrDevPtzCtrlHScanStop},				///< 30 停止水平巡航
	{NVR_PTZCTRL_TYPE_PATH_CRUISE_START,NvrDevPtzCtrlPathCruiseStart},				///< 31 开始路径巡航
	{NVR_PTZCTRL_TYPE_PATH_CRUISE_STOP,NvrDevPtzCtrlPathCruiseStop},				///< 32 停止路径巡航
	{NVR_PTZCTRL_TYPE_GOTOPOINT,NvrDevPtzCtrl3DCtrl},						///< 33 中心定位(双击居中)
	{NVR_PTZCTRL_TYPE_ZOOMPART,NvrDevPtzCtrl3DCtrl},						///< 34 局部放大(框选放大)
	{NVR_PTZCTRL_TYPE_ZOOMWHOLE,NvrDevPtzCtrl3DCtrl},						///< 35 局部缩小(框选缩小)
	{NVR_PTZCTRL_TYPE_PTZSTOP,NvrDevPtzCtrlPtzStop},						///< 36 特殊类型，业务从00 00 00 00 指令到结构体转换时
															///<	无法区分pt/zoom/focus/iris停止
	{NVR_PTZCTRL_TYPE_CRUISE_SCAN_STOP,NvrDevPtzCruiseScanStop},				///< 37 特殊类型，业务从00 01 00 05 指令到结构体转换时
															///<	无法区分是水平巡航/路径巡航停止
	{NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET,NvrDevPtzHScanSpeedSet},			///< 38 设置水平扫描速度
	{NVR_PTZCTRL_PRESET_CREATE,NvrDevPtzPresetCreate},					   ///< 39 创建预置位(入参中不指定预置位,NULL},出参中返回新的预置位)(目前只有onvif协议)

	{NVR_PTZCTRL_LIMIT_HORIZONALLEFT,NvrDevPtzLimitHLeft}, 				///< 40 设置水平左限位
	{NVR_PTZCTRL_LIMIT_HORIZONALRIGHT,NvrDevPtzLimitHRight},				///< 41 设置水平右限位
	{NVR_PTZCTRL_LIMIT_VERTICALUP,NvrDevPtzLimitVUp},					///< 42 设置垂直上限位
	{NVR_PTZCTRL_LIMIT_VERTICALDOWN,NvrDevPtzLimitVDown},					///< 43 设置垂直下限位
	{NVR_PTZCTRL_LIMIT_HORIZONAL_REMOVE,NvrDevPtzLimitHRemove},			///< 44 清除水平限位
	{NVR_PTZCTRL_LIMIT_VERTICAL_REMOVE,NvrDevPtzLimitVRemove},			///< 45 清除垂直限位
	{NVR_PTZCTRL_LIMIT_CLEAR,NvrDevPtzLimitHVRemove}, 					///< 46 清除限位
	{NVR_PTZCTRL_MANUALLIMITSWITCH_SET,NvrDevPtzManualLimitSwithch},				///< 47 键控限位(开关)
	{NVR_PTZCTRL_SCANLIMITSWITCH_SET,NvrDevPtzScanLimitSwithch}, 			///< 48 扫描限位(开关)
	
	{NVR_PTZCTRL_SYNCSCAN_REC,NULL},					///< 49 花样扫描记录
	{NVR_PTZCTRL_SYNCSCAN_STOPREC,NULL},					///< 50 花样扫描停止记录
	{NVR_PTZCTRL_SYNCSCAN_PREVIEW,NULL},					///< 51 花样扫描预览
	{NVR_PTZCTRL_SYNCSCAN_STOPPREVIEW,NULL},				///< 52 花样扫描停止预览
	{NVR_PTZCTRL_SYNCSCAN_DELETE,NULL}, 				///< 53 花样扫描删除路径

	{NVR_PTZCTRL_PRESET_REMOVE_ALL,NvrDevPtzPresetRemoveAll},				///< 54 清除所有预置位
	{NVR_PTZCTRL_PATH_CRUISE_REMOVE_ALL,NvrDevPtzPathCruiseRemoveAll},			///< 55 清除所有巡航路径
	{NVR_PTZCTRL_SYNCSCAN_DELETE_ALL,NULL}, 			///< 56 清除所有花样扫描路

	{NVR_PTZCTRL_PANPOSION_SET,NvrDevPtzCtrlPtzOther},						///< 57 设置水平位置
	{NVR_PTZCTRL_TILTPOSION_SET,NvrDevPtzCtrlPtzOther},						///< 58 设置俯仰位置
	{NVR_PTZCTRL_TURNTO_MACHINEZERO,NvrDevPtzCtrlPtzOther},					///< 59 水平、俯仰电机转动到零位（零位校正,NULL},到机械零位）

	{NVR_PTZCTRL_PTPOSION_SET,NvrDevPtzCtrlPtzOther},						///< 60 设置水平俯仰位置
	{NVR_PTZCTRL_PTPOSION_QUERY,NvrDevPtzCtrlPtzOther},						///< 61 查询水平俯仰位置
	{NVR_PTZCTRL_PANPOSION_QUERY,NvrDevPtzCtrlPtzOther}, 				///< 62 查询水平位置
	{NVR_PTZCTRL_TILTPOSION_QUERY,NvrDevPtzCtrlPtzOther},				///< 63 查询俯仰位置
	{NVR_PTZCTRL_PTZ_CALIBRATE,NULL},						///< 64 开始枪球标定
	{NVR_PTZCTRL_PTZ_LASER_ANGLE_INCRE,NvrDevLaserCtrlMoveCmd},				///< 65 激光照射角度增大
	{NVR_PTZCTRL_PTZ_LASER_ANGLE_DECRE,NvrDevLaserCtrlMoveCmd},				///< 66 激光照射角度减小
	{NVR_PTZCTRL_SOFTVER_QUERY,NvrDevPtzCtrlPtzOther},						///< 67 查询云台版本

	{NVR_PTZCTRL_RESTORE_FACTORY,NvrDevPtzCtrlPtzOther}, 					///< 68 云台恢复出厂，包括预置位、路径巡航、定时任务等

	{NVR_PTZCTRL_LASERCTRL_OPEN,NvrDevPtzLaserOpen},						///< 69 激光开
	{NVR_PTZCTRL_LASERCTRL_CLOSE,NvrDevPtzLaserClose}, 				///< 70 激光关
	{NVR_PTZCTRL_LASERCTRL_ANGLE,NULL}, 				///< 71 设激光角度
	{NVR_PTZCTRL_LASERCTRL_RESET,NULL}, 				///< 72 设激光复位，用于激光的自检

	{NVR_PTZCTRL_ZOOMINFO_SEND,NULL},						///< 73 发送机芯变倍信息给云台
	{NVR_PTZCTRL_WIPER_RESET,NULL}, 					///< 74 雨刷复位	
	{NVR_PTZCTRL_TIMINGTASK_SET,NvrDevPtzTimingTaskSet},						///< 75 定时任务设置	
	{NVR_PTZCTRL_TIMINGTASK_CLEAR,NvrDevPtzTimingTaskClear},					///< 76 定时任务清楚
	{NVR_PTZCTRL_SET_VOUT_OSD_MENU,NULL},					///< 77 字幕菜单入口
	{NVR_PTZCTRL_SET_PRESET_PLAN_ID,NULL},					///< 78 设置预置位联动方案
	{NVR_PTZCTRL_AQUILLA_SET_PTMOVE,NULL},					///< 79 安奎拉协议设置球机在规定的延迟时间内到达规定位置
	{NVR_PTZCTRL_AQUILLA_SET_PTZAngle,NULL},				///< 80 安奎拉协议设置云台角度
	{NVR_PTZCTRL_AQUILLA_SET_SPEED,NULL},					///< 81 安奎拉协议设置速度命令
	{NVR_PTZCTRL_HORIZONTAL_TURN,NULL}, 				///< 82 云台水平180度翻转
	{NVR_PTZCTRL_VERTICA_RANGE,NULL},						///< 83 设置云台垂直角度范围
	{NVR_PTZCTRL_AUTOFLIP_SET,NULL},						///< 84 自动翻转(开关)
	{NVR_PTZCTRL_DEPTHRATESPEED_SET,NULL},				///< 85 景深比例(开关)
	{NVR_PTZCTRL_IRCTRL_OPEN,NULL}, 					///< 86 红外开
	{NVR_PTZCTRL_IRCTRL_CLOSE,NULL},						///< 87 红外关
	{NVR_PTZCTRL_DEFROST_SET,NULL}, 					///< 88 除霜(开关)
	{NVR_PTZCTRL_DEMIST_SET,NULL},							///< 89 除雾(开关)
	{NVR_PTZCTRL_POWERON_MODE_SET,NULL},					///< 90 设置上电模式，用于布控球
	{NVR_PTZCTRL_POWEROFF_MODE_SET,NULL},					///< 91 设置断电关机模式，用于布控球
	{NVR_PTZCTRL_LEDMAINSWITCH,NULL},						///< 92 LED指示灯总开关，用于布控球
	{NVR_PTZCTRL_EXT_WIFI_SWITCH,NULL}, 				///< 93 外置扩展WIFI开关，用于布控球
	{NVR_PTZCTRL_REPORT_PT_REAL_TIME,NULL}, 			///< 94 云台转动时是否一直上报云台角度给机芯开关
	{NVR_PTZCTRL_PTMOVE_STATE_REPORT,NULL}, 			///< 95 云台转动停止时是否上报当前状态给机芯开关
	{NVR_PTZCTRL_VIRTUALZERO_SET,NULL}, 					///< 96 虚拟零位设置
	{NVR_PTZCTRL_VIRTUALZERO_REMOVE,NULL},					///< 97 虚拟零位清除
	{NVR_PTZCTRL_PTZ_RESTORE,NULL}, 					///< 98 已废弃，有需求请使用枚举68{NVR_PTZCTRL_RESTORE_FACTORY //云台单片机恢复出厂，只是云台恢复出厂，不包含和机芯相关的功能
	{NVR_PTZCTRL_SAVE_LONGRUNTEST_PRE,NULL},				///< 99 保存长拷预置位，用于生产测试长拷
	{NVR_PTZCTRL_VERTICAL_SCAN,NULL},						///< 100 垂直扫描
	{NVR_PTZCTRL_FRAME_SCAN,NULL},							///< 101 帧扫描
	{NVR_PTZCTRL_RAND_SCAN,NULL},							///< 102 随机扫描
	{NVR_PTZCTRL_FULLVIEW_SCAN,NULL},						///< 103 全景扫描
	{NVR_PTZCTRL_SYNCSCAN_EXEC,NULL},						///< 104 执行花样扫描时发送的指令
	{NVR_PTZCTRL_LDR_QUERY,NULL},							///< 105 光敏电阻查询
	{NVR_PTZCTRL_DAYNIGHTINFO_SEND,NULL},					///< 106 机芯发送当前实际日夜模式状态给云台
	{NVR_PTZCTRL_TEST_IRGROUPCTRL,NULL},					///< 107 红外灯组亮度控制(Kate测试)
	{NVR_PTZCTRL_TEST_PTZ_DEVIATION,NULL},					///< 108 云台误差测试(Kate测试)
	{NVR_PTZCTRL_TEST_IRON,NULL},							///< 109 开启红外灯(Kate测试用)
	{NVR_PTZCTRL_TEST_IROFF,NULL},							///< 110 关闭红外灯(Kate测试用)
	{NVR_PTZCTRL_TEST_IRONBYGROUP,NULL},					///< 111 开启某一路红外灯(Kate测试用)
	{NVR_PTZCTRL_LASER_INTENSITY,NULL}, 				///< 112 设置激光强度，即电流大小
	{NVR_PTZCTRL_LASER_INTENSITY_QUERY,NULL},				///< 113 查询激光强度，即电流大小
	{NVR_PTZCTRL_SET_PTZ_ADDRESS_ID,NULL},					///< 114 设置云台地址码
	{NVR_PTZCTRL_SET_MBLED_STATE,NULL}, 				///< 115 设置4g信号灯状态，用于布控球
	{NVR_PTZCTRL_STEPOUT_CORRECTION,NULL},					///< 116 云台失步矫正，目前只有427云台支持
	{NVR_PTZCTRL_TRACK_UP,NULL},							///< 117 球机图像追踪向上移动
	{NVR_PTZCTRL_TRACK_DOWN,NULL},							///< 118 球机图像追踪向下移动
	{NVR_PTZCTRL_TRACK_LEFT,NULL},							///< 119 球机图像追踪向左移动
	{NVR_PTZCTRL_TRACK_RIGHT,NULL}, 					///< 120 球机图像追踪向右移动
	{NVR_PTZCTRL_FAN_SCAN,NULL},							///< 121 球机风扇控制
	{NVR_PTZCTRL_PTZ_MCU_HEATER,NULL},						///< 122 云台MCU加热器控制
	{NVR_PTZCTRL_SET_SHAKE_A_THR,NULL}, 				///< 123 设置震动加速度阈值，用于布控球云台判断应该给多大的保持电流
	{NVR_PTZCTRL_CAR_MODE_SET,NULL},					///< 124 打开/关闭车载模式
	{NVR_PTZCTRL_SDI_FRAMERATE_CTRL,NULL},				///< 125 sdi回显芯片30/60帧切换，IPC442因为主板走线问题，主芯片没法直接控制，需要通过云台控制
	{NVR_PTZCTRL_PRESET_SPEED_SET,NvrDevPtzSetPresetSpeed},					///< 126 设置预置点等级速度
	{NVR_PTZCTRL_SET_ZOOM_SPEED,NvrDevPtzSetZoomSpeed},					///< 127 设置ZOOM拉伸速度
	{NVR_PTZCTRL_AZIMUTH_CALIBRATION,NULL}, 			///< 128 电子罗盘方位校准
	{NVR_PTZCTRL_POINT_NORTH,NULL}, 					///< 129 镜头与方位角指向北方
	{NVR_PTZCTRL_PT_OSD_MODE,NULL}, 					///< 130 设置字幕显示云台PT方式
	{NVR_PTZCTRL_SET_PTZ_ID,NULL},						///< 131 设置云台ID编号
	{NVR_PTZCTRL_SET_SRC_PRIORITY,NULL},				///< 132 设置消息来源优先级 以及延迟时间 
	{NVR_PTZCTRL_VID_ENC_FREEZE,NULL},					///< 133 设置是否开启视频冻结功能(开关)
	{NVR_PTZCTRL_IR_SET,NULL},							///< 134 红外  (开关)
	{NVR_PTZCTRL_LASER_SET,NvrDevPtzLaserSet},							///< 135 激光  (开关/自动)
	{NVR_PTZCTRL_PTZ_VERTICA_RANGE,NULL},					///< 136 设置云台垂直角度范围
	{NVR_PTZCTRL_SET_ZM_RATIO,NvrDevPtzSetZMRatio},					///< 137 设置ZOOM倍率
	{NVR_PTZCTRL_PTZUPDATE_TEST,NULL},						///< 138 云台升级测试
	{NVR_PTZCTRL_MOTOR_STOP,NULL},							///< 139 电机停止运动
	{NVR_PTZCTRL_SET_PINOUT_ALARM,NULL},				///< 140 设置告警输出告警
	{NVR_PTZCTRL_ALARMINPUT_QUERY,NULL},					///< 141 并口告警查询
	{NVR_PTZCTRL_SET_MAGNETIC,NULL},					///< 142 设置磁偏角标定
	{NVR_PTZCTRL_SET_PTZ_RECOVERY,NULL},				///< 143 磁偏角反馈失步恢复开关
	{NVR_PTZCTRL_TI_RESUME_SETTINGS,NULL},				///< 144 高德热成像模组恢复出厂
	{NVR_PTZCTRL_SET_PTZ_RATIO,NvrDevPtzSetPTZMRatio},						///< 145 设置水平垂直角度与倍率
	{NVR_PTZCTRL_HOME_SET,NULL},							///< 146 设置home位
	{NVR_PTZCTRL_LIMIT_EXEC,NULL},							///< 147 调用home位
	{NVR_PTZCTRL_LIMIT_REMOVE,NULL},						///< 148 删除home位
	{NVR_PTZCTRL_CMD_LENS_RESET,NULL},					///< 149 镜头复位

	{NVR_PTZCTRL_REPORT_PTZ_WIFI_STA_INFO,NULL},			///< 150，上报单片机wifista模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_WIFI_AP_INFO,NULL}, 		///< 151，上报单片机wifiap模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_MBNET1_INFO,NULL},				///< 152，上报单片机移动卡1模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_MBNET2_INFO,NULL},				///< 153，上报单片机移动卡2模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_TF1_INFO,NULL}, 			///< 154，上报单片机存储卡1模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_TF2_INFO,NULL}, 			///< 155，上报单片机存储卡1模式状态信息
	{NVR_PTZCTRL_REPORT_PTZ_BT_STATE,NULL}, 			///< 156，上报单片机蓝牙状态信息
	{NVR_PTZCTRL_REPORT_PTZ_LOCATION_STATE,NULL},			///< 157，上报单片机定位状态信息
	{NVR_PTZCTRL_REPORT_PTZ_PUBSEC_LINK_STATE,NULL},		///< 158，上报单片机视图库连接状态
	{NVR_PTZCTRL_REPORT_PTZ_TF_REC_STATE,NULL}, 		///< 159，上报单片机录像状态信息
	{NVR_PTZCTRL_REPORT_PTZ_BAT_QUERY,NULL},				///< 160，通过单片机查询电量
	
	{NVR_PTZCTRL_QUERY_PTZ_DEVIATION,NULL}, 			///< 161 查询云台误差测试结果 
	{NVR_PTZCTRL_QUERY_BATCYCLE_COUNT,NULL},			   ///< 162 查询电池放电周期计数
    {NVR_PTZCTRL_QUERY_PTZ_MCU_TYPE,NULL},              ///< 163 查询云台单片机类型，部分科达云台类型支持，用于区分云台升级校验头
    {NVR_PTZCTRL_TYPE_LASER_MOVEUP,NvrDevLaserCtrlMoveCmd}, 		        ///< 164，激光向上
	{NVR_PTZCTRL_TYPE_LASER_MOVEDOWN,NvrDevLaserCtrlMoveCmd},				///< 165，激光向下
	{NVR_PTZCTRL_TYPE_LASER_MOVELEFT,NvrDevLaserCtrlMoveCmd}, 			///< 166 激光向左
	{NVR_PTZCTRL_TYPE_LASER_MOVERIGHT,NvrDevLaserCtrlMoveCmd},			   ///< 167 激光向右
	{NVR_PTZCTRL_TYPE_LASER_MOVELEFTUP,NULL},              ///< 168 激光左上
    {NVR_PTZCTRL_TYPE_LASER_MOVELEFTDOWN,NULL},            ///< 169 激光左下
    {NVR_PTZCTRL_TYPE_LASER_MOVERIGHTUP,NULL},             ///< 170 激光右上
    {NVR_PTZCTRL_TYPE_LASER_MOVERIGHTDOWN,NULL},           ///< 171 激光右下
    {NVR_PTZCTRL_TYPE_LASER_MOVESTOP, NvrDevLaserCtrlMoveCmd},    ///< 172 激光停止
    {NVR_PTZCTRL_LASER_SET_MODE,NULL},                      ///< 173 光斑模式
    {NVR_PTZCTRL_TYPE_BF_NEAR,NvrDevBackFocusCtrlCmd},                            ///< 174 后焦调近   
    {NVR_PTZCTRL_TYPE_BF_FAR,NvrDevBackFocusCtrlCmd},                            ///< 175 后焦调近   
    {NVR_PTZCTRL_TYPE_BF_AUTO,NvrDevBackFocusCtrlCmd},                            ///< 176 后焦调近
    {NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY, NvrDevPtzCtrlVerQuery},        ///< 177 云台主板版本号查询
    {NVR_PTZCTRL_CAMERA_SOFTVER_QUERY,NvrDevPtzCtrlVerQuery},            ///< 178 查询云台镜头版本
    {NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY, NvrDevPtzCtrlVerQuery},         ///<179 聚焦板版本查询

};



NVRSTATUS NvrFixDevDealCamCmd(u16 wChnId,TNvrPtzCtrlInfo tCtrlInfo)
{     
	NVRSTATUS eRet = NVR_ERR__OK;
    TLcamMcPtzParam tPTZParam;          
    TEdgeOsInterCapSysInfo tInterSysCap;
	static s32 lastCtrlType = 0;
    
    mzero(tPTZParam);
    mzero(tInterSysCap);
    NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &tInterSysCap);

    eRet = LcamMcGetPTZCfgChn(wChnId,&tPTZParam);
	if (NVR_ERR__OK != eRet)
	{
		PRINTERR("get dev param failed ret %d\n", eRet);
		return eRet;
	}

	PRINTTMP("eCtrlType:%d ptz:%d, bUse:%d g_byPtzType:%d tPTZParam.bIsp:%d\n", tCtrlInfo.eCtrlType, tPTZParam.bPtz,
	        tInterSysCap.tNvrMicroControllersInfo.byUse, g_byPtzType, tPTZParam.bIsp);

    if((tCtrlInfo.eCtrlType >= NVR_PTZCTRL_TYPE_MOVEUP && tCtrlInfo.eCtrlType <= NVR_PTZCTRL_TYPE_MOVESTOP) )
    {
        if((tPTZParam.bPtz != TRUE) || (tCtrlInfo.eCtrlType == NVR_PTZCTRL_PANPOSION_SET) || (tCtrlInfo.eCtrlType == NVR_PTZCTRL_TILTPOSION_SET) || (tCtrlInfo.eCtrlType == NVR_PTZCTRL_SOFTVER_QUERY)
            || (tCtrlInfo.eCtrlType == NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY)|| (tCtrlInfo.eCtrlType == NVR_PTZCTRL_CAMERA_SOFTVER_QUERY)|| (tCtrlInfo.eCtrlType == NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY))//断电恢复不受云台锁定影响
        {
        
			if(tCtrlInfo.eCtrlType == g_tPtzCmdMap[tCtrlInfo.eCtrlType].eCtrlType && g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc != NULL )
			{
				g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc(wChnId,tCtrlInfo.eCtrlType,&tCtrlInfo);
			}
        }
		
    }
	else if(tCtrlInfo.eCtrlType >= NVR_PTZCTRL_TYPE_ZOOMTELE && tCtrlInfo.eCtrlType <= NVR_PTZCTRL_TYPE_IRIS_STOP)
    {
    
        if(tInterSysCap.tNvrMicroControllersInfo.byUse == TRUE)
        { 
			if(tCtrlInfo.eCtrlType == g_tPtzCmdMap[tCtrlInfo.eCtrlType].eCtrlType && g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc != NULL )
			{
				g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc(wChnId,tCtrlInfo.eCtrlType,&tCtrlInfo);
			}
        }
        else
        {	
            if(tPTZParam.bIsp != TRUE)
            {
                if(g_byPtzType != NVR_CAP_PTZ_TYPE_MINJIA2)//敏佳不需要此操作
                {
					eRet = NvrFixDevHandleZoomOperation(tCtrlInfo.eCtrlType, TRUE);
					if (NVR_ERR__OK != eRet)
					{
						PRINTERR("NvrFixDevHandleZoomOperation failed, not need digital zoom\n");
						eRet = LcamIspCamCtrl(wChnId,tCtrlInfo);
					}

					if ((NVR_PTZCTRL_TYPE_ZOOMTELE != lastCtrlType && NVR_PTZCTRL_TYPE_ZOOMWIDE != lastCtrlType) 
						&& (tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_ZOOMTELE || tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_ZOOMWIDE)) //tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_ZOOMSTOP ||
					{
						eRet = NvrFixDevHandleZoomOperation(tCtrlInfo.eCtrlType, FALSE);
					}

        			if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_ZOOMSTOP)
        			{
        				///<电动镜头 focus  zoom 后都要更新focus位置
        				OsApi_TimerSet( g_hGetFocusTimer, 1000, NvrFixDevGetFocusTimerCB, NULL);//停止1000ms后获取
#ifndef _QCOM_
    					OsApi_TimerSet( g_hPowerOffMemTimer, g_nPowerOffInterval * 1000, NvrFixDevPowerOffMemTimerCB, NULL);//若位置固定60s，则保存位置信息
#endif
        			}
    				else if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSSTOP)
    				{
    					///<电动镜头 focus  zoom 后都要更新focus位置
    					OsApi_TimerSet( g_hGetFocusTimer, 1000, NvrFixDevGetFocusTimerCB, NULL);//停止1000ms后获取
    				}
                    else if(tCtrlInfo.eCtrlType == NVR_PTZCTRL_TYPE_FOCUSAUTO)
                    {
                        ///<自动调焦不会下发stop,需要延时后更新focus位置
                        OsApi_TimerSet( g_hGetFocusTimer, 30 * 1000, NvrFixDevGetFocusTimerCB, NULL);//30s后获取
#ifndef _QCOM_
                        OsApi_TimerSet( g_hPowerOffMemTimer, g_nPowerOffInterval * 1000, NvrFixDevPowerOffMemTimerCB, NULL);//若位置固定60s，则保存位置信息
#endif
                    }

					lastCtrlType = tCtrlInfo.eCtrlType;
                }

    			///< LcamIspCamCtrl 接口调用的zoom统计放在此处
    			if (NVR_PTZCTRL_TYPE_ZOOMTELE == tCtrlInfo.eCtrlType || NVR_PTZCTRL_TYPE_ZOOMWIDE == tCtrlInfo.eCtrlType)
    			{
    			    g_aLifeStat[wChnId].dwZoomChangeTimes++;
    			}
				
            }
        }
    }
	else
	{
		if(tCtrlInfo.eCtrlType == g_tPtzCmdMap[tCtrlInfo.eCtrlType].eCtrlType && g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc != NULL )
		{
			g_tPtzCmdMap[tCtrlInfo.eCtrlType].pHandleFunc(wChnId,tCtrlInfo.eCtrlType,&tCtrlInfo);
		}
	}
    if(g_aLifeStat[wChnId].wVidSrcID < NVR_MAX_LCAM_CHN_NUM)
    {
        //保存ptz操作以用于寿命统计
        switch(tCtrlInfo.eCtrlType)
        {
            case NVR_PTZCTRL_TYPE_MOVEUP:
            case NVR_PTZCTRL_TYPE_MOVEDOWN:
            case NVR_PTZCTRL_VERTICAL_SCAN:
            case NVR_PTZCTRL_TILTPOSION_SET:
            {
                g_aLifeStat[wChnId].dwVerticalTurnTimes++;
            }
            break;

            case NVR_PTZCTRL_TYPE_MOVELEFT:
            case NVR_PTZCTRL_TYPE_MOVERIGHT:
//            case INVR_PTZCTRL_HORIZON_SCAN:
            case NVR_PTZCTRL_HORIZONTAL_TURN:
            case NVR_PTZCTRL_PANPOSION_SET:
            {
                g_aLifeStat[wChnId].dwHorizontalTurnTimes++;
            }
            break;

            case NVR_PTZCTRL_TYPE_MOVELEFTUP:
            case NVR_PTZCTRL_TYPE_MOVELEFTDOWN:
            case NVR_PTZCTRL_TYPE_MOVERIGHTUP:
            case NVR_PTZCTRL_TYPE_MOVERIGHTDOWN:
            case NVR_PTZCTRL_TURNTO_MACHINEZERO:
//            case IPC_CAM_CMD_PRESET_TOUR_START:
            case NVR_PTZCTRL_TYPE_ZOOMPART:
            case NVR_PTZCTRL_TYPE_ZOOMWHOLE:
            case NVR_PTZCTRL_TYPE_GOTOPOINT:
            {
                g_aLifeStat[wChnId].dwHorizontalTurnTimes++;
                g_aLifeStat[wChnId].dwVerticalTurnTimes++;
            }
            break;

            case NVR_PTZCTRL_AQUILLA_SET_PTMOVE:
            case NVR_PTZCTRL_AQUILLA_SET_PTZAngle:
            {
                g_aLifeStat[wChnId].dwHorizontalTurnTimes++;
                g_aLifeStat[wChnId].dwVerticalTurnTimes++;
            }
            break;

            default:
            {
                break;
            }
        }
    }
    return eRet;
}


NVRSTATUS NvrFixDevGetCmdFromBuf(u16 *pwChnId,TNvrPtzCtrlInfo *ptCtrlInfo)
{
	s32 nRet = 0;
	TNvrDoubleListPopAttr tPopAttr;
    
	mzero(tPopAttr);

	tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
	tPopAttr.dwDataLen = sizeof(TNvrPtzCtrlInfo);
	tPopAttr.pchDataBuf = (s8*)ptCtrlInfo;

	nRet = NvrQueuePop(g_ptLcDevQueue, &tPopAttr);
	if (NVR_ERR__OK != nRet)
	{
		PRINTERR( "[IPCDEV]IpcDevGetCmdFromBuf queue pop failed ret:%d\n",nRet);
		return NVR_ERR__ERROR;
	}

    *pwChnId = (u16)tPopAttr.dwType;

    PRINTTMP( "NvrFixDevGetCmdFromBuf [Pop]++ Id:%u, Type:%u, IspSpeed:%u, PanSpeed:%u, TilSpeed:%u, Num:%u\n",
                            *pwChnId,
                            ptCtrlInfo->eCtrlType,
                            ptCtrlInfo->wIspSpeed,
                        	ptCtrlInfo->wPanSpeed,
                        	ptCtrlInfo->wTilSpeed,
                        	ptCtrlInfo->wNumber);

    return NVR_ERR__OK;
}

static void* NvrFixDevDealCamCmdThread()
{
	s32 nRet = 0;
	TNvrPtzCtrlInfo tCtrlInfo;
	TThreadInfoRecord tThreadInfo;
	mzero(tCtrlInfo);
	mzero(tThreadInfo);
    u16 wChnId;

	//添加线程信息
	tThreadInfo.m_dwThreadId = getpid();
	mcopy(tThreadInfo.m_strThreadName, "NvrFixDevComWrite");
	OsApi_AddThreadInfo( &tThreadInfo );

	PRINTDBG("[IPCDEV]NvrFixDevDealCamCmdThread Thread %d created!\n", getpid());

#ifndef WIN32
	prctl(PR_SET_NAME, "NvrFixDevComWrite", 0, 0, 0);
#endif

	while(TRUE)
	{
		nRet = NvrFixDevGetCmdFromBuf(&wChnId,&tCtrlInfo);
		if (NVR_ERR__OK == nRet)
		{
		    PRINTDBG( "NvrFixDevGetCmdFromBuf get cmd type: %d\n", tCtrlInfo.eCtrlType);
            NvrFixDevDealCamCmd(wChnId,tCtrlInfo);
		}
	}

	PRINTDBG( "NvrFixDevDealCamCmdThread Task Quit...\n" );
	OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
	OsApi_TaskExit();
	return NULL;

}


static s32 g_nNotify = 0;
static s32 g_byQueryChnid = 0;

#define QUERY_NOTIFY_NONE       0 //没有通知
#define QUERY_NOTIFY_NEEDPAUSE  1 //通知查询暂停
#define QUERY_NOTIFY_NOPAUSE    2 //通知查询继续执行
#define QUERY_NOTIFY_NEEDQUERY  3 //通知查询
#define QUERY_NOTIFY_NONEEDQUERY 4 //通知不需查询
#define QUERY_NOTIFY_HASDQUERYRES 5 //通知已经查询回复
#define QUERY_NOTIFY_HASRES     6 //通知有回复
#define QUERY_NOTIFY_RESUME  7 //通知恢复查询



#define  QUERY_STATE_NOEXE    1//未查询
#define  QUERY_STATE_NORES    2//查询未收到回复
#define  QUERY_STATE_HASRES   3//查询收到回复
#define  QUERY_STATE_PAUSED   4//暂停
#define  QUERY_STATE_TIMEOUT   5//超时
#define  QUERY_STATE_WAIT   6//等待
#define  QUERY_STATE_STOP   7//停止


static BOOL32 g_bBlock = FALSE;
static u32 g_dwHasStart = FALSE;//用于在unblock时判断，前面是否有start请求


//只能在unblock时调用，其余地方要用请用NvrFixDevStartPTZQuery
static void NvrFixDevResumePTZQuery()
{

    PRINTDBG("enter，query state:%d\n",g_nQuerying);
    pthread_mutex_lock(&g_hSerialQueryMutex);


    if(g_nQuerying == QUERY_STATE_PAUSED)
    {
        lastPtzAngle_H = -1;
        lastPtzAngle_V = -1;
        lastPtzZoom    = -1;
        g_dwEERecvCount=0;
        g_dwEESendCount=0;
        g_nNotify = QUERY_NOTIFY_RESUME;
        OsApi_SemGive(g_hSemExeWait);
        //pthread_cond_signal(&querywait);
        #if 0
        OsApi_SemTake(g_hSemQueryThread);

        if(g_byQueryChnid==SERIAL_TYPE_RECHENGXIANG)
        {
            g_hThreadQueryPTZ = OsApi_TaskCreate((void*)NvrFixDevXCoreQueryThread, "NvrFixDevXCoreQueryThread", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL);
            if ((TASKHANDLE)NULL == g_hThreadQueryPTZ)
            {
                PRINTERR(" NvrFixDevXCoreQueryThread create failed\n");
            }
        }
        else
        {
        }
        OsApi_SemGive(g_hSemQueryThread);
        #endif
    }    
    else
    {
        pthread_mutex_unlock(&g_hSerialQueryMutex);
        return;
    }
    pthread_mutex_unlock(&g_hSerialQueryMutex);

    PRINTDBG("wait resume g_nQuerying:%d\n",g_nQuerying);
    OsApi_SemTake(g_hSemExeOver);
    PRINTDBG("wait resume ovre g_nQuerying:%d\n",g_nQuerying);

#if 0
    pthread_mutex_lock(&g_hSerialExeMutex);
    g_bWait = TRUE;
    pthread_cond_wait(&exeover, &g_hSerialExeMutex);//等待指令执行完毕，在指令历史线程中会确保移动停止指令执行成功（会不停尝试）。
    g_bWait = FALSE;
    pthread_mutex_unlock(&g_hSerialExeMutex);
#endif

    return;

}



static void NvrFixDevStartPTZQuery(u8 chnid)
{
   ///<不支持ptz查询
	if(NVR_CAP_NONSUPPORT == g_tFixInterCapInfo.tFixPtzInternalCap.bySupportPtzQuery)
	{
		PRINTTMP("not support ptz query!\n");
		return;
	}

    PRINTDBG("enter,query state:%d,%d,%x\n",g_nQuerying,g_bBlock,g_hThreadQueryPTZ);
    pthread_mutex_lock(&g_hSerialQueryMutex);
    g_byQueryChnid = chnid;
    g_dwHasStart++;
    if(g_bBlock == FALSE&& g_nQuerying == QUERY_STATE_NOEXE)//如果是在暂停状态，不允许启动线程
    {
        lastPtzAngle_H = -1;
        lastPtzAngle_V = -1;
        lastPtzZoom    = -1;
        g_dwEERecvCount=0;
        g_dwEESendCount=0;
        g_nNotify = QUERY_NOTIFY_NEEDQUERY;
        g_nQuerying=QUERY_STATE_NOEXE;//查询未返回
        OsApi_SemGive(g_hSemExeWait);
        //pthread_cond_signal(&querywait);

        #if 0
        if(g_byQueryChnid==SERIAL_TYPE_RECHENGXIANG)
        {

            g_hThreadQueryPTZ = OsApi_TaskCreate((void*)NvrFixDevXCoreQueryThread, "NvrFixDevXCoreQueryThread", NVR_TASK_COMMON_PRIORITY, 256<<10, 0, 0, NULL);
            if ((TASKHANDLE)NULL == g_hThreadQueryPTZ)
            {
                PRINTERR(" NvrFixDevXCoreQueryThread create failed\n");
            }

        }
        else
        {

        }
        #endif 

    }
    else
    {
        pthread_mutex_unlock(&g_hSerialQueryMutex);
        return ;
    }

    pthread_mutex_unlock(&g_hSerialQueryMutex);

    PRINTDBG("wait query start g_nQuerying:%d\n",g_nQuerying);
    OsApi_SemTake(g_hSemExeOver);
    PRINTDBG("wait query start ovre g_nQuerying:%d\n",g_nQuerying);
	
    return;
}

/*
static void NvrFixDevStopPTZQuery()
{
     PRINTDBG("enter\n");
     OsApi_SemTake(g_hSemQueryPTZ);
     g_dwMoving = FALSE;
     lastPtzAngle_H = -1;
     lastPtzAngle_V = -1;
     lastPtzZoom    = -1;
     g_nNotify = QUERY_NOTIFY_STOPQUERY;
     g_nQuerying=QUERY_STATE_NOEXE;//查询未返回
     g_dwHasStart--;
     OsApi_SemGive(g_hSemQueryPTZ);

    OsApi_SemTake(g_hSemQueryThread);
    if(g_hThreadQueryPTZ)
    {
         PRINTDBG("wait query thread end\n");
         OsApi_TaskWaitEnd(g_hThreadQueryPTZ);
         PRINTDBG("wait query thread has end\n");
         while(g_nQuerying != QUERY_STATE_PAUSED)
         {
            usleep(5000);
            PRINTDBG("wait thread end g_nQuerying:%d\n",g_nQuerying);
            
         }
         g_hThreadQueryPTZ=NULL;
    }
    OsApi_SemGive(g_hSemQueryThread);

    OsApi_SemGive(g_hSemQueryRES);
}
*/


static void NrvFixDevWaitPTZQueryOverBlock()
{
     PRINTDBG("g_nQuerying:%d\n",g_nQuerying);
     pthread_mutex_unlock(&g_hSerialQueryMutex);
     g_bBlock = TRUE;
     //如果线程还没有完全执行，仅从g_nQuerying == QUERY_STATE_NOEXE判断会立马返回，所以只要有线程在时就必须等待停止
     if(g_nQuerying == QUERY_STATE_NOEXE)
     {  //如果是未查询状态，立马返回
        pthread_mutex_unlock(&g_hSerialQueryMutex);
        PRINTDBG("no need block\n");
        return;
     }


     //如果是QUERY_STATE_NOEXE，或线程线程不在时无需通知暂停
     if(g_nQuerying != QUERY_STATE_NOEXE)
     {
        PRINTDBG("notify to pause g_nQuerying:%d\n",g_nQuerying);
        g_nNotify = QUERY_NOTIFY_NEEDPAUSE;//通知暂停
        //pthread_cond_signal(&querywait);//通知本线程继续查询
        OsApi_SemGive(g_hSemExeWait);
        //pthread_cond_signal(&querywait);
     }
     else
    {
        pthread_mutex_unlock(&g_hSerialQueryMutex);
        return;
        
    }
     pthread_mutex_unlock(&g_hSerialQueryMutex);


     PRINTDBG("wait block g_nQuerying:%d\n",g_nQuerying);
     OsApi_SemTake(g_hSemExeOver);
     PRINTDBG("wait block ovre g_nQuerying:%d\n",g_nQuerying);


#if 0
     pthread_mutex_lock(&g_hSerialExeMutex);
     PRINTDBG("wait block g_nQuerying:%d\n",g_nQuerying);
     g_bWait = TRUE;
     pthread_cond_wait(&exeover, &g_hSerialExeMutex);//等待指令执行完毕，在指令历史线程中会确保移动停止指令执行成功（会不停尝试）。
     g_bWait = FALSE;
     PRINTDBG("wait block ovre g_nQuerying:%d\n",g_nQuerying);
     pthread_mutex_unlock(&g_hSerialExeMutex);
#endif 
     
#if 0
     BOOL32 bRet = TRUE;
     bRet = OsApi_SemTake(g_hSemQueryPTZ);
     if(bRet)
     {
             g_bBlock = TRUE;
             //如果线程还没有完全执行，仅从g_nQuerying == QUERY_STATE_NOEXE判断会立马返回，所以只要有线程在时就必须等待停止
             if(g_nQuerying == QUERY_STATE_NOEXE && g_hThreadQueryPTZ==NULL)
             {  //如果是未查询状态，立马返回
                OsApi_SemGive(g_hSemQueryPTZ);
                PRINTDBG("no need block\n");
                return;
             }
             //如果是QUERY_STATE_NOEXE，或线程线程不在时无需通知暂停
             if(g_nQuerying != QUERY_STATE_NOEXE && g_hThreadQueryPTZ!=NULL)
             {
                PRINTDBG("notify to pause g_nQuerying:%d\n",g_nQuerying);
                g_nNotify = QUERY_NOTIFY_NEEDPAUSE;//通知暂停
             }
             PRINTDBG("has notify to pause g_nQuerying:%d\n",g_nQuerying);
             OsApi_SemGive(g_hSemQueryPTZ);//先释放查询锁
        

        //等待线程退出,退出后g_bBlock = TRUE;阻止新线程创建
        
        OsApi_SemTake(g_hSemQueryThread);

        #if 1
        if(g_hThreadQueryPTZ)//查询线程由于QUERY_NOTIFY_NEEDPAUSE或自我停止，可能已经被清掉了
        {
            PRINTDBG("query need pause ,block\n");
            //OsApi_TaskWaitEnd(g_hThreadQueryPTZ);

            //上述PAUSE还未来得及终止时，线程本身已经退出情况也需要考虑（g_nQuerying != QUERY_STATE_NOEXE）,已经主动停止，g_nQuerying = QUERY_STATE_NOEXE
            while(g_nQuerying != QUERY_STATE_PAUSED && g_nQuerying != QUERY_STATE_NOEXE )
            {
                usleep(10000);
                PRINTDBG("wait thread end g_nQuerying:%d\n",g_nQuerying);
                
            }//等待线程退出

            while(g_nQuerying == QUERY_STATE_TIMEOUT && g_dwEESendCount-g_dwEERecvCount>0)//等待数据全部接收完，可能存在阻塞风险s
            {
                PRINTDBG("wait recv remain data\n");
                usleep(10000);
            }
            PRINTDBG("query need pause ,block succ\n");
            g_hThreadQueryPTZ = NULL;
        }
        #endif
        OsApi_SemGive(g_hSemQueryThread);
     }
     else
     {
        PRINTDBG("take timeout\n");
     }

    //OsApi_SemTake(g_hSemQueryPTZ);
#endif 
}

static void NrvFixDevWaitPTZQueryOverUnBlock()
{
    PRINTDBG("enter un block:%d\n",g_nQuerying);
    pthread_mutex_lock(&g_hSerialQueryMutex);
    g_bBlock = FALSE;
     if(g_nQuerying == QUERY_STATE_NOEXE)
     {  //如果是未查询状态，立马返回
        pthread_mutex_unlock(&g_hSerialQueryMutex);
        PRINTDBG("no block\n");
        return;
     }
     pthread_mutex_unlock(&g_hSerialQueryMutex);

    //只有在block状态下才能启动
     PRINTDBG("unblock\n");
     NvrFixDevResumePTZQuery();

#if 0
    BOOL32 bRet = TRUE;
    bRet = OsApi_SemTake(g_hSemQueryPTZ);
    if(bRet)
    {
        g_bBlock = FALSE;
         if(g_nQuerying == QUERY_STATE_NOEXE)
         {  //如果是未查询状态，立马返回
            OsApi_SemGive(g_hSemQueryPTZ);
            PRINTDBG("no block\n");
            return;
         }

         PRINTDBG("start continue\n");
         //OsApi_SemTake(g_hSemQueryPTZ);
         if(g_nQuerying == QUERY_STATE_PAUSED )
         {
            g_nNotify = QUERY_NOTIFY_NEEDQUERY; 
         }

         OsApi_SemGive(g_hSemQueryPTZ);
         NvrFixDevResumePTZQuery();
        //只有在block状态下才能启动
         PRINTDBG("unblock\n");
    }
    else
    {
        PRINTDBG("take timeout\n");
    }
#endif
}



#ifdef XCORE
static void NrvFixDevXcoreWaitPTZQueryOverBlock()
{
     PRINTDBG("g_nQuerying:%d\n",g_nQuerying);
     BOOL32 bRet = TRUE;
     bRet = OsApi_SemTakeByTime(g_hSemQueryPTZ,200);
     if(bRet)
     {
             g_bBlock = TRUE;
             //如果线程还没有完全执行，仅从g_nQuerying == QUERY_STATE_NOEXE判断会立马返回，所以只要有线程在时就必须等待停止
             if(g_nQuerying == QUERY_STATE_NOEXE && g_hThreadQueryPTZ==NULL)
             {  //如果是未查询状态，立马返回
                OsApi_SemGive(g_hSemQueryPTZ);
                PRINTDBG("no need block\n");
                return;
             }
             //如果是QUERY_STATE_NOEXE，或线程线程不在时无需通知暂停
             if(g_nQuerying != QUERY_STATE_NOEXE && g_hThreadQueryPTZ!=NULL)
             {
                PRINTDBG("notify to pause g_nQuerying:%d\n",g_nQuerying);
                g_nNotify = QUERY_NOTIFY_NEEDPAUSE;//通知暂停
             }
             PRINTDBG("has notify to pause g_nQuerying:%d\n",g_nQuerying);
             OsApi_SemGive(g_hSemQueryPTZ);//先释放查询锁
        

        //等待线程退出,退出后g_bBlock = TRUE;阻止新线程创建
        
        OsApi_SemTake(g_hSemQueryThread);

        #if 1
        if(g_hThreadQueryPTZ)//查询线程由于QUERY_NOTIFY_NEEDPAUSE或自我停止，可能已经被清掉了
        {
            PRINTDBG("query need pause ,block\n");
            //OsApi_TaskWaitEnd(g_hThreadQueryPTZ);

            //上述PAUSE还未来得及终止时，线程本身已经退出情况也需要考虑（g_nQuerying != QUERY_STATE_NOEXE）,已经主动停止，g_nQuerying = QUERY_STATE_NOEXE
            while(g_nQuerying != QUERY_STATE_PAUSED && g_nQuerying != QUERY_STATE_NOEXE )
            {
                usleep(10000);
                PRINTDBG("wait thread end g_nQuerying:%d\n",g_nQuerying);
                
            }//等待线程退出

            while(g_nQuerying == QUERY_STATE_TIMEOUT && g_dwEESendCount-g_dwEERecvCount>0)//等待数据全部接收完，可能存在阻塞风险s
            {
                PRINTDBG("wait recv remain data\n");
                usleep(10000);
            }
            PRINTDBG("query need pause ,block succ\n");
            g_hThreadQueryPTZ = NULL;
        }
        #endif
        OsApi_SemGive(g_hSemQueryThread);
     }
     else
     {
        PRINTDBG("take timeout\n");
     }

    //OsApi_SemTake(g_hSemQueryPTZ);


}

static void NrvFixDevXcoreWaitPTZQueryOverUnBlock()
{
    PRINTDBG("enter un block:%d\n",g_nQuerying);
    BOOL32 bRet = TRUE;
    bRet = OsApi_SemTakeByTime(g_hSemQueryPTZ,50);
    if(bRet)
    {
        g_bBlock = FALSE;
         if(g_nQuerying == QUERY_STATE_NOEXE)
         {  //如果是未查询状态，立马返回
            OsApi_SemGive(g_hSemQueryPTZ);
            PRINTDBG("no block\n");
            return;
         }

         PRINTDBG("start continue\n");
         //OsApi_SemTake(g_hSemQueryPTZ);
         if(g_nQuerying == QUERY_STATE_PAUSED )
         {
            g_nNotify = QUERY_NOTIFY_NEEDQUERY; 
         }

         OsApi_SemGive(g_hSemQueryPTZ);
         
        //只有在block状态下才能启动
         NvrFixDevResumePTZQuery();
         PRINTDBG("unblock\n");
    }
    else
    {
        PRINTDBG("take timeout\n");
    }

}


static void* NvrFixDevXCoreQueryThread()
{

	TNvrPtzCtrlInfo tCtrlInfo;
	TThreadInfoRecord tThreadInfo;
	mzero(tCtrlInfo);
	mzero(tThreadInfo);

	//添加线程信息
	tThreadInfo.m_dwThreadId = getpid();
	mcopy(tThreadInfo.m_strThreadName, "NvrFixDevXCoreQueryThread");
	OsApi_AddThreadInfo( &tThreadInfo );

	PRINTDBG("[IPCDEV]NvrFixDevXCoreQueryThread Thread %d created!\n", getpid());

#ifndef WIN32
	prctl(PR_SET_NAME, "NvrFixDevXCoreQueryThread", 0, 0, 0);
#endif

    //u8 abyCmdStop[6] = {0xff, 0x01, 0x0, 0x0, 0x00, 0x01};//查询PTZ指令
    u8 abyCmdQueryAA[9] = {0xAA,0x05,0x08,0x33,0x00,0x00,0xEA,0xEB,0xAA};
    u32 dwLen = 0;


    OsApi_SemTake(g_hSemQueryRES);

    u32 dwTimeOutCnt = 0;
    u32 dwEqualCnt = 0;

	while(TRUE)
	{
        OsApi_SemTake(g_hSemQueryPTZ);

        if(g_nNotify == QUERY_NOTIFY_NONEEDQUERY)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NONEEDQUERY\n");
            g_nQuerying=QUERY_STATE_NOEXE;//查询停止
            g_dwHasStart--;
            PRINTDBG("g_dwHasStart:%d\n",g_dwHasStart);
            g_hThreadQueryPTZ =NULL;
            OsApi_SemGive(g_hSemQueryPTZ);
            break;
        }
        
        if(g_nNotify == QUERY_NOTIFY_NEEDPAUSE)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NEEDPAUSE\n");
            g_nQuerying=QUERY_STATE_PAUSED;//查询暂停
            g_hThreadQueryPTZ =NULL;
            OsApi_SemGive(g_hSemQueryPTZ);
            break;
        }

        if(g_nNotify == QUERY_NOTIFY_NEEDQUERY)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NEEDQUERY\n");
          
            //继续查询
            PRINTDBG("querying....\n");

            //为了控制不要查询太快
            PRINTDBG("query control:send:%d,recv:%d\n",g_dwEESendCount,g_dwEERecvCount);

            NvrFixDevWriteSerial(g_byQueryChnid, abyCmdQueryAA, 9, &dwLen);
            
            g_dwEESendCount++;
            g_nQuerying=QUERY_STATE_NORES;//查询未返回
            if(TRUE ==OsApi_SemTakeByTime(g_hSemQueryRES,(g_dwEESendCount-g_dwEERecvCount)*50))
            {
                PRINTDBG("querying....end,%d\n",g_nQuerying);
                g_nQuerying=QUERY_STATE_HASRES;//查询y返回
            }
            else
            {
                g_nQuerying=QUERY_STATE_TIMEOUT;//查询超时
                PRINTDBG("querying....end timeout,%d\n",g_nQuerying);
            }

                        //如果超时了继续查询
            if(g_nQuerying ==QUERY_STATE_TIMEOUT)
            {
                g_nNotify = QUERY_NOTIFY_NEEDQUERY;
                lastXcoreZoom    = -1;
                dwTimeOutCnt++;
                //增加查询间隔
            }
            else
            {
                dwTimeOutCnt=0;
                PRINTDBG("query stop:lasth:%d,z:%d\n",lastXcoreZoom,g_dwCurrenXcoreZoom);
                if(g_dwCurrenXcoreZoom != lastXcoreZoom )
                {
                    g_dwMoving++;
                    g_nNotify = QUERY_NOTIFY_NEEDQUERY;
                    lastXcoreZoom    = g_dwCurrenXcoreZoom;
                    dwEqualCnt = 0;
                }
                else
                {
                    //如果相等了，停止查询
                     dwEqualCnt++;
                     if(dwEqualCnt >= 3)//连续5次相等，认为已经停止
                    {
                         g_dwMoving = 0;
                         lastXcoreZoom    = -1;
                         g_bStoping = FALSE;//相等表示已经停止
                         g_nQuerying=QUERY_STATE_NOEXE;//未查询
                         g_nNotify = QUERY_NOTIFY_NONEEDQUERY;
                         
                         dwEqualCnt = 0;
                         OsApi_SemGive(g_hSemQueryRES);
                         PRINTDBG("query stop\n");
                    }
                    else
                    {
                        g_nNotify = QUERY_NOTIFY_NEEDQUERY;
                        lastXcoreZoom    = g_dwCurrenXcoreZoom;
                    }
                }
            }

        }

        OsApi_SemGive(g_hSemQueryPTZ);
        usleep(50000);
        

	}

	PRINTDBG( "NvrFixDevPTZQueryThread Task Quit...\n" );
	OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
	OsApi_TaskExit();
    
	
	return NULL;

}

#endif

NVRSTATUS NvrFixHistoryCmdInsert(u8 bySerialID,u8*buf,u32 len,BOOL bNeedRes,ENvrPtzCtrlType eCmd)
{
	FIX_ASSERT(buf);
	THistoryCmdNode *ptCmdNode = NULL;  //节点指针
	PRINTDBG("insert msg cmd %d id:%d\n",eCmd,bySerialID);
    pthread_mutex_lock(&g_hSerialHistoryListMutex);//访问共享区域必须加锁
    ptCmdNode = malloc(sizeof(THistoryCmdNode));
	memset(ptCmdNode,0,sizeof(THistoryCmdNode));
    len = MIN(len, 64);
    ptCmdNode->tCmdNode.eMsgCmd=eCmd;
	memcpy(ptCmdNode->tCmdNode.abyCmd,buf,len);
    ptCmdNode->tCmdNode.byCmdLen = len;
    ptCmdNode->tCmdNode.bySerialID=bySerialID;
    ptCmdNode->tCmdNode.dwCurrentHang = g_dwCurrentHAngle;
    ptCmdNode->tCmdNode.dwCurrentVang = g_dwCurrentVAngle;
    ptCmdNode->tCmdNode.byNeedRes = bNeedRes;
    ptCmdNode->tCmdNode.dwSendTime = NvrSysGetCurTimeMSec();
    ptCmdNode->tCmdNode.dwDelay = 1000;
    ptCmdNode->tCmdNode.dwTimeOut = 3000;
	list_add_tail(&ptCmdNode->listnode,&g_SerialHistoryList);
	PRINTDBG("insert msg recv:%d cmd:%d curh:%d,curv:%d\n",ptCmdNode->tCmdNode.dwRecvNum,ptCmdNode->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle);
    pthread_mutex_unlock(&g_hSerialHistoryListMutex);

	pthread_cond_signal(&hasmsg);

    return NVR_ERR__OK;
}


NVRSTATUS NvrFixHistoryCmdInsert2(u8 bySerialID,u8*buf,u32 len,BOOL bNeedRes,ENvrPtzCtrlType eCmd,u32 dwDelay,u32 dwTimeOut)
{
	FIX_ASSERT(buf);
	THistoryCmdNode *ptCmdNode = NULL;  //节点指针
	PRINTDBG("insert msg cmd %d id:%d\n",eCmd,bySerialID);
    pthread_mutex_lock(&g_hSerialHistoryListMutex);//访问共享区域必须加锁
    ptCmdNode = malloc(sizeof(THistoryCmdNode));
	memset(ptCmdNode,0,sizeof(THistoryCmdNode));
    len = MIN(len, 64);
    ptCmdNode->tCmdNode.eMsgCmd=eCmd;
	memcpy(ptCmdNode->tCmdNode.abyCmd,buf,len);
    ptCmdNode->tCmdNode.byCmdLen = len;
    ptCmdNode->tCmdNode.bySerialID=bySerialID;
    ptCmdNode->tCmdNode.dwCurrentHang = g_dwCurrentHAngle;
    ptCmdNode->tCmdNode.dwCurrentVang = g_dwCurrentVAngle;
    ptCmdNode->tCmdNode.byNeedRes = bNeedRes;
    ptCmdNode->tCmdNode.dwSendTime = NvrSysGetCurTimeMSec();
    ptCmdNode->tCmdNode.dwDelay = dwDelay;
    ptCmdNode->tCmdNode.dwTimeOut = dwTimeOut;
	list_add_tail(&ptCmdNode->listnode,&g_SerialHistoryList);
	PRINTDBG("insert2 msg recv:%d cmd:%d curh:%d,curv:%d\n",ptCmdNode->tCmdNode.dwRecvNum,ptCmdNode->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle);
    pthread_mutex_unlock(&g_hSerialHistoryListMutex);

	pthread_cond_signal(&hasmsg);

    return NVR_ERR__OK;
}



NVRSTATUS NvrFixHistoryCmdClear(u8 bySerialID,ENvrPtzCtrlType eCmd)
{
	PRINTDBG("clear msg cmd %d id:%d\n",eCmd,bySerialID);
    struct list_head  *s, *n;
    THistoryCmdNode *msg=NULL;

    pthread_mutex_lock(&g_hSerialHistoryListMutex);

    if(!list_empty(&g_SerialHistoryList))
    {
        list_for_each_safe(s, n, &g_SerialHistoryList)
        {
            msg = container_of(s,THistoryCmdNode,listnode);
            PRINTDBG("cmd type:%d,%d\n",msg->tCmdNode.eMsgCmd ,msg->tCmdNode.bySerialID);
            if(msg->tCmdNode.bySerialID == 0 && msg->tCmdNode.eMsgCmd == eCmd)//PTZ
            {
                PRINTDBG("clear delete msg %d\n",msg->tCmdNode.eMsgCmd);
                list_del(s);    
                free(msg);
                break;
            }
        }   
    }
    pthread_mutex_unlock(&g_hSerialHistoryListMutex);
    return NVR_ERR__OK;
}



static void* NvrFixDevCmdHistoryCheckThread()
{
    struct list_head  *s, *n;
    THistoryCmdNode *msg=NULL;
    u8 *pbuff=NULL;
    u32 dwTime=0;
    u32 dwCurTime=0;
    u32 dwRlen=0;
    u32 dwCmdLen=0;
    BOOL32 bNeedAgain=TRUE;
    
    do{
    	pthread_mutex_lock(&g_hSerialHistoryListMutex);
    	if(!list_empty(&g_SerialHistoryList))
    	{
    		list_for_each_safe(s, n, &g_SerialHistoryList)
    		{
    			msg = container_of(s,THistoryCmdNode,listnode);
    			//pthread_mutex_unlock(&g_hSerialHistoryListMutex);
    			PRINTDBG("recv type:%d,%d\n",msg->tCmdNode.eMsgCmd ,msg->tCmdNode.bySerialID);
    			pbuff = msg->tCmdNode.abyCmd;
                dwCmdLen = msg->tCmdNode.byCmdLen;
                dwTime= msg->tCmdNode.dwSendTime;
                bNeedAgain=TRUE;
                dwCurTime = NvrSysGetCurTimeMSec();

                if(msg->tCmdNode.bySerialID == 0)//PTZ
                {
        			if(pbuff[0] == 0xFF) 
        			{
                        PRINTDBG("ptz ff msg\n");

                        /*
                        if(msg->tCmdNode.eMsgCmd >= NVR_PTZCTRL_TYPE_MOVEUP && msg->tCmdNode.eMsgCmd < NVR_PTZCTRL_TYPE_MOVESTOP)
                        {
                            if(msg->tCmdNode.dwCurrentHang != g_dwCurrentHAngle || msg->tCmdNode.dwCurrentVang != g_dwCurrentVAngle)
                            {
                                
                                PRINTDBG("cmd:%d cur h:%d v:%d ,lasth:%d,lastv:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang);
                                msg->tCmdNode.bDone = TRUE;
                                bNeedAgain = FALSE;
                            }
                            else
                            {
                                if(msg->tCmdNode.dwDelay >= (dwCurTime - dwTime))
                                {
                                    PRINTDBG("no reach time:%d,%d,%d\n",dwCurTime,dwTime,dwCurTime - dwTime);
                                    continue;
                                }
                                //dwCurTime = NvrSysGetCurTimeMSec();
                                PRINTDBG("cmd:%d equal cur h:%d v:%d ,lasth:%d,lastv:%d,interval:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang,dwCurTime-dwTime);
                                if(dwCurTime-dwTime>msg->tCmdNode.dwTimeOut+msg->tCmdNode.dwDelay)//两秒还没移动，继续发送
                                {
                                    //移动指令未生效，继续发送
                                    
                                    PRINTDBG("retry send cmd:%d",msg->tCmdNode.eMsgCmd);
                                    NvrFixDevWriteSerial(msg->tCmdNode.bySerialID,pbuff, dwCmdLen, &dwRlen);
                                    msg->tCmdNode.dwSendTime=dwCurTime;
                                    msg->tCmdNode.dwDelay = 0;
                                    bNeedAgain = TRUE;
                                }
                                
                            }
                        }*/

                        if(msg->tCmdNode.eMsgCmd == NVR_PTZCTRL_TYPE_MOVESTOP)
                        {
                            pthread_mutex_lock(&g_hSerialQueryMutex);

                            if(msg->tCmdNode.dwCurrentHang == g_dwCurrentHAngle && msg->tCmdNode.dwCurrentVang == g_dwCurrentVAngle && g_nQuerying == QUERY_STATE_NOEXE )
                            {
                                
                                PRINTDBG("cmd:%d stop equal cur h:%d v:%d ,lasth:%d,lastv:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang);
                                msg->tCmdNode.bDone = TRUE;
                                bNeedAgain = FALSE;
                            }
                            else
                            {
                                //dwCurTime = NvrSysGetCurTimeMSec();
                                PRINTDBG("cmd:%d stop diff cur h:%d v:%d ,lasth:%d,lastv:%d,interval:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang,dwCurTime-dwTime);
                                if(dwCurTime-dwTime > msg->tCmdNode.dwTimeOut)//两秒还没移动，继续发送
                                {
                                    //移动指令未生效，继续发送
                                    PRINTDBG("re send cmd:%d",msg->tCmdNode.eMsgCmd);
                                    NvrFixDevWriteSerial(msg->tCmdNode.bySerialID,pbuff, dwCmdLen, &dwRlen);
                                    msg->tCmdNode.dwSendTime=dwCurTime;
                                    bNeedAgain = TRUE;
                                }
                                msg->tCmdNode.dwCurrentHang = g_dwCurrentHAngle;
                                msg->tCmdNode.dwCurrentVang = g_dwCurrentVAngle;
                            }
                            pthread_mutex_unlock(&g_hSerialQueryMutex);
                        }

                        if(msg->tCmdNode.eMsgCmd == NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY ||  msg->tCmdNode.eMsgCmd == NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY)
                        {
                            PRINTDBG("cmd:%d req diff cur h:%d v:%d ,lasth:%d,lastv:%d,interval:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang,dwCurTime-dwTime);
                            if(dwCurTime-dwTime > msg->tCmdNode.dwTimeOut)//超时,继续发送
                            {
                                //移动指令未生效，继续发送
                                PRINTDBG("re send cmd:%d",msg->tCmdNode.eMsgCmd);
                                NvrFixDevWriteSerial(msg->tCmdNode.bySerialID,pbuff, dwCmdLen, &dwRlen);
                                msg->tCmdNode.dwSendTime=dwCurTime;
                                bNeedAgain = TRUE;
                            }
                        }
                        
        			}
        			else if(pbuff[0] == 0xEE)
        			{
        				PRINTDBG("ptz ee msg\n");
                        if(msg->tCmdNode.eMsgCmd == NVR_PTZCTRL_SET_PTZ_RATIO)
                        {
                            PRINTDBG("cmd:%d NVR_PTZCTRL_SET_PTZ_RATIO diff cur h:%d v:%d ,lasth:%d,lastv:%d,interval:%d\n",msg->tCmdNode.eMsgCmd,g_dwCurrentHAngle,g_dwCurrentVAngle,msg->tCmdNode.dwCurrentHang,msg->tCmdNode.dwCurrentVang,dwCurTime-dwTime);
                            if(dwCurTime-dwTime > msg->tCmdNode.dwDelay)//延时继续发送
                            {
                                //移动指令未生效，继续发送
                                PRINTDBG("re send cmd:%d",msg->tCmdNode.eMsgCmd);
                                NvrFixDevWriteSerial(msg->tCmdNode.bySerialID,pbuff, dwCmdLen, &dwRlen);
                                msg->tCmdNode.dwSendTime=dwCurTime;
                                msg->tCmdNode.bDone = TRUE;
                                bNeedAgain = FALSE;
                                NvrFixDevStartPTZQuery(0);
                            }
                        }
        			}
                    else if(pbuff[0] == 0xAA)
                    {
                        PRINTDBG("ptz aa msg\n");
                    }
                    else
                    {
                        PRINTDBG("ptz err msg\n");
                    }
                }
#if 0
                if(msg->tCmdNode.bySerialID == 1)//热成像
                {
        			if(NvrDevXcoreCmdLegalVerify(pbuff)) 
        			{
                        PRINTDBG("R C XIANG cmd\n");
        			}
        			else
        			{
        				PRINTDBG("rcx err msg\n");
        			}
                }
#endif
                if(!bNeedAgain)
                {
                    pthread_cond_signal(&exeover);
                    PRINTDBG("delete msg %d\n",msg->tCmdNode.eMsgCmd);
        			list_del(s);	
        			free(msg);
                }

    		}	
    	}
    	else
    	{							
            pthread_cond_wait(&hasmsg, &g_hSerialHistoryListMutex);
    	}
        pthread_mutex_unlock(&g_hSerialHistoryListMutex);
        usleep(500000);
    }while(1);
    return NULL;
}


static void* NvrFixDevPTZQueryThread()
{

	TNvrPtzCtrlInfo tCtrlInfo;
	TThreadInfoRecord tThreadInfo;
	mzero(tCtrlInfo);
	mzero(tThreadInfo);

	//添加线程信息
	tThreadInfo.m_dwThreadId = getpid();
	mcopy(tThreadInfo.m_strThreadName, "NvrFixDevPTZQueryThread");
	OsApi_AddThreadInfo( &tThreadInfo );

	PRINTDBG("[IPCDEV]NvrFixDevPTZQueryThread Thread %d created!\n", getpid());

#ifndef WIN32
	prctl(PR_SET_NAME, "NvrFixDevPTZQueryThread", 0, 0, 0);
#endif

    u8 abyCmdQueryEE[6] = {0xee, 0x06, 0x02, 0x09, 0x00, 0xff};//查询PTZ指令
    //u8 abyCmdStop[6] = {0xff, 0x01, 0x0, 0x0, 0x00, 0x01};//查询PTZ指令
    u32 dwLen = 0;



    u32 dwTimeOutCnt = 0;
    u32 dwEqualCnt = 0;
    BOOL32 bQueryContinue = FALSE; //告诉线程不需要WAIT
    BOOL32 bQueryStart = FALSE;//告诉线程继续查询
    OsApi_SemTake(g_hSemExeOver);
    OsApi_SemTake(g_hSemExeWait);


	while(TRUE)
	{
        pthread_mutex_lock(&g_hSerialQueryMutex);
        //g_nQuerying=QUERY_STATE_WAIT;
        if(!bQueryContinue)
        {
            PRINTDBG("wait recv cmd\n");
            pthread_mutex_unlock(&g_hSerialQueryMutex);
            OsApi_SemTake(g_hSemExeWait);
            pthread_mutex_lock(&g_hSerialQueryMutex);
            PRINTDBG("wait recv cmd over\n");
            //pthread_cond_wait(&querywait,&g_hSerialQueryMutex);
        }
        else
        {
            bQueryContinue = FALSE;
        }
        
        if(g_nNotify == QUERY_NOTIFY_NONEEDQUERY)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NONEEDQUERY\n");
            g_nQuerying=QUERY_STATE_NOEXE;//查询停止
            g_dwHasStart--;
            PRINTDBG("g_dwHasStart:%d\n",g_dwHasStart);
            pthread_mutex_unlock(&g_hSerialQueryMutex);
            bQueryStart = FALSE;
            g_nNotify = QUERY_NOTIFY_NONE;
            continue;
        }
        
        if(g_nNotify == QUERY_NOTIFY_NEEDPAUSE)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NEEDPAUSE\n");
            g_nQuerying=QUERY_STATE_PAUSED;//查询暂停
            OsApi_SemGive(g_hSemExeOver);
            pthread_mutex_unlock(&g_hSerialQueryMutex);
            bQueryStart = FALSE;
            g_nNotify = QUERY_NOTIFY_NONE;
            continue;
        }

        if(g_nNotify == QUERY_NOTIFY_RESUME)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_RESUME\n");
            OsApi_SemGive(g_hSemExeOver);
            bQueryStart = TRUE;
        }

        if(g_nNotify == QUERY_NOTIFY_NEEDQUERY)
        {
            PRINTDBG("recv cmd:QUERY_NOTIFY_NEEDQUERY\n");
            bQueryStart = TRUE;
            OsApi_SemGive(g_hSemExeOver);
            
        }
        g_nNotify = QUERY_NOTIFY_NONE;

        if(bQueryStart)
        {
           //继续查询
           PRINTDBG("querying....\n");
            
            //为了控制不要查询太快
            PRINTDBG("query control:send:%d,recv:%d\n",g_dwEESendCount,g_dwEERecvCount);
            NvrFixDevWriteSerial(g_wPtzCtrlChnId, abyCmdQueryEE, 6, &dwLen);
            g_dwEESendCount++;
            g_nQuerying=QUERY_STATE_NORES;//查询未返回

            struct timespec abstime;
            struct timeval now;
            long timeout_ms = 500+(g_dwEESendCount-g_dwEERecvCount)*50; // wait time ms
            gettimeofday(&now, NULL);
            long nsec = now.tv_usec * 1000 + (timeout_ms % 1000) * 1000000;
            abstime.tv_sec=now.tv_sec + nsec / 1000000000 + timeout_ms / 1000;
            abstime.tv_nsec=nsec % 1000000000;
            
            PRINTDBG("query control 2:send:%d,recv:%d timeout time:%d\n",g_dwEESendCount,g_dwEERecvCount,timeout_ms);
            if(0 == pthread_cond_timedwait(&queryReswait,&g_hSerialQueryMutex,&abstime))
            {
                PRINTDBG("querying....end,%d\n",g_nQuerying);
                g_nQuerying=QUERY_STATE_HASRES;//查询y返回
            }
            else
            {
                g_nQuerying=QUERY_STATE_TIMEOUT;//查询超时
                PRINTDBG("querying....end timeout,%d\n",g_nQuerying);
            }

                        //如果超时了继续查询
            if(g_nQuerying ==QUERY_STATE_TIMEOUT)
            {
                lastPtzAngle_H = -1;
                lastPtzAngle_V = -1;
                lastPtzZoom    = -1;
                bQueryContinue = TRUE;//无需WAIT
                dwTimeOutCnt++;
                //增加查询间隔
            }
            else
            {
                dwTimeOutCnt=0;
                PRINTDBG("query stop:lasth:%d,v:%d,curh%d,v%d\n",lastPtzAngle_H,lastPtzAngle_V,g_dwCurrentHAngle,g_dwCurrentVAngle);
                if(lastPtzAngle_H != g_dwCurrentHAngle || g_dwCurrentVAngle != lastPtzAngle_V ||g_dwCurrentZoom != lastPtzZoom )
                {
                    g_dwMoving++;
                    lastPtzAngle_H = g_dwCurrentHAngle;
                    lastPtzAngle_V = g_dwCurrentVAngle;
                    lastPtzZoom    = g_dwCurrentZoom;
                    bQueryContinue = TRUE;
                    NvrFixPTZHVReport();
                    NvrFixDevNotifyUpdatePtzOsd();
                }
                else
                {
                    //如果相等了，停止查询
                     dwEqualCnt++;
                     if(dwEqualCnt >= 5)//连续5次相等，认为已经停止
                    {
                         g_dwMoving = 0;
                         lastPtzAngle_H = -1;
                         lastPtzAngle_V = -1;
                         lastPtzZoom    = -1;
                         g_bStoping = FALSE;//相等表示已经停止
                         g_nQuerying=QUERY_STATE_NOEXE;//未查询
                         bQueryContinue = FALSE;
                         bQueryStart = FALSE;
                         dwEqualCnt = 0;
                         //g_nQuerying = QUERY_STATE_STOP;
                         PRINTDBG("query stop\n");
                         NvrFixDevPowerOffMemSavePTZ();
                         NvrFixPTZHVReport();
                         NvrFixDevNotifyUpdatePtzOsd();

                         ///< 目前云台通道只有可见光通道，暂时默认为1
                         NvrFixDevSmokeFireTrigger(NVR_DEV_SMOKE_FIREE_START, 1, g_byPtzPresetId);
                    }
                    else
                    {
                        lastPtzAngle_H = g_dwCurrentHAngle;
                        lastPtzAngle_V = g_dwCurrentVAngle;
                        lastPtzZoom    = g_dwCurrentZoom;
                        bQueryContinue = TRUE;
                    }

                }
            }

           usleep(100000);

        }
        pthread_mutex_unlock(&g_hSerialQueryMutex);

	}
    
	PRINTDBG( "NvrFixDevPTZQueryThread Task Quit...\n" );
	OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );
	OsApi_TaskExit();
    
	
	return NULL;

}



NVRSTATUS NvrFixDevPtzCtrl(const u16 wChnId,TNvrPtzCtrlInfo tCtrlInfo)
{
	s32 nRet = 0;
	TNvrDoubleListPushAttr tCmdAttr;
	mzero(tCmdAttr);

	PRINTTMP( "NvrFixDevPtzCtrl [PUSH]++ Id:%u, Type:%u, IspSpeed:%u, PanSpeed:%u, TilSpeed:%u, Num:%u\n",
                            wChnId,
                            tCtrlInfo.eCtrlType,
                            tCtrlInfo.wIspSpeed,
                        	tCtrlInfo.wPanSpeed,
                        	tCtrlInfo.wTilSpeed,
                        	tCtrlInfo.wNumber);

    tCmdAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_HIGH;
    tCmdAttr.byMergerType = NVR_QUEUE_NODE_MERGER_TYPE;
	tCmdAttr.dwType = (u32)wChnId;//用来传设备id
	tCmdAttr.dwDataLen = sizeof(tCtrlInfo);
	tCmdAttr.pchDataBuf = (s8*)(&tCtrlInfo);                         
	nRet = NvrQueuePush(g_ptLcDevQueue, &tCmdAttr);
	if (NVR_ERR__OK != nRet)
	{
		PRINTERR( "[IPCDEV]NvrFixDevPtzCtrl queue push failed, ret:%d\n",nRet);
		return NVR_ERR__ERROR;
	}


	return NVR_ERR__OK;
}

#define FINDHEAD  1
#define FINDLEN   2
#define FINDBODY  3
#define FINDCRC   4
#define FINDEND   5
#define CMDLEN 1024


NVRSTATUS NvrSysSetPTZVer(char *pPtzver,char*pLenver,char*pFocusVer)
{	
    static s8 tmp[NVR_MAX_STR32_LEN+1] = {0};
	if(NULL != pPtzver && NULL != pLenver && NULL != pFocusVer )
	{
		snprintf(tmp,NVR_MAX_STR32_LEN,"%s-%s-%s",pPtzver,pLenver,pFocusVer);
        snprintf(g_achPtzVer,NVR_MAX_STR32_LEN,tmp);//由于系统版本直接使用的是g_achPtzVer，进行扩展
		PRINTDBG("singleclipver:%s\n",tmp);
	}
    NvrSysSetClipVer(tmp);
	return NVR_ERR__OK;	
}


//此接口从IPCV7拷贝过来
NVRSTATUS NvrDevParseNewSerialPro(u8 bySerialId,u8 *pbyBuf, u8 byLen)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    u16 wHPos = 0;
    u16 wVPos = 0;
    u16 wZoom = 0;
    //u32 dwHVPos = 0;
    //float fxangle;
    //float fyangle;
    //float fwangle;
    //float fhangle;
    //float fCurZoomRatio;
    u8 checksum = 0;

    //校验和
    u8 CurrentLen = pbyBuf[1];
    if(pbyBuf[0] == 0xEE)//新协议
    {
        g_dwEERecvCount++;
        checksum = 0xEE + NvrFixDevCalcCheckSum(pbyBuf, CurrentLen);
        if (( pbyBuf[CurrentLen - 1]) != checksum)
        {
            PRINTERR( "[fixDEV]NvrDevParseNewSerialPro:Serial data checksum err.\n");
            return eRet;
        }

        if(pbyBuf[2] == 0x2)        //自定义的命令类型
        {
            if(pbyBuf[4] != 0)
            {
                PRINTERR( "[fixDEV]Operation Failed\n");
                return eRet;
            }
            else 
            {
                PRINTERR("[fixDEV]Operation Succed\n");
                switch (pbyBuf[3])
                {
                    //云台缩放操作完成返回的ptz信息
                    case 0x8:
                    {
                        wHPos = (pbyBuf[6]<<8)|pbyBuf[7];
                        wVPos = (pbyBuf[8]<<8)|pbyBuf[9];
                        g_dwCurrentHAngle = wHPos;
                        g_dwCurrentVAngle = wVPos;
       
                        wZoom = (pbyBuf[10]<<8)|pbyBuf[11];
                        g_dwCurrentZoom = wZoom;
                        g_dwEERecvCount--;//在FF 00 5D中会增加
                        
                        PRINTDBG("[fixDEV] Current H Angle: %d\n",g_dwCurrentHAngle);
                        PRINTDBG("[fixDEV] Current V Angle: %d\n",g_dwCurrentVAngle);
                        PRINTDBG("[fixDEV] Current Zoom: %d\n",g_dwCurrentZoom);

        
                        //上报AR平台ptz信息,暂不处理
                        #if 0
                        fxangle =(float) g_dwCurrentHAngle /100 ;
                        fyangle =(float)((((36000 - g_dwCurrentVAngle) <= 6000) ? (g_dwCurrentVAngle - 36000):g_dwCurrentVAngle ))/100;
                        IpcDevPTZGetHVAngle(g_dwCurrentZoom,&dwHVPos);
        
                        fhangle = (float)(dwHVPos & (0xFFFF))/100;
                        fwangle = (float)((dwHVPos >> 16)&(0xFFFF))/100;
                        fCurZoomRatio = (float)g_dwCurZoomRatio /100 ;
                        IpcDevReportDataToARbyHTTP(fwangle,fxangle,fhangle,fyangle,g_dwMinZoomRatio,g_dwMaxZoomRatio,fCurZoomRatio);
        
                        //PTZ值上报联动处理使用定时器函数来处理
                        IpcCoreApi_TimerSet( g_hPtzProcessTimer, 1, IpcDevPtzProcessTimerCB, NULL);
                        #endif 
                    }
                    break;

                    //云台返回GPS经度信息
                    case 0x1:
                	{
                        TNvrGeoGpsInfo tGpsInfo;
                        s32 nLatitude;		    ///<纬度
                        s32 nLongitude;		    ///<经度
                        s32 nAltitude;		    ///<经度
                		nLongitude = (pbyBuf[6] << 16) | (pbyBuf[7] << 8) | pbyBuf[8];
                        nLatitude  = (pbyBuf[9] << 16) | (pbyBuf[10] << 8) | pbyBuf[11];
                        nAltitude  = (pbyBuf[12] << 16) | (pbyBuf[13] << 8) | pbyBuf[11];
                    	NvrGeoGetGpsInfo(&tGpsInfo);
                        tGpsInfo.dLongitude = 1.0*nLongitude/3600;
                        tGpsInfo.dLatitude = 1.0*nLatitude/3600;
                        tGpsInfo.dAltitude = 1.0*nAltitude/3600;
                    	NvrGeoSetGpsInfo(&tGpsInfo);
                	}
                    break;
                    default:
                    {
                        PRINTERR("[fixDEV]IpcDevParseSerialDate:Unknown Cmd.\n");
                        return eRet;
                    }
                    break;
                }
            }
        }
        else
        {
            PRINTERR("[fixDEV]NvrDevParseNewSerialPro:New protocol has error.\n");
            return eRet;
        }
    }
    else if(pbyBuf[0] == 0xFF)//标准PELCO_D协议
    {
        checksum = NvrFixDevCalcCheckSum(pbyBuf, byLen);
        if (( pbyBuf[byLen - 1]) != checksum)
        {
            PRINTERR("[fixDEV]NvrDevParseNewSerialPro:Serial data checksum err.\n");
            return eRet;
        }

        if(pbyBuf[3] == 0x5D)//解析可见光镜头步长
        {
            wZoom = (pbyBuf[4]<<8)|pbyBuf[5];
            g_dwCurrentZoom = wZoom;
            g_dwEERecvCount++;

            PRINTDBG("[fixDEV] Current H Angle: %d\n",g_dwCurrentHAngle);
            PRINTDBG("[fixDEV] Current V Angle: %d\n",g_dwCurrentVAngle);
            PRINTDBG("[fixDEV] Current Zoom: %d\n",g_dwCurrentZoom);

            pthread_cond_signal(&queryReswait);

            //上报AR平台ptz信息,暂不处理
            #if 0
            fxangle =(float) g_dwCurrentHAngle /100 ;
            fyangle =(float)((((36000 - g_dwCurrentVAngle) <= 6000) ? (g_dwCurrentVAngle - 36000):g_dwCurrentVAngle ))/100;
            IpcDevPTZGetHVAngle(g_dwCurrentZoom,&dwHVPos);
            
            fhangle = (float)(dwHVPos & (0xFFFF))/100;
            fwangle = (float)((dwHVPos >> 16)&(0xFFFF))/100;
            fCurZoomRatio = (float)g_dwCurZoomRatio /100 ;
            IpcDevReportDataToARbyHTTP(fwangle,fxangle,fhangle,fyangle,g_dwMinZoomRatio,g_dwMaxZoomRatio,fCurZoomRatio);
            
            //PTZ值上报联动处理使用定时器函数来处理
            IpcCoreApi_TimerSet( g_hPtzProcessTimer, 1, IpcDevPtzProcessTimerCB, NULL);
            #endif 
        }
        if(pbyBuf[0] == 0xff && pbyBuf[3] == 0x73)
        {
            u16 wPtzFirmVer = 0;     //云台固件版本号
            u16 wPtzSotfVer = 0;       //云台软件版本号
            
            //科达球机固件版本号
            wPtzFirmVer = pbyBuf[2];

            //返回软件版本号
            wPtzSotfVer = pbyBuf[4] << 8 | pbyBuf[5];


            if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
            {
                if(pbyBuf[2] == 0x10)
                {                
                    NvrFixHistoryCmdClear(g_byPtzCtrlSerialId,NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY);
                    memset(g_achPtzVertmp, 0 , sizeof(g_achPtzVertmp));
                    sprintf(g_achPtzVertmp, "%d.%d", pbyBuf[4], pbyBuf[5]);
                    //NvrSysSetPTZVer(g_achPtzVer,g_achLensVer,g_achFocusVer);
                }

                if(pbyBuf[2] == 0x30)
                {
                    NvrFixHistoryCmdClear(g_byPtzCtrlSerialId,NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY);
                    if(pbyBuf[4] == 0x04)
                    {
                        //暂时固定，安霸528使用c775镜头
                        sprintf(g_achFocusVer, "C775-%d.%d", pbyBuf[4], pbyBuf[5]);
                    }
                    else
                    {
                        sprintf(g_achFocusVer, "UNKNOW-%d.%d", pbyBuf[4], pbyBuf[5]);
                    }
                    //NvrSysSetPTZVer(g_achPtzVer,g_achLensVer,g_achFocusVer);
                }

            }
            else
            {
                sprintf(g_achPtzVer, "%d.%d.%d", wPtzFirmVer, (wPtzSotfVer>>8), (wPtzSotfVer & 0xFF));
            }

#ifdef _CV2X_
            g_bPtzGetVerSuc = TRUE;
            if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
            {
                NvrSysSetPTZVer(g_achPtzVertmp,g_achLensVer,g_achFocusVer);
            }
            else
            { 
                NvrSysSetClipVer(g_achPtzVer);
            }
#else
            NvrSlaveToHostMessage(1,0,(void *)g_achPtzVer);
#endif

            PRINTDBG("NvrFixDevParseSerialDate g_achPtzVer %s,wPtzFirmVer %x,wPtzSotfVer %x,focus ver:%s\n",g_achPtzVer,wPtzFirmVer,wPtzSotfVer,g_achFocusVer);
        }
    }

    return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevParseSerialReadDate(u8 bySerialId,u8 *pbyBuf, s32 nLen)
{
	u32 i = 0;

    //专门为热成像的，其他请不要引用及修改
	static s32 nProcessState = FINDHEAD;
	static u8 abyCmdbuff[CMDLEN]={0};
	static u32 dwPos=0;
	static u32 dwCurSize = 0;
	static u32 dwSecSize = 0;

    //for ptz
    u8 abyCmdbuffTmp[128];
    u32 dwCmdLen=0;

    static RingBuffer *pRingBuff = NULL;
    if(!pRingBuff)//为云台读取准备的，其他的串口可以再建一个独立的
    {
        pRingBuff = RingBuffer_create(512);
    }

    //科达云台同时返回水平俯仰角度,特殊处理，NvrDevParseNewSerialPro中只处理FF 7字节指令，和EE指令
    if (9 == nLen && pbyBuf[0] == 0xff && pbyBuf[2] == 0x5D)
    {
        g_tPowerOffMemCfg[bySerialId].wHorizonPos = (pbyBuf[3]<<8)|pbyBuf[4];
        g_tPowerOffMemCfg[bySerialId].wVerticalPos = (pbyBuf[5]<<8)|pbyBuf[6];
        
        PRINTDBG("NvrFixDevParseSerialDate id:%d curH %u,curV %u\n", bySerialId, g_tPowerOffMemCfg[bySerialId].wHorizonPos, g_tPowerOffMemCfg[bySerialId].wVerticalPos);

        if(g_byPtzType != NVR_CAP_PTZ_TYPE_MINJIA2)
        {
            OsApi_TimerSet( g_hPowerOffMemTimer, g_nPowerOffInterval * 1000, NvrFixDevPowerOffMemTimerCB, NULL);//若位置固定60s，则保存位置信息
        }
        return NVR_ERR__OK;
    }

    if(bySerialId == SERIAL_TYPE_PTZCTRL && pRingBuff)
    {
        //串口收到的数据全部存入buff，指令全部从buff中获取.V7方式write完立马读容易超时，指令也容易不完整或指令合并导致漏解析（只解析前一条）
        // mzero(abyCmdbuffTmp);
        // while(RingBuffer_Getcmd(pRingBuff,abyCmdbuffTmp,&dwCmdLen)>0)
        // {
            //先处理buff中指令，保证后面的插入都是成功的，当然必须小于512字节
        //    NvrDevParseNewSerialPro(bySerialId,abyCmdbuffTmp,dwCmdLen);
        //    mzero(abyCmdbuffTmp);
       //  }
        

        //指令处理完再放入，只要小于512，能够保证全部放入
        RingBuffer_write(pRingBuff,pbyBuf,nLen);

        //再次执行
         mzero(abyCmdbuffTmp);
        //RingBuffer_Getcmd 内部会自动跳过无效数据，不用担心来自串口插入的无效数据
         while(RingBuffer_Getcmd(pRingBuff,abyCmdbuffTmp,&dwCmdLen)>0)
         {
            NvrDevParseNewSerialPro(bySerialId,abyCmdbuffTmp,dwCmdLen);
            mzero(abyCmdbuffTmp);
         }

    }


    //目前固定是艾瑞FT2,如果没有开启不投递，在初始化时设置艾瑞参数会有返回值，不用处理
	if(bySerialId == SERIAL_TYPE_RECHENGXIANG )//温度查询回复
	{

		for(i = 0;i<nLen;i++)
		{
			if(nProcessState == FINDHEAD)
			{
				if(pbyBuf[i]== 0x55)
				{
					abyCmdbuff[dwPos] = pbyBuf[i];
					nProcessState = FINDLEN;
					dwPos++;
				}
				else
				{
					continue;
				}
			}else
			if (nProcessState == FINDLEN)
			{
				abyCmdbuff[dwPos] = pbyBuf[i];
				dwSecSize = pbyBuf[i];
				nProcessState = FINDBODY;
				dwPos++;
			}else
			if (nProcessState == FINDBODY)
			{
				abyCmdbuff[dwPos] = pbyBuf[i];
				dwCurSize++;
				if(dwSecSize == dwCurSize)
				{
				   nProcessState = FINDEND;
				   dwSecSize = 2;
				   dwCurSize =0;
				}
				dwPos++;
			}else
		
			if(nProcessState == FINDEND)
			{
			
				abyCmdbuff[dwPos] = pbyBuf[i];
				dwCurSize++;
				
				if (dwSecSize == dwCurSize)
				{
					nProcessState = FINDHEAD;
					dwSecSize = 0;
					dwCurSize = 0;
					if(abyCmdbuff[dwPos]==0xAA && abyCmdbuff[dwPos-1]==0xEB)
					{
					    if( g_bTempCheck)
                        {
					        NvrFixXcoreQueryAckNotify(abyCmdbuff,abyCmdbuff[1]+4);
                        }

                        if(abyCmdbuff[3] == 0x33 && abyCmdbuff[4] == 0x33)
                        {
                            g_dwCurrenXcoreZoom=abyCmdbuff[6]<<8|abyCmdbuff[5];
                            PRINTDBG("xcore zoom:%d,low:%d,hight:%d\n",g_dwCurrenXcoreZoom,abyCmdbuff[5],abyCmdbuff[6]);
                            OsApi_SemGive(g_hSemQueryRES);
                            
                        }

                       /*
					   PRINTDBG("push cmd:\n");
					   u32 j=0;
					   for(j=0;j<(abyCmdbuff[1]+4);j++)
					   {
					   	 PRINTDBG("%x ",abyCmdbuff[j]);
					   }
					   PRINTDBG("\n");*/
					}
   				   dwPos=0;

				}
				else
				{
					dwPos++;
				}
			}
		}
	}

    return NVR_ERR__OK;

}

void NvrFixDevParseSerialReadDateCB(u8 bySerialId,u8 *pbyBuf, s32 nLen)
{
	s8 achPrintBuf[3*NVR_FIX_DEV_MAX_TRANSDATA_LEN+1] = {0};

	u32 i = 0;

	for(i=0; i<nLen; i++)
	{
		snprintf(achPrintBuf+i*3, sizeof(achPrintBuf), " %.2x", pbyBuf[i]);
	}
	PRINTDBG("SerialId %d, pbyBuf: %s, nLen: %d\n",bySerialId, achPrintBuf, nLen);

	if(g_bPtzUpdating)
	{
		if((7 == nLen && 0xFD == pbyBuf[3]) || (8 == nLen && (0xFD == pbyBuf[4]||0xFD == pbyBuf[3])))	//云台对开始升级指令的回复
		{
			PRINTDBG("ReadSerial receive start cmd respond\n");
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_RSP);
		}
		else if(1 == nLen && 'C' == pbyBuf[0] && !g_bRecevieC)	//云台对'd'指令回复
		{
			PRINTDBG("ReadSerial receive 'C'\n");
			//接收到一次'C'后不再接收直到下次升级开始，防止升级过程死循环。
			g_bRecevieC = TRUE;
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_C);
		}
		else if( NVR_FIX_DEV_UPTMSG_ACK == pbyBuf[0])
		{
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
		}
		else if(NVR_FIX_DEV_UPTMSG_KEDA_ACK == pbyBuf[0])
		{
			PRINTDBG("receive ACK\n");
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
		}
		else if(NVR_FIX_DEV_UPTMSG_CAN == pbyBuf[0])
		{
			PRINTDBG("ReadSerial receive CAN\n");
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_CAN);

		}
		else if( NVR_FIX_DEV_UPTMSG_NAK == pbyBuf[0])
		{
			PRINTDBG("ReadSerial receive NAK\n");
			NvrFixDevSetUptStat(NVR_FIX_DEV_UPTSTAT_RCV_NAK);
		}
		else
		{

		}
    }
    if(g_bLensUpdating)
    { 
        if((0xFD == pbyBuf[3]) && (0xFE == NvrFixDevCalcCheckSum(pbyBuf, 7)))   //对开始升级指令的回复
        {
            byLensUptStartStat = NVR_FIX_DEV_UPTSTAT_RCV_RSP;
        }
        else if(NVR_FIX_DEV_UPTMSG_ACK == pbyBuf[0])
        {
            NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
        }
        else if(NVR_FIX_DEV_UPTMSG_KEDA_ACK == pbyBuf[0])
        {
            PRINTVIP("LensUpdateReadSerial receive ACK\n");
            NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_ACK);
        }
        else if(NVR_FIX_DEV_UPTMSG_CAN == pbyBuf[0])
        {
            PRINTVIP("LensUpdateReadSerial receive CAN\n");
            NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_CAN);
        
        }
        else if(NVR_FIX_DEV_UPTMSG_NAK == pbyBuf[0])
        {
            PRINTVIP("LensUpdateReadSerial receive NAK\n");
            if(!g_bLensRecevieC)
            {
                g_bLensRecevieC = TRUE;
                NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_C);
            }
            else
            {
                NvrFixDevSetLensUptStat(NVR_FIX_DEV_UPTSTAT_RCV_NAK);
            }
        }
        else
        {
        
        }
    }
    if(g_bLensVerGeting)
    {
        if((7 == nLen) && (NvrFixDevCalcCheckSum(pbyBuf, 7)  == pbyBuf[6]))
        {
            g_bLensVerGeting = FALSE;
            if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
            {
              snprintf(g_achLensVer,NVR_FIX_MAX_LENSVER_LEN,"%d.%d", pbyBuf[4], pbyBuf[5]);
              PRINTDBG("NvrFixDevParseSerialDate g_achPtzVer %s,lensver %s,focus ver:%s\n",g_achPtzVer,g_achLensVer,g_achFocusVer);
              NvrSysSetPTZVer(g_achPtzVertmp,g_achLensVer,g_achFocusVer);
            }
            else
            { 
              snprintf(g_achLensVer,NVR_FIX_MAX_LENSVER_LEN,"%.2x.%.2x", pbyBuf[4], pbyBuf[5]);
              PRINTVIP("LensVer %s !\n", g_achLensVer);
            }
            
            NvrSysSetCameraLensVer(g_achLensVer);
        }
        else
        {
            PRINTVIP("get LensVer failed, data error\n");
           // return;
        }
    }

	NvrFixDevParseSerialReadDate(bySerialId,pbyBuf,nLen);

    return;
}

s32 NvrFixDevPowerOffMemTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
	FILE* pFile = NULL;
	
#ifndef _QCOM_
	u32 dwCurZoomPosition;
	
	IspActionParam(0,ISP_ACT_GET_ZOOM_POSITION,(void *)&dwCurZoomPosition);
	g_tPowerOffMemCfg[0].nZoomPos = (s32)dwCurZoomPosition;

	NvrFixMcSetStablizerByZoom(0,dwCurZoomPosition);

	PRINTDBG("NvrFixDevGetZoomTimerCB dwCurZoomPosition:%lu\n",dwCurZoomPosition);
#endif

	PRINTDBG("NvrFixDevPowerOffMemTimerCB save cfg\n");

	pFile = fopen(LCAM_DEV_POWEROFFRSM_CFG_PATH, "w+");
	if(NULL == pFile)
	{
		PRINTERR("IpcDevPowerOffMemTimerCB open file failed! \n");
		return -1;
	}

	fseek(pFile, 0, SEEK_SET);

	fwrite(&g_tPowerOffMemCfg[0], sizeof(g_tPowerOffMemCfg), 1, pFile);

	PRINTDBG( "[IPCDEV]IpcDevPowerOffMemTimerCB, serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u saved to file\n", \
                    g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[0].nZoomPos);
	fclose(pFile);

    
    pFile = fopen(LCAM_DEV_POWEROFFRSM_CFG_PATH, "r+");
     if(pFile == NULL)
     {
        PRINTERR("fopen LCAM_DEV_POWEROFFRSM_CFG_PATH fail\n"); 
        return NVR_ERR__OK;
     }
    
     fseek(pFile, 0, SEEK_SET);
     fread(&g_tPowerOffMemCfg[0], sizeof(g_tPowerOffMemCfg), 1, pFile);
    
     PRINTERR( "[IPCDEV]NvrFixDevPowerOffMemTimerCB, serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u read from file\n", \
                     g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[0].nZoomPos);
     fclose(pFile);

     ///< 防止文件没有真实保存falsh，增加同步
     sync();
	return 0;
}

s32 NvrFixDevPowerOffMemSavePTZ()
{
	FILE* pFile = NULL;
	

	g_tPowerOffMemCfg[0].nZoomPos = (s32)g_dwCurrentZoom;
    g_tPowerOffMemCfg[0].wHorizonPos = (s32)g_dwCurrentHAngle;
    g_tPowerOffMemCfg[0].wVerticalPos = (s32)g_dwCurrentVAngle;

	PRINTDBG("NvrFixDevGetZoomTimerCB dwCurZoomPosition:%lu,h:%lu,v:%lu\n",g_dwCurrentZoom,g_dwCurrentHAngle,g_dwCurrentVAngle);

	pFile = fopen(LCAM_DEV_POWEROFFRSM_CFG_PATH, "w+");
	if(NULL == pFile)
	{
		PRINTERR("IpcDevPowerOffMemTimerCB open file failed! \n");
		return -1;
	}

	fseek(pFile, 0, SEEK_SET);

	fwrite(&g_tPowerOffMemCfg[0], sizeof(g_tPowerOffMemCfg), 1, pFile);

	PRINTDBG( "[IPCDEV]IpcDevPowerOffMemTimerCB, serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u saved to file\n", \
                    g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[0].nZoomPos);
	fclose(pFile);

    
    pFile = fopen(LCAM_DEV_POWEROFFRSM_CFG_PATH, "r+");
     if(pFile == NULL)
     {
        PRINTERR("fopen LCAM_DEV_POWEROFFRSM_CFG_PATH fail\n"); 
        return NVR_ERR__OK;
     }
    
     fseek(pFile, 0, SEEK_SET);
     fread(&g_tPowerOffMemCfg[0], sizeof(g_tPowerOffMemCfg), 1, pFile);
    
     PRINTERR( "[IPCDEV]NvrFixDevPowerOffMemTimerCB, serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u read from file\n", \
                     g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[0].nZoomPos);
     fclose(pFile);

	return 0;
}


s32 NvrFixDevSetFocusTimerCB( HTIMERHANDLE dwTimerId, void* param)
{
	TLensParams tLensParams;
	mzero(tLensParams);
	/// <步长1000经验值， 默认id为0
	tLensParams.dwLensId = 0;
	tLensParams.adwVal32[0] = 1000; 
	VidLensCtrl(0, g_eVidLensCmd,&tLensParams);
	if(g_eVidLensCmd != LENS_CTRL_FOCUS_STOP)
	{
		OsApi_TimerSet( g_hFocusCtrlTimer, 30, NvrFixDevSetFocusTimerCB, NULL);///<50m调用一次
	}
	else
	{
		OsApi_TimerStop(g_hFocusCtrlTimer); 
	}
	return 0;
}

s32 NvrFixDevGetFocusTimerCB( HTIMERHANDLE dwTimerId, void* param)
{
	u32 dwCurFocusPosition = 0;
	static TNvrFixCfg tNvrFixCfg;
    s32 nCurDayNightState = ISP_IRCUT_MODE_DAY;
	
	NvrFixCfgGetParam(&tNvrFixCfg);
	
	IspActionParam(0,ISP_ACT_GET_ZOOM_POSITION,(void *)&dwCurFocusPosition);
	PRINTDBG("NvrFixDevGetFocusTimerCB zoom postion:%lu\n",dwCurFocusPosition);
	IspActionParam(0,ISP_ACT_GET_FOCUS_POSITION,(void *)&dwCurFocusPosition);
    IspDoCmd(0,ISP_CMD_GET_DAYNIGHT_STATUS, &nCurDayNightState);

	tNvrFixCfg.tIspCfg[0].dwManualFocus = dwCurFocusPosition;
    tNvrFixCfg.tIspCfg[0].tZFCfg[nCurDayNightState].dwFocus = dwCurFocusPosition;///<也不知道这个在哪被赋值的,先在这里加上

	NvrFixCfgSetParam(&tNvrFixCfg);

	PRINTDBG("NvrFixDevGetFocusTimerCB dwCurFocusPosition:%lu\n",dwCurFocusPosition);

	return 0;
}

void NvrFixDevFocusExtraOperate(BOOL bAllRecover)
{
 	TNvrFixCfg tNvrFixCfg;
    TLcamIspParam tIspParam;

    mzero(tNvrFixCfg);
    mzero(tIspParam);

	LcamIspGetParam(0, &tIspParam);
	
	NvrFixCfgGetParam(&tNvrFixCfg);

	if(tIspParam.tAction.eIpcIspFocusMode == NVR_ISP_FOCUS_MODE_MANUAL)
	{
	    ///<恢复手动聚焦参数
		PRINTERR("dwCurZoomPosition:%lu\n",tNvrFixCfg.tIspCfg[0].dwManualFocus);

		sleep(2);//延时2s保证zoom完成
		IspActionParam(0,ISP_ACT_SET_FOCUS_POSITION,(void *)&tNvrFixCfg.tIspCfg[0].dwManualFocus);
	}
	else if (NVR_ISP_FOCUS_MODE_AUTO_C == tIspParam.tAction.eIpcIspFocusMode || NVR_ISP_FOCUS_MODE_AUTO_S == tIspParam.tAction.eIpcIspFocusMode)
	{
	    ///<电动镜头，完全恢复后，需要在zoom定位后，触发聚焦
	    if (bAllRecover)
	    {
            sleep(1);//延时1s保证zoom完成
            IspActionParam(0, ISP_ACT_SET_AUTO_FOCUS_ONCE, NULL);
            NvrFixLog("auto focus once \n");
	    }
	}


}

NVRSTATUS NvrFixDevPtzExtraOperate()
{
	FILE* pFile = NULL;
    TNvrFixPowerOffMemCfg tPowerOffMemCfg[NVR_DEV_SERIAL_MAX_NUM];//断电记忆参数
	NVRSTATUS eNvrRet = NVR_ERR__OK;
	u8 bySerialId = 0;
    ENvrCapClassId eCapClassId = NVR_CAP_ID_HW;  	///<能力集id

	TNvrPowerOffRsmCfg tPowerOffRsmCfg; 
	TNvrPtzCtrlInfo tPtzCtrlInfo;
	TNvrPtzBasicState tIpcPtzState;

	TNvrPresetInfo tPreInfo;
	s32 nZoomPos = 0;
	TNvrBrdPrdInfo tPinfo;
	u8 byTestFlag = 0;
	TNvrCooParam   tCooParam;	///<方位角参数配置

	ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型
	BOOL bPowerOffCfg = TRUE;

	mzero(tPinfo);
	mzero(tIpcPtzState);
	mzero(tPowerOffRsmCfg);
	mzero(tPtzCtrlInfo);
	mzero(tPreInfo);
	mzero(tCooParam);

	eNvrRet = NvrCapGetCapParam(eCapClassId, (void*)&g_tLcHwCapInfo);
	if(NVR_ERR__OK != eNvrRet)
	{
		PRINTERR("NvrFixDevPtzExtraOperate get cap failed, capid:%d, ret:%d\n",eCapClassId, eNvrRet);
		return eNvrRet;
	}

	NvrBrdApiBrdPinfoQuery(&tPinfo);
	byTestFlag = (tPinfo.dwFlag0 & 0x00000001);

	NvrFixDevGetPtzState(&tIpcPtzState);
	NvrFixCfgGetParamByFlag(&tPowerOffRsmCfg, sizeof(TNvrPowerOffRsmCfg), NVR_PTZ_MODULE_POWE_RSM_PARAM);
	NvrFixCfgGetParamByFlag(&tCooParam, sizeof(tCooParam), NVR_PTZ_MODULE_COOR_PARAM);	

	///<方位图基准角赋值
	g_tBasicPos.wBasicHPos = tCooParam.nX;
	g_tBasicPos.wBasicVPos = tCooParam.nY;
	if(g_tBasicPos.wBasicHPos >= 36000)//校验0~360
	{
		g_tBasicPos.wBasicHPos = 0;
	}


    //断电记忆
    pFile = fopen(LCAM_DEV_POWEROFFRSM_CFG_PATH, "r+");
    if(pFile == NULL)
    {
       PRINTERR("fopen LCAM_DEV_POWEROFFRSM_CFG_PATH fail\n"); 
		//恢复出厂时zoom恢复到最小变倍即最大视野
#ifndef _QCOM_
		nZoomPos = 0;
		NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&nZoomPos);
#endif
       //return NVR_ERR__OK;
		PRINTDBG("read file vh no cfg serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u,read from file\n", \
			                    g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[bySerialId].nZoomPos);

		bPowerOffCfg = FALSE;
	}
	else
	{
	    fseek(pFile, 0, SEEK_SET);
	    fread(&tPowerOffMemCfg, sizeof(tPowerOffMemCfg), 1, pFile);

	    memcpy(g_tPowerOffMemCfg,tPowerOffMemCfg,sizeof(tPowerOffMemCfg));
	    
	    NvrFixLog("read file vh cfg serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u,read from file\n", \
                g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[bySerialId].nZoomPos);

	    PRINTDBG("read file vh cfg serial0 Hpos:%u, Vpos:%u, serial1 Hpos:%u, Vpos:%u nZoomPos %u,read from file\n", \
	                    g_tPowerOffMemCfg[0].wHorizonPos, g_tPowerOffMemCfg[0].wVerticalPos,g_tPowerOffMemCfg[1].wHorizonPos, g_tPowerOffMemCfg[1].wVerticalPos,g_tPowerOffMemCfg[bySerialId].nZoomPos);
	    fclose(pFile);
	}


#ifndef _QCOM_
	NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&g_tPowerOffMemCfg[0].nZoomPos);
#endif

      
    
        NvrSysGetRecoveryFlag(&eRecoveryType, NVR_EVENT_CFG_RESET);
        PRINTDBG("resume bEnable %d, eResumeMode:%d byTestFlag %d\n", tPowerOffRsmCfg.bEnable, tPowerOffRsmCfg.eResumeMode, byTestFlag);
        //断电恢复(生产测试时不生效)
        if (tPowerOffRsmCfg.bEnable && !byTestFlag)
        {
            //上电加载预置位
            if (NVR_DEV_POWEROFF_LOAD_PRESET == tPowerOffRsmCfg.eResumeMode)
            {
                tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_LOAD;
                tPtzCtrlInfo.wNumber = (u16)tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_LOAD_PRESET];
    
                NvrFixCfgGetPresetInfo(tPtzCtrlInfo.wNumber, &tPreInfo);
                if(tPreInfo.bIsSet)
                {
                    NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
                    
                    //确保跑到位置，及查到的角度的准确性
                    OsApi_Delay(1000);
                }
                mzero(tPtzCtrlInfo);
            }
            else if (NVR_DEV_POWEROFF_MEMORY == tPowerOffRsmCfg.eResumeMode) //断电记忆
            {
                //恢复出厂或者断电记忆文件不存在不进行操作
                if (NVR_CFG_RESET_NO == eRecoveryType && bPowerOffCfg)
                {
                    PRINTDBG("cfg resume %d and ptz reset\n", eRecoveryType);
                    g_dwCurrentHAngle = g_tPowerOffMemCfg[0].wHorizonPos;
                    g_dwCurrentVAngle = g_tPowerOffMemCfg[0].wVerticalPos;
                    g_dwCurrentZoom   = g_tPowerOffMemCfg[0].nZoomPos;
    
                    if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)//敏佳云台
                    {
                        u8 abyCmd[20];
                        u8 byCmdLen = 12;
                        u32 wRealLen; 
                    
                        abyCmd[0] = 0xEE;
                        abyCmd[1] = 0xC;
                        abyCmd[2] = 0x2;
                        abyCmd[3] = 0x8;
                        abyCmd[4] = 0x6;
                    
                        abyCmd[6] = (g_dwCurrentHAngle & 0xFF);
                        abyCmd[5] = (g_dwCurrentHAngle & 0xFF00)>> 8;
                    
                        abyCmd[8] = (g_dwCurrentVAngle & 0xFF);
                        abyCmd[7] = (g_dwCurrentVAngle & 0xFF00)>> 8;
            
                        abyCmd[10] = g_dwCurrentZoom & 0xFF;
                        abyCmd[9] = (g_dwCurrentZoom & 0xFF00)>>8; 
                        
                        abyCmd[11] = (NvrFixDevCalcCheckSum(abyCmd,12) + 0xEE);
                        byCmdLen = 12;
    
                        NvrFixDevWriteSerial(g_wPtzCtrlChnId,abyCmd,byCmdLen,&wRealLen);
                        int i = 0;
                        for(i=0;i<12;i++)
                        {
                            PRINTDBG("cfg resume send date:%x\n", abyCmd[i]);
                        }
    
                       NvrFixHistoryCmdInsert2(g_wPtzCtrlChnId, abyCmd, 12, FALSE,NVR_PTZCTRL_SET_PTZ_RATIO,60000,3000);
                        
                    }
                    else
                    {
    
                        g_dwCurrentZoom = tPowerOffMemCfg[0].nZoomPos;
    
                        nZoomPos = tPowerOffMemCfg[0].nZoomPos;
                        NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&nZoomPos);
    
                        tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PANPOSION_SET;
                        tPtzCtrlInfo.wXposition = g_tPowerOffMemCfg[bySerialId].wHorizonPos;
                        eNvrRet = NvrFixDevPtzCtrl(g_wPtzCtrlChnId,tPtzCtrlInfo);
                        if (NVR_ERR__OK != eNvrRet)
                        {
                            PRINTERR("NvrFixDevPtzCtrl NVR_PTZCTRL_PANPOSION_SET failed, ret:%d\n", eNvrRet);
                        }
    
                        PRINTDBG("resume NVR_PTZCTRL_PANPOSION_SET bySerialId %u,type %u,x %u\n",bySerialId,tPtzCtrlInfo.eCtrlType,tPtzCtrlInfo.wXposition);
    
                        sleep(1);
    
                        tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TILTPOSION_SET;
                        tPtzCtrlInfo.wYposition = g_tPowerOffMemCfg[bySerialId].wVerticalPos;
                        eNvrRet = NvrFixDevPtzCtrl(g_wPtzCtrlChnId,tPtzCtrlInfo);
                        if (NVR_ERR__OK != eNvrRet)
                        {
                            PRINTERR("NvrFixDevPtzCtrl NVR_PTZCTRL_TILTPOSION_SET failed, ret:%d\n", eNvrRet);
                        }
    
                        PRINTDBG("resume NVR_PTZCTRL_TILTPOSION_SET bySerialId %u,type %u,y %u\n",bySerialId,tPtzCtrlInfo.eCtrlType,tPtzCtrlInfo.wYposition);
                        sleep(1);
                        
                    }
                    
    //
    //              tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_RESET;
    //              NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
                }
                else
                {
                    ///< 及时更新断电记忆坐标
                    NvrFixGetvh();
                }
            }
        }
        else
        {   
            //断电恢复未开启zoom恢复最小变倍
            nZoomPos = 0;
            NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&nZoomPos);
            //开启一个定时器来完成初始化时的红外或者激光随动，避免了延时等待
            //OsApi_TimerSet( g_hLaserInfredLinkTimer, NVR_PTZ_DEV_LIGHT_GETZOOM_DELAY, NvrPtzDevInfredLaserChangeTimerCB, NULL);
        }


	sleep(1);

    //查询云台版本号
    if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
    {
    	mzero(tPtzCtrlInfo);
    	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_MAINBOARD_SOFTVER_QUERY;
    	NvrFixDevPtzCtrl(0,tPtzCtrlInfo);

        OsApi_TaskDelay(1000);
    	mzero(tPtzCtrlInfo);
    	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_FOCUSER_SOFTVER_QUERY;
    	NvrFixDevPtzCtrl(0,tPtzCtrlInfo);
        OsApi_TaskDelay(1000);

    }
    else
    {
        tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_SOFTVER_QUERY;
        NvrFixDevPtzCtrl(0,tPtzCtrlInfo);
    }



    sleep(1);

    if (g_tFixInterCapInfo.tFixHwInternalCap.bySupFocus)
    {
        NvrFixDevFocusExtraOperate( NVR_CFG_RESET_ALL == eRecoveryType );//电动镜头聚焦处理
    }

	OsApi_Delay(100);

	//初始化键控限位(手动限位)
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_MANUALLIMITSWITCH_SET;
	if (NVR_PTZ_MODE_ON == tIpcPtzState.dwManuLimitPos)
	{
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
	}
	else
	{
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
	}
	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);

	OsApi_Delay(100);
	//初始化扫描限位
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_SCANLIMITSWITCH_SET;
	if (NVR_PTZ_MODE_ON == tIpcPtzState.dwScanLimitPos)
	{
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
	}
	else
	{
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
	}
	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);
	OsApi_Delay(100);

	//初始化扫描速度
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET;
	tPtzCtrlInfo.wPanSpeed = tIpcPtzState.dwScanSpeedValue;

	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);
	OsApi_Delay(100);

	//初始化预置点切换速度
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PRESET_SPEED_SET;
	tPtzCtrlInfo.dwRes = tIpcPtzState.dwPreSetSpdValue;

	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);
	OsApi_Delay(100);

	//初始化ZOOM拉伸速度
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_SET_ZOOM_SPEED;
	tPtzCtrlInfo.dwRes = tIpcPtzState.eZoomSpeedValue;

	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);
	OsApi_Delay(100);

	//初始化字幕显示PT方式
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PT_OSD_MODE;
	tPtzCtrlInfo.dwRes = tIpcPtzState.ePTOsdMode;

	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	mzero(tPtzCtrlInfo);
	OsApi_Delay(500);

	//查询初始角度
	sleep(2);
	tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PTPOSION_QUERY;
	NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);

	OsApi_Delay(1000);

#if 0
	//上报zoom信息,断电记忆未开户启上报0，开启时上报实际值
	//NvrPtzDevGetZoomPosition(&nZoomPos);
	//PRINTDBG("[IPCDEV]IpcPtzExtraOperate, get zoom pos:%d \n", nZoomPos);
	//NvrPtzDevReportZm(nZoomPos);

	//上报当前视场角给app
	u32 dwZoomAngle = 0;
	float fZoomAngle = 0;
	//球机GetZoomAngle接口获取的是水平视场角，垂直视场角通过16:9的比例换算得来
	NvrPtzGetZoomHAngle(nZoomPos, &fZoomAngle);
	dwZoomAngle = (u32)(fZoomAngle*100);
	dwZoomAngle = dwZoomAngle<<16|((dwZoomAngle*9/16)&0xFFFF);
	NvrPtzDevReportData(NVR_PTZ_STAT_REPORT_HVANGLE, &dwZoomAngle, sizeof(dwZoomAngle), NULL);
	PRINTDBG("NvrPtzDevReport  ZoomAngle:%u\n", dwZoomAngle);
	
	NvrPtzDevReportZmRatio(FALSE);
#endif
	
	mzero(tPtzCtrlInfo);

	if ((NVR_CAP_SUPPORT == g_tPtzCap.byLaserSupport))
	{
		//初始化激光聚光模式
		//NvrFixDevSetLaserMode(tIpcPtzState, nZoomPos);

	}


	//初始化激光强度
	if ((NVR_CAP_SUPPORT == g_tPtzCap.byLaserSupport) &&  NVR_DEV_LASER_OPEN == tIpcPtzState.eLaserSwitch)
	{
		tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_LASER_INTENSITY;
		tPtzCtrlInfo.dwRes = tIpcPtzState.dwLaserIntensity;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
		mzero(tPtzCtrlInfo);
		OsApi_Delay(100);
	}


	//初始化垂直角度范围
	if (NVR_CAP_SUPPORT == g_tPtzCap.byVerticaRangeSupport)
	{
		tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PTZ_VERTICA_RANGE;
		tPtzCtrlInfo.dwRes = tIpcPtzState.eVerticaRange;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
		mzero(tPtzCtrlInfo);
		OsApi_Delay(100);
	}


	//初始化除雾
	#if 0
	if (NVR_CAP_SUPPORT == g_tPtzHwInternalCap.tDeMistInfo.bSupport)
	{
		tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_DEMIST_SET;
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
		//tPtzCtrlInfo.dwRes = 60;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
	}
	#endif

	if(NVR_CAP_SUPPORT == g_tFixInterCapInfo.tFixPtzInternalCap.bPtzSup)
	{
		/*初始化开启云台角度上报功能*/
		NvrDevSetReadSerialDataDelay(50);
		tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_REPORT_PT_REAL_TIME;
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
		mzero(tPtzCtrlInfo);

		///<初始化开启云台状态上报
		tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PTMOVE_STATE_REPORT;
		tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
		NvrFixDevPtzCtrl(g_wPtzCtrlChnId, tPtzCtrlInfo);
		mzero(tPtzCtrlInfo);
	}



    OsApi_TaskDelay(500);

	NvrFixDevGetHvAngleTimerCB(NULL,NULL);//启动后查询一次云台坐标	

    OsApi_TaskDelay(500);

    
    //查询GPS一次
    if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
    {
        u8 abyCmdLensVer[6] = {0xEE, 0x06, 0x02, 0x01, 0x00, 0xF7};  ///<获取GPS
        u32 wRealLen;
        NvrFixDevWriteSerial(g_byLensCtrlSerialId, abyCmdLensVer, 6, &wRealLen);
    }

	return eNvrRet;

}

NVRSTATUS NvrFixDevSetFocusZoomXcoreFTII(u8 bySerialId, u8 byOptType, u8 bySpeed)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwFocusSpeed = 0;
    u8 abyCmd[NVR_FIX_DEV_MAX_BUF_LEN] = {0};		//生成串口指令buf
    u32 dwLen = 7;
	u32 dwReLen = 0;

    dwFocusSpeed = (s32)((bySpeed * NVR_FIX_DEV_MAX_ZOOM_SPEED / 100.0 ) + 0.50);
    dwFocusSpeed = dwFocusSpeed ? dwFocusSpeed : 1;

	PRINTDBG("NvrDevSetFocus type:%d, speed:%d, dwFocusSpeed:%d\n", byOptType, bySpeed, dwFocusSpeed);

	switch (byOptType)
	{
		case NVR_PTZCTRL_TYPE_FOCUSFAR:
		{
			u8 abyFocusFar[10] = {0xAA, 0x06, 0x08, 0x21, 0x01, 0x01, 0x00, 0xDB, 0xEB, 0xAA};
			dwLen = 10;
			memcpy(abyCmd, abyFocusFar, dwLen);
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSNEAR:
		{
			u8 abyFocusNear[10] = {0xAA, 0x06, 0x08, 0x21, 0x01, 0x02, 0x00, 0xDC, 0xEB, 0xAA};
			dwLen = 10;
			memcpy(abyCmd, abyFocusNear, dwLen);
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSAUTO:
		{
			u8 abyFocusAuto[9] = {0xAA, 0x05, 0x08, 0x2F, 0x01, 0x00, 0xE7, 0xEB, 0xAA};
			dwLen = 9;
			memcpy(abyCmd, abyFocusAuto, dwLen);
		}
		break;

		case NVR_PTZCTRL_TYPE_FOCUSSTOP:
		{
			u8 abyFocusStop[9] = {0xAA, 0x05, 0x08, 0x22, 0x01, 0x00, 0xDA, 0xEB, 0xAA};
			dwLen = 9;
			memcpy(abyCmd, abyFocusStop, dwLen);

		}
		break;
		case NVR_PTZCTRL_TYPE_ZOOMTELE:
		{
			u8 abyZoomTele[10] = {0xAA, 0x06, 0x08, 0x31, 0x01, 0x02, 0x00, 0xEC, 0xEB, 0xAA};
			dwLen = 10;
			memcpy(abyCmd, abyZoomTele, dwLen);
		}
		break;
		case NVR_PTZCTRL_TYPE_ZOOMWIDE:
		{
			u8 abyZoomWide[10] = {0xAA, 0x06, 0x08, 0x31, 0x01, 0x01, 0x00, 0xEB, 0xEB, 0xAA};
			dwLen = 10;
			memcpy(abyCmd, abyZoomWide, dwLen);
		}
		break;
		case NVR_PTZCTRL_TYPE_ZOOMSTOP:
		{
			u8 abyZoomStop[9] = {0xAA, 0x05, 0x08, 0x32, 0x01, 0x00, 0xEA, 0xEB, 0xAA};
			dwLen = 9;
			memcpy(abyCmd, abyZoomStop, dwLen);
		}
		break;

		default:
		break;
	}
    NrvFixDevWaitPTZQueryOverBlock();
    eRet = NvrFixDevWriteSerial(bySerialId, abyCmd, dwLen, &dwReLen);
    NrvFixDevWaitPTZQueryOverUnBlock();
    if(eRet != NVR_ERR__OK)
    {
        PRINTERR("NvrFixDevSetFocus NvrFixDevWriteSerial error ,ret %u\n",eRet);
        return NVR_ERR__ERROR;
    }

	return eRet;
}

NVRSTATUS NvrFixDevSetFocusZoomMINJIA2(u8 bySerialId, u8 byOptType, u8 bySpeed)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwFocusSpeed = 0;
    u8 abyCmd[NVR_FIX_DEV_MAX_BUF_LEN] = {0};		//生成串口指令buf
    u32 dwLen = 7;
	u32 dwReLen = 0;

    dwFocusSpeed = (s32)((bySpeed * NVR_FIX_DEV_MAX_ZOOM_SPEED / 100.0 ) + 0.50);
    dwFocusSpeed = dwFocusSpeed ? dwFocusSpeed : 1;

	PRINTDBG("NvrDevSetFocus type:%d, speed:%d, dwFocusSpeed:%d\n", byOptType, bySpeed, dwFocusSpeed);

	switch (byOptType)
	{
		case NVR_PTZCTRL_TYPE_FOCUSFAR:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x01;
            abyCmd[3] = 0x00;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x02;
		}
		break;

		case NVR_PTZCTRL_TYPE_FOCUSNEAR:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x80;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x81;
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSAUTO:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x80;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x81;
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSSTOP:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x00;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x01;
		}
		break;

		default:
		break;
	}

    eRet = NvrFixDevWriteSerial(bySerialId, abyCmd, dwLen, &dwReLen);
    if(eRet != NVR_ERR__OK)
    {
        PRINTERR("NvrFixDevSetFocus NvrFixDevWriteSerial error ,ret %u\n",eRet);
        return NVR_ERR__ERROR;
    }

	return eRet;
}

NVRSTATUS NvrFixDevSetIris(u8 bySerialId, u8 byOptType, u8 bySpeed)
{
	u8 byId = 0;
    ENvrIspIrisMode eIrisMode = NVR_ISP_IRIS_AUTO;
    u32 dwIrisSpeed = bySpeed;
	PRINTDBG("NvrFixDevSetIris type:%d, speed:%d\n", byOptType, bySpeed);

	dwIrisSpeed = dwIrisSpeed < 1 ? 1 : dwIrisSpeed;

	//设置光圈加减时，先设置为手动模式
	switch (byOptType)
	{
		case NVR_PTZCTRL_TYPE_IRIS_PLUS:
		{
			NvrFixIspSetKeyParam(byId, NVR_FIX_ISP_ACTION_KEY_IRIS_ENLARGE, &dwIrisSpeed);
		}
		break;

		case NVR_PTZCTRL_TYPE_IRIS_MINUS:
		{
			NvrFixIspSetKeyParam(byId, NVR_FIX_ISP_ACTION_KEY_IRIS_SHRINK, &dwIrisSpeed);
		}
		break;

		case NVR_PTZCTRL_TYPE_IRIS_AUTO:
		{
			TLcamIspParam tLcamIspParam;

			mzero(tLcamIspParam);			
			LcamIspGetParam(0, &tLcamIspParam);
            eIrisMode = tLcamIspParam.tAction.eIrisMode;
			NvrFixIspSetKeyParam(byId, NVR_FIX_ISP_ACTION_KEY_IRIS_MODE ,&eIrisMode);		
		}
		break;

		case NVR_PTZCTRL_TYPE_IRIS_STOP:
		{
		}
		break;

		default:
		break;
	}
	
	return NVR_ERR__OK;

}



/**
 * @brief 处理zoom操作，判断是否需要数字变倍
 * @param[in] eCtrlType zoom控制类型
 * @return 1:需要数字变倍 0:不需要数字变倍
 */
static s32 NvrFixDevHandleZoomOperation(ENvrPtzCtrlType eCtrlType, BOOL32 bPre)
{
    /* C89标准：所有变量声明必须在函数开始处 */
    static s32 s_nPosDiff = 0;
    u32 dwCurZoomPosition;
    u16 wCropWidth;
    u16 wCropHeight;
    const u32 ZOOM_THRESHOLD = 2342;
    const s32 ZOOM_STEP = 300;
    const u32 CANVAS_SIZE = 10000;

    dwCurZoomPosition = 0;

    /* 获取当前zoom位置 */
    IspActionParam(0, ISP_ACT_GET_ZOOM_POSITION, (void *)&dwCurZoomPosition);
	if (bPre)
    {
		PRINTDBG("isp do cmd get zoom position:%lu\n", dwCurZoomPosition);
	}

    /* 判断当前物理zoom位置是否大于等于2342 */
    if (dwCurZoomPosition >= ZOOM_THRESHOLD)
    {
		if (bPre)
		{
			PRINTDBG("zoom position:%lu, not need digital zoom\n", dwCurZoomPosition);
			return NVR_ERR__OK;
		}
        /* 进行数字变倍 */
        if (eCtrlType == NVR_PTZCTRL_TYPE_ZOOMTELE)
        {
            s_nPosDiff += ZOOM_STEP;
        }
        else if (eCtrlType == NVR_PTZCTRL_TYPE_ZOOMWIDE)
        {
            s_nPosDiff -= ZOOM_STEP;
            /* 确保posDiff不小于0 */
            if (s_nPosDiff < 0)
            {
                s_nPosDiff = 0;
            }
        }

        /* 在10000*10000的画布上进行数字变倍 */
        if (s_nPosDiff > 0)
        {
            wCropWidth = CANVAS_SIZE - 2 * s_nPosDiff;
            wCropHeight = CANVAS_SIZE - 2 * s_nPosDiff;

            /* 确保裁剪区域不会过小 */
            if (wCropWidth > 0 && wCropHeight > 0)
            {
                NvrFixMcSetCrop(1, 0, (u16)s_nPosDiff, (u16)s_nPosDiff, wCropWidth, wCropHeight);
                PRINTDBG("Digital zoom: posDiff=%d, crop region=(%d,%d,%d,%d)\n",
                         s_nPosDiff, s_nPosDiff, s_nPosDiff, wCropWidth, wCropHeight);
            }
			return NVR_ERR__OK;
        }
        else
        {
            /* 关闭数字变倍 */
            NvrFixMcSetCrop(0, 0, 0, 0, CANVAS_SIZE, CANVAS_SIZE);
            PRINTDBG("Digital zoom disabled\n");
			return NVR_ERR__ERROR;
        }

		
    }
	else
	{
		return NVR_ERR__ERROR;
	}
    /* 否则进行光学变倍（原有逻辑已在调用处处理） */
}

NVRSTATUS NvrFixDevSetFocus(u8 bySerialId, u8 byOptType, u8 bySpeed)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwFocusSpeed = 0;
    u8 abyCmd[NVR_FIX_DEV_MAX_BUF_LEN] = {0};		//生成串口指令buf
    u32 dwLen = 7;
	u32 dwReLen = 0;

    dwFocusSpeed = (s32)((bySpeed * NVR_FIX_DEV_MAX_ZOOM_SPEED / 100.0 ) + 0.50);
    dwFocusSpeed = dwFocusSpeed ? dwFocusSpeed : 1;

	PRINTDBG("NvrDevSetFocus type:%d, speed:%d, dwFocusSpeed:%d\n", byOptType, bySpeed, dwFocusSpeed);

	switch (byOptType)
	{
		case NVR_PTZCTRL_TYPE_FOCUSFAR:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x01;
            abyCmd[3] = 0x00;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x02;
		}
		break;

		case NVR_PTZCTRL_TYPE_FOCUSNEAR:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x80;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x81;
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSAUTO:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x80;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x81;
		}
		break;
		case NVR_PTZCTRL_TYPE_FOCUSSTOP:
		{
            abyCmd[0] = 0xff;
            abyCmd[1] = 0x01;
            abyCmd[2] = 0x00;
            abyCmd[3] = 0x00;
            abyCmd[4] = 0x00;
            abyCmd[5] = 0x00;
            abyCmd[6] = 0x01;
		}
		break;

		default:
		break;
	}

    eRet = NvrFixDevWriteSerial(bySerialId, abyCmd, dwLen, &dwReLen);
    if(eRet != NVR_ERR__OK)
    {
        PRINTERR("NvrFixDevSetFocus NvrFixDevWriteSerial error ,ret %u\n",eRet);
        return NVR_ERR__ERROR;
    }

	return eRet;
}

NVRSTATUS NvrFixDevPtzFactoryReset()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrPtzCtrlInfo tCtrlInfo;

    mzero(tCtrlInfo);

    tCtrlInfo.eCtrlType = NVR_PTZCTRL_RESTORE_FACTORY;
    eRet = NvrFixDevDealPtzCtrl(0, tCtrlInfo,NVR_CAP_PTZ_TYPE_NONE);


    sleep(3);
    return eRet;
}

/************************方位角ptz    osd 开始************************/
// 获取底层磁感应坐标
NVRSTATUS NvrDevGetCurMagnetic(TMagneticInfo *ptMagnetic)
{
	if(NULL == ptMagnetic)
    {
        PRINTERR("NvrHalApiGetMagnetic error \n");
        return NVR_ERR__ERROR;
    }

    s32 nRet = 0;
    THwmonStat tHwmonStat;
    memset(&tHwmonStat, 0, sizeof(tHwmonStat));

    //获取磁力感应方向
    tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_MAGNETIC, 0);
    nRet = BrdHwmonGetStatus(&tHwmonStat);
    if (0 != nRet)
    {
        PRINTERR("NvrHalApiGetMagnetic error 1\n");
        return NVR_ERR__ERROR;
    }

    ptMagnetic->x = tHwmonStat.tEntry.tMagnetic.x;
    ptMagnetic->y = tHwmonStat.tEntry.tMagnetic.y;
    ptMagnetic->z = tHwmonStat.tEntry.tMagnetic.z;

    return NVR_ERR__OK;
}

static s16 NvrDevMagAtan2_c(s16 wAngX , s16 wAngY)
{
    u8 i = 0;
    s16 wXnew, wYnew;
    s16 wAngleSum = 0;

    for(i = 0; i < 15; i++)
    {
        if(wAngY >= 0)
        {
            wXnew = wAngX + (wAngY >> i);
            wYnew = wAngY - (wAngX >> i);
            wAngX = wXnew;
            wAngY = wYnew;
            wAngleSum += g_wAtantab[i];
        }
        else
        {
            wXnew = wAngX - (wAngY >> i);
            wYnew = wAngY + (wAngX >> i);
            wAngX = wXnew;
            wAngY = wYnew;
            wAngleSum -= g_wAtantab[i];
        }
	}

	if(wAngleSum>=0)
		wAngleSum += 128;
	else
		wAngleSum -= 128;

  return wAngleSum/256;
}

static s16 NvrDevMagCordicAngle(s16 wAngX , s16 wAngY)
{
	if(wAngX >= 0)
	{
		if(wAngY >= 0)
		{
			return NvrDevMagAtan2_c(wAngX, wAngY);
		}
		else
		{
			return (360 + NvrDevMagAtan2_c(wAngX, wAngY));
		}
	}
	else
	{
		return (180 - NvrDevMagAtan2_c(-wAngX, wAngY));
	}
}

s32 NvrFixDevPtzUpdatePtzOsdPositionCB(HTIMERHANDLE dwTimerId, void* param)
{
    double dbSlantAngle = 0, dbRotateAngle = 0; //当前垂直水平弧度
    TNvrOsdParam tOsdParam;
    s16 wPreLpf_x=0, wPreLpf_y=0, wPreLpf_z=0;//上次坐标
    s16 wMag_x=0, wMag_y=0, wMag_z=0;         //当前磁感应坐标
    s16 wMap_x=0, wMap_y=0, wMap_z=0;         // 映射后的坐标值
    s16 wXh=0, wYh=0;
    TMagneticInfo tMagneticInfo;
    TEdgeOsInterCapSysInfo tInterSysCap;
    u16 g_wHorzAngle = 0;               //当前水平角度
    u16 wIndex = 0;
    static u8 byInitCfg = 0;            //用来初始化偏差值配置

    memset(&tMagneticInfo, 0, sizeof(tMagneticInfo));
    mzero(tOsdParam);
    mzero(tInterSysCap);

    // 初始化坐标偏差值
    if (!byInitCfg)
    {
        TNvrFixMagneticInfo tMagInfo;
        mzero(tMagInfo);

        NvrFixGetMagneticCfg(&tMagInfo);
        g_wXoff = tMagInfo.wXoff;
        g_wYoff = tMagInfo.wYoff;
        g_wZoff = tMagInfo.wZoff;
        byInitCfg = 1;
    }

    NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &tInterSysCap); 
    LcamMcCalcAcceleAngle(&dbSlantAngle, &dbRotateAngle);
    NvrDevGetCurMagnetic(&tMagneticInfo);

    wPreLpf_x = g_wLpf_x;
    wPreLpf_y = g_wLpf_y;
    wPreLpf_z = g_wLpf_z;

    g_wLpf_x = tMagneticInfo.x & 0xFFFF;
    g_wLpf_y = tMagneticInfo.y & 0xFFFF;
    g_wLpf_z = tMagneticInfo.z & 0xFFFF;

    //PRINTDBG("NvrHalApiGetMagnetic :%lf,%lf (%d,%d,%d), (%d,%d,%d) \n",dbSlantAngle,dbRotateAngle, g_wLpf_x, g_wLpf_y, g_wLpf_z, wPreLpf_x, wPreLpf_y, wPreLpf_z);

    if(byUpdatePtzOsd || (wPreLpf_x - g_wLpf_x) > 10 || (g_wLpf_x - wPreLpf_x) > 10 || (wPreLpf_y - g_wLpf_y) > 10 || (g_wLpf_y - wPreLpf_y) > 10 || (wPreLpf_z - g_wLpf_z) > 10 || (g_wLpf_z - wPreLpf_z) > 10)
    {
        byUpdatePtzOsd = 0;
        wMag_x = g_wLpf_x-g_wXoff;
        wMag_y = g_wLpf_y-g_wYoff;
        wMag_z = g_wLpf_z-g_wZoff;

        // 根据M-Sensor安装方式对对应坐标轴进行映射(参考坐标系: X轴朝前，Y轴朝右，Z轴朝下)
        if (NVR_BASICDIRECT_XFYRZD == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = wMag_x;
            wMap_y = wMag_y;
            wMap_z = wMag_z;
        }
        else if (NVR_BASICDIRECT_XLYFZD == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = wMag_y;
            wMap_y = -wMag_x;
            wMap_z = wMag_z;
        }
        else if (NVR_BASICDIRECT_XBYLZD == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_x;
            wMap_y = -wMag_y;
            wMap_z = wMag_z;
        }
        else if (NVR_BASICDIRECT_XRYBZD == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_y;
            wMap_y = wMag_x;
            wMap_z = wMag_z;
        }
        else if (NVR_BASICDIRECT_XLYUZB == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_z;
            wMap_y = -wMag_x;
            wMap_z = -wMag_y;
        }
    	else if (NVR_BASICDIRECT_XRYDZB == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_z;
            wMap_y = wMag_x;
            wMap_z = wMag_y;
        }
        else if (NVR_BASICDIRECT_XLYBZD == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_y;
            wMap_y = -wMag_x;
            wMap_z = wMag_z;
        }
        else if (NVR_BASICDIREDT_XBYLZU == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = -wMag_x;
            wMap_y = -wMag_y;
            wMap_z = -wMag_z;
        }
        else if (NVR_BASICDIRECT_XFYRZU == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = wMag_x;
            wMap_y = wMag_y;
            wMap_z = -wMag_z;
        }
        else if (NVR_BASICDIRECT_XFYLZU == tInterSysCap.byMagneticBasicDirect)
        {
            wMap_x = wMag_x;
            wMap_y = -wMag_y;
            wMap_z = -wMag_z;
        }

        // 计算水平x,y轴分量
        wXh = wMap_x*cos(dbSlantAngle)+wMap_y*sin(dbSlantAngle)*sin(dbRotateAngle)+wMap_z*sin(dbSlantAngle)*cos(dbRotateAngle);
        wYh = wMap_y*cos(dbRotateAngle)-wMap_z*sin(dbRotateAngle);

        g_wHorzAngle = NvrDevMagCordicAngle(wXh, wYh);
        if(g_wHorzAngle >= 360)
        {
            g_wHorzAngle -= 360;
        }

        if(g_wHorzAngle > 337 || g_wHorzAngle <= 22)
        {
            wIndex = 0;
        }
        if(g_wHorzAngle > 22 && g_wHorzAngle <= 67)
        {
            wIndex = 45;
        }
        if(g_wHorzAngle > 67 && g_wHorzAngle <= 112)
        {
            wIndex = 90;
        }
        if(g_wHorzAngle > 112 && g_wHorzAngle <= 157)
        {
            wIndex = 135;
        }
        if(g_wHorzAngle > 157 && g_wHorzAngle <= 202)
        {
            wIndex = 180;
        }
        if(g_wHorzAngle > 202 && g_wHorzAngle <= 247)
        {
            wIndex = 225;
        }
        if(g_wHorzAngle > 247 && g_wHorzAngle <= 292)
        {
            wIndex = 270;
        }
        if(g_wHorzAngle > 292 && g_wHorzAngle <= 337)
        {
            wIndex = 315;
        }
        PRINTDBG("NvrHalApiGetMagnetic off %d, %d, %d \n", g_wXoff, g_wYoff, g_wZoff);
        PRINTDBG("NvrHalApiGetMagnetic update index:%d  angle:%d \n", wIndex ,g_wHorzAngle);
        NvrOsdUpdataPtz(wIndex);
    }

    OsApi_TimerSet(g_hUpdateOsdTimer, 1000, NvrFixDevPtzUpdatePtzOsdPositionCB, NULL);

    return 0;
}

void NvrFixDevPtzOsdUpdateTriger()
{
    OsApi_TimerSet(g_hUpdateOsdTimer, 500, NvrFixDevPtzUpdatePtzOsdPositionCB, NULL);
}

// 计算x/y/z轴上的偏差值
static void NvrFixDevMagOffsetCali(s16 wX_m, s16 wY_m, s16 wZ_m)
{
    if(g_wXmax < wX_m)
    {
        g_wXmax = wX_m;
    }
    if(g_wXmin > wX_m)
    {
        g_wXmin = wX_m;
    }
    if(g_wYmax < wY_m)
    {
        g_wYmax = wY_m;
    }
    if(g_wYmin > wY_m)
    {
        g_wYmin = wY_m;
    }
    if(g_wZmax < wZ_m)
    {
        g_wZmax = wZ_m;
    }
    if(g_wZmin > wZ_m)
    {
        g_wZmin = wZ_m;
    }
    g_wXoff = (g_wXmax >> 1) + (g_wXmin >> 1);
    g_wYoff = (g_wYmax >> 1) + (g_wYmin >> 1);
    g_wZoff = (g_wZmax >> 1) + (g_wZmin >> 1);

	return;
}

// 出厂标定偏差值
NVRSTATUS NvrFixDevMagneticCalibration()
{
    TMagneticInfo tMagneticInfo;
    TNvrFixMagneticInfo tMagInfo;

    memset(&tMagneticInfo, 0, sizeof(tMagneticInfo));
    memset(&tMagInfo, 0, sizeof(tMagInfo));

    NvrDevGetCurMagnetic(&tMagneticInfo);

    g_wLpf_x = tMagneticInfo.x & 0xFFFF;
    g_wLpf_y = tMagneticInfo.y & 0xFFFF;
    g_wLpf_z = tMagneticInfo.z & 0xFFFF;

    NvrFixDevMagOffsetCali(g_wLpf_x,g_wLpf_y,g_wLpf_z);

    tMagInfo.wXoff = g_wXoff;
    tMagInfo.wYoff = g_wYoff;
    tMagInfo.wZoff = g_wZoff;

    NvrFixSetMagneticCfg(&tMagInfo);

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixSetMagneticCfg(TNvrFixMagneticInfo *ptMagInfo)
{
    FILE* fpMag =NULL;

    if (!ptMagInfo)
    {
        return NVR_ERR__ERROR;
    }

    fpMag = fopen(NVR_FIX_MAG_FILE,"wb");
    if(fpMag != NULL)
    {
        fwrite((TNvrFixMagneticInfo*)ptMagInfo, sizeof(TNvrFixMagneticInfo), 1, fpMag);
        fclose(fpMag);
    }
    else
    {
        PRINTERR("NvrFixDevMagneticCalibration update mag cfg fail. \n");
        return NVR_ERR__ERROR;
    }

    PRINTDBG("NvrFixDevMagneticCalibration update mag cfg succ. \n");
    return NVR_ERR__OK;
}

NVRSTATUS NvrFixGetMagneticCfg(TNvrFixMagneticInfo *ptMagInfo)
{
    FILE* fpMag =NULL;

    if (!ptMagInfo)
    {
        return NVR_ERR__ERROR;
    }

    if(0 == access(NVR_FIX_MAG_FILE, 0))
    {
        fpMag = fopen(NVR_FIX_MAG_FILE, "rb");
        if(fpMag != NULL)
        {
            fread((TNvrFixMagneticInfo*)ptMagInfo, sizeof(TNvrFixMagneticInfo), 1, fpMag);
            fclose(fpMag);
        }
        else
        {
            PRINTERR("NvrFixDevMagneticCalibration open mag cfg fail. \n");
            return NVR_ERR__ERROR; 
        }
    }
    else
    {
        PRINTERR("NvrFixDevMagneticCalibration access mag cfg fail. \n");
        return NVR_ERR__ERROR; 
    }

    //PRINTDBG("NvrFixDevMagneticCalibration get mag cfg succ.(%d,%d,%d) \n", ptMagInfo->wXoff, ptMagInfo->wYoff, ptMagInfo->wZoff);
    return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevOsdInfoAdjust()
{
	int i = 0, byChnId = 0;
	BOOL32 bReSetFlag =FALSE;
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrOsdParam tOsdParam;
	TNvrPtzBasicState tNvrPtzState;
	TNvrCapLcam tCapLcamInfo;
	mzero(tNvrPtzState);
	mzero(tOsdParam);
	mzero(tCapLcamInfo);

	eRet = NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcamInfo);
	if (NVR_ERR__OK != eRet)
	{
		PRINTERR("get lcam capbility failed ret:%d\n", eRet);
		return eRet;
	}

	NvrFixCfgGetParamByFlag(&tNvrPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);

	for(byChnId = 0; byChnId < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; byChnId++)
	{
		NvrOsdGetParam(byChnId, &tOsdParam);
		for(i=0;i<NVR_OSD_MAX_USERDEFINED_NUM;i++)
		{
			///web设置时width会被改掉成osdi长度，需从新设置回来
			if(NVR_OSD_CONTEXT_TYPE_ZM == tOsdParam.atNvrUserParam[i].eContextType&&TRUE == tOsdParam.atNvrUserParam[i].byShow)
			{
				tOsdParam.atNvrUserParam[i].wWidth = 32*7;
				bReSetFlag = TRUE;
			}
			if(NVR_OSD_CONTEXT_TYPE_BAT == tOsdParam.atNvrUserParam[i].eContextType&&TRUE == tOsdParam.atNvrUserParam[i].byShow)
			{
				//u8 byBatValue = 0;
				//NvrPtzDevGetBatteryInfo(&byBatValue);
				tOsdParam.atNvrUserParam[i].wWidth = 32*8;
				bReSetFlag = TRUE;
			}

		}
		if(TRUE == tOsdParam.tNvrPtzParam.byShow)
		{
			if(NVR_DEV_PT_MODE_POINTER == tNvrPtzState.ePTOsdMode)
			{
				if(tOsdParam.tNvrPtzParam.wHeight<214)
				{
					 tOsdParam.tNvrPtzParam.wWidth = 214;
					 tOsdParam.tNvrPtzParam.wHeight = 214*2;
					 bReSetFlag = TRUE;
				}
			}
			else 
			{
				if(32*13 != tOsdParam.tNvrPtzParam.wWidth)
				{
					tOsdParam.tNvrPtzParam.wWidth = 32*13;
					tOsdParam.tNvrPtzParam.wHeight = 64;
					tOsdParam.tNvrPtzParam.eContextType = NVR_OSD_CONTEXT_TYPE_DYNAMIC;
					bReSetFlag = TRUE;
				}
			}
		}

		if(TRUE == bReSetFlag)
		{
			NvrOsdSetParam(byChnId, &tOsdParam);
		}
	}

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevNotifyUpdatePtzOsd()
{
    byUpdatePtzOsd = 1;
    NvrDevNotifyUpdatePtzOsd(g_dwCurrentHAngle,g_dwCurrentVAngle,g_tBasicPos);
    return NVR_ERR__OK;
}

/************************方位角ptz    osd 结束************************/
static u16 g_wCurPtzIndex = 0;
static u16 g_wPanAngle = 0;
static s32 g_nTiltAngle = 0;

NVRSTATUS NvrDevNotifyUpdatePtzOsd(u16 wPanAngle, u16 wTiltAngle, TNvrBasicPosInfo tBasicAngle)
{
    u16 wIndex = 0;
    u16 wPanAngleTmp = 0;
    //u16 wTiltAngleTmp = 0;
    s32 byChnId = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrPtzBasicState tNvrPtzState;
	TNvrCapLcam tCapLcamInfo;
	mzero(tNvrPtzState);
	mzero(tCapLcamInfo);

	NvrFixCfgGetParamByFlag(&tNvrPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);

	eRet = NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcamInfo);
	if (NVR_ERR__OK != eRet)
	{
		PRINTERR("get lcam capbility failed ret:%d\n", eRet);
		return eRet;
	}

	PRINTDBG( "NvrDevNotifyUpdatePtzOsd,pt:%d:%d!\n",wPanAngle,wTiltAngle);
	///<获取当前配置，字幕显示类型
#if 1
    if(36000 < wPanAngle || 36000 < tBasicAngle.wBasicHPos)
    {
        PRINTERR( "NvrDevNotifyUpdatePtzOsd :param err!\n");
        return NVR_ERR__ERROR;
    }
    if(tBasicAngle.wBasicHPos >= wPanAngle)
    {
        wPanAngleTmp = wPanAngle + 36000 - tBasicAngle.wBasicHPos;
    }
    else
    {
        wPanAngleTmp = wPanAngle - tBasicAngle.wBasicHPos;
    }
    //实际方位与云台水平角度相反,维多云台实际方位和水平角度是统一的不需要做相反处理
    if(g_byPtzType == NVR_CAP_PTZ_TYPE_MINJIA2)
    {
        wPanAngleTmp = 36000 - wPanAngleTmp;
    }

    // 根据角度，计算需要贴哪张方位图
    wIndex = (wPanAngleTmp + 2250) / 4500 * 45;
    if(wIndex > 315)
    {
        wIndex = 0;
    }
    if(wIndex != g_wCurPtzIndex || wPanAngleTmp/100 != g_wPanAngle/100)
    {
        g_wCurPtzIndex = wIndex;//更新当前方位角索引
        g_wPanAngle = wPanAngleTmp;//更新当前水平坐标
    }

#if 0
    if(36000 < wTiltAngle || 36000 < tBasicAngle.wBasicVPos)
    {
        PRINTERR( "NvrDevNotifyUpdatePtzOsd :param err!\n");
        return NVR_ERR__ERROR;
    }
    if(tBasicAngle.wBasicVPos > wTiltAngle)
    {
        wTiltAngleTmp = wTiltAngle + 36000 - tBasicAngle.wBasicVPos;
    }
    else
    {
        wTiltAngleTmp = wTiltAngle - tBasicAngle.wBasicVPos;
    }
    if(wTiltAngle > 18000)
    {
        wTiltAngle = wTiltAngle - 36000;
    }
    PRINTDBG( "NvrDevNotifyUpdatePtzOsd :wTiltAngle %d!!!!\n",wTiltAngle);
    if(wTiltAngleTmp != g_wTiltAngle)
    {
        g_wTiltAngle = wTiltAngleTmp;//更新当前垂直坐标
    }
#endif
    g_nTiltAngle = wTiltAngle;
    g_nTiltAngle = g_nTiltAngle<=9000?g_nTiltAngle:(s32)(g_nTiltAngle%36000-36000);

    
	PRINTDBG( "NvrDevNotifyUpdatePtzOsd :LINE:%d,ePTOsdMode:%d!\n",__LINE__,tNvrPtzState.ePTOsdMode);
	if(NVR_DEV_PT_MODE_POINTER == tNvrPtzState.ePTOsdMode)
	{
		NvrOsdUpdataPtz(g_wCurPtzIndex);
	}
	else if(NVR_DEV_PT_MODE_NUMBER == tNvrPtzState.ePTOsdMode)
	{
		u32 dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		s8 achTemp[NVR_MAX_OSD_CONTENT_LEN] = {0};
		u8 abyStr[NVR_MAX_OSD_CONTENT_LEN] = {0};
		TNvrOsdMobileUpdateContent tUpdateContent;
		mzero(tUpdateContent);
		tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_PTZ;

		memset(abyStr, 0, sizeof(abyStr));
		mzero(achTemp); 
		mzero(tUpdateContent.abyContent);
		
		sprintf(achTemp,"P:%03d T:%03d",g_wPanAngle/100,g_nTiltAngle/100);
		PRINTDBG( "NvrDevNotifyUpdatePtzOsd :LINE:%d,achTemp:%s,ePTOsdMode:%d!\n",__LINE__,achTemp,tNvrPtzState.ePTOsdMode);

		dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		CharConvConvertGbktoUnicode(achTemp, abyStr, &dwStrLen);
		memcpy(tUpdateContent.abyContent,abyStr,dwStrLen);
		tUpdateContent.byBufLen = dwStrLen;
        
		for(byChnId = 0; byChnId < tCapLcamInfo.tLcamMcInfo.byLcamChnNum; byChnId++)
		{
			tUpdateContent.byChnId = byChnId;
			NvrOsdMobileUpdateOsd(&tUpdateContent);
		}
	}
#endif
    return NVR_ERR__OK;
}



//将0~24小时的秒数,转换为时分秒
void NvrFixDevTcConvertTimeToSec(TNvrTimeSec *ptTimeSec, s32 nInTimeSec)
{
	int tmp = 0;

	//时
	ptTimeSec->nHour = nInTimeSec / NVR_HOUR_SEC;

	//分
	tmp = nInTimeSec - ptTimeSec->nHour*NVR_HOUR_SEC;
	ptTimeSec->nMin = tmp/NVR_MIN_SEC;

	//秒
	tmp = tmp - ptTimeSec->nMin*NVR_MIN_SEC;
	ptTimeSec->nSec = tmp;

	//时间秒数为负值时
	if(nInTimeSec < 0)
	{
		//分秒转为正值
		ptTimeSec->nMin = -(ptTimeSec->nMin);
		ptTimeSec->nSec = -(ptTimeSec->nSec);
	}
}

void NvrFixDevTcConvertWeekDay(u8 byIndex)
{
	switch(byIndex)
	{
		case 0:
			PRINTDBG("Sun ");
			break;
		case 1:
			PRINTDBG("Mon ");
			break;
		case 2:
			PRINTDBG("Tues ");
			break;
		case 3:
			PRINTDBG("Wed ");
			break;
		case 4:
			PRINTDBG("Thurs ");
			break;
		case 5:
			PRINTDBG("Fri ");
			break;
		case 6:
			PRINTDBG("Sat ");
			break;
		default:
			PRINTDBG("[IpcTc]IpcTcConvertWeekDay not found!\n");
			break;
	}
}



ENvrCfgTimeSegment NvrFixDevTcGetTimeSegDist(s32 nStartTime, s32 nEndTime)
{
	//检验时间段,是否在正常范围内
	if((nStartTime >= MAX_EASTERN_TIME_ZONE_SEC && nStartTime <= MAX_WESTERN_TIME_ZONE_SEC)
		&&(nEndTime >= MAX_EASTERN_TIME_ZONE_SEC && nEndTime <= MAX_WESTERN_TIME_ZONE_SEC)
		&&(nStartTime <= nEndTime))
	{
		if(nStartTime < 0 && nEndTime <= 0)
		{
			return IPC_CFG_IN_LAST_DAY;
		}
		else if(nStartTime < 0 && nEndTime > 0)
		{
			return IPC_CFG_CROSS_LAST_THIS_DAY;
		}
		else if(nStartTime >= 0 && nEndTime > 0)
		{
			if(nStartTime <= ALL_DAY_SEC && nEndTime <= ALL_DAY_SEC)
			{
				return IPC_CFG_IN_THIS_DAY;
			}
			else if(nStartTime <= ALL_DAY_SEC && nEndTime > ALL_DAY_SEC)
			{
				return IPC_CFG_CROSS_THIS_NEXT_DAY;
			}
			else if(nStartTime >= ALL_DAY_SEC && nEndTime > ALL_DAY_SEC)
			{
				return IPC_CFG_IN_NEXT_DAY;
			}
		}
		else
		{
			PRINTDBG("[IpcTimeConvert]IpcTcGetTimeSegmentDistribution not find type, nStartTime = %d, nEndTime = %d!\n", nStartTime, nEndTime);
		}
	}
	else
	{
		PRINTDBG("[IpcTimeConvert]IpcTcGetTimeSegmentDistribution range is not right! nStartTime = %d, nEndTime = %d\n", nStartTime, nEndTime);
	}
	return IPC_CFG_NOT_IN_ANY_DAY;
}


void NvrFixDevTcUtcTimeConvertForEvent(TNvrDisarmingTime tInTime, TNvrDisarmingTime *ptOutTime, u8 byTimeconvType)
{
	u8 i = 0;//week day
	u8 j = 0;//time scale

	u8 byCurIndex = 0;
	u8 byTaskType = 0;
	u8 byTaskParam = 0;
	TNvrTmingTaskParam tTmingTaskParam;

	mzero(tTmingTaskParam);

	NvrFixCfgGetParamByFlag(&tTmingTaskParam, sizeof(TNvrTmingTaskParam), NVR_PTZ_MODULE_TIMING_TASK_PARAM);

	ENvrCfgTimeSegment eNvrCfgTimeType = IPC_CFG_NOT_IN_ANY_DAY;

	for(i = 0; i < NVR_WEEK_DAY; i++)
	{
		//计算当天-下标
		byCurIndex = i;

		//PRINTDBG "-[%d]-chLastIndex = %d, chCurIndex = %d, chNextIndex = %d\n",
			//i, chLastIndex, chCurIndex, chNextIndex);

		//先获取10个时间段,分布状态
		for(j = 0; j < NVR_MAX_TIME_SCALE; j++)
		{
			//时间段起效,才进行处理
			if(tInTime.atWeekScale[i].atTimeScale[j].bEnable == TRUE)
			{
				eNvrCfgTimeType = NvrFixDevTcGetTimeSegDist(tInTime.atWeekScale[i].atTimeScale[j].nStartTime, tInTime.atWeekScale[i].atTimeScale[j].nEndTime);

				byTaskType = tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].eTaskType;
				byTaskParam = tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].byTaskParam;
				 if(eNvrCfgTimeType == IPC_CFG_IN_THIS_DAY)
				{
					//转换成-当天-时间段
					ptOutTime->atWeekScale[byCurIndex].atTimeScale[j].bEnable = TRUE;
					ptOutTime->atWeekScale[byCurIndex].atTimeScale[j].nStartTime = tInTime.atWeekScale[i].atTimeScale[j].nStartTime;
					ptOutTime->atWeekScale[byCurIndex].atTimeScale[j].nEndTime = tInTime.atWeekScale[i].atTimeScale[j].nEndTime;

					if (IPC_TIMECONV_TMINGTASK == byTimeconvType)
					{
						g_tFixTimingTaskEx.atWeekScale[byCurIndex].atTmingInfoEx[j].byTaskType = byTaskType;
						g_tFixTimingTaskEx.atWeekScale[byCurIndex].atTmingInfoEx[j].byTaskParam = byTaskParam;
					}
			 	}
				else
				{
					PRINTDBG("error!!!\n");
				}
			}
		}
	}
}


void NvrFixDevTcConvertPtzTime(void)
{
	u8 i = 0;
	u8 j = 0;
	TNvrTmingTaskParam tTmingTaskParam;
	TNvrDisarmingTime tInTime;
	TTmingTaskParamEx tTimingTaskEx;
	TNvrTimeSec tNvrTimeSec;

	mzero(tTmingTaskParam);
	mzero(tInTime);
	mzero(tTimingTaskEx);
	mzero(tNvrTimeSec);
	mzero(g_tFixPtzTime);
	mzero(g_tFixTimingTaskEx);

	NvrFixCfgGetParamByFlag(&tTmingTaskParam, sizeof(TNvrTmingTaskParam), NVR_PTZ_MODULE_TIMING_TASK_PARAM);

	for (i = 0; i < NVR_WEEK_DAY; i++)
	{
		for (j = 0; j < NVR_MAX_TIME_SCALE; j++)
		{
			tInTime.atWeekScale[i].atTimeScale[j].bEnable = tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].bIsEnable;
			tInTime.atWeekScale[i].atTimeScale[j].nStartTime = tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].nStartTime;
			tInTime.atWeekScale[i].atTimeScale[j].nEndTime = tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].nEndTime;
		}
	}

	NvrFixDevTcUtcTimeConvertForEvent(tInTime, &g_tFixPtzTime, IPC_TIMECONV_TMINGTASK);

	//打印出转换前\后 时间段
	PRINTDBG("====== Before Converted PTZ Utc Time ======\n");
	for(i = 0; i < NVR_WEEK_DAY; i++)
	{
		for(j = 0; j < NVR_MAX_TIME_SCALE; j++)
		{
			if (tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].bIsEnable)
			{
				NvrFixDevTcConvertWeekDay(i);
				PRINTDBG("[%d][%d] ", i, j);

				memset(&tNvrTimeSec, 0x0, sizeof(TNvrTimeSec));
				NvrFixDevTcConvertTimeToSec(&tNvrTimeSec, tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].nStartTime);
				PRINTDBG("%d:%d:%d~", tNvrTimeSec.nHour, tNvrTimeSec.nMin, tNvrTimeSec.nSec);

				memset(&tNvrTimeSec, 0x0, sizeof(TNvrTimeSec));
				NvrFixDevTcConvertTimeToSec(&tNvrTimeSec, tTmingTaskParam.atEvdayParam[i].atTmingInfo[j].nEndTime);
				PRINTDBG("%d:%d:%d\n", tNvrTimeSec.nHour, tNvrTimeSec.nMin, tNvrTimeSec.nSec);
			}
		}
	}

	PRINTDBG("====== After Converted PTZ Utc Time ======\n");

	for(i = 0; i < NVR_WEEK_DAY; i++)
	{
		for(j = 0; j < NVR_MAX_DISARMING_PERIOD; j++)
		{
			if(g_tFixPtzTime.atWeekScale[i].atTimeScale[j].bEnable)
			{
				NvrFixDevTcConvertWeekDay(i);
				PRINTDBG("[%d][%d] ", i, j);

				memset(&tNvrTimeSec, 0x0, sizeof(tNvrTimeSec));
				NvrFixDevTcConvertTimeToSec(&tNvrTimeSec, g_tFixPtzTime.atWeekScale[i].atTimeScale[j].nStartTime);
				PRINTDBG("%d:%d:%d~", tNvrTimeSec.nHour, tNvrTimeSec.nMin, tNvrTimeSec.nSec);

				memset(&tNvrTimeSec, 0x0, sizeof(tNvrTimeSec));
				NvrFixDevTcConvertTimeToSec(&tNvrTimeSec, g_tFixPtzTime.atWeekScale[i].atTimeScale[j].nEndTime);
				PRINTDBG("%d:%d:%d type:%d, param:%d\n", tNvrTimeSec.nHour, tNvrTimeSec.nMin, tNvrTimeSec.nSec,
				g_tFixTimingTaskEx.atWeekScale[i].atTmingInfoEx[j].byTaskType,
				g_tFixTimingTaskEx.atWeekScale[i].atTmingInfoEx[j].byTaskParam);
			}
		}
	}


	return;
}

NVRSTATUS NvrFixDevGetCurrentPTZHV(u32 *pdwPan, u32 *pdwTite,u32*pdwZoom,u32*pdwH,u32*pdwV,u32*pdwRatio)
{
    u16 wPanAngleTmp = 0;
    //u16 wTiltAngleTmp = 0;
     float fTmp=0.0;
	if(pdwPan == NULL || pdwTite == NULL || pdwZoom == NULL || pdwH == NULL || pdwV == NULL || pdwRatio == NULL)
	{
		PRINTERR("NvrFixDevGetCurrentPTAngel NULL point.\n");
		return NVR_ERR__ERROR;
	}

    if(36000 < g_dwCurrentHAngle || 36000 < g_tBasicPos.wBasicHPos)
    {
        PRINTERR( "NvrFixDevGetCurrentPTAngel :param err!\n");
        return NVR_ERR__ERROR;
    }
    if(g_tBasicPos.wBasicHPos > g_dwCurrentHAngle)
    {
        wPanAngleTmp = g_dwCurrentHAngle + 36000 - g_tBasicPos.wBasicHPos;
    }
    else
    {
        wPanAngleTmp = g_dwCurrentHAngle - g_tBasicPos.wBasicHPos;
    }
    
    //实际方位与云台水平角度相反,维多云台实际方位和水平角度是统一的不需要做相反处理
    if(NVR_CAP_PTZ_TYPE_WEIDUO != g_tPtzCap.byPtzType)
    {
        if(wPanAngleTmp > 0)//如果已经为0则不需要转换
        {
            wPanAngleTmp = 36000 - wPanAngleTmp;
        }
    }
    g_dwCurrentZoom=MAX(g_dwCurrentZoom,g_dwMinZoom);
    g_dwCurrentZoom=MIN(g_dwCurrentZoom,g_dwMaxZoom);

    *pdwPan = wPanAngleTmp;
	*pdwTite = g_dwCurrentVAngle<=9000?g_dwCurrentVAngle:(s32)(g_dwCurrentVAngle%36000-36000);
    *pdwZoom   = g_dwCurrentZoom;
    NvrFixGetZoomHAngle(g_dwCurrentZoom,&fTmp);
    *pdwH      = fTmp*100;
    NvrFixGetZoomVAngle(g_dwCurrentZoom,&fTmp);
    *pdwV      = fTmp*100;
    *pdwRatio  = ((g_dwCurrentZoom-g_dwMinZoom)*(g_dwMaxZoomRatio-g_dwMinZoomRatio)*1.0/(g_dwMaxZoom-g_dwMinZoom)+1)*100;
	PRINTDBG(" get pan:%d, tile:%d,zoom:%d,hang:%d,vang:%d,ratio:%d\n", *pdwPan, *pdwTite,*pdwZoom,*pdwH,*pdwV,*pdwRatio);

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevGetCurrentPTAngel(u32 *pdwHAngle, u32 *pdwVAngle)
{
    u16 wPanAngleTmp = 0;
    //u16 wTiltAngleTmp = 0;
	if(pdwHAngle == NULL || pdwVAngle == NULL)
	{
		PRINTERR("NvrFixDevGetCurrentPTAngel NULL point.\n");
		return NVR_ERR__ERROR;
	}

    if(36000 < g_dwCurrentHAngle || 36000 < g_tBasicPos.wBasicHPos)
    {
        PRINTERR( "NvrFixDevGetCurrentPTAngel :param err!\n");
        return NVR_ERR__ERROR;
    }
    if(g_tBasicPos.wBasicHPos > g_dwCurrentHAngle)
    {
        wPanAngleTmp = g_dwCurrentHAngle + 36000 - g_tBasicPos.wBasicHPos;
    }
    else
    {
        wPanAngleTmp = g_dwCurrentHAngle - g_tBasicPos.wBasicHPos;
    }
    
    //实际方位与云台水平角度相反,维多云台实际方位和水平角度是统一的不需要做相反处理
    if(NVR_CAP_PTZ_TYPE_WEIDUO != g_tPtzCap.byPtzType)
    {
        if(wPanAngleTmp > 0)//如果已经为0则不需要转换
        {
            wPanAngleTmp = 36000 - wPanAngleTmp;
        }
    }

    *pdwHAngle = wPanAngleTmp;
	*pdwVAngle = g_dwCurrentVAngle;
	PRINTDBG("NvrFixDevGetCurrentPTAngel query pt :%d, %d\n", g_dwCurrentHAngle, g_dwCurrentVAngle);

	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevPTAngelConvert2Vir(u32 dwRealHAngle, u32 dwRealVAngle, u32 *pdwVirHAngle, u32 *pdwVirVAngle)
{
	///<对角度进行判断，防止范围超限

	*pdwVirHAngle = 30;
	*pdwVirVAngle = 20;
	
	PRINTDBG("NvrFixDevPTAngelConvert2Vir, dwRealHAngle: %d, dwRealVAngle: %d, pdwVirHAngle: %d, pdwVirVAngle: %d.\n", dwRealHAngle, dwRealVAngle, *pdwVirHAngle, *pdwVirVAngle);

	return NVR_ERR__OK;
}


NVRSTATUS NvrFixDevGetCurrentPresetId(u16 *wPresetId)
{
	FIX_ASSERT(wPresetId);
	*wPresetId = g_byPtzPresetId;
    return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevGetZoomPosition(s32 *pnZmPos)
{
	FIX_ASSERT(pnZmPos);	
	TLcamIspParam tLcamIspParam;
	mzero(tLcamIspParam);
	
	LcamIspGetParam(0,  &tLcamIspParam);
	///<镜头锁定状态下采用断电记忆zoom值作为当前zoom位置
	if (NVR_ISP_FOCUS_MODE_LOCK == tLcamIspParam.tAction.eIpcIspFocusMode)
	{
		*pnZmPos = g_tPowerOffMemCfg[0].nZoomPos;
	}
	else
	{
		NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACT_GET_ZOOM_POSITION,pnZmPos);
	}
	PRINTDBG( "NvrFixDevGetZoomPosition ZoomPos:%d\n", *pnZmPos);
 	return NVR_ERR__OK;
}

NVRSTATUS NvrFixDevGetCurrentZoomRatio(s32 *pnZoomRatio)
{
	s32 nZoomPos = 0;
	
	NvrFixDevGetZoomPosition(&nZoomPos);
	NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACT_GET_APPOINTED_ZOOM_RATIO, &nZoomPos);
	nZoomPos = nZoomPos<=100?100:nZoomPos;
	*pnZoomRatio = nZoomPos;
	
	PRINTDBG( "NvrFixDevGetCurrentZoomRatio Zoomratio:%d\n", nZoomPos);
 	return NVR_ERR__OK;
}



NVRSTATUS NvrFixDevGetFocusPosition(s32 *pnFcPos)
{
	FIX_ASSERT(pnFcPos);	
	TLcamIspParam tLcamIspParam;
	mzero(tLcamIspParam);
	
	LcamIspGetParam(0,  &tLcamIspParam);
	 
	///<镜头锁定状态下采用断电记忆focus值作为当前focus位置
	if (NVR_ISP_FOCUS_MODE_LOCK == tLcamIspParam.tAction.eIpcIspFocusMode)
	{
		*pnFcPos = 9;
	}
	else
	{
		NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACT_GET_FOCUS_POSITION, pnFcPos);
	}
	PRINTDBG( "NvrFixDevGetFocusPosition FocusPos:%d\n", *pnFcPos);
 	return NVR_ERR__OK;
}


typedef struct  {
	u32 dwZmPos;
	u32 dwHangle;
	u32 dwVangle;
}STRUCT_mtZmParam;

volatile STRUCT_mtZmParam m_mtZmAngle2[256]={};//热成像


volatile STRUCT_mtZmParam m_mtZmAngle1[32]={};//可见光

//热成像
static BOOL NvrFixDevLensIrayInit(EIpcCamLensType byLenType)
{
    //暂时只有一种，不做区分
    m_mtZmAngle2[0].dwZmPos = 314;
    m_mtZmAngle2[0].dwHangle=1466;
    m_mtZmAngle2[0].dwVangle=1343;
    
    m_mtZmAngle2[1].dwZmPos = 465;
    m_mtZmAngle2[1].dwHangle=1398;
    m_mtZmAngle2[1].dwVangle=1274;
    
    m_mtZmAngle2[2].dwZmPos = 571;
    m_mtZmAngle2[2].dwHangle=1345;
    m_mtZmAngle2[2].dwVangle=1231;
    
    m_mtZmAngle2[3].dwZmPos = 703;
    m_mtZmAngle2[3].dwHangle=1288;
    m_mtZmAngle2[3].dwVangle=1167;
    
    m_mtZmAngle2[4].dwZmPos = 847;
    m_mtZmAngle2[4].dwHangle=1224;
    m_mtZmAngle2[4].dwVangle=1108;
    
    m_mtZmAngle2[5].dwZmPos = 985;
    m_mtZmAngle2[5].dwHangle=1158;
    m_mtZmAngle2[5].dwVangle=1059;
    
    m_mtZmAngle2[6].dwZmPos = 1137;
    m_mtZmAngle2[6].dwHangle=1099;
    m_mtZmAngle2[6].dwVangle=1002;
    
    m_mtZmAngle2[7].dwZmPos = 1269;
    m_mtZmAngle2[7].dwHangle=1040;
    m_mtZmAngle2[7].dwVangle=953;
    
    m_mtZmAngle2[8].dwZmPos = 1412;
    m_mtZmAngle2[8].dwHangle=984;
    m_mtZmAngle2[8].dwVangle=897;
    
    m_mtZmAngle2[9].dwZmPos = 1539;
    m_mtZmAngle2[9].dwHangle=932;
    m_mtZmAngle2[9].dwVangle=843;
    
    m_mtZmAngle2[10].dwZmPos = 1688;
    m_mtZmAngle2[10].dwHangle=876;
    m_mtZmAngle2[10].dwVangle=798;
    
    m_mtZmAngle2[11].dwZmPos = 1845;
    m_mtZmAngle2[11].dwHangle=815;
    m_mtZmAngle2[11].dwVangle=741;
    
    m_mtZmAngle2[12].dwZmPos = 1958;
    m_mtZmAngle2[12].dwHangle=773;
    m_mtZmAngle2[12].dwVangle=701;
    
    m_mtZmAngle2[13].dwZmPos = 2073;
    m_mtZmAngle2[13].dwHangle=736;
    m_mtZmAngle2[13].dwVangle=664;
    
    m_mtZmAngle2[14].dwZmPos = 2222;
    m_mtZmAngle2[14].dwHangle=681;
    m_mtZmAngle2[14].dwVangle=619;
    
    m_mtZmAngle2[15].dwZmPos = 2361;
    m_mtZmAngle2[15].dwHangle=636;
    m_mtZmAngle2[15].dwVangle=578;
    
    m_mtZmAngle2[16].dwZmPos = 2490;
    m_mtZmAngle2[16].dwHangle=594;
    m_mtZmAngle2[16].dwVangle=5.4;
    
    m_mtZmAngle2[17].dwZmPos = 2661;
    m_mtZmAngle2[17].dwHangle=541;
    m_mtZmAngle2[17].dwVangle=491;
    
    m_mtZmAngle2[18].dwZmPos = 2811;
    m_mtZmAngle2[18].dwHangle=499;
    m_mtZmAngle2[18].dwVangle=451;
    
    m_mtZmAngle2[19].dwZmPos = 2967;
    m_mtZmAngle2[19].dwHangle=463;
    m_mtZmAngle2[19].dwVangle=415;
    
    m_mtZmAngle2[20].dwZmPos = 3115;
    m_mtZmAngle2[20].dwHangle=414;
    m_mtZmAngle2[20].dwVangle=382;
    
    m_mtZmAngle2[21].dwZmPos = 3228;
    m_mtZmAngle2[21].dwHangle=394;
    m_mtZmAngle2[21].dwVangle=349;
    
    m_mtZmAngle2[22].dwZmPos = 3381;
    m_mtZmAngle2[22].dwHangle=346;
    m_mtZmAngle2[22].dwVangle=314;
    
    m_mtZmAngle2[23].dwZmPos = 3545;
    m_mtZmAngle2[23].dwHangle=308;
    m_mtZmAngle2[23].dwVangle=287;
    
    m_mtZmAngle2[24].dwZmPos = 3642;
    m_mtZmAngle2[24].dwHangle=289;
    m_mtZmAngle2[24].dwVangle=265;
    return TRUE;


}


///<获取垂直视场角
BOOL32 NvrFixIrayGetZoomVAngle(u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;

    u32 V1=0;
    u32 V2=0;
    u32 Z1=0;
    u32 Z2=0;
	if(!pZoomValue)
	{
	    PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle2[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<314)
    {
        zoomPostion=314;
    }

    if(zoomPostion>=3642)
    {
        zoomPostion=3642;
    }

	for(i=0;i<24;i++)
	{
	    Z1=m_mtZmAngle2[i].dwZmPos;
        Z2=m_mtZmAngle2[i+1].dwZmPos;
        V1=m_mtZmAngle2[i].dwVangle;
        V2=m_mtZmAngle2[i+1].dwVangle;
        
		if(zoomPostion >= Z1 && zoomPostion < Z2)
		{
		    *pZoomValue = ((V2 - V1)*zoomPostion+V1*Z2-Z1*V2)*1.0/(Z2 - Z1)/100;          
			break;
		}
	}

	PRINTDBG("Vangle:%f zoomPostion pos:%u \n",*pZoomValue,zoomPostion);
	return TRUE;
}

///<获取水平视场角
BOOL32 NvrFixIrayGetZoomHAngle(u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;
    u32 H1=0;
    u32 H2=0;
    u32 Z1=0;
    u32 Z2=0;
	if( !pZoomValue)
	{
        PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle2[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<314)
    {
        zoomPostion=314;
    }

    if(zoomPostion>=3642)
    {
        zoomPostion=3642;
    }

	for(i=0;i<24;i++)
	{
        Z1=m_mtZmAngle2[i].dwZmPos;
        Z2=m_mtZmAngle2[i+1].dwZmPos;

        H1=m_mtZmAngle2[i].dwHangle;
        H2=m_mtZmAngle2[i+1].dwHangle;
        
		if(zoomPostion >= Z1 && zoomPostion < Z2)
		{
		    *pZoomValue = ((H2 - H1)*zoomPostion+H1*Z2-Z1*H2)*1.0/(Z2 - Z1)/100;          
			break;
		}
	}

	PRINTDBG("hangle:%f zoomPostion pos:%u \n",*pZoomValue,zoomPostion);
	return TRUE;
}

///<根据倍率获取ZOOM值
BOOL32 NvrFixIrayGetZoomByRatio(float ration,u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;
    s32 H1=0;
    s32 H2=0;
    s32 Z1=0;
    s32 Z2=0;
    

    float fCurH,fTargetH;
    s32 dwH=0;
	if( !pZoomValue)
	{
        PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle2[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<314)
    {
        zoomPostion=314;
    }

    if(zoomPostion>=3642)
    {
        zoomPostion=3642;
    }

    NvrFixGetZoomHAngle(zoomPostion,&fCurH);
        
    fTargetH = 100*2*atan(ration*tan((fCurH/2)*3.14159/180))*180/3.14159;
    dwH=fTargetH;

    dwH=dwH>m_mtZmAngle2[0].dwHangle?m_mtZmAngle2[0].dwHangle:dwH;
    dwH=dwH<m_mtZmAngle2[24].dwHangle?m_mtZmAngle2[24].dwHangle:dwH;

	for(i=0;i<24;i++)
	{
        Z1=m_mtZmAngle2[i].dwZmPos;
        Z2=m_mtZmAngle2[i+1].dwZmPos;

        H1=m_mtZmAngle2[i].dwHangle;
        H2=m_mtZmAngle2[i+1].dwHangle;
        
		if(dwH >= H2 && dwH <= H1)
		{
            PRINTDBG("z2-z1:%d,z1*h2-h1*z2:%d,h2-h1:%d,(Z2-Z1)*fTargetH:%lu,total:%lu,zoom:%d\n",(Z2-Z1),(Z1*H2-H1*Z2),(H2-H1),(Z2-Z1)*dwH,(Z2-Z1)*dwH+(Z1*H2-H1*Z2),((Z2-Z1)*dwH+(Z1*H2-H1*Z2))/(H2-H1));
            *pZoomValue =1.0*((Z2-Z1)*dwH+(Z1*H2-H1*Z2))/(H2-H1);
			break;
		}
    }
	PRINTDBG("i:%d,ZOOM:%f zoomPostion pos:%u RATION:%f,b:%f,h:%d\n",i,*pZoomValue,zoomPostion,ration,fTargetH,dwH);
	return TRUE;
}



//可见光
static BOOL NvrFixDevLensInit(EIpcCamLensType byLenType)
{
    if(byLenType == CamType_CBC_775)
    {
        m_mtZmAngle1[0].dwZmPos = 430;
        m_mtZmAngle1[0].dwHangle=2280;
        m_mtZmAngle1[0].dwVangle=1485;
        
        m_mtZmAngle1[1].dwZmPos = 533;
        m_mtZmAngle1[1].dwHangle=1965;
        m_mtZmAngle1[1].dwVangle=1310;
        
        m_mtZmAngle1[2].dwZmPos = 634;
        m_mtZmAngle1[2].dwHangle=1828;
        m_mtZmAngle1[2].dwVangle=1223;
        
        m_mtZmAngle1[3].dwZmPos = 737;
        m_mtZmAngle1[3].dwHangle=1658;
        m_mtZmAngle1[3].dwVangle=1107;
        
        m_mtZmAngle1[4].dwZmPos = 824;
        m_mtZmAngle1[4].dwHangle=1472;
        m_mtZmAngle1[4].dwVangle=980;
        
        m_mtZmAngle1[5].dwZmPos = 925;
        m_mtZmAngle1[5].dwHangle=1311;
        m_mtZmAngle1[5].dwVangle=875;
        
        m_mtZmAngle1[6].dwZmPos = 1022;
        m_mtZmAngle1[6].dwHangle=1176;
        m_mtZmAngle1[6].dwVangle=784;
        
        m_mtZmAngle1[7].dwZmPos = 1126;
        m_mtZmAngle1[7].dwHangle=1029;
        m_mtZmAngle1[7].dwVangle=684;
        
        m_mtZmAngle1[8].dwZmPos = 1226;
        m_mtZmAngle1[8].dwHangle=881;
        m_mtZmAngle1[8].dwVangle=582;
        
        m_mtZmAngle1[9].dwZmPos = 1327;
        m_mtZmAngle1[9].dwHangle=745;
        m_mtZmAngle1[9].dwVangle=490;
        
        m_mtZmAngle1[10].dwZmPos = 1430;
        m_mtZmAngle1[10].dwHangle=633;
        m_mtZmAngle1[10].dwVangle=414;
        
        m_mtZmAngle1[11].dwZmPos = 1528;
        m_mtZmAngle1[11].dwHangle=514;
        m_mtZmAngle1[11].dwVangle=338;
        
        m_mtZmAngle1[12].dwZmPos = 1623;
        m_mtZmAngle1[12].dwHangle=425;
        m_mtZmAngle1[12].dwVangle=282;
        
        m_mtZmAngle1[13].dwZmPos = 1729;
        m_mtZmAngle1[13].dwHangle=344;
        m_mtZmAngle1[13].dwVangle=228;
        
        m_mtZmAngle1[14].dwZmPos = 1821;
        m_mtZmAngle1[14].dwHangle=277;
        m_mtZmAngle1[14].dwVangle=175;
        
        m_mtZmAngle1[15].dwZmPos = 1925;
        m_mtZmAngle1[15].dwHangle=218;
        m_mtZmAngle1[15].dwVangle=147;
        
        m_mtZmAngle1[16].dwZmPos = 2020;
        m_mtZmAngle1[16].dwHangle=178;
        m_mtZmAngle1[16].dwVangle=119;
        
        m_mtZmAngle1[17].dwZmPos = 2120;
        m_mtZmAngle1[17].dwHangle=144;
        m_mtZmAngle1[17].dwVangle=94;
        
        m_mtZmAngle1[18].dwZmPos = 2223;
        m_mtZmAngle1[18].dwHangle=115;
        m_mtZmAngle1[18].dwVangle=77;
        
        m_mtZmAngle1[19].dwZmPos = 2324;
        m_mtZmAngle1[19].dwHangle=94;
        m_mtZmAngle1[19].dwVangle=62;
        
        m_mtZmAngle1[20].dwZmPos = 2414;
        m_mtZmAngle1[20].dwHangle=77;
        m_mtZmAngle1[20].dwVangle=51;
        
        m_mtZmAngle1[21].dwZmPos = 2514;
        m_mtZmAngle1[21].dwHangle=64;
        m_mtZmAngle1[21].dwVangle=42;
        
        m_mtZmAngle1[22].dwZmPos = 2615;
        m_mtZmAngle1[22].dwHangle=52;
        m_mtZmAngle1[22].dwVangle=37;
        
        m_mtZmAngle1[23].dwZmPos = 2713;
        m_mtZmAngle1[23].dwHangle=45;
        m_mtZmAngle1[23].dwVangle=30;
        
        m_mtZmAngle1[24].dwZmPos = 2742;
        m_mtZmAngle1[24].dwHangle=42;
        m_mtZmAngle1[24].dwVangle=28;
        
    }
    
	if(byLenType == CamType_ZhongTian_500)  //500MM镜头
	{
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 32;
	}
	else if(byLenType == CamType_ZhongTian_750) //750MM镜头
	{
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 60;
	}
	else if(byLenType == CamType_ZhongTian_1000)  //1000MM镜头
	{
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 60;
	}
    else if(byLenType == CamType_CBC_500)  //CBC500MM镜头
	{
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 24;
	}
    else if(byLenType == CamType_CBC_775)  //CBC775MM镜头，安霸ipc528使用此镜头
    {
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 62;
    }
    else if(byLenType == CamType_YAMANO_755)  //YAMANO775MM镜头
    {
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 62;
    }
    else if(byLenType == CamType_ZHONGYOU_800)  //800MM镜头
    {
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 64;
    }
    else if(byLenType == CamType_ZHONGYOU_500)  //500MM镜头
    {
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 50;
    }
    else if(byLenType == CamType_ZHONGYOU_350)  //350MM镜头
    {
        g_dwMinZoomRatio = 1;
        g_dwMaxZoomRatio = 35;
    }

	return TRUE;
}


///<获取垂直视场角
BOOL32 NvrFixGetZoomVAngle(u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;

    u32 V1=0;
    u32 V2=0;
    u32 Z1=0;
    u32 Z2=0;
	if(!pZoomValue)
	{
	    PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle1[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<g_dwMinZoom)
    {
        zoomPostion=g_dwMinZoom;
    }

    if(zoomPostion>=g_dwMaxZoom)
    {
        zoomPostion=g_dwMaxZoom;
    }

	for(i=0;i<24;i++)
	{
	    Z1=m_mtZmAngle1[i].dwZmPos;
        Z2=m_mtZmAngle1[i+1].dwZmPos;
        V1=m_mtZmAngle1[i].dwVangle;
        V2=m_mtZmAngle1[i+1].dwVangle;
        
		if(zoomPostion >= Z1 && zoomPostion <= Z2)
		{
		    *pZoomValue = ((V2 - V1)*zoomPostion+V1*Z2-Z1*V2)*1.0/(Z2 - Z1)/100;          
			break;
		}
	
	}

	PRINTDBG("Vangle:%f zoomPostion pos:%u \n",*pZoomValue,zoomPostion);
	return TRUE;
}

///<获取水平视场角
BOOL32 NvrFixGetZoomHAngle(u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;
    u32 H1=0;
    u32 H2=0;
    u32 Z1=0;
    u32 Z2=0;
	if( !pZoomValue)
	{
        PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle1[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<g_dwMinZoom)
    {
        zoomPostion=g_dwMinZoom;
    }

    if(zoomPostion>=g_dwMaxZoom)
    {
        zoomPostion=g_dwMaxZoom;
    }

	for(i=0;i<24;i++)
	{
        Z1=m_mtZmAngle1[i].dwZmPos;
        Z2=m_mtZmAngle1[i+1].dwZmPos;

        H1=m_mtZmAngle1[i].dwHangle;
        H2=m_mtZmAngle1[i+1].dwHangle;
        
		if(zoomPostion >= Z1 && zoomPostion <= Z2)
		{
		    *pZoomValue = ((H2 - H1)*zoomPostion+H1*Z2-Z1*H2)*1.0/(Z2 - Z1)/100;          
			break;
		}
	
	}

	PRINTDBG("hangle:%f zoomPostion pos:%u \n",*pZoomValue,zoomPostion);
	return TRUE;
}

///<根据倍率获取ZOOM值
BOOL32 NvrFixGetZoomByRatio(float ration,u32 zoomPostion, float *pZoomValue)
{
    u32 i = 0;
    s32 H1=0;
    s32 H2=0;
    s32 Z1=0;
    s32 Z2=0;
    

    float fCurH,fTargetH;
    s32 dwH=0;
	if( !pZoomValue)
	{
        PRINTDBG("%u zoomPostion pos:%u err\n",i,m_mtZmAngle1[i].dwZmPos);
		return FALSE;
	}

    if(zoomPostion<g_dwMinZoom)
    {
        zoomPostion=g_dwMinZoom;
    }

    if(zoomPostion>=g_dwMaxZoom)
    {
        zoomPostion=g_dwMaxZoom;
    }

    NvrFixGetZoomHAngle(zoomPostion,&fCurH);
        
    fTargetH = 100*2*atan(ration*tan((fCurH/2)*3.14159/180))*180/3.14159;
    dwH=fTargetH;

    dwH=dwH>m_mtZmAngle1[0].dwHangle?m_mtZmAngle1[0].dwHangle:dwH;
    dwH=dwH<m_mtZmAngle1[24].dwHangle?m_mtZmAngle1[24].dwHangle:dwH;

	for(i=0;i<24;i++)
	{
        Z1=m_mtZmAngle1[i].dwZmPos;
        Z2=m_mtZmAngle1[i+1].dwZmPos;

        H1=m_mtZmAngle1[i].dwHangle;
        H2=m_mtZmAngle1[i+1].dwHangle;
        
		if(dwH >= H2 && dwH <= H1)
		{
            PRINTDBG("z2-z1:%d,z1*h2-h1*z2:%d,h2-h1:%d,(Z2-Z1)*fTargetH:%lu,total:%lu,zoom:%d\n",(Z2-Z1),(Z1*H2-H1*Z2),(H2-H1),(Z2-Z1)*dwH,(Z2-Z1)*dwH+(Z1*H2-H1*Z2),((Z2-Z1)*dwH+(Z1*H2-H1*Z2))/(H2-H1));
            *pZoomValue =1.0*((Z2-Z1)*dwH+(Z1*H2-H1*Z2))/(H2-H1);
			break;
		}
    }
	PRINTDBG("i:%d,ZOOM:%f zoomPostion pos:%u RATION:%f,b:%f,h:%d\n",i,*pZoomValue,zoomPostion,ration,fTargetH,dwH);
	return TRUE;
}




ENvrPtzTaskType NvrFixDevWatchOnTypeConvert2Lcam(EAppCltPtzTaskType eCltTaskType)
{
	ENvrPtzTaskType eNvrType;
	u16 i = 0;
	
	for (i = 0; i < NVR_PTZCTRL_PTZ_TASK_COUNT - 1; i++)
	{
		if (g_atWatchOnTypeMap[i].eApCltType == eCltTaskType)
		{
			eNvrType = g_atWatchOnTypeMap[i].eNvrType;
			PRINTDBG("NvrPtzDevWatchOnTypeConvert2Lcam, eNvrType %d, eCltTaskType %d\n", eNvrType, eCltTaskType);
			return eNvrType;
		}
	}
	
	PRINTDBG("NvrPtzDevWatchOnTypeConvert2Lcam not match pui ctrltype %d\n", eCltTaskType);

	return NVR_PTZCTRL_PTZ_TASK_COUNT;
}

ENvrPtzTaskType NvrFixDevWatchOnTypeConvert2AppClt(ENvrPtzTaskType eNvrType)
{
	EAppCltPtzTaskType eCltTaskType;
	u16 i = 0;
	
	for (i = 0; i < NVR_PTZCTRL_PTZ_TASK_COUNT - 1; i++)
	{ 
		if (g_atWatchOnTypeMap[i].eNvrType == eNvrType)
		{
			eCltTaskType = g_atWatchOnTypeMap[i].eApCltType;
			PRINTDBG("NvrPtzDevWatchOnTypeConvert2AppClt, eNvrType %d, ctrl type %d\n", eNvrType, eCltTaskType);
			return eCltTaskType;
		}
	}
	
	PRINTDBG("NvrPtzDevWatchOnTypeConvert2AppClt not match pui eNvrType %d\n", eNvrType);

	return APP_CLT_PTZ_TASK_COUNT;
}

NVRSTATUS NvrFixDevGetPtzState(PTNvrPtzBasicState ptIpcPtzState)
{
	NVRSTATUS eRetValue = NVR_ERR__OK;
	TNvrPtzBasicState  tIpcPtzState;

	FIX_ASSERT(ptIpcPtzState);
	mzero(tIpcPtzState);
	eRetValue = NvrFixCfgGetParamByFlag(&tIpcPtzState, sizeof(TNvrPtzBasicState), NVR_PTZ_MODULE_STATE_PARAM);
	if (NVR_ERR__OK != eRetValue)
	{
		PRINTERR("NvrPtzDevGetPtzState get state failed, err no:%d\n", eRetValue);
		eRetValue = NVR_ERR__ERROR;
	}
	memcpy(ptIpcPtzState, &tIpcPtzState, sizeof(TNvrPtzBasicState));

	PRINTDBG("NvrFixDevGetPtzState Flip:%u, Depthrate:%u, Mlimit:%u, SLimit:%u, Speed:%u, PreSetSpd:%u, ZoomSpd:%u, IR:%u, IRmode:%u, IRsens:%u, IRvalue:%u, Laser:%u, Laserdist:%u,Laserintensity:%ld,lasermode:%ld, Wipe:%u, Defrost:%u, LimitDis:%u, PtzVer:%s, VidEncFreeze:%d,DeMistState:%u,DeMistTime:%u,dwSLState:%d,PowerOnMode:%d,LedMainSwitch:%d,PowerOffMode:%d,CarMode:%d,PtzId:%d,ExtWifiSwitch:%d\n",
	ptIpcPtzState->dwAutoFlip,
	ptIpcPtzState->dwDepthrateSpd,
	ptIpcPtzState->dwManuLimitPos,
	ptIpcPtzState->dwScanLimitPos,
	ptIpcPtzState->dwScanSpeedValue,
	ptIpcPtzState->dwPreSetSpdValue,
	ptIpcPtzState->eZoomSpeedValue,
	ptIpcPtzState->dwInfaredState,
	ptIpcPtzState->eInfaredMode,
	ptIpcPtzState->dwInfaredSens,
	ptIpcPtzState->dwInfaredValue,
	ptIpcPtzState->eLaserSwitch,
	ptIpcPtzState->dwLaserDist,
	ptIpcPtzState->dwLaserIntensity,
	ptIpcPtzState->eLaserMode,
	ptIpcPtzState->dwWiperState,
	ptIpcPtzState->dwDefrostState,
	ptIpcPtzState->dwPosLimitDisplay,
	ptIpcPtzState->achPtzSoftVer,
	ptIpcPtzState->dwVidEncFreeze,
	ptIpcPtzState->dwDeMistState,
	ptIpcPtzState->dwDeMistTime,
	ptIpcPtzState->dwSLState,
	ptIpcPtzState->byPowerOnMode,
	ptIpcPtzState->dwLedMainSwitch,
	ptIpcPtzState->byPowerOffMode,
	ptIpcPtzState->byCarMode,
	ptIpcPtzState->byPtzId,
	ptIpcPtzState->byExtWifiSwitch);

	return eRetValue;
}

NVRSTATUS NvrFixDevSavePtzState(TNvrPtzCtrlInfo tPtzCmdInfo)
{
	TNvrPtzBasicState  tIpcPtzState;
	
	mzero(tIpcPtzState);
	NvrFixCfgGetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
	PRINTDBG("NvrPtzDevSavePtzState save eCtrlType:%d\n", tPtzCmdInfo.eCtrlType);

	switch (tPtzCmdInfo.eCtrlType)
	{
		case NVR_PTZCTRL_MANUALLIMITSWITCH_SET:		//手动限位(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwManuLimitPos = NVR_PTZ_MODE_ON;
				
			}
			else
			{
				tIpcPtzState.dwManuLimitPos = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;

		case NVR_PTZCTRL_SCANLIMITSWITCH_SET:		//扫描限位(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwScanLimitPos = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwScanLimitPos = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;

		case NVR_PTZCTRL_AUTOFLIP_SET:				//自动翻转(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwAutoFlip = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwAutoFlip = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;

		case NVR_PTZCTRL_DEPTHRATESPEED_SET:		//景深比例(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwDepthrateSpd = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwDepthrateSpd = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;
		
		case NVR_PTZCTRL_IR_SET:					//红外(记录开关状态)
		{
			//ptz控制面板上的开灯与关灯操作，对于激光和红外发的都是同一条ptz命令
			//如果当前支持的是红外保存红外的状态
			if (NVR_CAP_SUPPORT == g_tPtzCap.byInfredSupport)
			{
				tIpcPtzState.dwInfaredState = tPtzCmdInfo.eMode;
				if(NVR_PTZCTRL_MODE_CLOSE == tIpcPtzState.dwInfaredState)
				{
					tIpcPtzState.eInfaredMode = NVR_INFRARED_AUTO_CLOSE;
				}
				else
				{
					if (NVR_CAP_SUPPORT == g_tPtzCap.byWhiteLightSupport && NVR_CAP_SUPPORT == g_tPtzCapInfo.abyInfredMode[NVR_INFRARED_MANUAL_OPEN] && FALSE == tPtzCmdInfo.bTaskTrg)
					{
						tIpcPtzState.eInfaredMode = NVR_INFRARED_MANUAL_OPEN;
					}
					else
					{
						tIpcPtzState.eInfaredMode = NVR_INFRARED_AUTO_OPEN;
					}
				}
				NvrFixIspSetKeyParam(0, NVR_FIX_ISP_CMD_SET_INFRARED_MODE, &(tIpcPtzState.eInfaredMode));
			}
			//ptz控制面板上的开灯与关灯操作，对于激光和红外发的都是同一条ptz命令
			//如果当前支持的是激光保存激光的状态
			else if(NVR_CAP_SUPPORT == g_tPtzCap.byLaserSupport)
			{
				if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
				{
					tIpcPtzState.eLaserSwitch = NVR_PTZ_MODE_ON;
				}
				else
				{
					tIpcPtzState.eLaserSwitch = NVR_PTZ_MODE_CLOSE;
				}
			}
		}
		break;

		case NVR_PTZCTRL_DEFROST_SET:				//除霜(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwDefrostState = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwDefrostState = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;
		
		case NVR_PTZCTRL_DEMIST_SET:				//除雾(记录开关状态)
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwDeMistState = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwDeMistState = NVR_PTZ_MODE_CLOSE;
			}
			tIpcPtzState.dwDeMistTime = tPtzCmdInfo.dwRes;
		}
		break;

		/*设置上电启动模式*/
		case NVR_PTZCTRL_POWERON_MODE_SET:
		{
			tIpcPtzState.byPowerOnMode = tPtzCmdInfo.eMode;
			if (NVR_PTZCTRL_MODE_POWERON_PLUGIN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.byPowerOnMode = NVR_POWERON_MODE_PLUGIN;
			}
			else
			{
				tIpcPtzState.byPowerOnMode = NVR_POWERON_MODE_BUTTON;
			}
		}
		break;

		/*设置断电关机模式*/
		case NVR_PTZCTRL_POWEROFF_MODE_SET:
		{
			tIpcPtzState.byPowerOffMode = tPtzCmdInfo.eMode;
		}
		break;

		/*LED指示灯总开关设置*/
		case NVR_PTZCTRL_LEDMAINSWITCH:
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwLedMainSwitch = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwLedMainSwitch = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;

		/*外置扩展WIFI开关设置*/
		case NVR_PTZCTRL_EXT_WIFI_SWITCH:
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.byExtWifiSwitch = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.byExtWifiSwitch = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;

		case NVR_PTZCTRL_LASER_SET:					//激光(开关/自动)
		{
			tIpcPtzState.eLaserSwitch = tPtzCmdInfo.eMode;
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.eLaserSwitch = NVR_DEV_LASER_OPEN;
			}
			else if (NVR_PTZCTRL_MODE_CLOSE== tPtzCmdInfo.eMode)
			{
				tIpcPtzState.eLaserSwitch = NVR_DEV_LASER_CLOSE;
			}
			else if(NVR_PTZCTRL_MODE_AUTO== tPtzCmdInfo.eMode)
			{
				tIpcPtzState.eLaserSwitch = NVR_DEV_LASER_AUTO;
			}
		}
		break;

		case NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET:				//水平扫描速度
		{
			tIpcPtzState.dwScanSpeedValue = tPtzCmdInfo.wPanSpeed;
		}
		break;

		case NVR_PTZCTRL_PRESET_SPEED_SET:			//预置位切换速度
		{
			tIpcPtzState.dwPreSetSpdValue = tPtzCmdInfo.dwRes;
		}
		break;

		case NVR_PTZCTRL_SET_ZOOM_SPEED:           //zoom拉伸速度
		{
			tIpcPtzState.eZoomSpeedValue = tPtzCmdInfo.dwRes;
		}
		break;

		case NVR_PTZCTRL_PT_OSD_MODE:			//设置PT字幕显示方式
		{
			tIpcPtzState.ePTOsdMode = tPtzCmdInfo.dwRes;
		}
		break;

		case NVR_PTZCTRL_LASER_INTENSITY:
		{
			tIpcPtzState.dwLaserIntensity = tPtzCmdInfo.dwRes;
		}
		break;
		case NVR_PTZCTRL_SET_SRC_PRIORITY:
		{
			tIpcPtzState.byPriorityType = tPtzCmdInfo.bySourceType;
			tIpcPtzState.byPriDelayTime = tPtzCmdInfo.dwRes;
		}
		break;
		case NVR_PTZCTRL_VID_ENC_FREEZE:
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.dwVidEncFreeze = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.dwVidEncFreeze = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;
		case NVR_PTZCTRL_PTZ_VERTICA_RANGE:
		{
			tIpcPtzState.eVerticaRange = tPtzCmdInfo.dwRes;
			switch(tIpcPtzState.eVerticaRange)
			{
				case NVR_DEV_VERTICA_RANGE_MINUS_15_90:
					
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 36000-1500;
					break;
				case NVR_DEV_VERTICA_RANGE_MINUS_10_90:
					
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 36000-1000;
					break;
				case NVR_DEV_VERTICA_RANGE_MINUS_5_90:
					
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 36000-500;
					break;
					
				case NVR_DEV_VERTICA_RANGE_0_90:
					
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 0;
					break;

				case NVR_DEV_VERTICA_RANGE_5_90:
				
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 500;
					break;
				case NVR_DEV_VERTICA_RANGE_10_90:
				
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 1000;
					break;
				
				case NVR_DEV_VERTICA_RANGE_15_90:
				
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 1600;
					break;
				
				case NVR_DEV_VERTICA_RANGE_20_90:
				
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 1900;
					break;
				
				case NVR_DEV_VERTICA_RANGE_8_90:
				
					g_tPtzCapInfo.tStrucTAngleRange.dwMinValue = 800;
					break;
				
				default:
					break;
			}

		}
		break;
		case NVR_PTZCTRL_CAR_MODE_SET:
		{
			if(NVR_PTZCTRL_MODE_OPEN == tPtzCmdInfo.eMode)
			{
				tIpcPtzState.byCarMode = NVR_PTZ_MODE_ON;
			}
			else
			{
				tIpcPtzState.byCarMode = NVR_PTZ_MODE_CLOSE;
			}
		}
		break;
        case NVR_PTZCTRL_SET_PTZ_ID:
        {
            tIpcPtzState.byPtzId = (u8)tPtzCmdInfo.dwRes;
        }
        break;
		default:
		PRINTERR("NvrPtzDevSavePtzState unknow operate type %d\n", tPtzCmdInfo.eCtrlType);
	}

	NvrFixCfgSetParamByFlag(&tIpcPtzState, sizeof(tIpcPtzState), NVR_PTZ_MODULE_STATE_PARAM);

	return NVR_ERR__OK;

}


//激光开关自动
static  u8 abyLaesr[NVR_DEV_LASER_SWITCH_NUM][7] =
{
	{0xff ,0x01 ,0x00 ,0x07 ,0x00 ,0xfa ,0x02},//自动
	{0xff ,0x01 ,0x00 ,0x07 ,0x00 ,0xf8 ,0x00},//开
	{0xff ,0x01 ,0x00 ,0x07 ,0x00 ,0xf7 ,0xff},//关
};



#define IPC_THRMAL_IMAGER_FT22640_COLORIZE_MAX 28

//图像增强
typedef enum
{
    IPC_THRMAL_IMAGER_IDE_ENHANCE_OPEN = 0,       //开启
    IPC_THRMAL_IMAGER_IDE_ENHANCE_CLOSE,          //关闭

    IPC_THRMAL_IMAGER_IDE_ENHANCE_MAX             //自动枚举值，不要动

}EIpcThrmalImagerIdeEnhanceMode;


//图像增强滤波
typedef enum
{
    IPC_THRMAL_IMAGER_IDE_FILTER_OPEN = 0,       //开启
    IPC_THRMAL_IMAGER_IDE_FILTER_CLOSE,          //关闭

    IPC_THRMAL_IMAGER_IDE_FILTER_MAX             //自动枚举值，不要动

}EIpcThrmalImagerIdeFilterMode;


typedef enum
{
    IPC_XCORE_THRMAL_COLORIZE_WHITE_HOT = 0,    //白热
    IPC_XCORE_THRMAL_COLORIZE_BALCK_HOT = 1,    //黑热
    IPC_XCORE_THRMAL_COLORIZE_RAINBOW,          //彩虹
    IPC_XCORE_THRMAL_COLORIZE_PRIMARY_COLOR,    //三原色
    IPC_XCORE_THRMAL_COLORIZE_BLUE_RED_YELLOW,  //蓝红黄
    IPC_XCORE_THRMAL_COLORIZE_BLUE_PURPLE_RED,  //蓝紫红
    IPC_XCORE_THRMAL_COLORIZE_MIX_COLOR,        //混合色
    IPC_XCORE_THRMAL_COLORIZE_BLUE_GREEN_RED,   //蓝绿红
    IPC_XCORE_THRMAL_COLORIZE_DARK_GREEN_RED,   //墨绿红
    IPC_XCORE_THRMAL_COLORIZE_LAVA,             //熔岩
    IPC_XCORE_THRMAL_COLORIZE_BLUE_GREEN_ORANGE,//蓝青橙
    IPC_XCORE_THRMAL_COLORIZE_WARN_RED,         //警示红
    IPC_XCORE_THRMAL_COLORIZE_ICE_FIRE,         //冰火
    IPC_XCORE_THRMAL_COLORIZE_BLACK_RED,        //黑红
    IPC_XCORE_THRMAL_COLORIZE_BLUE_RED,         //蓝红
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_RED,     //渐变红
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_GREEN,   //渐变绿
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_YELLOW,  //渐变黄
    IPC_XCORE_THRMAL_COLORIZE_WARN_GREEN,       //警示绿
    IPC_XCORE_THRMAL_COLORIZE_WARN_BLUE,        //警示蓝

    IPC_XCORE_THRMAL_COLORIZE_MAX         //自动枚举值，不要动
}EIpcXcoreTherColorRizeMode;

#if 0
static const u8 adwCubeToXCORE[IPC_THRMAL_IMAGER_FT22640_COLORIZE_MAX] =
{
	IPC_XCORE_THRMAL_COLORIZE_WHITE_HOT,
	IPC_XCORE_THRMAL_COLORIZE_BALCK_HOT,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_RAINBOW,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_LAVA,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
	IPC_XCORE_THRMAL_COLORIZE_MAX,
    IPC_XCORE_THRMAL_COLORIZE_PRIMARY_COLOR,
    IPC_XCORE_THRMAL_COLORIZE_BLUE_RED_YELLOW,
    IPC_XCORE_THRMAL_COLORIZE_BLUE_PURPLE_RED,
    IPC_XCORE_THRMAL_COLORIZE_MIX_COLOR,
    IPC_XCORE_THRMAL_COLORIZE_BLUE_GREEN_RED,
    IPC_XCORE_THRMAL_COLORIZE_DARK_GREEN_RED,
    IPC_XCORE_THRMAL_COLORIZE_BLUE_GREEN_ORANGE,
    IPC_XCORE_THRMAL_COLORIZE_WARN_RED,
    IPC_XCORE_THRMAL_COLORIZE_ICE_FIRE,
    IPC_XCORE_THRMAL_COLORIZE_BLACK_RED,
    IPC_XCORE_THRMAL_COLORIZE_BLUE_RED,
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_RED,
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_GREEN,
    IPC_XCORE_THRMAL_COLORIZE_GRADIENT_YELLOW,
    IPC_XCORE_THRMAL_COLORIZE_WARN_GREEN,
    IPC_XCORE_THRMAL_COLORIZE_WARN_BLUE,
};
#endif

u8 byIdeEnhanceGrade = 0;//IDE增强等级
u8 abyIdeEnhanceGradeValue[9] = {0xAA, 0x05, 0x01, 0x19, 0x01, 0x04, 0xCE, 0xEB, 0xAA};


//IDE增强串口指令
static  u8 abyXcoreFT2640ThrmalIdeEnhance[IPC_THRMAL_IMAGER_IDE_ENHANCE_MAX][9] =
{
	{0xAA, 0x05, 0x01, 0x1A, 0x02, 0x00, 0xCC, 0xEB, 0xAA},//开
	{0xAA, 0x05, 0x01, 0x1A, 0x02, 0x01, 0xCD, 0xEB, 0xAA},//关
};

//IDE图像过滤串口指令
static  u8 abyXcoreFT2640ThrmalIdeFilter[IPC_THRMAL_IMAGER_IDE_FILTER_MAX][9] =
{
	{0xAA, 0x05, 0x01, 0x1A, 0x02, 0x01, 0xCD, 0xEB, 0xAA},//开
	{0xAA, 0x05, 0x01, 0x1A, 0x02, 0x00, 0xCC, 0xEB, 0xAA},//关
};



//热成像伪彩串口指令---XCORE系列
static  u8 adwXCORETherColorize[IPC_XCORE_THRMAL_COLORIZE_MAX][9] =
{ 
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x00, 0xF4, 0xEB, 0xAA},//白热
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x01, 0xF5, 0xEB, 0xAA},//黑热 
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x02, 0xF6, 0xEB, 0xAA},//彩虹
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x05, 0xF9, 0xEB, 0xAA},//熔岩
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x03, 0xF7, 0xEB, 0xAA},//高对比度彩虹
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x04, 0xF8, 0xEB, 0xAA},//铁红

	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x06, 0xFA, 0xEB, 0xAA},//天空
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x07, 0xFB, 0xEB, 0xAA},//中灰
	
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x09, 0xFD, 0xEB, 0xAA},//紫橙
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0A, 0xFE, 0xEB, 0xAA},//特殊
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0E, 0x02, 0xEB, 0xAA},//特殊2
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0B, 0xFF, 0xEB, 0xAA},//警示红
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0C, 0x00, 0xEB, 0xAA},//冰火
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x08, 0xFC, 0xEB, 0xAA},//灰红
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0D, 0x01, 0xEB, 0xAA},//青红
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x0F, 0x03, 0xEB, 0xAA},//渐变红
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x10, 0x04, 0xEB, 0xAA},//渐变绿
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x11, 0x05, 0xEB, 0xAA},//渐变蓝
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x12, 0x06, 0xEB, 0xAA},//警示绿
	{0xAA, 0x05, 0x01, 0x42, 0x02, 0x13, 0x07, 0xEB, 0xAA},//警示蓝
};




u8 NvrDevXcoreCalcCheckSum(u8 * pbybuf, u8 byBufLen)
{
    u8 i = 0;
    u8 sum = 0;

    for (i = 0; i < byBufLen - 3; i++)
    {
        sum +=pbybuf[i];
    }
    sum = sum%256;
    return sum;
}


NVRSTATUS NvrDevSetThermalImagerEnhance(TNvrIspTherEnhanceCfg tThermalEnhance)
{
	//图像增强
	static ENvrIspThrmalImagerIdeEnhanceMode eEnhanceMode = NVR_ISP_THRMAL_IMAGER_IDE_ENHANCE_MAX;
	static ENvrIspThrmalImagerIdeFilterMode eIdeFilter = NVR_ISP_THRMAL_IMAGER_IDE_FILTER_MAX;
	static u8 byLevel = 0;
	u32 dwReallen = 0;
	u8 byCheckSum = 0;
	if(eEnhanceMode != tThermalEnhance.eIdeEnhance)
	{
		NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyXcoreFT2640ThrmalIdeEnhance[tThermalEnhance.eIdeEnhance], 9,&dwReallen);
		eEnhanceMode = tThermalEnhance.eIdeEnhance;
	}
	
	//图像增强等级
	if(byLevel != tThermalEnhance.byIdeEnhanceGrade)
	{
		abyIdeEnhanceGradeValue[5] = tThermalEnhance.byIdeEnhanceGrade;
		byCheckSum = NvrDevXcoreCalcCheckSum(abyIdeEnhanceGradeValue, 9);
		//校正码
		abyIdeEnhanceGradeValue[6] = byCheckSum;
		NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyIdeEnhanceGradeValue, 9,&dwReallen);
		byLevel = tThermalEnhance.byIdeEnhanceGrade;

	}
	
	//图像增强滤波开关
	if(eIdeFilter != tThermalEnhance.eIdeFilter)
	{
		NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyXcoreFT2640ThrmalIdeFilter[tThermalEnhance.eIdeFilter], 9,&dwReallen);
		eIdeFilter = tThermalEnhance.eIdeFilter;
	}
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevSetLaserParam(u16 wChnId,TNvrLaserCfg tLaser)
{
	u32 dwReallen = 0;
    u8 byCmdLen = 0;
    u8 abyBuf[10];
    mzero(abyBuf);

    if(tLaser.bSetSwitch)//激光开关状态
    {
        printf("set laser switch:%d\n",tLaser.eSwitch);
		NvrFixDevWriteSerial(SERIAL_TYPE_PTZCTRL, abyLaesr[tLaser.eSwitch-1], 7,&dwReallen);	
    }

    if(tLaser.bSetIntensity)//激光强度
    {
        printf("set laser intensity:"FORMAT_U32"\n",tLaser.dwIntensity);
        byCmdLen = 7;
        abyBuf[0] = 0xff;
        abyBuf[1] = 0x01;
		abyBuf[2] = 0x01;
		abyBuf[3] = 0x03;
	    abyBuf[4] = ((tLaser.dwIntensity) * 254 / 100 + 1)&0xFF;
        abyBuf[5] = 0x00;
        abyBuf[6] = NvrFixDevCalcCheckSum(abyBuf, byCmdLen);
        NvrFixDevWriteSerial(SERIAL_TYPE_PTZCTRL, abyBuf, 7,&dwReallen);  
    }

    if(tLaser.bSetMode)//光斑模式
    {
        printf("set laser mode:%d\n",tLaser.eMode);
        byCmdLen = 7;
        abyBuf[0] = 0xff;
        abyBuf[1] = 0x01;
		abyBuf[2] = 0xb2;
   		abyBuf[3] = 0x00;
	    abyBuf[4] = 0x00;
        abyBuf[5] = 0x00;
        abyBuf[6] = 0xb3;

        if(tLaser.eMode == NVR_DEV_LASER_CUSTOM_1)//自动
        {
            NvrFixDevWriteSerial(SERIAL_TYPE_PTZCTRL, abyBuf, 7,&dwReallen);  
        }

    }
    /*
    if(tLaser.bSetCentradMode)
    {
         printf("set laser bSetCentradMode:%d\n",tLaser.eCentradMode);
    }

    if(tLaser.bSetCentradModeSpeed)
    {
         printf("set laser bSetCentradModeSpeed:%d\n",tLaser.dwCentradModeSpeed);
    }*/
	return NVR_ERR__OK;
}


NVRSTATUS NvrDevSetThermalImagerAdjust(TNvrIspTherAdjustCfg tAdjust)
{
	//图像调节
	static ENvrIspThrmalImagerColorRizeMode eColorMode = NVR_ISP_THRMAL_IMAGER_COLORIZE_MAX;
    u8 abyXcoreFT2640Brightness[10] = {0xAA, 0x06, 0x01, 0x23, 0x01, 0xC8, 0x00, 0x9D, 0xEB, 0xAA};//亮度
    u8 abyXcoreFT2640Contrast[9] = {0xAA, 0x05, 0x01, 0x22, 0x01, 0x64, 0x37, 0xEB, 0xAA};//对比度

	static u8 byBright = 0;
	static u8 byContrast = 0;
	u32 dwReallen = 0;
	u8 byCheckSum = 0;
	if(byBright != tAdjust.byBrightness)
	{
	   //映射到0~511范围
	   abyXcoreFT2640Brightness[5] = (tAdjust.byBrightness*511/100) & 0xFF;
	   abyXcoreFT2640Brightness[6] = (tAdjust.byBrightness*511/100) >> 8;
	   byCheckSum = NvrDevXcoreCalcCheckSum(abyXcoreFT2640Brightness, 10);
	   //校正码
	   abyXcoreFT2640Brightness[7] = byCheckSum;
	   NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyXcoreFT2640Brightness, 10,&dwReallen);
	   byBright = tAdjust.byBrightness;
	}
	
	//伪彩
	if(eColorMode != tAdjust.eColorizeParam)
	{
		NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, adwXCORETherColorize[tAdjust.eColorizeParam], 9,&dwReallen);
		eColorMode = tAdjust.eColorizeParam;
	}
	
	if(byContrast != tAdjust.byContrast)
	{
	   //映射到0~255范围
	   abyXcoreFT2640Contrast[5] = (byContrast*255/100) & 0xFF;
	   byCheckSum = NvrDevXcoreCalcCheckSum(abyXcoreFT2640Contrast, 9);
	   //校正码
	   abyXcoreFT2640Contrast[6] = byCheckSum;
	   NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyXcoreFT2640Contrast, 9,&dwReallen);
	   byContrast = tAdjust.byContrast;
	}
	return NVR_ERR__OK;
}

#define NVR_MAX_HOT_POINT_CHECK_NUM 4
#define IPC_DEV_XCORE_THERMAL_WIDTH 640
#define IPC_DEV_XCORE_THERMAL_HEIGHT 512


void IpcDevSetXcoreParam(TNvrIntelTherMeasTempInfo tTherMeasTempInfo)
{
	u32 dwReallen = 0;
    static TNvrIntelTherMeasTempInfo tTherMeasTempParam;   // 热点高温检测参数
    u8 abyFormatC[9] = {0xAA, 0x05, 0x07, 0x02, 0x01, 0x00, 0xB9, 0xEB, 0xAA};  //摄氏度
    u8 abyFormatF[9] = {0xAA, 0x05, 0x07, 0x02, 0x01, 0x01, 0xBA, 0xEB, 0xAA};  //华氏度
    u8 abyFormatK[9] = {0xAA, 0x05, 0x07, 0x02, 0x01, 0x02, 0xBB, 0xEB, 0xAA};  //开氏度
    u32 dwEmissivity = 0;
    u8 abyEmissivity[12] = {0xAA, 0x08, 0x07, 0x12, 0x01, 0x10, 0x27, 0x00, 0x00, 0x03, 0xEB, 0xAA};    //发射率
    u8 byCheckSum = 0;//校正码

    u8 abySaveCmd[8] = {0xAA, 0x04, 0x01, 0x7F, 0x02, 0x30, 0xEB, 0xAA}; //保存配置
    u8 abyAreaPos[17] =  {0xAA, 0x0D, 0x07, 0x42, 0x01, 0x00, 0x64, 0x00, 0x64, 0x00, 0xC8, 0x00, 0xC8, 0x00, 0x59, 0xEB, 0xAA};
	u8 i=0;u32 x=0,y=0,w=0,h=0;
	BOOL bNeedSave = FALSE;

    OsApi_TaskSuspend(g_hXcoreQueryTask);

    usleep(100000);

    //温度格式
    if(tTherMeasTempParam.eTherTempFormat!=  tTherMeasTempInfo.eTherTempFormat)
    {
        if(NVR_CAP_THER_TEMP_CELSIUS == tTherMeasTempInfo.eTherTempFormat)
        {
            NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyFormatC, 9, &dwReallen);
        }
        else if(NVR_CAP_THER_TEMP_FAHRENHEIT == tTherMeasTempInfo.eTherTempFormat)
        {
            NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyFormatF, 9, &dwReallen);
        }
        else
        {
            NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyFormatK, 9, &dwReallen);
        }

		tTherMeasTempParam.eTherTempFormat = tTherMeasTempInfo.eTherTempFormat;
		bNeedSave = TRUE;
        usleep(100000);
    }

    //发射率
    if(tTherMeasTempParam.fEmissivity !=  tTherMeasTempInfo.fEmissivity)
    {
        dwEmissivity = tTherMeasTempInfo.fEmissivity * 10000 /1;
        abyEmissivity[5] = dwEmissivity & 0xFF;
        abyEmissivity[6] = dwEmissivity >> 8;

        abyEmissivity[7] = dwEmissivity >> 16;
        abyEmissivity[8] = dwEmissivity >> 24;

        byCheckSum = NvrDevXcoreCalcCheckSum(abyEmissivity, 12);
        abyEmissivity[9] = byCheckSum;
        NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyEmissivity,12, &dwReallen);

		tTherMeasTempParam.fEmissivity =  tTherMeasTempInfo.fEmissivity;
		bNeedSave = TRUE;
        usleep(100000);
    }

	//区域设置
	if(memcmp(tTherMeasTempParam.tArea,tTherMeasTempInfo.tArea,sizeof(tTherMeasTempInfo.tArea)))
	{
	    for(i = 0;i < NVR_MAX_HOT_POINT_CHECK_NUM; i++)
	    {

			x=tTherMeasTempInfo.tArea[i].tRegion[0].wStartX;
			y=tTherMeasTempInfo.tArea[i].tRegion[0].wStartY;
			if(tTherMeasTempInfo.eTherTempType == NVR_CAP_THER_TEMP_TYPE_AREA)
			{
				w=tTherMeasTempInfo.tArea[i].tRegion[1].wStartX - tTherMeasTempInfo.tArea[i].tRegion[0].wStartX;
				h=tTherMeasTempInfo.tArea[i].tRegion[3].wStartY - tTherMeasTempInfo.tArea[i].tRegion[0].wStartY;
			}
			else //点设置
			{
				w=10;
				h=10;
			}
			
	        if(w>0&&h>0)
	        {
	            PRINTDBG("[IPCDEV]%s: wStartX = %d, wStartY = %d,wWidth = %d,wHeight = %d\n", __FUNCTION__,
	                x,y,w,h);
	            //计算位置信息与校验位
	            abyAreaPos[5] = i;
	            abyAreaPos[6] = (x*IPC_DEV_XCORE_THERMAL_WIDTH/10000) & 0xFF;
	            abyAreaPos[7] = (x*IPC_DEV_XCORE_THERMAL_WIDTH/10000) >> 8;
	            abyAreaPos[8] = (y*IPC_DEV_XCORE_THERMAL_HEIGHT/10000) & 0xFF;
	            abyAreaPos[9] = (y*IPC_DEV_XCORE_THERMAL_HEIGHT/10000) >> 8;
	            abyAreaPos[10] = ((x+w)*IPC_DEV_XCORE_THERMAL_WIDTH/10000) & 0xFF;
	            abyAreaPos[11] = ((x+w)*IPC_DEV_XCORE_THERMAL_WIDTH/10000) >> 8;
	            abyAreaPos[12] = ((y+h)*IPC_DEV_XCORE_THERMAL_HEIGHT/10000) & 0xFF;
	            abyAreaPos[13] = ((y+h)*IPC_DEV_XCORE_THERMAL_HEIGHT/10000) >> 8;

	            byCheckSum = NvrDevXcoreCalcCheckSum(abyAreaPos, 17);
	            abyAreaPos[14] = byCheckSum; 
	            PRINTDBG("[IPCDEV]%s: wStartX = %d, wStartY = %d,wWidth = %d,wHeight = %d\n", __FUNCTION__,
	                (x*IPC_DEV_XCORE_THERMAL_WIDTH/10000),
	                (y*IPC_DEV_XCORE_THERMAL_HEIGHT/10000),
	                (w*IPC_DEV_XCORE_THERMAL_WIDTH/10000),
	                (h*IPC_DEV_XCORE_THERMAL_HEIGHT/10000));

	            NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abyAreaPos, 17,&dwReallen);
	            usleep(200000);
	            PRINTDBG("[IPCDEV]%s: Set Area:%d Exc\n", __FUNCTION__,i);
	        }
	    }
		memcpy(tTherMeasTempParam.tArea,tTherMeasTempInfo.tArea,sizeof(tTherMeasTempInfo.tArea));
		bNeedSave = TRUE;
	}

	if(bNeedSave)
	{
    	NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG, abySaveCmd, 8,&dwReallen);  //保存配置
	}

    OsApi_TaskResume(g_hXcoreQueryTask);
    return;
}


BOOL32 NvrDevXcoreCmdLegalVerify(u8 *pbybuf)
{
    int byCount = 0;
    BOOL32 bCmdLegal = FALSE;
    u8 byCheckSum = 0;
    u8 wCmdLen = 0;

    //艾睿xcore类型
    while(pbybuf[byCount] == 0x55)
    {
        wCmdLen = pbybuf[byCount+1];
    
        byCheckSum = NvrDevXcoreCalcCheckSum(&pbybuf[byCount], wCmdLen+4);
        if(byCheckSum == pbybuf[byCount+1+wCmdLen])
        {
            //当前指令校验合法，再验证剩余指令是否合法
            bCmdLegal = TRUE;
            byCount = byCount + wCmdLen + 4;
        }
        else
        {
            bCmdLegal = FALSE;
            break;
        }
    }
    return bCmdLegal;
}




///<热成像数据
typedef struct tagXcoreTaskState
{
	u32 dwMsgNum;//发送消息号
	u32 dwMsgCmd;//发送命令
	u32 dwMsgVer;//版本号
	u32 dwTimeOut;//超时时间
	u32 dwRecvNum;//接收消息号
	u8  abyRecv[1024];//接收buf
}TXcoreTaskState, *PTXcoreTaskState;


typedef struct tagTXCOREACK
{
    TXcoreTaskState tTaskState; //数据区
	struct list_head listnode;
}TXCOREACK;



NVRSTATUS NvrFixXcoreQueryAckNotify(u8*buf,u32 len)
{
	FIX_ASSERT(buf);
	TXCOREACK *ptTaskBody = NULL;  //节点指针
	static u32 dwCnt=0;

    pthread_mutex_lock(&mutex);//访问共享区域必须加锁
    ptTaskBody = malloc(sizeof(TXCOREACK));
	memset(ptTaskBody,0,sizeof(TXCOREACK));
    ptTaskBody->tTaskState.dwRecvNum = dwCnt++;
	memcpy(ptTaskBody->tTaskState.abyRecv,buf,len);
	list_add_tail(&ptTaskBody->listnode,&g_task_list);
	PRINTDBG("insert msg %d %d\n",ptTaskBody->tTaskState.dwRecvNum,ptTaskBody->tTaskState.dwMsgCmd);
    pthread_mutex_unlock(&mutex);

	//pthread_cond_signal(&actviemsg);

    return NVR_ERR__OK;
}

#define IPC_TML_BMP_OFFSET                118                           //BMP4bit位图头部偏移量

#define IPC_TML_BMP_X                     0                             //BMP离左上角x距离
#define IPC_TML_BMP_Y                     0                             //BMP离左上角y距离
#define IPC_TML_BMP_W                     800                           //BMP宽
#define IPC_TML_BMP_H                     600                           //BMP高
#define IPC_TML_BMP_SQUARE_LINE_PIX               2                             //图中框的描线宽度 pix
#define IPC_TML_BMP_SQUARE_LINE                   20                            //图中框的描线长度 pix
#define YELLOW_OSD 0xB
#define RED_OSD 0x9
static u8 *pbyOsdArea = NULL;

static s32 gThermalOsdAreaId = 0;

static s32 gThermalWidth = 800;
static s32 gThermalHeight = 600;
static const TRgbQuad atTmlBmiColors1[16] = {
        {0x00, 0x00, 0x00, 0x00},   /*0透明*/
        {0x00, 0x00, 0x80, 0x00},   /*1栗色*/
        {0x00, 0x80, 0x00, 0x00},   /*2暗绿*/
        {0x00, 0x80, 0x80, 0x00},   /*3暗黄*/
        {0x80, 0x00, 0x00, 0x00},   /*4深蓝*/
        {0x80, 0x00, 0x80, 0x00},   /*5紫色*/
        {0x80, 0x80, 0x00, 0x00},   /*6暗蓝*/
        {0x80, 0x80, 0x80, 0x00},   /*7灰色*/
        {0xC0, 0xC0, 0xC0, 0x00},   /*8淡灰*/
        {0x00, 0x00, 0xFF, 0x00},   /*9大红*/
        {0x00, 0xFF, 0x00, 0x00},   /*10绿黄*/
        {0x00, 0xFF, 0xFF, 0x00},   /*11黄色*/
        {0xFF, 0x00, 0x00, 0x00},   /*12青色*/
        {0xFF, 0x00, 0xFF, 0x00},   /*13紫红*/
        {0xFF, 0xFF, 0x00, 0x00},   /*14浅蓝*/
        {0xFF, 0xFF, 0xFF, 0x00}    /*15白色*/
};


s32 IpcDevDrawFrame(u8 *pBmpFile,u32 dwBytePerLine,s32 startx,s32 starty, u16 height, u16 wOsdColor)
{
	u8 *pBmpData = NULL;
    s32 i = 0, h = 0, w = 0;

    PRINTDBG("startx %u,starty %u,ow %u,oh %u\n",startx,starty,height,wOsdColor);

    s32 cursorHeight = 15*4;
    s32 cursorWidth = 15*4;
    //十字光标--Y
    for (i = 0; i <= cursorHeight/2; i++)
    {
        h = starty + i;
        if(h < gThermalHeight)
        {
            pBmpData = pBmpFile + IPC_TML_BMP_OFFSET + dwBytePerLine * (height - 1 - h);
            *(pBmpData + startx / 2) |= wOsdColor;
            *(pBmpData + startx/ 2) |= wOsdColor<<4;
        }

        h = starty - i;
        if(h > 0)
        {
            pBmpData = pBmpFile + IPC_TML_BMP_OFFSET + dwBytePerLine * (height - 1 - h);
            *(pBmpData + startx / 2) |= wOsdColor;
            *(pBmpData + startx / 2) |= wOsdColor<<4;
        }
    }
    //十字光标--X
    pBmpData = pBmpFile + IPC_TML_BMP_OFFSET + dwBytePerLine * (height - 1 - starty);
    for (i = 0; i <= cursorWidth/2; i++)
    {
        w = i + startx;
        if(w < gThermalWidth)
        {
            *(pBmpData + w / 2) |= wOsdColor;
            *(pBmpData + w / 2) |= wOsdColor << 4;
        }

        w = startx - i;
        if(w > 0)
        {
            *(pBmpData + w / 2) |= wOsdColor;
            *(pBmpData + w / 2) |= wOsdColor << 4;
        }
    }
        //十字光标--X
    pBmpData = pBmpFile + IPC_TML_BMP_OFFSET + dwBytePerLine * (height  - starty);
    for (i = 0; i <= cursorWidth/2; i++)
    {
        w = i + startx;
        if(w < gThermalWidth)
        {
            *(pBmpData + w / 2) |= wOsdColor;
            *(pBmpData + w / 2) |= wOsdColor << 4;
        }

        w = startx - i;
        if(w > 0)
        {
            *(pBmpData + w / 2) |= wOsdColor;
            *(pBmpData + w / 2) |= wOsdColor << 4;
        }
    }
    return 0;
}


s32 IpcDevTherMalCreatOsd(TNvrPoint tHotPoint, TNvrPoint tColdPoint, u32 dwWidth, u32 dwHeight, u32 dwOsdAreaId,u8 byClarity )
{
    u32 dwBytePerLine = 0;
    TMediaCtrlAreaOsdInfo tAreaInfo;
    TNvrBmpFileHeader tBmpFileHeader;
    TNvrBmpInfoHeader tBmpInfoHeader;
    TMediaCtrlRectRegion tRect = {0};
    s32 nRet = 0;
    
    memset(&tBmpInfoHeader, 0, sizeof(tBmpInfoHeader));
    memset(&tBmpFileHeader, 0, sizeof(tBmpFileHeader));
    memset(&tAreaInfo, 0, sizeof(tAreaInfo));

    //填充BMP数据信息结构
    tBmpInfoHeader.dwSize = sizeof(tBmpInfoHeader);
    tBmpInfoHeader.dwWidth = dwWidth;
    tBmpInfoHeader.dwHeight = dwHeight;
    tBmpInfoHeader.wPlanes = 1;
    tBmpInfoHeader.wBitCount = 4;
    tBmpInfoHeader.dwCompression = 0L;
    dwBytePerLine = (tBmpInfoHeader.dwWidth * tBmpInfoHeader.wBitCount + 31) / 32 * 4;
    tBmpInfoHeader.dwSizeImage = dwBytePerLine * tBmpInfoHeader.dwHeight ;
    tBmpInfoHeader.dwXPelsPerMeter = 0;
    tBmpInfoHeader.dwYPelsPerMeter = 0;
    tBmpInfoHeader.dwClrUsed = 0;
    tBmpInfoHeader.dwClrImportant = 0;

    //填充BMP头结构
    tBmpFileHeader.dwSize = IPC_TML_BMP_OFFSET + tBmpInfoHeader.dwSizeImage;
    tBmpFileHeader.wType = 0x4D42;
    tBmpFileHeader.dwOffBits = IPC_TML_BMP_OFFSET;

    if(pbyOsdArea == NULL)
    {
        pbyOsdArea = (u8*)malloc(tBmpFileHeader.dwSize);
        if(!pbyOsdArea)
        {
            PRINTDBG("calloc bmp err\n");
            return 0;
        }
    }

    tAreaInfo.tBmpInfo.pBmpFile = pbyOsdArea;// 内存首地址赋值
    tAreaInfo.tBmpInfo.dwBmpLen = tBmpFileHeader.dwSize;
    tAreaInfo.nAreaId = dwOsdAreaId;
    tAreaInfo.byClarity = byClarity;//透明度

    PRINTDBG("dwOsdAreaId %u\n",dwOsdAreaId);
    
    memcpy(tAreaInfo.tBmpInfo.pBmpFile, &tBmpFileHeader, sizeof(tBmpFileHeader));
    memcpy(tAreaInfo.tBmpInfo.pBmpFile + sizeof(tBmpFileHeader), &tBmpInfoHeader, sizeof(tBmpInfoHeader));
    memcpy(tAreaInfo.tBmpInfo.pBmpFile + 54, atTmlBmiColors1, 64);// 调色板信息放进内存
    memcpy(tAreaInfo.tBmpInfo.pBmpFile + sizeof(tBmpFileHeader) + sizeof(tBmpInfoHeader), atTmlBmiColors1, sizeof(atTmlBmiColors1));
    memset(tAreaInfo.tBmpInfo.pBmpFile + IPC_TML_BMP_OFFSET, 0, tBmpInfoHeader.dwSizeImage);

    //绘制高温光标
    IpcDevDrawFrame(tAreaInfo.tBmpInfo.pBmpFile,dwBytePerLine,tHotPoint.wPosX,tHotPoint.wPosY, dwHeight, RED_OSD);

    //绘制低温光标
    IpcDevDrawFrame(tAreaInfo.tBmpInfo.pBmpFile,dwBytePerLine,tColdPoint.wPosX,tColdPoint.wPosY, dwHeight, YELLOW_OSD);

    mzero(tRect);
    tAreaInfo.tStartPos.wPosX = IPC_TML_BMP_X;
    tAreaInfo.tStartPos.wPosY = IPC_TML_BMP_Y;
    
    tRect.wStartX = tAreaInfo.tStartPos.wPosX;
    tRect.wStartY = tAreaInfo.tStartPos.wPosY;
    tRect.wWidth = dwWidth;
    tRect.wHeight = dwHeight;

/*
 FILE*pFile=NULL;
		pFile = fopen("/usr/osd/cross.bmp", "wb");
		if(pFile)
		{
		    printf("cross w:%d,h:%d\n",dwWidth,dwHeight);
			fwrite(tAreaInfo.tBmpInfo.pBmpFile, 1, tAreaInfo.tBmpInfo.dwBmpLen, pFile);
			fclose(pFile);
			pFile = NULL;
		}*/

    nRet = MediaCtrlUpdateOsdArea(0, 4, tAreaInfo.nAreaId, &tAreaInfo, &tRect);
    if(nRet != 0)
    {
        PRINTDBG("MediaCtrlUpdateOsdArea fail\n"); 
        return 0;
    }

    return 1;
}

u8 abyLocalAreaAvgTempQuery[NVR_MAX_HOT_POINT_CHECK_NUM][9] = {{0xAA,0x05,0x07,0x4C,0x00,0x00,0x02,0xEB,0xAA},
                                                                {0xAA,0x05,0x07,0x4C,0x00,0x01,0x03,0xEB,0xAA},
                                                                {0xAA,0x05,0x07,0x4C,0x00,0x02,0x04,0xEB,0xAA},
                                                                {0xAA,0x05,0x07,0x4C,0x00,0x03,0x05,0xEB,0xAA}};


u8 abyLocalAreaHighTempQuery[NVR_MAX_HOT_POINT_CHECK_NUM][9] = {{0xAA, 0x05, 0x07, 0x45, 0x00, 0x00, 0xFB, 0xEB, 0xAA},
																{0xAA, 0x05, 0x07, 0x45, 0x00, 0x01, 0xFC, 0xEB, 0xAA},
																{0xAA, 0x05, 0x07, 0x45, 0x00, 0x02, 0xFD, 0xEB, 0xAA},
																{0xAA, 0x05, 0x07, 0x45, 0x00, 0x03, 0xFE, 0xEB, 0xAA}}; //局部区域高温查询
u8 abyLocalAreaLowTempQuery[NVR_MAX_HOT_POINT_CHECK_NUM][9] = {{0xAA, 0x05, 0x07, 0x48, 0x00, 0x00, 0xFE, 0xEB, 0xAA},
															   {0xAA, 0x05, 0x07, 0x48, 0x00, 0x01, 0xFF, 0xEB, 0xAA},
															   {0xAA, 0x05, 0x07, 0x48, 0x00, 0x02, 0x00, 0xEB, 0xAA},
															   {0xAA, 0x05, 0x07, 0x48, 0x00, 0x03, 0x01, 0xEB, 0xAA}}; //局部区域低温查询

/*
send "aa 05 07 48 00 00 fe eb aa"
send "aa 05 07 48 00 01 ff eb aa"
send "aa 05 07 48 00 02 00 eb aa"
send "AA 05 07 42 00 00 F8 EB AA"
send "AA 05 07 42 00 01 F9 EB AA"
send "AA 05 07 42 00 02 Fa EB AA"
send "AA 05 07 42 00 03 Fb EB AA"

send "aa 05 07 48 00 01 ff eb aa"
send "aa 05 07 48 00 02 00 eb aa"
send "aa 05 07 48 00 03 01 eb aa"

send "AA 05 07 4C 00 01 03 EB AA"
send "AA 05 07 4C 00 02 04 EB AA"
send "AA 05 07 4C 00 03 05 EB AA"

读取温度单位
send "AA 05 07 02 00 00 B8 EB AA"
55 05 07 02 33 00 96 eb aa

[NVRDEV]: NvrDevWriteSerial SerialId:2 len:17 Data: aa 0d 07 42 01 03 1a 02 3a 01 02 03 0e 02 70 eb aa
send "aa 05 07 4b 00 00 01 eb aa"


*/

void *NvrFixDevCoretaskCheckThread()

{
	 struct list_head  *s, *n;
	TXCOREACK *msg= NULL;
    u32 dwRealLen = 0;
	u8 *pbuff;
	u8 i = 0,j = 0;

    s32 nHighTemp = 0;
    s32 nLowTemp = 0x7FFFFFFF;//初始化成最高温度值
    s32 nReadHighTemp = 0;
    s32 nReadLowTemp = 0;
    s32 nAvgTemp = 0;
    TNvrPoint tHotPoint = {0};
    TNvrPoint tColdPoint = {0};
    u32 dwRegionNum = 0;// 区域数量
    TNvrFixCfg tFixCfg;
    s32 nTempTol = 0;
    s32 nTempAlarm = 0;

    s32 nThermalSrcWidth = 640;
    s32 nThermalSrcHeight = 512;
	TNvrOsdEncBufInfo tBufInfo;

	TNvrOsdMobileUpdateContent tOsdInfo;
	mzero(tOsdInfo);
	u8 achTemp[1024];
	s8 achtemp2[1024];
	mzero(achtemp2);
	u32 dwStrLen=1024;
	u8 *strType = NULL;
	u8 *strFormat = NULL;

    s32 nAlarmHState[NVR_MAX_HOT_POINT_CHECK_NUM] = {0} ;
    s32 nAlarmLState[NVR_MAX_HOT_POINT_CHECK_NUM] = {0} ;
    s32 nAlarmAState[NVR_MAX_HOT_POINT_CHECK_NUM] = {0} ;
    u8  byId = 0;
	TNvrAlarmSrc tAlmNftType;
	BOOL32 bStartOrStop = TRUE;
	void* pvContext = NULL;
    BOOL bHasClear = TRUE;

    
	while(TRUE)
	{
		
		
		NvrFixCfgGetParam(&tFixCfg);
		if(TRUE != tFixCfg.tIntelCfg.tTherMeasTemp.bEnable)
		{

            if(!bHasClear && g_bTempCheck)
            {
    		    nReadHighTemp = 0;
    		    nReadLowTemp = 0;
    		    nAvgTemp = 0;
    		    mzero(tHotPoint);
    		    mzero(tColdPoint);

                snprintf(achtemp2, sizeof(achtemp2), "NO CHECKING ");
                CharConvConvertGbktoUnicode( achtemp2,achTemp, &dwStrLen);
                memcpy(tOsdInfo.abyContent,achTemp,dwStrLen+1);

                //清除高低温OSD贴图
                for(i=0;i<NVR_MAX_LCAM_CHN_NUM;i++)
                {
                    tOsdInfo.bFlashSwitch = 0;
                    tOsdInfo.byBufLen = dwStrLen;
                    tOsdInfo.byChnId = i;
                    tOsdInfo.wOsdId  = 0;
                    tOsdInfo.eOsdType = NVR_OSD_ALLTYPE_USER;
                    NvrOsdMobileUpdateOsd(&tOsdInfo);
                }
            
                //不显示十字
                tHotPoint.wPosX = tHotPoint.wPosX*gThermalWidth/nThermalSrcWidth;
                tHotPoint.wPosY = tHotPoint.wPosY*gThermalHeight/nThermalSrcHeight;
                tColdPoint.wPosX = tColdPoint.wPosX*gThermalWidth/nThermalSrcWidth;
                tColdPoint.wPosY = tColdPoint.wPosY*gThermalHeight/nThermalSrcHeight;
            
                IpcDevTherMalCreatOsd(tHotPoint, tColdPoint, gThermalWidth, gThermalHeight, gThermalOsdAreaId,0);
                bHasClear = TRUE;
            }
			g_bTempCheck = FALSE;
		}
        else
        {
    		//不是所设预置位不用检测，如果是0号预置位检测
    		if(	g_byPtzPresetId != tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].wPresetId && 	0!= tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].wPresetId)
    		{
    		    if(!bHasClear&& g_bTempCheck)
                {
        		    nReadHighTemp = 0;
        		    nReadLowTemp = 0;
        		    nAvgTemp = 0;
        		    mzero(tHotPoint);
        		    mzero(tColdPoint);


                    snprintf(achtemp2, sizeof(achtemp2), "PRESET UNMATCH ");
                    CharConvConvertGbktoUnicode( achtemp2,achTemp, &dwStrLen);
                    memcpy(tOsdInfo.abyContent,achTemp,dwStrLen+1);
                
                    //清除高低温OSD贴图
                    for(i=0;i<NVR_MAX_LCAM_CHN_NUM;i++)
                    {
                        tOsdInfo.bFlashSwitch = 0;
                        tOsdInfo.byBufLen = dwStrLen;
                        tOsdInfo.byChnId = i;
                        tOsdInfo.wOsdId  = 0;
                        tOsdInfo.eOsdType = NVR_OSD_ALLTYPE_USER;
                        NvrOsdMobileUpdateOsd(&tOsdInfo);
                    }
                
                    //不显示十字
                    tHotPoint.wPosX = tHotPoint.wPosX*gThermalWidth/nThermalSrcWidth;
                    tHotPoint.wPosY = tHotPoint.wPosY*gThermalHeight/nThermalSrcHeight;
                    tColdPoint.wPosX = tColdPoint.wPosX*gThermalWidth/nThermalSrcWidth;
                    tColdPoint.wPosY = tColdPoint.wPosY*gThermalHeight/nThermalSrcHeight;
                
                    IpcDevTherMalCreatOsd(tHotPoint, tColdPoint, gThermalWidth, gThermalHeight, gThermalOsdAreaId,0);
                    bHasClear = TRUE;
                
                }
                
    			g_bTempCheck = FALSE;
    		}
            else
            {
                g_bTempCheck = TRUE;
            }
        }
        



		dwRegionNum =0;

		for(i = 0; i<NVR_MAX_HOT_POINT_CHECK_NUM; i++)
		{

			if(!g_bTempCheck)
			{
				break;
			}

         
			if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].tRegion[0].wStartX ==0
			&&tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].tRegion[0].wStartY ==0)
			{
				PRINTDBG("is zero\n");
                nAlarmAState[i] = 0;
                nAlarmHState[i] = 0;
                nAlarmLState[i] = 0;
				continue;
			}
			if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].bEnable )
			{

                //触发一次获取高温
				PRINTDBG("get hight temp\n");
				NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG,abyLocalAreaHighTempQuery[i], 9,&dwRealLen);
				usleep(100000);
                //触发一次获取低温
				NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG,abyLocalAreaLowTempQuery[i], 9,&dwRealLen);
				PRINTDBG("get low temp\n");
                usleep(100000);
                //触发一次平均温度获取
				NvrFixDevWriteSerial(SERIAL_TYPE_RECHENGXIANG,abyLocalAreaAvgTempQuery[i], 9,&dwRealLen);
				PRINTDBG("get avg temp\n");
                usleep(100000);
				dwRegionNum++;

				do
				{
					pthread_mutex_lock(&mutex);
					if(!list_empty(&g_task_list))
					{
						list_for_each_safe(s, n, &g_task_list)
						{
							msg = container_of(s,TXCOREACK,listnode);
							pthread_mutex_unlock(&mutex);
							PRINTDBG("recv type:%d,%d\n",msg->tTaskState.dwRecvNum,msg->tTaskState.dwMsgCmd);
							pbuff = msg->tTaskState.abyRecv;
							if(NvrDevXcoreCmdLegalVerify(pbuff)) 
							{
								if(pbuff[3]==0x45)
								{
					                nReadHighTemp = pbuff[6] |pbuff[7] << 8|pbuff[8] << 16 | pbuff[9] << 24;//高温数据
					                byId = pbuff[5];
                                    nAlarmHState[byId] = nReadHighTemp;
					                PRINTDBG("id:%d readhigh:%d,hight:%d,x:%d,y:%d\n",byId,nReadHighTemp,nHighTemp,pbuff[10] | pbuff[11] << 8,pbuff[12] | pbuff[13]);
					                if(nReadHighTemp > nHighTemp)
					                {
					                    nHighTemp = nReadHighTemp;
					                    tHotPoint.wPosX = pbuff[10] | pbuff[11] << 8;
					                    tHotPoint.wPosY = pbuff[12] | pbuff[13] << 8;
					                }
								}
								if(pbuff[3]==0x48)
								{
									nReadLowTemp = pbuff[6] |pbuff[7] << 8|pbuff[8] << 16 | pbuff[9] << 24;//低温数据
									byId = pbuff[5];
                                    nAlarmLState[byId] = nReadLowTemp;
					                PRINTDBG("id:%d readlow:%d,low:%d\n",byId,nReadLowTemp,nLowTemp,pbuff[10] | pbuff[11] << 8,pbuff[12] | pbuff[13]);
									if(nReadLowTemp < nLowTemp)
									{
										nLowTemp = nReadLowTemp;
										tColdPoint.wPosX = pbuff[10] | pbuff[11] << 8;
										tColdPoint.wPosY = pbuff[12] | pbuff[13] << 8;
									}
								}
                                if(pbuff[3]==0x4C)
								{
									nAvgTemp = pbuff[6] |pbuff[7] << 8|pbuff[8] << 16 | pbuff[9] << 24;//平均温度
									byId = pbuff[5];
                                    nAlarmAState[byId] = nAvgTemp;
					                PRINTDBG("readavg:%d\n",nAlarmAState[byId]);
								}
							}
							else
							{
								PRINTDBG("err msg when low \n");
							}
							list_del(s);	
							free(msg);

						}
						pthread_mutex_unlock(&mutex);
						break;
						
					}
					else
					{							
						pthread_mutex_unlock(&mutex);
                        /*
						dwCnt++;
						if(dwCnt>30){dwCnt = 0;break;}
						usleep(10000);*/
						printf("good, no msg is good msg\n");
                        break;
					}
				}while(1);

                 mzero(tAlmNftType);
                 tAlmNftType.byAlarmType = NVR_ALARM_TYPE_SMART;
                 tAlmNftType.byAlarmNum = NVR_ALARM_SD_THERMEASTEMP;

                 nTempTol = tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].byAlarmTol*10;
                 nTempAlarm = tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].nAlarmTemp*10;
                 
                 //高温大于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_HIGH_TEMP_GREATER)
                 {

                    if(nTempAlarm < nAlarmHState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                         PRINTDBG("hight great %d %d\n",nTempAlarm,nAlarmHState[i]);
                    }else
                    if(nTempAlarm - nTempTol >  nAlarmHState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                         PRINTDBG("hight great %d %d no alarm\n",nTempAlarm,nAlarmHState[i]);
                    }
                 }
                
                //高温小于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_HIGH_TEMP_LESS)
                 {
                    if(nTempAlarm > nAlarmHState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("hight less %d %d\n",nTempAlarm,nAlarmHState[i]);
                    }
                    else if(nTempAlarm + nTempTol < nAlarmHState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                          PRINTDBG("hight less %d %d no alarm\n",nTempAlarm,nAlarmHState[i]);
                   
                    }
                 }
                
                
                //低温大于于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_LOW_TEMP_GREATER)
                 {
                    if(nTempAlarm < nAlarmLState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("low great %d %d\n",nTempAlarm,nAlarmLState[i]);
                    }else if(nTempAlarm - nTempTol < nAlarmLState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                          PRINTDBG("low great %d %d no alarm\n",nTempAlarm,nAlarmLState[i]);
                    }
                    
                 }

                //低温小于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_LOW_TEMP_LESS)
                 {
                    if(nTempAlarm > nAlarmLState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("low less %d %d\n",nTempAlarm,nAlarmLState[i]);
                    }else if(nTempAlarm + nTempTol < nAlarmLState[i])
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                         PRINTDBG("low less %d %d noalarm\n",nTempAlarm,nAlarmLState[i]);
                    }
                 }

                //平均温大于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_TEMP_AVG_GREATER)
                 {
                    if(nTempAlarm < (nAlarmLState[i]+nAlarmHState[i])/2)
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                         PRINTDBG("avg great %d %d\n",nTempAlarm,(nAlarmLState[i]+nAlarmHState[i])/2);
                    }
                    else if(nTempAlarm - nTempTol > (nAlarmLState[i]+nAlarmHState[i])/2)
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                         PRINTDBG("avg great %d %d no alarm\n",nTempAlarm,(nAlarmLState[i]+nAlarmHState[i])/2);
                    }
                 }

                 
                //平均温度小于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_TEMP_AVG_LESS)
                 {
                    if(nTempAlarm > (nAlarmLState[i]+nAlarmHState[i])/2)
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("avg less %d %d\n",nTempAlarm,(nAlarmLState[i]+nAlarmHState[i])/2);
                    }else if(nTempAlarm+nTempTol<(nAlarmLState[i]+nAlarmHState[i])/2)
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                          PRINTDBG("avg less %d %d no alarm\n",nTempAlarm,(nAlarmLState[i]+nAlarmHState[i])/2);
                    }
                 }

                
                //温差大于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_TEMP_VARIES_GREATER)
                 {
                    if(nTempAlarm < (nAlarmHState[i]-nAlarmLState[i]))
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("delt great %d %d\n",nTempAlarm,(nAlarmHState[i]-nAlarmLState[i]));
                    }else if(nTempAlarm - nTempTol > (nAlarmHState[i]-nAlarmLState[i]))
                    {
                         LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                         PRINTDBG("delt great %d %d no alarm\n",nTempAlarm,(nAlarmHState[i]-nAlarmLState[i]));
                    }
                    
                 }

                //温差小于报警温度
                if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].tArea[i].eTherTempAlarmRule == NVR_CAP_THER_ALARM_RULE_TEMP_VARIES_LESS)
                 {
                    if(nTempAlarm > (nAlarmHState[i]-nAlarmLState[i]))
                    {
                         LcamCltAlmNotify(tAlmNftType,2,bStartOrStop,pvContext);
                          PRINTDBG("delt less %d\n",nTempAlarm,(nAlarmHState[i]-nAlarmLState[i]));
                    }
                    else if(nTempAlarm+nTempTol < (nAlarmHState[i]-nAlarmLState[i]))
                    {
                          LcamCltAlmNotify(tAlmNftType,2,FALSE,pvContext);
                          PRINTDBG("delt less %d %d no alarm\n",nTempAlarm,(nAlarmHState[i]-nAlarmLState[i]));
                    }
                 }

			}
            else
            {
                nAlarmAState[i] = 0; 
                nAlarmHState[i] = 0;
                nAlarmLState[i] = 0;
            }
		}

        nAvgTemp = 0;
        if(dwRegionNum)//如果有有效区域则计算平均温度，否则为0
        {
            for(j = 0; j <NVR_MAX_HOT_POINT_CHECK_NUM;j++)
            {
    	        nAvgTemp += nAlarmAState[j];
                //printf("avg:%d %d %d\n",nAvgTemp,j,nAlarmAState[j]);
            }
            nAvgTemp = nAvgTemp/dwRegionNum;
        }

        if(g_bTempCheck)
        {
		    PRINTTMP("H:%d,L:%d,A:%d,num:%d\n",nHighTemp,nLowTemp,nAvgTemp,dwRegionNum);
		    PRINTTMP("H:%d,L:%d,A:%d,num:%d hx:%d,hy:%d,lx:%d,ly:%d\n",nReadHighTemp,nReadLowTemp,nAvgTemp,dwRegionNum,tHotPoint.wPosX,tHotPoint.wPosY,tColdPoint.wPosX,tColdPoint.wPosY);
        }
        
        if(g_bTempCheck)
        {
    		if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempType == NVR_CAP_THER_TEMP_TYPE_AREA)
    		{
    			strType = (u8*)"AC";
    		}
    		else
    		{
    			strType = (u8*)"PC";
    		}

    		if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempFormat == NVR_CAP_THER_TEMP_CELSIUS)
    		{
    			strFormat = (u8*)"℃";
    		}
    		else if(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempFormat == NVR_CAP_THER_TEMP_FAHRENHEIT)
    		{
    			strFormat = (u8*)"F";
    		}
    		else
    		{
    			strFormat = (u8*)"K";
    		}
    		dwStrLen=1024;
            if(dwRegionNum == 0)
            {
                snprintf(achtemp2, sizeof(achtemp2), "NO VALID AREA ");
                 bHasClear = TRUE;
            }
            else
            {
			    snprintf(achtemp2, sizeof(achtemp2), "%s H:%.1f%s L:%.1f%s A:%.1f%s",strType,nHighTemp/10.0,  strFormat,nLowTemp/10.0,  strFormat, nAvgTemp/10.0,  strFormat);
                 bHasClear = FALSE;
            }
            CharConvConvertGbktoUnicode( achtemp2,achTemp, &dwStrLen);
    		memcpy(tOsdInfo.abyContent,achTemp,dwStrLen+1);
    		//高低温OSD贴图
    		for(j=0;j<NVR_MAX_LCAM_CHN_NUM;j++)
    		{
    			tOsdInfo.bFlashSwitch = 0;
    			tOsdInfo.byBufLen = dwStrLen;
    			tOsdInfo.byChnId = j;
    			tOsdInfo.wOsdId  = 0;
    			tOsdInfo.eOsdType = NVR_OSD_ALLTYPE_USER;
    			NvrOsdMobileUpdateOsd(&tOsdInfo);
    		}

            TLcamMcVidEncParam tMcParam;
            LcamMcGetVidEncParam(2,0,&tMcParam);
            gThermalWidth = tMcParam.wResWidth;
            gThermalHeight= tMcParam.wResHeight;

            //画十字
            tHotPoint.wPosX = tHotPoint.wPosX*gThermalWidth/nThermalSrcWidth;
            tHotPoint.wPosY = tHotPoint.wPosY*gThermalHeight/nThermalSrcHeight;
            tColdPoint.wPosX = tColdPoint.wPosX*gThermalWidth/nThermalSrcWidth;
            tColdPoint.wPosY = tColdPoint.wPosY*gThermalHeight/nThermalSrcHeight;

            NvrOsdGetEncBufInfo(2,0,&tBufInfo);
            for(i=0;i<NVR_OSD_MAX_SAVE_AREA_NUM;i++)
            {
                if(tBufInfo.atOsdAreaInfo[i].eType == NVR_OSD_ALLTYPE_EXT)
                {
                    gThermalOsdAreaId = tBufInfo.atOsdAreaInfo[i].wAreaId;
                    break;
                }
            }

            //gThermalWidth = (gThermalWidth/32)*32;
            if(dwRegionNum == 0)
            {
    	        IpcDevTherMalCreatOsd(tHotPoint, tColdPoint, gThermalWidth, gThermalHeight, gThermalOsdAreaId,0);
                bHasClear = TRUE;
            }
            else
            {
                IpcDevTherMalCreatOsd(tHotPoint, tColdPoint, gThermalWidth, gThermalHeight, gThermalOsdAreaId,255);
                bHasClear = FALSE;
            }
            
		}

        //清除所有数据，重新计算
		mzero(tColdPoint);
		mzero(tHotPoint);
        nReadHighTemp = 0;
        nReadLowTemp =0;
		nLowTemp = 0x7FFFFFFF;
		nHighTemp = -300;
		nAvgTemp =0;

        if(dwRegionNum)
        {
            usleep(1500000-dwRegionNum*100000*3);//1.5秒钟减去检查所耗费的时间
        }
        else
        {
            sleep(1);
        }

	}
}

s32 NvrDevHeaterOperation(s32 nCurrentTemp)
{
    static u8 byHeaterState = FALSE; ///< 加热气开启状态，避免重复开启
    static u8 byFirstFlag = FALSE; ///< 针对加热器状态未知情况，第一次默认先关闭加热器

    /*加热计时*/
    static s32 nByUnitCount = 7;
    static s32 nByUnitHeatCount = 0;
    static u8 byHeaterCount = 0;
    static u8 byCountCount = 0;
    static u8 bCountClear = TRUE;

    if(byFirstFlag == FALSE)
    {
        PRINTDBG("---------------firstflag close heater .\n ");
        NvrFixDevSetDefog(0);

        byFirstFlag = TRUE;
    }

    //获取当前温度

    if (bCountClear)
    {
        if(nCurrentTemp >= -10)
        {
            nByUnitHeatCount = 0;
        }
        else if(nCurrentTemp <= -12 && nCurrentTemp > -15)
        {
            nByUnitHeatCount = 1;
        }
        else if(nCurrentTemp <= -15 && nCurrentTemp > -25)
        {
            nByUnitHeatCount = 2;
        }
        else if(nCurrentTemp <= -25 )
        {
            nByUnitHeatCount = 3;
        }


        byCountCount = 0;
        byHeaterCount = 0;
        bCountClear = FALSE;
    }

    if (byHeaterCount < nByUnitHeatCount)
    {
        byHeaterState = TRUE;
        PRINTTMP("  -------------- open heater %d %d total:%d\n", byHeaterCount, nByUnitHeatCount);
        NvrFixDevSetDefog(100);
        byHeaterCount++;
    }
    else
    {
        byHeaterState = FALSE;
        PRINTTMP("  ---------------close heater %d %d\n", byHeaterCount, nByUnitHeatCount);
        NvrFixDevSetDefog(0);
    }

    byCountCount++;
    bCountClear = byCountCount / nByUnitCount;
    PRINTTMP("The Current Temperature is %d .%d %d \n ", nCurrentTemp, byCountCount, nByUnitCount);

    MAKE_COMPILER_HAPPY(byHeaterState);

    return 0;
}

s32 NvrDevHeaterOpStrategy(s32 nStrategy)
{
    static u32 nCount = 0;
    switch (nStrategy)
    {
        case 0:
        {

            break;
        }

        case 1:
        {
            s32 nCurDayNightState = ISP_IRCUT_MODE_DAY;         //判断当前日夜状态

            //获取当前日夜状态
            IspDoCmd(0,ISP_CMD_GET_DAYNIGHT_STATUS, &nCurDayNightState);

            PRINTDBG("dn %d  n:%d f:%d\n ", nCurDayNightState, g_nInfraredVal[0], g_nInfraredVal[1]);
            if (ISP_IRCUT_MODE_DAY == nCurDayNightState)
            {
                NvrFixDevSetDefog(60);
            }
            else
            {
                if (g_nInfraredVal[1] < 50 && g_nInfraredVal[0] > 99)
                {
                    NvrFixDevSetDefog(30);
                }
                else if (g_nInfraredVal[1] >= 50 && g_nInfraredVal[0] > 99)
                {
                    NvrFixDevSetDefog(0);
                }
            }
            break;
        }

        case 2:///< 红外亮度值控制加热功率
        {
            /*
             * IPC2X52 变焦镜头sigmastar 平台 加热防雾策略如下：
                不区分自动、手动模式；不区分金属外壳、塑料外壳产品
                以3个红外灯界面设置的亮度为准，总亮度L=2*远光灯亮度+1*近光灯亮度（远光灯2颗，近光灯1颗）;
                加热周期为10s;
                1、总亮度L＞200             加热膜开25%
                2、150<总亮度L≤200          加热膜开40%
                3、100<总亮度L≤150          加热膜开50%
                4、50<总亮度L≤100           加热膜开60%
                5、0 <总亮度L≤50            加热膜开67%
                6、L=0                      加热膜开75%
             */

            s32 i = 0;
            s32 nLevelnum = 6;
            s32 nIrVal[10] = {200, 150, 100, 50, 0, -1};
            s32 nIrValheat[10] = {25, 40, 50, 60, 67, 75};
            s32 nTotalIrVal = g_nInfraredVal[1] * 2 + g_nInfraredVal[0];

            if(nCount%10==0)
            {
                for (i = 0;  i < nLevelnum; i++)
                {
                    if (nTotalIrVal > nIrVal[i])
                    {
                        PRINTDBG("true ---> total:%d index:%d irval:%d heatval:%d\n", nTotalIrVal, i, nIrVal[i], nIrValheat[i]);
                        NvrFixDevSetDefog(nIrValheat[i]);
                        PRINTDBG("set fog  %d and break\n", nIrValheat[i]);
                        break;
                    }
                }
            }
            nCount++;

            break;
        }
        case 3:///<加热逻辑完全由硬件控制，只需获取是否达到加热条件，然后开启加热即可
        {
            s32 nValue = 1;
            
            //value=0时开启加热,value=1时关闭加热
            //BrdGpioExtCardGetInput(0,8,&nValue);

            s32 nRet = 0;
            THwmonStat tHwmonStat;
            memset(&tHwmonStat, 0, sizeof(tHwmonStat));
            
            ///获取加热状态 heater以前定位实现的时候 只有set  没有get,使用level替代，1关闭加热
            tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_LEVEL, 0);
            nRet = BrdHwmonGetStatus(&tHwmonStat);
            if (NVRBRD_RES_OK != nRet)
            {
                NVRBRDAPIERR("BrdHwmonGetStatus heatId:%d failed,errno=%d\n", 0, nRet);
                return NVR_ERR__ERROR;
            }
            nValue = tHwmonStat.tEntry.tLevel.level;


            PRINTDBG("enter heat mode 3,get value:%d s:%d e:%d c:%d\n",nValue,g_byHeatS,g_byHeatE,nCount);


            memset(&tHwmonStat, 0, sizeof(tHwmonStat));
            tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_HEATER, 0);
            if(nValue == 1)//关闭加热
            {
                //BrdGpioExtCardSetoutput(0,0,nValue);
                tHwmonStat.tEntry.tHeater.on_off=0;
                nRet = BrdHwmonSetStatus(&tHwmonStat);
                if (NVRBRD_RES_OK != nRet)
                {
                    NVRBRDAPIERR("BrdHwmonSetStatus heatId:%d failed,errno=%d\n", 0, nRet);
                    return NVR_ERR__ERROR;
                }
                nCount = 0;
            }
            else
            {
                if(nCount%( g_byHeatS+g_byHeatE) == 0)
                {
                    PRINTDBG("heatstart:%d s:%d e:%d c:%d\n",nValue,g_byHeatS,g_byHeatE,nCount);
                    //BrdGpioExtCardSetoutput(0,0,0);
                    tHwmonStat.tEntry.tHeater.on_off=1;
                    nRet = BrdHwmonSetStatus(&tHwmonStat);
                    if (NVRBRD_RES_OK != nRet)
                    {
                        NVRBRDAPIERR("BrdHwmonSetStatus heatId:%d failed,errno=%d\n", 0, nRet);
                        return NVR_ERR__ERROR;
                    }
                }

                if((nCount >= g_byHeatS) && (nCount-g_byHeatS)%( g_byHeatS+g_byHeatE) == 0)
                {
                    PRINTDBG("heatend:%d s:%d e:%d c:%d\n",nValue,g_byHeatS,g_byHeatE,nCount);
                    //BrdGpioExtCardSetoutput(0,0,1);
                    tHwmonStat.tEntry.tHeater.on_off=0;
                    nRet = BrdHwmonSetStatus(&tHwmonStat);
                    if (NVRBRD_RES_OK != nRet)
                    {
                        NVRBRDAPIERR("BrdHwmonSetStatus heatId:%d failed,errno=%d\n", 0, nRet);
                        return NVR_ERR__ERROR;
                    }
                }
                nCount++;
            }
            break;
        }

        default:
        {
            break;
        }
    }

    return 0;
}

s32 NvrDevHeaterTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
    PRINTDBG("byHeatingStrategy %d\n", g_tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy);
    NvrDevHeaterOpStrategy(g_tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy);

    OsApi_TimerSet(g_hHeaterTimer, 1 * 1000, NvrDevHeaterTimerCB, NULL);

    return 0;
}

s32 NvrDevGetTempatureTimerCB(HTIMERHANDLE dwTimerId, void* param)
{
    s32 nCurrentTemp = 0;
    static s32 s_nHeaterCount = 0;

    //获取当前温度
    NvrFixDevGetTemperature(&nCurrentTemp);

    if (g_tFixInterCapInfo.tFixHwInternalCap.byHeaterSupport)
    {
        PRINTTMP("byHeatingStrategy %d count %d\n", g_tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy, s_nHeaterCount);
        ///< 优先判断是否有加热策略
        if (g_tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy)
        {
            s_nHeaterCount++;
            if (s_nHeaterCount > 10)
            {
                NvrDevHeaterOpStrategy(g_tFixInterCapInfo.tFixSoftInternalCap.byHeatingStrategy);
                s_nHeaterCount = 1;
            }
        }
        else
        {
            NvrDevHeaterOperation(nCurrentTemp);
        }
    }

    NvrFixDevLowTempLimitAf();

    OsApi_TimerSet(g_hGetTempatureTimer, 1000, NvrDevGetTempatureTimerCB, NULL);

	return 0;
}

NVRSTATUS NvrFixDevGetTemperature(s32 *Temperature)
{    
#ifndef WIN32
    NVRSTATUS eRet = NVR_ERR__OK;

    if(g_bLensUpdating)
    {
        PRINTDBG("[NVRFIXDEV]NvrFixDevGetTemperature Lens updating, skip\n");
        return NVR_ERR__OK; 
    }
    s32 nRet = 0;

    if(NVR_CAP_TEMPERATURE_QUERYTYPE_DRVHWMONVOLT == g_tFixInterCapInfo.tFixHwInternalCap.byTemperatureTransducerQueryType)
    {
        THwmonStat tHwmonStat;
        memset(&tHwmonStat, 0, sizeof(tHwmonStat));
    
        //获取温度传感器的温度
        tHwmonStat.dwId = HWMON_ID_TEMP(0);
        nRet = BrdHwmonGetStatus(&tHwmonStat);
        if (0 != nRet)
        {
            PRINTERR("BrdHwmonGetStatus errno=%d\n", nRet);
            return NVR_ERR__ERROR;
        }
        PRINTTMP("NVR_CAP_TEMPERATURE_QUERYTYPE_DRVHWMONVOLT get dwCur=%d\n", tHwmonStat.tEntry.tTemp.dwCur);


        ///< 针对根据电压值转换温度值做处理,根据硬件与驱动提供映射表计算温度值(R-center)
        float sRcenter = ((float)(4.7*tHwmonStat.tEntry.tTemp.dwCur))/((float)(1024- tHwmonStat.tEntry.tTemp.dwCur));

        if(sRcenter > 195.652)
        {
            *Temperature = -40;
        }
        else if(sRcenter <= 195.652 && sRcenter > 113.3471)
        {
            *Temperature = -35;
        }
        else if(sRcenter <= 113.3471 && sRcenter > 79.1663)
        {
            *Temperature = -25;
        }
        else if(sRcenter <= 79.1663 && sRcenter > 71.6768)
        {
            *Temperature = -22;
        }
        else if(sRcenter <= 71.6768 && sRcenter > 61.919)
        {
            *Temperature = -19;
        }
        else if(sRcenter <= 61.919 && sRcenter > 56.2579)
        {
            *Temperature = -17;
        }
        else if(sRcenter <= 56.2579 && sRcenter > 51.1779)
        {
            *Temperature = -15;
        }
        else if(sRcenter <= 51.1779 && sRcenter > 48.8349)
        {
            *Temperature = -14;
        }
        else if(sRcenter <= 48.8349 && sRcenter > 46.6132)
        {
            *Temperature = -13;
        }
        else if(sRcenter <= 46.6132 && sRcenter > 44.5058)
        {
            *Temperature = -12;
        }
        else if(sRcenter <= 44.5058 && sRcenter > 42.5062)
        {
            *Temperature = -11;
        }
        else if(sRcenter <= 42.5062 && sRcenter > 40.5997)
        {
            *Temperature = -10;
        }
        else if(sRcenter <= 40.5997 && sRcenter > 38.7905)
        {
            *Temperature = -9;
        }
        else if(sRcenter <= 38.7905 && sRcenter > 37.0729)
        {
            *Temperature = -8;
        }
        else if(sRcenter <= 37.0729 && sRcenter > 35.4417)
        {
            *Temperature = -7;
        }
        else if(sRcenter <= 35.4417 && sRcenter > 33.8922)
        {
            *Temperature = -6;
        }
        else if(sRcenter <= 33.8922 && sRcenter > 28.4321)
        {
            *Temperature = -5;
        }
        else if(sRcenter <= 28.4321 && sRcenter > 17.9255)
        {
            *Temperature = 5;
        }
        else if(sRcenter <= 17.9255 && sRcenter > 12.0805)
        {
            *Temperature = 15;
        }
        else if(sRcenter <= 12.0805 && sRcenter > 5.8336)
        {
            *Temperature = 30;
        }
        else if(sRcenter <= 5.8336 && sRcenter > 3.0143)
        {
            *Temperature = 50;
        }
        else if(sRcenter <= 3.0143 && sRcenter > 0.5311)
        {
            *Temperature = 80;
        }
        else
        {
            *Temperature = 125;
        }
    }
    else if(NVR_CAP_TEMPERATURE_QUERYTYPE_DRVHWMONTEMP == g_tFixInterCapInfo.tFixHwInternalCap.byTemperatureTransducerQueryType)
    {
        THwmonStat tHwmonStat;
        memset(&tHwmonStat, 0, sizeof(tHwmonStat));

        //获取温度传感器的温度
        tHwmonStat.dwId = HWMON_ID_TEMP(1);
        nRet = BrdHwmonGetStatus(&tHwmonStat);
        if (0 != nRet)
        {
            PRINTERR("BrdHwmonGetStatus errno=%d\n", nRet);
            return NVR_ERR__ERROR;
        }
        PRINTTMP("NVR_CAP_TEMPERATURE_QUERYTYPE_DRVHWMONTEMP get dwCur=%d\n", tHwmonStat.tEntry.tTemp.dwCur);

        *Temperature = tHwmonStat.tEntry.tTemp.dwCur / 1000;
    }
    
    PRINTTMP("true temp  : %d\n", *Temperature);

    //模拟高低温，方便测试(只有驱动返回的是正常温度值才生效)
	if(0 == access("/usr/temp_lenstest/low_tempture", F_OK))
	{
	    *Temperature = -40 ;
	    PRINTTMP("[NVRFIXDEV]NvrFixDevGetTemperature  In debugging mode, forced to low temperature : %d\n", *Temperature);
	}
    if(0 == access("/usr/temp_lenstest/low_tempture25", F_OK))
    {
        *Temperature = -25 ;
        PRINTTMP("[NVRFIXDEV]NvrFixDevGetTemperature  In debugging mode, forced to low temperature : %d\n", *Temperature);
    }
    if(0 == access("/usr/temp_lenstest/low_tempture15", F_OK))
    {
        *Temperature = -15 ;
        PRINTTMP("[NVRFIXDEV]NvrFixDevGetTemperature  In debugging mode, forced to low temperature : %d\n", *Temperature);
    }
    if(0 == access("/usr/temp_lenstest/low_tempture5", F_OK))
    {
        *Temperature = -5 ;
        PRINTTMP("[NVRFIXDEV]NvrFixDevGetTemperature  In debugging mode, forced to low temperature : %d\n", *Temperature);
    }
    if(0 == access("/usr/temp_lenstest/high_tempture", F_OK))
	{
	    *Temperature = 4000 ;
	    PRINTTMP("[NVRFIXDEV]NvrFixDevGetTemperature  In debugging mode, forced to high temperature: %d\n", *Temperature);
	}

	return eRet;
#else
	return eRet;
#endif
}

static NVRSTATUS NvrFixDevSetLensMode(BOOL bFirstIn, BOOL bLockAf)       //只用于低温下，根据温度进行镜头锁定与解锁(解锁指切换到原来的聚焦模式)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    static BOOL s_bNeedInitAF = -1;
    FILE *fp = NULL;
    s32 nRet = 0;
    s32 nStatus = -1, nZoom = 0, nPos = 0;
    BOOL bCreate = TRUE;
    TLcamIspParam tIspParam;
    static ENvrIspFocusMode s_eBootMode = NVR_ISP_FOCUS_MODE_COUNT;
    ENvrIspFocusMode eFileFocusMode = NVR_ISP_FOCUS_MODE_COUNT;

    mzero(tIspParam);

    eRet = LcamIspGetParam(0, &tIspParam);
    if(NVR_ERR__OK != eRet)
    {
        NvrFixLog("get isp param failed ret:%d\n", eRet);
        return eRet;
    }

    if (NVR_ISP_FOCUS_MODE_COUNT == s_eBootMode)
    {
        s_eBootMode = tIspParam.tAction.eIpcIspFocusMode;
    }

    if (bFirstIn && -1 == s_bNeedInitAF)
    {
        s_bNeedInitAF = TRUE;
    }

    NvrFixMem("bLockAf:%d, FocusMode:%d s_bInitAf:%d\n", bLockAf, tIspParam.tAction.eIpcIspFocusMode, s_bNeedInitAF);
    NvrFixLog("bLockAf:%d, FocusMode:%d s_bInitAf:%d\n", bLockAf, tIspParam.tAction.eIpcIspFocusMode, s_bNeedInitAF);

    nRet = access(IPC_DEV_LOWTEMPATURE_CFG_PATH , 0);
    if(nRet == 0)
    {
        fp = fopen(IPC_DEV_LOWTEMPATURE_CFG_PATH,"r");
        if(fp == NULL)
        {
            NvrFixLog("open %s err! \n", IPC_DEV_LOWTEMPATURE_CFG_PATH);
            eFileFocusMode = NVR_ISP_FOCUS_MODE_COUNT;
        }
        else
        {
            fscanf(fp, "%d", (s32*)&eFileFocusMode);
            fclose(fp);
            fp = NULL;
        }

        NvrFixLog("read file focus mode %d \n", eFileFocusMode);
    }

    if(bLockAf)
    {
        if (0 == access("/usr/config/lens.log", F_OK))
        {
            fp = fopen("/usr/config/lens.log","r");
            if (fp)
            {
                fscanf(fp, "%d", &nStatus);
                if (1 == nStatus)
                {
                    bCreate = FALSE;
                    fscanf(fp, "%d", &nZoom);
                    fscanf(fp, "%d", &nPos);
                }

                fclose(fp);
                fp = NULL;
            }
            NvrFixLog("lens file exist. nStatus %d, bCreate %d \n", nStatus, bCreate);
        }

        if (bCreate)
        {
            fp = fopen("/usr/config/lens.log","wb+");
            if(fp == NULL)
            {
                NvrFixLog("Write Open file failed!");
            }
            else
            {
                nStatus = 1;                           //1代表锁住，0不锁
                fprintf(fp, "%d\n",nStatus);
                fprintf(fp, ""FORMAT_U32"\n", tIspParam.tAction.dwFocusStation);
                fprintf(fp, ""FORMAT_U32"\n", tIspParam.tAction.dwFocusStation);
                fflush(fp);
                fsync(fileno(fp));
                fclose(fp);
                fp = NULL;
                NvrFixLog("lens file create.\n");
            }
        }

        //写入文件，保存当前用户设置聚焦模式
        if(NVR_ISP_FOCUS_MODE_COUNT == eFileFocusMode)
        {
            NvrFixLog("ini file not exist. \n");

            fp = fopen(IPC_DEV_LOWTEMPATURE_CFG_PATH, "wb+");  //保存当前聚焦模式，用于温度上来，镜头解锁时恢复
            {
                fprintf(fp, "%d\n", tIspParam.tAction.eIpcIspFocusMode);
                fflush(fp);
                fsync(fileno(fp));
                fclose(fp);
                fp = NULL;
                NvrFixLog("ini file create. \n");
            }

            NvrFixLog("create lock file and lock mode %x\n", (tIspParam.tAction.eIpcIspFocusMode));
            printf("create lock file and lock mode %x\n", (tIspParam.tAction.eIpcIspFocusMode));

            //低温环境下切换至锁定模式
            tIspParam.tAction.eIpcIspFocusMode = NVR_ISP_FOCUS_MODE_LOCK;
            IspActionParam(0, ISP_ACT_SET_FOCUS_MODE, &(tIspParam.tAction.eIpcIspFocusMode));
        }

        eRet = LcamIspSetParam(0, &tIspParam);
        NvrFixLog("lock set isp ret :%d, eIpcIspFocusMode:%d (0:m 1:c 2:s 3:lock)\n", eRet, tIspParam.tAction.eIpcIspFocusMode);
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("set isp param failed ret:%d\n", eRet);
            return eRet;
        }
    }
    else
    {
        ///< 删除文件
        remove(""IPC_DEV_LOWTEMPATURE_CFG_PATH"");
        remove("/usr/config/lens.log");

        ///< 恢复锁定
        NvrFixLog("reset mode from file %x\n", eFileFocusMode);
        printf("reset mode from file %x\n", eFileFocusMode);
        if (NVR_ISP_FOCUS_MODE_COUNT != eFileFocusMode)
        {
            tIspParam.tAction.eIpcIspFocusMode = eFileFocusMode;
        }
        IspActionParam(0, ISP_ACT_SET_FOCUS_MODE, &(tIspParam.tAction.eIpcIspFocusMode));

        ///<  是否进行过初始化
        if (s_bNeedInitAF)
        {
            u32 dwParam = 0;
            s32 nCount = 0;
            TIspRange tResrInfo;

            mzero(tResrInfo);

            tResrInfo.label = 1;    ///< 传0:只是失步检测，传1:会重新建立坐标系
            IspActionParam(0, ISP_ACT_SET_LENS_RESET, &tResrInfo);
            while (1)
            {
                ///<获取当前镜头是否初始化完成，未完成等待完成
                IspActionParam(0, ISP_ACT_GET_AF_INIT, &dwParam);
                if (dwParam & 0x1)
                {
                    nCount += 11;
                }
                else
                {
                    sleep(1);
                    nCount++;
                }

                if (nCount > 10)
                {
                    NvrFixLog("==================== lens reeset and af init %d====================\n", dwParam & 0x1);
                    break;
                }
            }
            s_bNeedInitAF = FALSE;
        }

        eRet = LcamIspSetParam(0, &tIspParam);
        NvrFixLog("resume set isp ret :%d, eIpcIspFocusMode:%d (0:m 1:c 2:s 3:lock)\n", eRet, tIspParam.tAction.eIpcIspFocusMode);
        if(NVR_ERR__OK != eRet)
        {
            PRINTERR("set isp param failed ret:%d\n", eRet);
            return eRet;
        }


        if(NVR_ISP_FOCUS_MODE_LOCK != tIspParam.tAction.eIpcIspFocusMode)
        {
            NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION, (void *)&g_dwCurrentZoom);

            NvrFixLog("af to zoom %d! \n", g_dwCurrentZoom);
        }
    }

    return NVR_ERR__OK;
}

void NvrFixDevLowTempLimitAf()
{
    PRINTTMP("low temp limit af cap  %d %d %d\n", g_tFixInterCapInfo.tFixHwInternalCap.bySupFocus,
            g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTempLimit, g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTimeLimit);

    ///<变焦镜头增加低温锁定增加
    if (g_tFixInterCapInfo.tFixHwInternalCap.bySupFocus && (g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTempLimit != 0))
    {
        static u32 s_dwLastAFMode = -1; //1为打开AF，0为禁止AF,记录当前的af状态
        static u32 dwBootTime = 0;
        s32 nCurrentTemp = 0;
        struct timespec tRunTime;
        BOOL bFirstInit = FALSE;
        u32 dwAFEnableMode = -1; //1为打开AF，0为禁止AF
        s16 nLimitTemp = g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTempLimit;
        s32 nLimitTime = g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTimeLimit ? g_tFixInterCapInfo.tFixHwInternalCap.wSupAFLowTimeLimit : 0xffff;

        //获取当前温度
        NvrFixDevGetTemperature(&nCurrentTemp);

        ///<温度需要传给isp
        IspActionParam(0, ISP_ACT_SET_CURRENT_TEMPERATURE, &nCurrentTemp);

        clock_gettime(CLOCK_MONOTONIC, &tRunTime);
        if (0 == dwBootTime)
        {
            dwBootTime = tRunTime.tv_sec;
        }

        PRINTTMP("Temp:%d %d, Time:%d %d\n", nCurrentTemp, nLimitTemp, (tRunTime.tv_sec - dwBootTime), nLimitTime*60);
        if ((tRunTime.tv_sec - dwBootTime) > (nLimitTime * 60))
        {
            dwAFEnableMode = 1;
        }
        else if (nCurrentTemp > (nLimitTemp + 5))
        {
            dwAFEnableMode = 1;
        }
        else if (nCurrentTemp < nLimitTemp)
        {
            dwAFEnableMode = 0;
        }
        else
        {
            dwAFEnableMode = s_dwLastAFMode;
        }

        if(s_dwLastAFMode != dwAFEnableMode)
        {
            PRINTDBG("AFEnableMode  last:%d now:%d\n",  s_dwLastAFMode, dwAFEnableMode);
            NvrFixLog("Temp:%d %d, Time:%d %d, AFEnableMode  last:%d now:%d\n", nCurrentTemp, nLimitTemp,
                    (tRunTime.tv_sec - dwBootTime), nLimitTime*60, s_dwLastAFMode, dwAFEnableMode);

            bFirstInit =  (-1 == s_dwLastAFMode) ? TRUE : FALSE;

            NvrFixDevSetLensMode(bFirstInit, !dwAFEnableMode);
            s_dwLastAFMode = dwAFEnableMode;
        }

    }

}


/******ring buff，不支持多线程**************/

#define min(x, y) ((x) < (y) ? (x) : (y))
#define ROUND_UP_2(num)  (((num)+1)&~1)
#define DEFAULT_BUF_SIZE (2*1024*1024)

//长度必须是2的N次方
RingBuffer *RingBuffer_create(u32 nLength)
{
    u32 size = ROUND_UP_2(nLength);

    if ( (size&(size-1)) || (size < DEFAULT_BUF_SIZE) )
    {
        size = DEFAULT_BUF_SIZE;
    }
 
    RingBuffer *pRingBuffer = (RingBuffer *)malloc(sizeof(RingBuffer));  
    if (!pRingBuffer) 
    {
        return NULL; 
    }
    memset(pRingBuffer, 0, sizeof(RingBuffer)); 
 
    pRingBuffer->dwSize = size;  
    pRingBuffer->dwIn   = 0;
    pRingBuffer->dwOut  = 0;  
 
    pRingBuffer->pbyBuf = ( u8 *)malloc(size);  
    if (!pRingBuffer->pbyBuf)
    {
        free(pRingBuffer);
        return NULL;
    }

    memset(pRingBuffer->pbyBuf, 0, size);

    return pRingBuffer;
}

void RingBuffer_destroy(RingBuffer *pRingBuffer)
{
    if(pRingBuffer) {
        free(pRingBuffer->pbyBuf);
        free(pRingBuffer);
    }
}

s32 RingBuffer_Reset(RingBuffer *pRingBuffer)
{
    if (pRingBuffer == NULL)
    {
        return -1;
    }
     
    pRingBuffer->dwIn   = 0;
    pRingBuffer->dwOut  = 0;
    memset(pRingBuffer->pbyBuf, 0, pRingBuffer->dwSize);

    return 0;
}

s32 RingBuffer_empty(RingBuffer *pRingBuffer)
{
    return pRingBuffer->dwIn == pRingBuffer->dwOut;
}

s32 RingBuffer_remain(RingBuffer *pRingBuffer)
{
   return pRingBuffer->dwSize - pRingBuffer->dwIn + pRingBuffer->dwOut;
}

s32 RingBuffer_write(RingBuffer *pRingBuffer, u8 *data, u32 nLength)
{
    u32 len = 0;

    nLength = min(nLength, pRingBuffer->dwSize - pRingBuffer->dwIn + pRingBuffer->dwOut);  
    len    = min(nLength, pRingBuffer->dwSize - (pRingBuffer->dwIn & (pRingBuffer->dwSize - 1)));

 
    memcpy(pRingBuffer->pbyBuf + (pRingBuffer->dwIn & (pRingBuffer->dwSize - 1)), data, len);
    memcpy(pRingBuffer->pbyBuf, data + len, nLength - len);
 
    pRingBuffer->dwIn += nLength;
	
	printf("write in:"FORMAT_U32"\n",pRingBuffer->dwIn);
 
    return nLength;
}

s32 RingBuffer_read(RingBuffer *pRingBuffer, u8 *pbyBuf, u32 nLength)
{
    u32 len = 0;  

    nLength = min(nLength, pRingBuffer->dwIn - pRingBuffer->dwOut);
    len    = min(nLength, pRingBuffer->dwSize - (pRingBuffer->dwOut & (pRingBuffer->dwSize - 1)));
 
    memcpy(pbyBuf, pRingBuffer->pbyBuf + (pRingBuffer->dwOut & (pRingBuffer->dwSize - 1)), len);
    memcpy(pbyBuf + len, pRingBuffer->pbyBuf, nLength - len);
 
    pRingBuffer->dwOut += nLength;
 
    return nLength;
}

u8 RingBuffer_CheckSum(RingBuffer *pRingBuffer,u32 dwOut, u32 byBufLen)
{
	u32 i = 0;

	u8 sum = 0;
	for (i = 1; i < byBufLen - 1; i++)
	{
	    dwOut = (dwOut+1) & (pRingBuffer->dwSize - 1);
		sum +=pRingBuffer->pbyBuf[dwOut];
		//printf("%.2x ",pRingBuffer->pbyBuf[dwOut]);
	}
	printf("\n");
    dwOut = (dwOut+1) & (pRingBuffer->dwSize - 1);
	return sum==pRingBuffer->pbyBuf[dwOut];
}

s32 RingBuffer_Copy(RingBuffer *pRingBuffer,u32 dwOut,u8 *pbyBuf, u32 byBufLen)
{
	u8 i = 0;
	byBufLen = min(byBufLen, pRingBuffer->dwIn - pRingBuffer->dwOut);
	for (i = 0; i < byBufLen; i++)
	{
		pbyBuf[i]=pRingBuffer->pbyBuf[dwOut];
		dwOut = (dwOut+1) & (pRingBuffer->dwSize - 1);
	}
	return byBufLen;
}

//可以对其他指令进行扩展
s32 RingBuffer_Getcmd(RingBuffer *pRingBuffer, u8 *pbyBuf, u32* pdwLen)
{
    u32 len = 0;  
    u32 i = 0;
    u32 dwOut=0;
    
    len = pRingBuffer->dwIn - pRingBuffer->dwOut;//已用buff长度
    if(len < 6)
    {
        printf("data len less than 6\n");
		*pdwLen=0;
        return 0;
    }

    //find 0xee or 0xff
    for(i=0;i<len;i++)
    { 
        dwOut=pRingBuffer->dwOut & (pRingBuffer->dwSize - 1);
        if(pRingBuffer->pbyBuf[dwOut] == 0xff)
        {
           if(pRingBuffer->dwIn - dwOut <7)//如果buffer中长度小于7则返回指令数据不够,ff为7字节，ee为变长
           {
                printf("ff len is not enough\n");  
                *pdwLen=0;
                return 0;
           }
           else
           {
                //检查指令完整性
                if(RingBuffer_CheckSum(pRingBuffer,dwOut,7))
                {
                   
					RingBuffer_Copy(pRingBuffer,dwOut,pbyBuf,7);
                    printf("find a ff cmd\n");
					*pdwLen=7;
					pRingBuffer->dwOut += 7;
                    return 7;
                }
				printf("ff cmd check error\n");
           }
        }
        if(pRingBuffer->pbyBuf[dwOut] == 0xee)
        {
           if(pRingBuffer->pbyBuf[dwOut+1]>20)
           {
                //长度异常，直接跳过
                pRingBuffer->dwOut++;
                continue;
           }
           if(pRingBuffer->dwIn - dwOut <6)//如果buffer中长度小于7则返回指令数据不够,ff为7字节，ee为变长,最小6字节
           {
                printf("ee len is not enough\n");  
				*pdwLen=0;				
                return 0;
           }
           else
           {
                if(pRingBuffer->pbyBuf[dwOut+1] > (pRingBuffer->dwIn - dwOut))
                {
                    printf("ee cmd is not total,in:"FORMAT_U32",dwOut:"FORMAT_U32"\n",pRingBuffer->dwIn,dwOut);
					*pdwLen=0;
					return 0;
                }
                else
                {				
					//检查指令完整性
                    if(RingBuffer_CheckSum(pRingBuffer,dwOut-1,pRingBuffer->pbyBuf[dwOut+1]+1))//需要把0xee 计算在内，0xff时不需要
                    {
                        
						RingBuffer_Copy(pRingBuffer,dwOut,pbyBuf,pRingBuffer->pbyBuf[dwOut+1]);
                        printf("find a ee cmd,len:%d\n",pRingBuffer->pbyBuf[dwOut+1]);
						*pdwLen=pRingBuffer->pbyBuf[dwOut+1];
						pRingBuffer->dwOut += pRingBuffer->pbyBuf[dwOut+1];
                        return *pdwLen;
                    }
					printf("ee cmd check error\n");
                }
           }
        }
        //没有找到继续寻找
        pRingBuffer->dwOut++;
    }

    *pdwLen=0;
    return -1;
}

///<---------------------------------------------------------------烟火识别相关操作---------------------------------------------------------------
///< 烟火识别开始定时器
s32 NvrFixDevSomkeFireTimer( HTIMERHANDLE dwTimerId, void* param)
{
    s32 nPresetId= 0;
    u16 wChnId = 0;
    TNvrIntelSmokeFireCfg   tSmokefire;             ///< 烟火识别参数
    mzero(tSmokefire);

    wChnId = g_tTimerParam.wChnId;
    nPresetId = g_tTimerParam.nPresentId;;
    NvrFixCfgGetParamById(wChnId, NVR_FIX_CFG_SMOKEFIRE, &tSmokefire, sizeof(tSmokefire));
    if (tSmokefire.bEnable)
    {
        PRINTDBG("NVR_DEV_SMOKE_FIREE_START chnid:%d presetid:%d \n", wChnId, nPresetId);
#if defined(_CV2X_)
        NvrFixProfeIntelSmokefireAlgOperation(wChnId, &tSmokefire, nPresetId);
#endif
    }
    else
    {
        PRINTDBG("NVR_DEV_SMOKE_FIREE_START not enable chnid:%d presetid:%d \n",wChnId, nPresetId);
    }
    return 0;
}

///< 烟火识别二次确认
NVRSTATUS NvrFixDevSmokeFireSencondConfirm(BOOL bStart, u16 wChnId, u16 x, u16 y, u16 w, u16 h)
{
    static u32 Wangle= 0, Xangle = 0, Hangle = 0, Yangle = 0, Zoom = 0, CurZoomRatio = 1;
    NVRSTATUS eNvrRet = NVR_ERR__OK;

    if (bStart)
    {
        PRINTDBG("to ptz ctrl  wChnId:%d x:%d y:%d w:%d h:%d \n", wChnId, x, y, w, h);

        NvrFixDevGetCurrentPTZHV(&Xangle, &Yangle,&Zoom, &Wangle, &Hangle, &CurZoomRatio);

        ///< 火点居中
        TNvrPtzCtrlInfo tPtzInfo;
        mzero(tPtzInfo);
        tPtzInfo.wChnId = wChnId;
        tPtzInfo.wXposition = x;
        tPtzInfo.wYposition = y;

        tPtzInfo.wWide = w < 3000 ? 2500 : 1;
        tPtzInfo.wHeight = h < 3000 ? 2500 : 1;
        tPtzInfo.wWinWide = 10000;
        tPtzInfo.wWinHeight = 10000;
        NvrDevPtzCtrl3DCtrl(wChnId, NVR_PTZCTRL_TYPE_ZOOMPART, &tPtzInfo);
    }
    else
    {
        TNvrPtzCtrlInfo tPtzCtrlInfo;

        NvrFixIspSetKeyParam(0, NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION, (void *)&Zoom);

        tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PANPOSION_SET;
        tPtzCtrlInfo.wXposition = Xangle;
        eNvrRet = NvrFixDevPtzCtrl(g_wPtzCtrlChnId,tPtzCtrlInfo);
        if (NVR_ERR__OK != eNvrRet)
        {
            PRINTERR("NvrFixDevPtzCtrl NVR_PTZCTRL_PANPOSION_SET failed, ret:%d\n", eNvrRet);
        }

        sleep(1);

        tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TILTPOSION_SET;
        tPtzCtrlInfo.wYposition = Yangle;
        eNvrRet = NvrFixDevPtzCtrl(g_wPtzCtrlChnId,tPtzCtrlInfo);
        if (NVR_ERR__OK != eNvrRet)
        {
            PRINTERR("NvrFixDevPtzCtrl NVR_PTZCTRL_TILTPOSION_SET failed, ret:%d\n", eNvrRet);
        }
        sleep(1);
    }

    return NVR_ERR__OK;
}

///< pt操作触发
static NVRSTATUS NvrFixDevSmokeFireTrigger(ENvrFixDevSmokeFireOpType eType, u16 wChnId, s32 nPresetId)
{
#define SMOKE_FIRE_START_INTERVAL   (10)

    static u32 s_dwLastStartTime = 0;
    TNvrIntelSmokeFireCfg   tSmokefire;             ///< 烟火识别参数
    u32 dwOpTime = 0;
    s32 nDelayTime = 10;

    if (!g_hSmokeFireStartTimer)
    {
        PRINTERR("not sup smoke fire\n");
        return NVR_ERR__OK;
    }

    ///< 停止定时器
    OsApi_TimerStop(g_hSmokeFireStartTimer);

    mzero(tSmokefire);
    tSmokefire.bEnable = FALSE;
    PRINTDBG("NVR_DEV_SMOKE_FIREE_CLOSE chnid:%d presetid:%d \n", wChnId, nPresetId);
#if defined(_CV2X_)
    NvrFixProfeIntelSmokefireAlgOperation(wChnId, &tSmokefire, nPresetId);
#endif

    dwOpTime = NvrSysGetCurTimeSec();
    switch  (eType)
    {
        case NVR_DEV_SMOKE_FIREE_PTZ_START:
        case NVR_DEV_SMOKE_FIREE_START:
        {
            if (NVR_DEV_SMOKE_FIREE_PTZ_START == eType)
            {
                nDelayTime = SMOKE_FIRE_START_INTERVAL + 2;
                s_dwLastStartTime = dwOpTime;
            }
            else
            {
                ///< 查询线程判断pt停止了
                if (0 == s_dwLastStartTime)
                {
                    nDelayTime = SMOKE_FIRE_START_INTERVAL;
                }
                else
                {
                    s32 nDiff = (dwOpTime - s_dwLastStartTime);
                    if (nDiff < 5)
                    {
                        ///< 超过5s 认为查询线程卡住了，沿用ptz转动时触发的定时器。（启动烟火识别时间需要经验处理）
                        nDelayTime = SMOKE_FIRE_START_INTERVAL;
                    }
                }
            }

            ///< 开启定时器 
            mzero(g_tTimerParam);
            g_tTimerParam.wChnId = wChnId;
            g_tTimerParam.nPresentId = nPresetId;

            OsApi_TimerSet( g_hSmokeFireStartTimer, nDelayTime * 1000, NvrFixDevSomkeFireTimer, NULL);
            PRINTDBG("start g_hSmokeFireStartTimer wChnId:%d nPresetId:%d\n", wChnId, nPresetId);

            break;
        }
        default:
        {
            PRINTDBG("eType:%d not deal\n", eType);
        }

    }



    return NVR_ERR__OK;
}

static void NvrFixDevAlarmStatusChangeCB(TNvrAlarmSrc tAlarmSrc, u8 byAlarmStat, TNvrBrokenDownTime tAlarmTime, void* pParam)
{
    PRINTDBG("AlmSrc{DevId %d, AlmType %d, AlmNum %d} AlmStat %d\n", tAlarmSrc.wDevId, tAlarmSrc.byAlarmType, tAlarmSrc.byAlarmNum, byAlarmStat);

    if (NVR_ALARM_TYPE_PROSMART == tAlarmSrc.byAlarmType)
    {
        switch (tAlarmSrc.byAlarmNum)
        {
            case NVR_PROSD_SMOKE_FIRE:
            {
                ///< 消警清除二次确认标志位
                if (0 == byAlarmStat)
                {
#if defined(_CV2X_)
                    NvrFixProfeIntelSmokeFireManualClear(tAlarmSrc.wDevId - 1);
#endif
                }
                break;
            }
        }
    }
}

u8 NvrFixDevMcuCalcCheckSum(u8 *pbybuf, u8 byBufLen)
{
    u8 i = 0;

    u8 sum = 0;
    for (i = 1; i < byBufLen - 1; i++)
    {
        sum +=pbybuf[i];
    }
    return sum;
}
///< 通过单片机获取环境信息
static NVRSTATUS NvrFixDevGetEnvInfo(PTNvrSysEnvInfo ptNvrSysEnvInfo)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u8 abyCmdTemp[7] = {0xFF, 0x01, 0x10, 0x01, 0x00, 0x00, 0x12};      //温度查询指令
    u8 abyCmdHumi[7] = {0xFF, 0x01, 0x10, 0x02, 0x00, 0x00, 0x13};      //湿度查询指令
    u8 abyCmdPress[7] = {0xFF, 0x01, 0x10, 0x03, 0x00, 0x00, 0x14};     //气压查询指令
    static s32 s_nTempErr = 0;
    static s32 s_nHumiErr = 0;
    static s32 s_nKpaErr = 0;
    static TNvrSysEnvInfo s_tLastinfo = {0};

    s32 wRealWrLen = 0;

    if (g_bLensUpdating || g_bLensVerGeting)
    {
        memcpy(ptNvrSysEnvInfo, &s_tLastinfo, sizeof(s_tLastinfo));
        PRINTTMP(" updateding  use last \n");
        return eRet;
    }

    NvrFixDevEnvInfoGetBySerial(g_byLensCtrlSerialId, abyCmdTemp, sizeof(abyCmdTemp), &wRealWrLen);
    if((wRealWrLen != 7) || (NvrFixDevMcuCalcCheckSum(abyCmdTemp, 7)  != abyCmdTemp[6]))
    {
        PRINTERR("get nTemprature err! read len %d, checksum (%d %d)\n", wRealWrLen, NvrFixDevMcuCalcCheckSum(abyCmdTemp, 7), abyCmdTemp[6]);
        if (s_nTempErr++ < 5)
        {
            ptNvrSysEnvInfo->nTemprature = s_tLastinfo.nTemprature;
        }
    }
    else
    {
        ptNvrSysEnvInfo->nTemprature = (s32)abyCmdTemp[5] - 40;
        PRINTTMP(" get nTemprature %d\n", ptNvrSysEnvInfo->nTemprature);
        s_tLastinfo.nTemprature = ptNvrSysEnvInfo->nTemprature;
        s_nTempErr = 0;
    }

    NvrFixDevEnvInfoGetBySerial(g_byLensCtrlSerialId, abyCmdHumi, sizeof(abyCmdHumi), &wRealWrLen);
    if((wRealWrLen != 7) || (NvrFixDevMcuCalcCheckSum(abyCmdHumi, 7)  != abyCmdHumi[6]))
    {
        PRINTERR("get nHumidity err! read len %d, checksum (%d %d)\n", wRealWrLen, NvrFixDevMcuCalcCheckSum(abyCmdHumi, 7), abyCmdHumi[6]);
        if (s_nHumiErr++ < 5)
        {
            ptNvrSysEnvInfo->nHumidity = s_tLastinfo.nHumidity;
        }
    }
    else
    {
        ptNvrSysEnvInfo->nHumidity =abyCmdHumi[5];
        PRINTTMP("get nHumidity %d\n", ptNvrSysEnvInfo->nHumidity);
        s_tLastinfo.nHumidity = ptNvrSysEnvInfo->nHumidity;
        s_nHumiErr = 0;
    }

    NvrFixDevEnvInfoGetBySerial(g_byLensCtrlSerialId, abyCmdPress, sizeof(abyCmdPress), &wRealWrLen);
    if((wRealWrLen != 7) || (NvrFixDevMcuCalcCheckSum(abyCmdPress, 7)  != abyCmdPress[6]))
    {
        PRINTERR("get nDevKpa err! read len %d, checksum (%d %d)\n", wRealWrLen, NvrFixDevMcuCalcCheckSum(abyCmdPress, 7), abyCmdPress[6]);
        if (s_nKpaErr++ < 5)
        {
            ptNvrSysEnvInfo->nDevKpa = s_tLastinfo.nDevKpa;
        }
    }
    else
    {
        ptNvrSysEnvInfo->nDevKpa = (s32)abyCmdPress[4] * 256 + abyCmdPress[5];
        PRINTTMP(" get nDevKpa %d\n", ptNvrSysEnvInfo->nDevKpa);
        s_tLastinfo.nDevKpa = ptNvrSysEnvInfo->nDevKpa;
        s_nKpaErr = 0;
    }

    return eRet;
}


