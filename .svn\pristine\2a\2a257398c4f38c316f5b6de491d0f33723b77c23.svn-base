#ifndef _MANFACECOMPAR_H_
#define _MANFACECOMPAR_H_

#include "VideoAlg.h"

typedef struct tagManFaceComparOpen
{
    EMImageFormat emFormat;
}TKEDAManFaceComparOpen;

// roi info (input)
typedef struct tagManfacecomparInputInfo	//
{
    float * pInputOneFeature;
    float * pInputFeatureSet;
    int     NumOfFeatureSet;
    float   Threshold;
}TManFaceComparInputInfo;



typedef struct tagManFaceComparOutput
{
    float   VerScore; // 相似度
    int     Verified;   // 是否通过
    int     MaxScoreID;
}TManFaceComparOutputInfo;

#endif