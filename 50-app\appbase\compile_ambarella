#!/bin/sh
cd prj_linux
make -f makefile_appbase_amba_cv2x_release clean
make -f makefile_appbase_amba_cv2x_release 2>&1 |tee ../../../10-common/version/compileinfo/appbase_amba_cv2x_release.txt

make -f makefile_appbase_amba_cv2x_no_service_no_xml_release clean
make -f makefile_appbase_amba_cv2x_no_service_no_xml_release 2>&1 |tee ../../../10-common/version/compileinfo/appbase_amba_cv2x_no_service_no_xmlrelease.txt

cd ../