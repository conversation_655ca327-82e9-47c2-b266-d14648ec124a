/**
* @file     nvrosdcfg.proto
* @brief    nvr osdcfg proto
* <AUTHOR>
* @date     2018-07-02
* @version  1.0
* @copyright V1.0  Copyright(C) 2016 NVR All rights reserved.
*/
//省略package

/**字体风格结构体*/
message TPbNvrFontStyle
{
    optional uint32 name_front = 1;            	///<字体名称(前端使用)
    optional uint32 size = 2;                  	///<大小
    optional uint32 color = 3;                	///<颜色
    optional uint32 back_color = 4;           	///<背景色
    optional uint32 clarity = 5;              	///<背景透明(1:是,0:否)
    optional uint32 border = 6;              	///<是否勾边
    repeated int32 name_client = 7;      				///<字体名称(客户端使用)
}

/**字幕区域结构体*/
message TPbNvrOsdRegion
{
    optional uint32 top = 1;                               ///<字高，下同
    optional uint32 bottom = 2;
    optional uint32 right = 3;
    optional uint32 left = 4;
}

/**矩形框结构体*/
message TPbNvrOsdRect
{
    optional uint32 pos_x = 1;
    optional uint32 pos_y = 2;
    optional uint32 width = 3;
    optional uint32 height = 4;
}


/**自定义字幕属性结构体*/
message TPbNvrOsdPref
{
    optional uint32 osd_type = 1;                     			///<字幕类型(兼容osd，保留)
    optional uint32 context_type = 2;          					///<编辑方式
    optional uint32 dev_line_Id = 3;                           	///<编辑方式为 LCAM_OSD_CONTEXT_TYPE_DEV 下字幕对应的外设字符串的行id
    optional uint32 show = 4;                                	///<字幕显示
    optional uint32 pos_X = 5;                                	///<水平边距, 以osd区域左上角为原点
    optional uint32 pos_Y = 6;                                	///<垂直边距，以osd区域左上角为原点
    optional uint32 width = 7;                               	///<图片宽
    optional uint32 height = 8;                              	///<图片高
    optional uint32 X_mode = 9;                               	///<水平对齐方式 (预留)ENvrOsdXAlignMode
    optional uint32 Y_mode = 10;                               	///<垂直对齐方式 (预留)ENvrOsdYAlignMode
    repeated uint32 content = 11;   							///<字幕内容
    optional uint32 buf_len = 12;                              	///<字幕内容长度
}


/**告警字幕参数结构体*/
message TPbNvrOsdAlarmParam
{
    optional uint32 pin_num = 1;                                                 ///<并口数 
    optional TPbNvrOsdPref nvr_alarm_pref = 2;                                     ///<公共配置
}


/**时间字幕参数结构体*/
message TPbNvrOsdTimeParam
{
    optional uint32 multi_line = 1;                 ///<分行显示
    optional uint32 time_format = 2; 			 	///<时间格式(Y-M-D,D/M/Y等)
    optional TPbNvrOsdPref nvr_time_pref = 3;         			///<公共配置
	optional uint32 time_standard = 4; 			 	///<时间制式
	optional uint32 week_format = 5; 			 	///<星期显示格式
	optional uint32 week_language = 6; 			 	///<星期显示语言
}


/**字幕参数结构体*/
message TPbNvrOsdParam
{
///<以下为兼容IPCOSD的参数
    optional uint32 vid_width = 1;                                              ///<视频宽
    optional uint32 vid_height = 2;                                             ///<视频高   
    optional uint32 res_type = 3;                                 				///<分辨率类型
    optional uint32 pic_type = 4;                                     			///<图片类型
    optional uint32 app_type = 5;                                  				///<协议类型
    optional uint32 pic_bits = 6;                                             	///<图片位数, 4/8/24/32
    repeated int32 config = 7;                      							///<方案类型(未见到业务使用此参数)
    optional uint32 pic_num = 8;                                              	///<1:一张0:多张
    optional uint32 user_osd_num = 9;                                          	///<字幕个数
    optional uint32 font_edit = 10;                                           	///<是否由前端生成图片(0:否1:是)
    optional uint32 multi_flag = 11;                                          	///<多行标志，cgi-0，vsip-1
    optional uint32 area_size = 12;                             				///<用户字幕支持的最大面积(内部用)
    optional uint32 osd_enable = 13;                                          	///<支持的osd类型(按位操作)
    optional uint32 font_enable = 14;                                			///<字体设置使能(按位操作)
    optional TPbNvrFontStyle osd_font_style = 15;                           		///<osd字体风格
    repeated uint32 font_each_size = 18;                       						///<不同分辨率下字体大小
    optional TPbNvrOsdRegion osd_region = 19;                                 	///<osd区域边距
    optional TPbNvrOsdRect user_rect = 20;                                		///<用户自定义区域信息
	optional TPbNvrOsdAlarmParam nvr_alarm_param = 21;							///<告警字幕参数
	optional TPbNvrOsdTimeParam nvr_time_param = 22; 								///<时间字幕参数
	optional TPbNvrOsdPref nvr_label_param = 23; 									///<台标字幕参数
	repeated TPbNvrOsdPref nvr_user_param = 24;									///<用户自定义字幕参数
	optional TPbNvrOsdPref nvr_ptz_param = 25;									///<PTZ字幕参数
	optional TPbNvrOsdPref nvr_3g_Param = 26;										///<3G字幕参数

///<以下为NvrOsd新增区域参数
	optional uint32 font_size = 27;												///<字体大小(内部实现用)																					
	repeated TPbNvrOsdPref osd_pref = 28;											///<NVR新增字幕参数	
}

message TPbNvrOsdEncCfg
{
	repeated TPbNvrOsdParam nvr_osd_enc_cfg = 1; 								///<一路视频源的所有编码通道配置
}

message TPbNvrOsdCfg
{
	repeated TPbNvrOsdEncCfg nvr_osd_src_cfg = 1;								///<所有视频源的配置
}























