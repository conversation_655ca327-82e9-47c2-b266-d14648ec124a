#-------------------------------------------------
#
# Project created by QtCreator 2017-11-17T09:09:47
#
#-------------------------------------------------

QT       -= gui

TARGET = appbase
TEMPLATE = lib
CONFIG += staticlib

# The following define makes your compiler emit warnings if you use
# any feature of Qt which as been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS QT_WIN32 NO_SERVICE_LAYER _APP_MEMPOOL_USE_OSP

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

INCLUDEPATH += ../../include \
                ../../../../10-common/include/app \
                ../../../../10-common/include/system \
                ../../../../10-common/include/service \
                ../../../../10-common/include/cbb/charconversion \
                ../../../../10-common/include/cbb/debuglog \
                ../../../../10-common/include/cbb/protobuf/protobuf-c \
                ../../../../10-common/include/cbb/mxml \
                ../../../../10-common/include/cbb/osp

SOURCES += \
    ../../src/appbase_cross_win.c \
    ../../src/appbase_enum_str_conv.c \
    ../../src/appbase_mempool.c \
    ../../src/appbase_mempool_implement.c \
    ../../src/appbase_socket.c \
    ../../src/appbase_task.c \
    ../../src/appbase_utils.c \
    ../../src/appbase_utils_unicode.c \
    ../../src/appbase_xml.c

HEADERS += \
    ../../include/appbase_cross.h \
    ../../include/appbase_log.h \
    ../../include/appbase_mempool_interface.h \
    ../../include/appbase_utils.h \
    ../../../../10-common/include/app/appbase.h \
    ../../../../10-common/include/app/appbase_enum_str_conv.h \
    ../../../../10-common/include/app/appbase_xml.h

unix {
    target.path = /usr/lib
    INSTALLS += target
}
