path="../../10-common/version/compileinfo/basicintellialgctrl_ssc339g.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_basicintellialgctrl_linux for ssc339g    =
echo ==============================================

echo "============compile libbasicintellialgctrl ssc339g============">>../$path

make -e DEBUG=0 -f makefile_ssc339g clean
make -e DEBUG=0 -f makefile_ssc339g 2>>../$path

cd ..
