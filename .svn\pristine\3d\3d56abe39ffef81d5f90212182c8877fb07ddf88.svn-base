#ifndef _KDC_MEDIACTRL_SDK_VID_EXT_CAPTURE_H_
#define _KDC_MEDIACTRL_SDK_VID_EXT_CAPTURE_H_

#include <stdint.h>
#include <kam_vid_base.h>

#ifdef __cplusplus
extern "C" {
#endif

#define EVIDECAP_SUCCESS           (0)

#define EVIDECAP_ERROR_START       (0x2100)
#define EVIDECAP_NOMORE            (EVIDECAP_ERROR_START + 1)
#define EVIDECAP_INVALID_PARAM     (EVIDECAP_ERROR_START + 2)
#define EVIDECAP_INVALID_STATE     (EVIDECAP_ERROR_START + 3)
#define EVIDECAP_NO_MEM_RESOURCES  (EVIDECAP_ERROR_START + 4)
#define EVIDECAP_NOT_SUPPORT       (EVIDECAP_ERROR_START + 5)
#define EVIDECAP_UNKNOWN           (EVIDECAP_ERROR_START + 100)

typedef enum KEVidExternCapType {
  eVidExtCapTypeUVC = 0,
  eVidExtCapTypeUserUVC,
  eVidExtCapTypeHDMI,
  eVidExtCapTypeMax
} KEVidExternCapType;

typedef struct KTVidExtCapInit {
  int32_t resvred;
} KTVidExtCapInit;

typedef struct KTVidExtCapability {
  int32_t width;
  int32_t height;
  int32_t framerate[2];
} KTVidExtCapability;

typedef struct KTVidExtCapOParam {
  int32_t resvred;
} KTVidExtCapOParam;

typedef enum KEVidExtFrameType {
  eVidExtFrameH264 = 1,
  eVidExtFrameJPEG,
  eVidExtFramePCM,
  eVidExtFrameYUV,
} KEVidExtFrameType;

typedef struct KTVidExtFrame {
  int32_t type; //KEVidExtFrameType
  uint8_t *vaddr;
  uint32_t size;
  int64_t timestamp;
  union {
    struct {
      int32_t width;
      int32_t height;
    }video;
    struct {
      int32_t samplerate;
      int32_t bitwidth;
      int32_t channels;
    }audio;
  }u;

} KTVidExtFrame;

typedef enum KEVidExtEventType {
  eVidExtEventButton = 1,
} KEVidExtEventType;

typedef struct KTVidExtEvent {
  int32_t type; //KEVidExtEventType
  union {
    struct {
      int32_t button;
      int32_t state;
    } button;
  }u;

} KTVidExtEvent;

typedef enum KEVidExtCapUsage {
  eVidExtCapUsageVideo = 1,
  eVidExtCapUsageAudio = 2,
} KEVidExtCapUsage;

typedef void * KVID_ECAP_HANDLE;
typedef int32_t (*pVidExtCb)(KTVidExtFrame *frame, void *ctx);
typedef int32_t (*pVidExtEventCb)(KTVidExtEvent *event, void *ctx);

int32_t kam_vid_ecap_set_jvm(void * jvm, void * context);

int32_t kam_vid_ecap_init(KTVidExtCapInit *init);
int32_t kam_vid_ecap_deinit();

int32_t kam_vid_ecap_get_device_num(KEVidExternCapType dev_type);
int32_t kam_vid_ecap_query_capability(KEVidExternCapType dev_type, uint32_t dev_idx,
                                      uint32_t cap_idx, KTVidExtCapability * cap);

int32_t kam_vid_ecap_open(KEVidExternCapType dev_type, uint32_t dev_idx,
                          KTVidExtCapOParam *oparam,
                          KVID_ECAP_HANDLE *cap_hndl);
int32_t kam_vid_ecap_close(KVID_ECAP_HANDLE cap_hndl);

int32_t kam_vid_ecap_start(KVID_ECAP_HANDLE cap_hndl, const KTVidExtCapability * cap, int32_t usage);
int32_t kam_vid_ecap_stop(KVID_ECAP_HANDLE cap_hndl);

int32_t kam_vid_ecap_setparam(KVID_ECAP_HANDLE cap_hndl, const char *conf, int32_t size);
int32_t kam_vid_ecap_getparam(KVID_ECAP_HANDLE cap_hndl, char *conf, int32_t size);

int32_t kam_vid_ecap_regcb(KVID_ECAP_HANDLE cap_hndl, pVidExtCb cb, void *ctx,
                           KEVidExtCapUsage usage);
int32_t kam_vid_ecap_event_regcb(KVID_ECAP_HANDLE cap_hndl, pVidExtEventCb cb, void *ctx);

#ifdef __cplusplus
}
#endif

#endif //_KDC_MEDIACTRL_SDK_VID_EXT_CAPTURE_H_
