#!/bin/sh
#set -x
path="../../10-common/version/compileinfo/nvrlib_his3559a.txt"
date>>$path

module_name=$(basename $PWD)
cd ./prj_linux

echo ==============================================
echo =      "$module_name"_linux for his3559a           =
echo ==============================================

echo "============compile lib$module_name his3559a============">>../$path

make -e DEBUG=0 -f makefile_his3559a clean
make -e DEBUG=0 -f makefile_his3559a 2>&1 1>/dev/null |tee -a ../$path

#cp -L -r -f libnvrsys.so ../../../10-common/lib/release/his3559a/

cd ..

