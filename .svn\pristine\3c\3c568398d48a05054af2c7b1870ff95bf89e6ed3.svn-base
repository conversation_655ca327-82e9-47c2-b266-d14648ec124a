#!/bin/sh
#set -x
path="../../10-common/version/compileinfo/nvrlib_mlu220.txt"
date>>$path

module_name=$(basename $PWD)
cd ./prj_linux

echo ==============================================
echo =      "$module_name"_linux for mlu220           =
echo ==============================================

echo "============compile lib$module_name mlu220============">>../$path

make -e DEBUG=0 -f makefile_mlu220 clean
make -e DEBUG=0 -f makefile_mlu220 2>&1 1>/dev/null |tee -a ../$path


cd ..

