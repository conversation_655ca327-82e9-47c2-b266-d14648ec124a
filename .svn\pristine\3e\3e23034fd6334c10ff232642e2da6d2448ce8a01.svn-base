/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrais.proto */

#ifndef PROTOBUF_C_nvrais_2eproto__INCLUDED
#define PROTOBUF_C_nvrais_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1000000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1001001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _TPbAisCfg TPbAisCfg;
typedef struct _TPbAisAlgDetParamArray TPbAisAlgDetParamArray;
typedef struct _TPbAisDetProcessParamArray TPbAisDetProcessParamArray;
typedef struct _TPbAisDetCfg TPbAisDetCfg;
typedef struct _TPbAisDetCfgParam TPbAisDetCfgParam;
typedef struct _TPbAisAlgDetParam TPbAisAlgDetParam;
typedef struct _TPbAisRectRegion TPbAisRectRegion;
typedef struct _TPbAisPersonDetectAlarmParam TPbAisPersonDetectAlarmParam;
typedef struct _TPbAisCmpRuleMgr TPbAisCmpRuleMgr;
typedef struct _TPbAisCmpRuleParam TPbAisCmpRuleParam;
typedef struct _TPbAisUnicode64Str TPbAisUnicode64Str;
typedef struct _TPbAisGuardDayTime TPbAisGuardDayTime;
typedef struct _TPbAisTimeSegment TPbAisTimeSegment;
typedef struct _TPbAisLinkMode TPbAisLinkMode;
typedef struct _TPbAisAlarmLinkUploadCfg TPbAisAlarmLinkUploadCfg;
typedef struct _TPbAisRoutineLink TPbAisRoutineLink;
typedef struct _TPbAisLinkOut TPbAisLinkOut;
typedef struct _TPbAisLinkLocalOut TPbAisLinkLocalOut;
typedef struct _TPbAisLinkChnOut TPbAisLinkChnOut;
typedef struct _TPbAisLinkRec TPbAisLinkRec;
typedef struct _TPbAisLinkPtz TPbAisLinkPtz;
typedef struct _TPbAisLinkPtzUnit TPbAisLinkPtzUnit;
typedef struct _TPbAisLinkSound TPbAisLinkSound;
typedef struct _TPbAisDetProcessParam TPbAisDetProcessParam;
typedef struct _TPbAisCoiCfgParam TPbAisCoiCfgParam;


/* --- enums --- */

typedef enum _EPbAlarmLinkUploadMode {
  /*
   *仅告警
   */
  EPB_ALARM_LINK_UPLOAD_MODE__ALARM_ONLY = 0,
  /*
   *全部
   */
  EPB_ALARM_LINK_UPLOAD_MODE__ALL = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(EPB_ALARM_LINK_UPLOAD_MODE)
} EPbAlarmLinkUploadMode;
typedef enum _EPbAlarmLinkUploadType {
  /*
   *人脸照
   */
  EPB_ALARM_LINK_UPLOAD_TYPE__FACE = 0,
  /*
   *全景照
   */
  EPB_ALARM_LINK_UPLOAD_TYPE__BACKGROUND = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(EPB_ALARM_LINK_UPLOAD_TYPE)
} EPbAlarmLinkUploadType;

/* --- messages --- */

/*
 *AIS智能配置参数
 */
struct  _TPbAisCfg
{
  ProtobufCMessage base;
  /*
   *通道检测算法配置信息
   */
  size_t n_aisdetcfg;
  TPbAisDetCfg **aisdetcfg;
  /*
   *每个通道的检测算法参数
   */
  size_t n_aisalgdetparam;
  TPbAisAlgDetParamArray **aisalgdetparam;
  /*
   *智能比对规则相关配置信息
   */
  size_t n_aiscmprulemgr;
  TPbAisCmpRuleMgr **aiscmprulemgr;
  /*
   *每个通道的检测处理方式
   */
  size_t n_aisdetprocessparam;
  TPbAisDetProcessParamArray **aisdetprocessparam;
  /*
   *通道行人检测告警参数
   */
  size_t n_aispersondetectalarmparam;
  TPbAisPersonDetectAlarmParam **aispersondetectalarmparam;
};
#define TPB_AIS_CFG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_cfg__descriptor) \
    , 0,NULL, 0,NULL, 0,NULL, 0,NULL, 0,NULL }


struct  _TPbAisAlgDetParamArray
{
  ProtobufCMessage base;
  size_t n_aisalgdetparam_;
  TPbAisAlgDetParam **aisalgdetparam_;
};
#define TPB_AIS_ALG_DET_PARAM_ARRAY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_alg_det_param_array__descriptor) \
    , 0,NULL }


struct  _TPbAisDetProcessParamArray
{
  ProtobufCMessage base;
  size_t n_aisdetprocessparam_;
  TPbAisDetProcessParam **aisdetprocessparam_;
};
#define TPB_AIS_DET_PROCESS_PARAM_ARRAY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_det_process_param_array__descriptor) \
    , 0,NULL }


/*
 *智能通道检测算法配置
 */
struct  _TPbAisDetCfg
{
  ProtobufCMessage base;
  /*
   *通道号,从1开始，0表示未配置智能检测通道
   */
  protobuf_c_boolean has_wchnno;
  uint32_t wchnno;
  /*
   *当前已经配置算法数
   */
  protobuf_c_boolean has_wloadedalgnum;
  uint32_t wloadedalgnum;
  /*
   *已经配置加载的算法参数
   */
  size_t n_atloadedalgparam;
  TPbAisDetCfgParam **atloadedalgparam;
};
#define TPB_AIS_DET_CFG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_det_cfg__descriptor) \
    , 0,0, 0,0, 0,NULL }


/*
 *检测算法部分配置参数
 */
struct  _TPbAisDetCfgParam
{
  ProtobufCMessage base;
  /*
   *是否开启检测
   */
  protobuf_c_boolean has_bstart;
  uint32_t bstart;
  /*
   *检测模式
   */
  protobuf_c_boolean has_edetectmode;
  uint32_t edetectmode;
  /*
   *算法类型
   */
  protobuf_c_boolean has_eloadedtype;
  uint32_t eloadedtype;
};
#define TPB_AIS_DET_CFG_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_det_cfg_param__descriptor) \
    , 0,0, 0,0, 0,0 }


/*
 *检测算法配置参数
 */
struct  _TPbAisAlgDetParam
{
  ProtobufCMessage base;
  /*
   *是否启动
   */
  protobuf_c_boolean has_bstart;
  uint32_t bstart;
  /*
   *检测类型
   */
  protobuf_c_boolean has_ealgtype;
  uint32_t ealgtype;
  /*
   *检测模式
   */
  protobuf_c_boolean has_edetectmode;
  uint32_t edetectmode;
  /*
   *引擎名称
   */
  char *achenginename;
  /*
   *长方形面积，10000*10000
   */
  TPbAisRectRegion *trectregion;
  /*
   *最小瞳距,预留，暂时不用，内部使用
   */
  protobuf_c_boolean has_dwminpd;
  uint32_t dwminpd;
  /*
   *灵敏度
   */
  protobuf_c_boolean has_dwsensitivity;
  uint32_t dwsensitivity;
  /*
   *去重模式
   */
  protobuf_c_boolean has_ermrepeatmode;
  uint32_t ermrepeatmode;
  /*
   *仅检测抓拍图片,FALSE表示视频流和手动图片都检测，TRUE表示仅检测抓拍图片
   */
  protobuf_c_boolean has_bonlydetectpic;
  uint32_t bonlydetectpic;
  /*
   *检测间隔时间
   */
  protobuf_c_boolean has_dwdetectinterval;
  uint32_t dwdetectinterval;
  /*
   *抓拍图片质量
   */
  protobuf_c_boolean has_esnappicquailty;
  uint32_t esnappicquailty;
  /*
   *车牌省份
   */
  char *car_province;
  /*
   *替换可信度分数
   */
  protobuf_c_boolean has_replace_score;
  uint32_t replace_score;
  /*
   * /<最小人脸尺寸
   */
  protobuf_c_boolean has_nminfacesize;
  int32_t nminfacesize;
  /*
   * /<是否去模糊 0：不去模糊 1：去模糊
   */
  protobuf_c_boolean has_nisdeblur;
  int32_t nisdeblur;
  /*
   * /<最小人脸高度
   */
  protobuf_c_boolean has_dwminfaceheight;
  int32_t dwminfaceheight;
  /*
   * /<最小人脸宽度
   */
  protobuf_c_boolean has_dwminfacewidth;
  int32_t dwminfacewidth;
  /*
   * /<重复抓拍间隔
   */
  protobuf_c_boolean has_dwresnapinterval;
  int32_t dwresnapinterval;
};
#define TPB_AIS_ALG_DET_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_alg_det_param__descriptor) \
    , 0,0, 0,0, 0,0, NULL, NULL, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, NULL, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0 }


/*
 *长方形区域结构体
 */
struct  _TPbAisRectRegion
{
  ProtobufCMessage base;
  /*
   *起点x坐标
   */
  protobuf_c_boolean has_wstartx;
  uint32_t wstartx;
  /*
   *起点y坐标
   */
  protobuf_c_boolean has_wstarty;
  uint32_t wstarty;
  /*
   *宽
   */
  protobuf_c_boolean has_wwidth;
  uint32_t wwidth;
  /*
   *高
   */
  protobuf_c_boolean has_wheight;
  uint32_t wheight;
};
#define TPB_AIS_RECT_REGION__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_rect_region__descriptor) \
    , 0,0, 0,0, 0,0, 0,0 }


/*
 *行人检测通道告警参数
 */
struct  _TPbAisPersonDetectAlarmParam
{
  ProtobufCMessage base;
  /*
   *布防时间参数
   */
  size_t n_ataisguardtimeofday;
  TPbAisGuardDayTime **ataisguardtimeofday;
  /*
   *联动参数
   */
  TPbAisLinkMode *taislinkmode;
};
#define TPB_AIS_PERSON_DETECT_ALARM_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_person_detect_alarm_param__descriptor) \
    , 0,NULL, NULL }


/*
 *比对规则相关管理
 */
struct  _TPbAisCmpRuleMgr
{
  ProtobufCMessage base;
  /*
   *比对规则索引，从1开始
   */
  protobuf_c_boolean has_wruleindex;
  uint32_t wruleindex;
  /*
   *比对规则参数
   */
  TPbAisCmpRuleParam *truleparam;
  /*
   *布防时间参数
   */
  size_t n_ataisguardtimeofday;
  TPbAisGuardDayTime **ataisguardtimeofday;
  /*
   *联动参数
   */
  TPbAisLinkMode *taislinkmode;
};
#define TPB_AIS_CMP_RULE_MGR__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_cmp_rule_mgr__descriptor) \
    , 0,0, NULL, 0,NULL, NULL }


/*
 *比对规则参数
 */
struct  _TPbAisCmpRuleParam
{
  ProtobufCMessage base;
  /*
   *规则名称
   */
  TPbAisUnicode64Str *trulename;
  /*
   *布控库名称
   */
  TPbAisUnicode64Str *tlibname;
  /*
   *比对模式
   */
  protobuf_c_boolean has_emode;
  uint32_t emode;
  /*
   *相似度
   */
  protobuf_c_boolean has_wsimilarity;
  uint32_t wsimilarity;
  /*
   *参与比对的通道数量
   */
  protobuf_c_boolean has_wchnnum;
  uint32_t wchnnum;
  /*
   *对应通道号
   */
  size_t n_achchn;
  uint32_t *achchn;
  /*
   *告警去重时间间隔，时间间隔内相同的人员告警仅告警一次，当前仅黑名单告警有效
   */
  protobuf_c_boolean has_walarmdedupinterval;
  uint32_t walarmdedupinterval;
  /*
   *车辆匹配规则
   */
  protobuf_c_boolean has_ecarcule;
  uint32_t ecarcule;
  /*
   *匹配规则目标物，人脸或车辆
   */
  protobuf_c_boolean has_etarget;
  uint32_t etarget;
};
#define TPB_AIS_CMP_RULE_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_cmp_rule_param__descriptor) \
    , NULL, NULL, 0,0, 0,0, 0,0, 0,NULL, 0,0, 0,0, 0,0 }


/*
 *长度为64的unicode字符串
 */
struct  _TPbAisUnicode64Str
{
  ProtobufCMessage base;
  /*
   *unicode长度
   */
  protobuf_c_boolean has_dwlen;
  uint32_t dwlen;
  /*
   *长度为64的unicode字符串
   */
  protobuf_c_boolean has_abyunicodestr;
  ProtobufCBinaryData abyunicodestr;
};
#define TPB_AIS_UNICODE64_STR__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_unicode64_str__descriptor) \
    , 0,0, 0,{0,NULL} }


/*
 *一天布防时间
 */
struct  _TPbAisGuardDayTime
{
  ProtobufCMessage base;
  /*
   *有效时间段个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *时间段
   */
  size_t n_atguardtimeofday;
  TPbAisTimeSegment **atguardtimeofday;
};
#define TPB_AIS_GUARD_DAY_TIME__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_guard_day_time__descriptor) \
    , 0,0, 0,NULL }


/*
 *时间段参数
 */
struct  _TPbAisTimeSegment
{
  ProtobufCMessage base;
  /*
   *时间段开始时间(0~86399, 00:00:00~23:59:59)
   */
  protobuf_c_boolean has_dwstarttime;
  uint32_t dwstarttime;
  /*
   *时间段结束时间(0~86399, 00:00:00~23:59:59)
   */
  protobuf_c_boolean has_dwendtime;
  uint32_t dwendtime;
};
#define TPB_AIS_TIME_SEGMENT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_time_segment__descriptor) \
    , 0,0, 0,0 }


/*
 *告警联动方式
 */
struct  _TPbAisLinkMode
{
  ProtobufCMessage base;
  /*
   *常规联动参数
   */
  TPbAisRoutineLink *troutinelink;
  /*
   *联动输出参数
   */
  TPbAisLinkOut *tlinkout;
  /*
   *联动到通道录像参数
   */
  TPbAisLinkRec *tlinkrec;
  /*
   *联动到通道抓拍参数
   */
  TPbAisLinkRec *tlinksnap;
  /*
   *联动到通道ptz参数
   */
  TPbAisLinkPtz *tlinkptz;
  /*
   *联动声音参数
   */
  TPbAisLinkSound *tlinksound;
};
#define TPB_AIS_LINK_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_mode__descriptor) \
    , NULL, NULL, NULL, NULL, NULL, NULL }


/*
 *告警联动上传视图库配置
 */
struct  _TPbAisAlarmLinkUploadCfg
{
  ProtobufCMessage base;
  /*
   *是否使能
   */
  protobuf_c_boolean has_enable;
  uint32_t enable;
  /*
   *上传模式
   */
  protobuf_c_boolean has_mode;
  EPbAlarmLinkUploadMode mode;
  /*
   *上传类型
   */
  protobuf_c_boolean has_type;
  EPbAlarmLinkUploadType type;
};
#define TPB_AIS_ALARM_LINK_UPLOAD_CFG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_alarm_link_upload_cfg__descriptor) \
    , 0,0, 0,0, 0,0 }


/*
 *常规联动参数
 */
struct  _TPbAisRoutineLink
{
  ProtobufCMessage base;
  /*
   *联动声音报警
   */
  protobuf_c_boolean has_bysound;
  uint32_t bysound;
  /*
   *发送邮件
   */
  protobuf_c_boolean has_bymail;
  uint32_t bymail;
  /*
   *上报中心
   */
  protobuf_c_boolean has_bypostcenter;
  uint32_t bypostcenter;
  /*
   *上报云服务
   */
  protobuf_c_boolean has_bycloudsrv;
  uint32_t bycloudsrv;
  /*
   *HDMI输出使能
   */
  size_t n_abydsphdmi;
  uint32_t *abydsphdmi;
  /*
   *VGA输出使能
   */
  size_t n_abydspvga;
  uint32_t *abydspvga;
  /*
   *上传方式配置
   */
  size_t n_upload_cfg;
  TPbAisAlarmLinkUploadCfg **upload_cfg;
};
#define TPB_AIS_ROUTINE_LINK__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_routine_link__descriptor) \
    , 0,0, 0,0, 0,0, 0,0, 0,NULL, 0,NULL, 0,NULL }


/*
 *联动输出参数
 */
struct  _TPbAisLinkOut
{
  ProtobufCMessage base;
  /*
   *联动到本地输出参数
   */
  TPbAisLinkLocalOut *tlinklocalout;
  /*
   *有效通道个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *联动到通道告警输出参数
   */
  size_t n_atlinkchnout;
  TPbAisLinkChnOut **atlinkchnout;
};
#define TPB_AIS_LINK_OUT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_out__descriptor) \
    , NULL, 0,0, 0,NULL }


/*
 *联动到nvr本地输出参数
 */
struct  _TPbAisLinkLocalOut
{
  ProtobufCMessage base;
  /*
   *有效输出个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *输出编号列表,从0开始计算
   */
  size_t n_abyoutlist;
  uint32_t *abyoutlist;
};
#define TPB_AIS_LINK_LOCAL_OUT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_local_out__descriptor) \
    , 0,0, 0,NULL }


/*
 *联动到通道输出参数
 */
struct  _TPbAisLinkChnOut
{
  ProtobufCMessage base;
  /*
   *通道id
   */
  protobuf_c_boolean has_wchnid;
  uint32_t wchnid;
  /*
   *有效输出个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *输出编号列表
   */
  size_t n_abyoutlist;
  uint32_t *abyoutlist;
};
#define TPB_AIS_LINK_CHN_OUT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_chn_out__descriptor) \
    , 0,0, 0,0, 0,NULL }


/*
 *联动到通道录像参数
 */
struct  _TPbAisLinkRec
{
  ProtobufCMessage base;
  /*
   *有效通道个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *通道列表
   */
  size_t n_awchnlist;
  uint32_t *awchnlist;
};
#define TPB_AIS_LINK_REC__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_rec__descriptor) \
    , 0,0, 0,NULL }


/*
 *联动到通道ptz参数
 */
struct  _TPbAisLinkPtz
{
  ProtobufCMessage base;
  /*
   *有效通道个数
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
  /*
   *联动到ptz参数
   */
  size_t n_atptzunit;
  TPbAisLinkPtzUnit **atptzunit;
};
#define TPB_AIS_LINK_PTZ__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_ptz__descriptor) \
    , 0,0, 0,NULL }


/*
 *联动到通道预置位参数
 */
struct  _TPbAisLinkPtzUnit
{
  ProtobufCMessage base;
  /*
   *通道id
   */
  protobuf_c_boolean has_wchnid;
  uint32_t wchnid;
  /*
   *联动ptz类型
   */
  protobuf_c_boolean has_etype;
  uint32_t etype;
  /*
   *编号
   */
  protobuf_c_boolean has_wnum;
  uint32_t wnum;
};
#define TPB_AIS_LINK_PTZ_UNIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_ptz_unit__descriptor) \
    , 0,0, 0,0, 0,0 }


struct  _TPbAisLinkSound
{
  ProtobufCMessage base;
  /*
   *蜂鸣器使能
   */
  protobuf_c_boolean has_bybeepenable;
  uint32_t bybeepenable;
  /*
   *声音输出使能
   */
  protobuf_c_boolean has_bysoundout;
  uint32_t bysoundout;
  /*
   *文本数据库Id
   */
  protobuf_c_boolean has_bytextid;
  uint32_t bytextid;
};
#define TPB_AIS_LINK_SOUND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_link_sound__descriptor) \
    , 0,0, 0,0, 0,0 }


/*
 *检测处理方式
 */
struct  _TPbAisDetProcessParam
{
  ProtobufCMessage base;
  /*
   *检测类型
   */
  protobuf_c_boolean has_ealgtype;
  uint32_t ealgtype;
  /*
   *是否上传
   */
  size_t n_abupload;
  uint32_t *abupload;
  /*
   *上传方式
   */
  size_t n_aeuploadmode;
  uint32_t *aeuploadmode;
  /*
   *是否不使用移动网络上传
   */
  protobuf_c_boolean has_bnotusemobilenet;
  uint32_t bnotusemobilenet;
};
#define TPB_AIS_DET_PROCESS_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_det_process_param__descriptor) \
    , 0,0, 0,NULL, 0,NULL, 0,0 }


/*
 * /<Coi配置
 */
struct  _TPbAisCoiCfgParam
{
  ProtobufCMessage base;
  /*
   * /<黑名单
   */
  TPbAisUnicode64Str *black_lib;
  /*
   * /<白名单
   */
  TPbAisUnicode64Str *white_lib;
};
#define TPB_AIS_COI_CFG_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_ais_coi_cfg_param__descriptor) \
    , NULL, NULL }


/* TPbAisCfg methods */
void   tpb_ais_cfg__init
                     (TPbAisCfg         *message);
size_t tpb_ais_cfg__get_packed_size
                     (const TPbAisCfg   *message);
size_t tpb_ais_cfg__pack
                     (const TPbAisCfg   *message,
                      uint8_t             *out);
size_t tpb_ais_cfg__pack_to_buffer
                     (const TPbAisCfg   *message,
                      ProtobufCBuffer     *buffer);
TPbAisCfg *
       tpb_ais_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_cfg__free_unpacked
                     (TPbAisCfg *message,
                      ProtobufCAllocator *allocator);
/* TPbAisAlgDetParamArray methods */
void   tpb_ais_alg_det_param_array__init
                     (TPbAisAlgDetParamArray         *message);
size_t tpb_ais_alg_det_param_array__get_packed_size
                     (const TPbAisAlgDetParamArray   *message);
size_t tpb_ais_alg_det_param_array__pack
                     (const TPbAisAlgDetParamArray   *message,
                      uint8_t             *out);
size_t tpb_ais_alg_det_param_array__pack_to_buffer
                     (const TPbAisAlgDetParamArray   *message,
                      ProtobufCBuffer     *buffer);
TPbAisAlgDetParamArray *
       tpb_ais_alg_det_param_array__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_alg_det_param_array__free_unpacked
                     (TPbAisAlgDetParamArray *message,
                      ProtobufCAllocator *allocator);
/* TPbAisDetProcessParamArray methods */
void   tpb_ais_det_process_param_array__init
                     (TPbAisDetProcessParamArray         *message);
size_t tpb_ais_det_process_param_array__get_packed_size
                     (const TPbAisDetProcessParamArray   *message);
size_t tpb_ais_det_process_param_array__pack
                     (const TPbAisDetProcessParamArray   *message,
                      uint8_t             *out);
size_t tpb_ais_det_process_param_array__pack_to_buffer
                     (const TPbAisDetProcessParamArray   *message,
                      ProtobufCBuffer     *buffer);
TPbAisDetProcessParamArray *
       tpb_ais_det_process_param_array__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_det_process_param_array__free_unpacked
                     (TPbAisDetProcessParamArray *message,
                      ProtobufCAllocator *allocator);
/* TPbAisDetCfg methods */
void   tpb_ais_det_cfg__init
                     (TPbAisDetCfg         *message);
size_t tpb_ais_det_cfg__get_packed_size
                     (const TPbAisDetCfg   *message);
size_t tpb_ais_det_cfg__pack
                     (const TPbAisDetCfg   *message,
                      uint8_t             *out);
size_t tpb_ais_det_cfg__pack_to_buffer
                     (const TPbAisDetCfg   *message,
                      ProtobufCBuffer     *buffer);
TPbAisDetCfg *
       tpb_ais_det_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_det_cfg__free_unpacked
                     (TPbAisDetCfg *message,
                      ProtobufCAllocator *allocator);
/* TPbAisDetCfgParam methods */
void   tpb_ais_det_cfg_param__init
                     (TPbAisDetCfgParam         *message);
size_t tpb_ais_det_cfg_param__get_packed_size
                     (const TPbAisDetCfgParam   *message);
size_t tpb_ais_det_cfg_param__pack
                     (const TPbAisDetCfgParam   *message,
                      uint8_t             *out);
size_t tpb_ais_det_cfg_param__pack_to_buffer
                     (const TPbAisDetCfgParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisDetCfgParam *
       tpb_ais_det_cfg_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_det_cfg_param__free_unpacked
                     (TPbAisDetCfgParam *message,
                      ProtobufCAllocator *allocator);
/* TPbAisAlgDetParam methods */
void   tpb_ais_alg_det_param__init
                     (TPbAisAlgDetParam         *message);
size_t tpb_ais_alg_det_param__get_packed_size
                     (const TPbAisAlgDetParam   *message);
size_t tpb_ais_alg_det_param__pack
                     (const TPbAisAlgDetParam   *message,
                      uint8_t             *out);
size_t tpb_ais_alg_det_param__pack_to_buffer
                     (const TPbAisAlgDetParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisAlgDetParam *
       tpb_ais_alg_det_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_alg_det_param__free_unpacked
                     (TPbAisAlgDetParam *message,
                      ProtobufCAllocator *allocator);
/* TPbAisRectRegion methods */
void   tpb_ais_rect_region__init
                     (TPbAisRectRegion         *message);
size_t tpb_ais_rect_region__get_packed_size
                     (const TPbAisRectRegion   *message);
size_t tpb_ais_rect_region__pack
                     (const TPbAisRectRegion   *message,
                      uint8_t             *out);
size_t tpb_ais_rect_region__pack_to_buffer
                     (const TPbAisRectRegion   *message,
                      ProtobufCBuffer     *buffer);
TPbAisRectRegion *
       tpb_ais_rect_region__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_rect_region__free_unpacked
                     (TPbAisRectRegion *message,
                      ProtobufCAllocator *allocator);
/* TPbAisPersonDetectAlarmParam methods */
void   tpb_ais_person_detect_alarm_param__init
                     (TPbAisPersonDetectAlarmParam         *message);
size_t tpb_ais_person_detect_alarm_param__get_packed_size
                     (const TPbAisPersonDetectAlarmParam   *message);
size_t tpb_ais_person_detect_alarm_param__pack
                     (const TPbAisPersonDetectAlarmParam   *message,
                      uint8_t             *out);
size_t tpb_ais_person_detect_alarm_param__pack_to_buffer
                     (const TPbAisPersonDetectAlarmParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisPersonDetectAlarmParam *
       tpb_ais_person_detect_alarm_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_person_detect_alarm_param__free_unpacked
                     (TPbAisPersonDetectAlarmParam *message,
                      ProtobufCAllocator *allocator);
/* TPbAisCmpRuleMgr methods */
void   tpb_ais_cmp_rule_mgr__init
                     (TPbAisCmpRuleMgr         *message);
size_t tpb_ais_cmp_rule_mgr__get_packed_size
                     (const TPbAisCmpRuleMgr   *message);
size_t tpb_ais_cmp_rule_mgr__pack
                     (const TPbAisCmpRuleMgr   *message,
                      uint8_t             *out);
size_t tpb_ais_cmp_rule_mgr__pack_to_buffer
                     (const TPbAisCmpRuleMgr   *message,
                      ProtobufCBuffer     *buffer);
TPbAisCmpRuleMgr *
       tpb_ais_cmp_rule_mgr__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_cmp_rule_mgr__free_unpacked
                     (TPbAisCmpRuleMgr *message,
                      ProtobufCAllocator *allocator);
/* TPbAisCmpRuleParam methods */
void   tpb_ais_cmp_rule_param__init
                     (TPbAisCmpRuleParam         *message);
size_t tpb_ais_cmp_rule_param__get_packed_size
                     (const TPbAisCmpRuleParam   *message);
size_t tpb_ais_cmp_rule_param__pack
                     (const TPbAisCmpRuleParam   *message,
                      uint8_t             *out);
size_t tpb_ais_cmp_rule_param__pack_to_buffer
                     (const TPbAisCmpRuleParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisCmpRuleParam *
       tpb_ais_cmp_rule_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_cmp_rule_param__free_unpacked
                     (TPbAisCmpRuleParam *message,
                      ProtobufCAllocator *allocator);
/* TPbAisUnicode64Str methods */
void   tpb_ais_unicode64_str__init
                     (TPbAisUnicode64Str         *message);
size_t tpb_ais_unicode64_str__get_packed_size
                     (const TPbAisUnicode64Str   *message);
size_t tpb_ais_unicode64_str__pack
                     (const TPbAisUnicode64Str   *message,
                      uint8_t             *out);
size_t tpb_ais_unicode64_str__pack_to_buffer
                     (const TPbAisUnicode64Str   *message,
                      ProtobufCBuffer     *buffer);
TPbAisUnicode64Str *
       tpb_ais_unicode64_str__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_unicode64_str__free_unpacked
                     (TPbAisUnicode64Str *message,
                      ProtobufCAllocator *allocator);
/* TPbAisGuardDayTime methods */
void   tpb_ais_guard_day_time__init
                     (TPbAisGuardDayTime         *message);
size_t tpb_ais_guard_day_time__get_packed_size
                     (const TPbAisGuardDayTime   *message);
size_t tpb_ais_guard_day_time__pack
                     (const TPbAisGuardDayTime   *message,
                      uint8_t             *out);
size_t tpb_ais_guard_day_time__pack_to_buffer
                     (const TPbAisGuardDayTime   *message,
                      ProtobufCBuffer     *buffer);
TPbAisGuardDayTime *
       tpb_ais_guard_day_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_guard_day_time__free_unpacked
                     (TPbAisGuardDayTime *message,
                      ProtobufCAllocator *allocator);
/* TPbAisTimeSegment methods */
void   tpb_ais_time_segment__init
                     (TPbAisTimeSegment         *message);
size_t tpb_ais_time_segment__get_packed_size
                     (const TPbAisTimeSegment   *message);
size_t tpb_ais_time_segment__pack
                     (const TPbAisTimeSegment   *message,
                      uint8_t             *out);
size_t tpb_ais_time_segment__pack_to_buffer
                     (const TPbAisTimeSegment   *message,
                      ProtobufCBuffer     *buffer);
TPbAisTimeSegment *
       tpb_ais_time_segment__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_time_segment__free_unpacked
                     (TPbAisTimeSegment *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkMode methods */
void   tpb_ais_link_mode__init
                     (TPbAisLinkMode         *message);
size_t tpb_ais_link_mode__get_packed_size
                     (const TPbAisLinkMode   *message);
size_t tpb_ais_link_mode__pack
                     (const TPbAisLinkMode   *message,
                      uint8_t             *out);
size_t tpb_ais_link_mode__pack_to_buffer
                     (const TPbAisLinkMode   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkMode *
       tpb_ais_link_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_mode__free_unpacked
                     (TPbAisLinkMode *message,
                      ProtobufCAllocator *allocator);
/* TPbAisAlarmLinkUploadCfg methods */
void   tpb_ais_alarm_link_upload_cfg__init
                     (TPbAisAlarmLinkUploadCfg         *message);
size_t tpb_ais_alarm_link_upload_cfg__get_packed_size
                     (const TPbAisAlarmLinkUploadCfg   *message);
size_t tpb_ais_alarm_link_upload_cfg__pack
                     (const TPbAisAlarmLinkUploadCfg   *message,
                      uint8_t             *out);
size_t tpb_ais_alarm_link_upload_cfg__pack_to_buffer
                     (const TPbAisAlarmLinkUploadCfg   *message,
                      ProtobufCBuffer     *buffer);
TPbAisAlarmLinkUploadCfg *
       tpb_ais_alarm_link_upload_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_alarm_link_upload_cfg__free_unpacked
                     (TPbAisAlarmLinkUploadCfg *message,
                      ProtobufCAllocator *allocator);
/* TPbAisRoutineLink methods */
void   tpb_ais_routine_link__init
                     (TPbAisRoutineLink         *message);
size_t tpb_ais_routine_link__get_packed_size
                     (const TPbAisRoutineLink   *message);
size_t tpb_ais_routine_link__pack
                     (const TPbAisRoutineLink   *message,
                      uint8_t             *out);
size_t tpb_ais_routine_link__pack_to_buffer
                     (const TPbAisRoutineLink   *message,
                      ProtobufCBuffer     *buffer);
TPbAisRoutineLink *
       tpb_ais_routine_link__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_routine_link__free_unpacked
                     (TPbAisRoutineLink *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkOut methods */
void   tpb_ais_link_out__init
                     (TPbAisLinkOut         *message);
size_t tpb_ais_link_out__get_packed_size
                     (const TPbAisLinkOut   *message);
size_t tpb_ais_link_out__pack
                     (const TPbAisLinkOut   *message,
                      uint8_t             *out);
size_t tpb_ais_link_out__pack_to_buffer
                     (const TPbAisLinkOut   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkOut *
       tpb_ais_link_out__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_out__free_unpacked
                     (TPbAisLinkOut *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkLocalOut methods */
void   tpb_ais_link_local_out__init
                     (TPbAisLinkLocalOut         *message);
size_t tpb_ais_link_local_out__get_packed_size
                     (const TPbAisLinkLocalOut   *message);
size_t tpb_ais_link_local_out__pack
                     (const TPbAisLinkLocalOut   *message,
                      uint8_t             *out);
size_t tpb_ais_link_local_out__pack_to_buffer
                     (const TPbAisLinkLocalOut   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkLocalOut *
       tpb_ais_link_local_out__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_local_out__free_unpacked
                     (TPbAisLinkLocalOut *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkChnOut methods */
void   tpb_ais_link_chn_out__init
                     (TPbAisLinkChnOut         *message);
size_t tpb_ais_link_chn_out__get_packed_size
                     (const TPbAisLinkChnOut   *message);
size_t tpb_ais_link_chn_out__pack
                     (const TPbAisLinkChnOut   *message,
                      uint8_t             *out);
size_t tpb_ais_link_chn_out__pack_to_buffer
                     (const TPbAisLinkChnOut   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkChnOut *
       tpb_ais_link_chn_out__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_chn_out__free_unpacked
                     (TPbAisLinkChnOut *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkRec methods */
void   tpb_ais_link_rec__init
                     (TPbAisLinkRec         *message);
size_t tpb_ais_link_rec__get_packed_size
                     (const TPbAisLinkRec   *message);
size_t tpb_ais_link_rec__pack
                     (const TPbAisLinkRec   *message,
                      uint8_t             *out);
size_t tpb_ais_link_rec__pack_to_buffer
                     (const TPbAisLinkRec   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkRec *
       tpb_ais_link_rec__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_rec__free_unpacked
                     (TPbAisLinkRec *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkPtz methods */
void   tpb_ais_link_ptz__init
                     (TPbAisLinkPtz         *message);
size_t tpb_ais_link_ptz__get_packed_size
                     (const TPbAisLinkPtz   *message);
size_t tpb_ais_link_ptz__pack
                     (const TPbAisLinkPtz   *message,
                      uint8_t             *out);
size_t tpb_ais_link_ptz__pack_to_buffer
                     (const TPbAisLinkPtz   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkPtz *
       tpb_ais_link_ptz__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_ptz__free_unpacked
                     (TPbAisLinkPtz *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkPtzUnit methods */
void   tpb_ais_link_ptz_unit__init
                     (TPbAisLinkPtzUnit         *message);
size_t tpb_ais_link_ptz_unit__get_packed_size
                     (const TPbAisLinkPtzUnit   *message);
size_t tpb_ais_link_ptz_unit__pack
                     (const TPbAisLinkPtzUnit   *message,
                      uint8_t             *out);
size_t tpb_ais_link_ptz_unit__pack_to_buffer
                     (const TPbAisLinkPtzUnit   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkPtzUnit *
       tpb_ais_link_ptz_unit__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_ptz_unit__free_unpacked
                     (TPbAisLinkPtzUnit *message,
                      ProtobufCAllocator *allocator);
/* TPbAisLinkSound methods */
void   tpb_ais_link_sound__init
                     (TPbAisLinkSound         *message);
size_t tpb_ais_link_sound__get_packed_size
                     (const TPbAisLinkSound   *message);
size_t tpb_ais_link_sound__pack
                     (const TPbAisLinkSound   *message,
                      uint8_t             *out);
size_t tpb_ais_link_sound__pack_to_buffer
                     (const TPbAisLinkSound   *message,
                      ProtobufCBuffer     *buffer);
TPbAisLinkSound *
       tpb_ais_link_sound__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_link_sound__free_unpacked
                     (TPbAisLinkSound *message,
                      ProtobufCAllocator *allocator);
/* TPbAisDetProcessParam methods */
void   tpb_ais_det_process_param__init
                     (TPbAisDetProcessParam         *message);
size_t tpb_ais_det_process_param__get_packed_size
                     (const TPbAisDetProcessParam   *message);
size_t tpb_ais_det_process_param__pack
                     (const TPbAisDetProcessParam   *message,
                      uint8_t             *out);
size_t tpb_ais_det_process_param__pack_to_buffer
                     (const TPbAisDetProcessParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisDetProcessParam *
       tpb_ais_det_process_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_det_process_param__free_unpacked
                     (TPbAisDetProcessParam *message,
                      ProtobufCAllocator *allocator);
/* TPbAisCoiCfgParam methods */
void   tpb_ais_coi_cfg_param__init
                     (TPbAisCoiCfgParam         *message);
size_t tpb_ais_coi_cfg_param__get_packed_size
                     (const TPbAisCoiCfgParam   *message);
size_t tpb_ais_coi_cfg_param__pack
                     (const TPbAisCoiCfgParam   *message,
                      uint8_t             *out);
size_t tpb_ais_coi_cfg_param__pack_to_buffer
                     (const TPbAisCoiCfgParam   *message,
                      ProtobufCBuffer     *buffer);
TPbAisCoiCfgParam *
       tpb_ais_coi_cfg_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_ais_coi_cfg_param__free_unpacked
                     (TPbAisCoiCfgParam *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*TPbAisCfg_Closure)
                 (const TPbAisCfg *message,
                  void *closure_data);
typedef void (*TPbAisAlgDetParamArray_Closure)
                 (const TPbAisAlgDetParamArray *message,
                  void *closure_data);
typedef void (*TPbAisDetProcessParamArray_Closure)
                 (const TPbAisDetProcessParamArray *message,
                  void *closure_data);
typedef void (*TPbAisDetCfg_Closure)
                 (const TPbAisDetCfg *message,
                  void *closure_data);
typedef void (*TPbAisDetCfgParam_Closure)
                 (const TPbAisDetCfgParam *message,
                  void *closure_data);
typedef void (*TPbAisAlgDetParam_Closure)
                 (const TPbAisAlgDetParam *message,
                  void *closure_data);
typedef void (*TPbAisRectRegion_Closure)
                 (const TPbAisRectRegion *message,
                  void *closure_data);
typedef void (*TPbAisPersonDetectAlarmParam_Closure)
                 (const TPbAisPersonDetectAlarmParam *message,
                  void *closure_data);
typedef void (*TPbAisCmpRuleMgr_Closure)
                 (const TPbAisCmpRuleMgr *message,
                  void *closure_data);
typedef void (*TPbAisCmpRuleParam_Closure)
                 (const TPbAisCmpRuleParam *message,
                  void *closure_data);
typedef void (*TPbAisUnicode64Str_Closure)
                 (const TPbAisUnicode64Str *message,
                  void *closure_data);
typedef void (*TPbAisGuardDayTime_Closure)
                 (const TPbAisGuardDayTime *message,
                  void *closure_data);
typedef void (*TPbAisTimeSegment_Closure)
                 (const TPbAisTimeSegment *message,
                  void *closure_data);
typedef void (*TPbAisLinkMode_Closure)
                 (const TPbAisLinkMode *message,
                  void *closure_data);
typedef void (*TPbAisAlarmLinkUploadCfg_Closure)
                 (const TPbAisAlarmLinkUploadCfg *message,
                  void *closure_data);
typedef void (*TPbAisRoutineLink_Closure)
                 (const TPbAisRoutineLink *message,
                  void *closure_data);
typedef void (*TPbAisLinkOut_Closure)
                 (const TPbAisLinkOut *message,
                  void *closure_data);
typedef void (*TPbAisLinkLocalOut_Closure)
                 (const TPbAisLinkLocalOut *message,
                  void *closure_data);
typedef void (*TPbAisLinkChnOut_Closure)
                 (const TPbAisLinkChnOut *message,
                  void *closure_data);
typedef void (*TPbAisLinkRec_Closure)
                 (const TPbAisLinkRec *message,
                  void *closure_data);
typedef void (*TPbAisLinkPtz_Closure)
                 (const TPbAisLinkPtz *message,
                  void *closure_data);
typedef void (*TPbAisLinkPtzUnit_Closure)
                 (const TPbAisLinkPtzUnit *message,
                  void *closure_data);
typedef void (*TPbAisLinkSound_Closure)
                 (const TPbAisLinkSound *message,
                  void *closure_data);
typedef void (*TPbAisDetProcessParam_Closure)
                 (const TPbAisDetProcessParam *message,
                  void *closure_data);
typedef void (*TPbAisCoiCfgParam_Closure)
                 (const TPbAisCoiCfgParam *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    epb_alarm_link_upload_mode__descriptor;
extern const ProtobufCEnumDescriptor    epb_alarm_link_upload_type__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_cfg__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_alg_det_param_array__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_det_process_param_array__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_det_cfg__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_det_cfg_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_alg_det_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_rect_region__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_person_detect_alarm_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_cmp_rule_mgr__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_cmp_rule_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_unicode64_str__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_guard_day_time__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_time_segment__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_mode__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_alarm_link_upload_cfg__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_routine_link__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_out__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_local_out__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_chn_out__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_rec__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_ptz__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_ptz_unit__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_link_sound__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_det_process_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_ais_coi_cfg_param__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_nvrais_2eproto__INCLUDED */
