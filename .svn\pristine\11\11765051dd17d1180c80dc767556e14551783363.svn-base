#ifndef _H_PDNS_CLIENT_
#define _H_PDNS_CLIENT_

#define    USERDATA_MAX_LEN  1024
#define    USERNAME_LENGTH   32
#define    PASSWORD_LENGTH   128
#define    IPADDR_LENTH      64
#define    SN_LENGTH         128
#define    BUFFER_LENGTH     1024
#define    PORT_LENGTH       6
#define    PDNS_EPOLL_MAX_FD   1
#define    PDNS_DEFAULT_SELECT_TIMEOUT    10
#define    DEVICENAME_LENGTH    64
#define    VERSION_LENGTH    64
#define    DEVICETYPE_LENGTH    64
#define    DEV_USERNAME_LENGTH    64
#define    DEV_PASSWORD_LENGTH    64
#define    MAX_ERRBUF_LEN    10240

//api返回值
#define PDNS_ERR                    -1  //出错
#define PDNS_OK                 (int)0   //正常
#define PDNS_PARAMERR           (int)1   //传递的参数错误
#define PDNS_CREATESOCKERR      (int)2   //创建socket失败
#define PDNS_GETOPTERR          (int)3   //获取option选项是失败
#define PDNS_SETOPTERR          (int)4   //设置option选项失败
#define PDNS_SELECTERR          (int)5   //select调用失败
#define PDNS_CONNECTIMEOUT      (int)6   //连接超时
#define PDNS_CONNECTREFUSEDERR  (int)7   //连接被拒绝
#define PDNS_CONNECTFAILED      (int)8   //获取server连接状况失败
#define PDNS_PASSERR            (int)9   //密码错误
#define PDNS_USERNAMEERR        (int)10  //用户名出错
#define PDNS_CONNECTERR         (int)11  //connect调用出错
#define PDNS_MEMMALLOCERR       (int)12  //malloc memory失败
#define PDNS_REGISTER_ERR       (int)13  //注册失败
#define PDNS_WRITE_ERR          (int)14

typedef struct{
    char servaddr[IPADDR_LENTH];/*服务器ip字符串*/
    unsigned short servport;/*服务器port*/
}TServerInfo;

#define LOGIN_CENTER_CMD_REGINFO_1        0x1/*response ws port*/
#define LOGIN_CENTER_CMD_REGINFO_2        0x2/*should response ws & wss port*/
#define LOGIN_CENTER_CMD_UPDATE_DEVINFO   0x3/*update*/
//LOGIN_CENTER_CMD_REGINFO_1
typedef struct{
    char appid[USERNAME_LENGTH];/*用户名*/
    char appkey[PASSWORD_LENGTH];/*密码*/
    char sn[SN_LENGTH];/*设备串号，唯一的*/
    char achDeviceName[DEVICENAME_LENGTH];/*设备名称*/
    char achDeviceType[DEVICETYPE_LENGTH];/*设备版本号*/
    char achVersion[VERSION_LENGTH];/*设备版本号*/
}TRegInfo_1;
//LOGIN_CENTER_CMD_REGINFO_2
typedef struct{
    char appid[USERNAME_LENGTH];/*用户名*/
    char appkey[PASSWORD_LENGTH];/*密码*/
    char sn[SN_LENGTH];/*设备串号，唯一的*/
    char achDeviceName[DEVICENAME_LENGTH];/*设备名称*/
    char achDeviceType[DEVICETYPE_LENGTH];/*设备版本号*/
    char achVersion[VERSION_LENGTH];/*设备版本号*/
    char achDeviceUserName[DEV_USERNAME_LENGTH];
    char achDevicePassword[DEV_PASSWORD_LENGTH];
    unsigned short supportwss;
}TRegInfo_2;
//LOGIN_CENTER_CMD_UPDATE_DEVINFO
typedef struct{
    char appid[USERNAME_LENGTH];/*用户名*/
    char appkey[PASSWORD_LENGTH];/*密码*/
    char sn[SN_LENGTH];/*设备串号，唯一的*/
    char achDeviceUserName[DEV_USERNAME_LENGTH];
    char achDevicePassword[DEV_PASSWORD_LENGTH];
}TUpdateInfo;

typedef struct
{
    TServerInfo tServer;//服务器信息
    unsigned char cmd;//操作命令
    void *msg;//消息参数
}TPdnsRegInfo;

#define LOGIN_CENTER_CMD_REGINFO_1_RESPONSE 0x2
#define LOGIN_CENTER_CMD_REGINFO_2_RESPONSE 0x3
#define LOGIN_CENTER_CMD_UPDATE_DEVINFO_RESPONSE   0x4/*update*/

typedef struct
{
    char proxyaddr[IPADDR_LENTH];     //服务器回应的ip
    unsigned short httpPort;                   //浏览器<-->dnsproxy的http通道端口
    unsigned short websocketHttpPort;          //dnsproxy<-->pdns app的http通道端口
    unsigned short wssport;         //security websocket的port, just valid for LOGIN_CENTER_CMD_REGINFO_2
    unsigned short dataPort;                   //浏览器<-->dnsproxy的数据通道端口
    unsigned short websocketDataPort;          //dnsproxy<-->pdns app的数据通道端口
}TPdnsReplyInfo;

typedef void (*pdns_sighandler_t)(int);/*from 3.13.0-24 define  __sighandler_t, so define this for previous version*/

int pdns_query_version(char *pch_ver, unsigned int buf_len);
int pdns_set_debuglevel(unsigned int nSwitch);
int pdns_register(TPdnsRegInfo *pTServer, TPdnsReplyInfo *pTRegReply, int nTimeout);
#endif
