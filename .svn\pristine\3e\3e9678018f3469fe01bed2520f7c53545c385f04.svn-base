#-------------------------------------------------
#
# Project created by QtCreator 2018-04-17T14:14:27
#
#-------------------------------------------------

QT       -= core gui

TARGET = nvrcap_vstation_d
TEMPLATE = lib

DEFINES += NVRCAP_DYNAMIC_LIBRARY

# The following define makes your compiler emit warnings if you use
# any feature of Qt which as been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS QT_WIN32

win32:CONFIG(release, debug|release): DESTDIR  += $$PWD/../../../../10-common/lib/release/win32
else:win32:CONFIG(debug, debug|release): DESTDIR  += $$PWD/../../../../10-common/lib/debug/win32

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

INCLUDEPATH += ../../../../41-service_vstation/nvrproto/include \
                ../../../../10-common/include/service \
                ../../include \
                ../../../../10-common/include/system \
                ../../../../10-common/include/cbb/osp \
                ../../../../10-common/include/cbb/protobuf/protobuf-c \
                ../../../../10-common/include/cbb/charconversion \
                ../../../../10-common/include/cbb/debuglog

SOURCES += ../../source/nvrcap.c

HEADERS += ../../../../10-common/include/service/nvrcap.h \
            ../../include/nvrcap_in.h

win32: LIBS += -lWs2_32

# OspLib
win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../60-nms/vistation/dependence/osp/lib/release/ -lOspLib
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../60-nms/vistation/dependence/osp/lib/debug/ -lOspLib

INCLUDEPATH += $$PWD/../../../../60-nms/vistation/dependence/osp/include
DEPENDPATH += $$PWD/../../../../60-nms/vistation/dependence/osp/include

# debuglog
win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../10-common/lib/release/win32/ -ldebuglog
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../10-common/lib/debug/win32/ -ldebuglog

INCLUDEPATH += $$PWD/../../../../10-common/include/cbb/debuglog
DEPENDPATH += $$PWD/../../../../10-common/include/cbb/debuglog

win32-g++:CONFIG(release, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/release/win32/libdebuglog.a
else:win32-g++:CONFIG(debug, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/debug/win32/libdebuglog.a
else:win32:!win32-g++:CONFIG(release, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/release/win32/debuglog.lib
else:win32:!win32-g++:CONFIG(debug, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/debug/win32/debuglog.lib

# charconv
win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../../10-common/lib/release/win32/ -lcharconv
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../../10-common/lib/debug/win32/ -lcharconv

INCLUDEPATH += $$PWD/../../../../10-common/include/cbb/charconversion
DEPENDPATH += $$PWD/../../../../10-common/include/cbb/charconversion

win32-g++:CONFIG(release, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/release/win32/libcharconv.a
else:win32-g++:CONFIG(debug, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/debug/win32/libcharconv.a
else:win32:!win32-g++:CONFIG(release, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/release/win32/charconv.lib
else:win32:!win32-g++:CONFIG(debug, debug|release): PRE_TARGETDEPS += $$PWD/../../../../10-common/lib/debug/win32/charconv.lib


unix {
    target.path = /usr/lib
    INSTALLS += target
}
