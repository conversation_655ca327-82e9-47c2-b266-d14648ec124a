/**
* @file nvrsys.c
* @brief    系统
* <AUTHOR>
* @date 2016-1-5
* @version  1.0
* @copyright V1.0  Copyright(C) 2016 NVR All rights reserved.
*/

#include "nvrcommon.h"
#include "netcbb_wrapper.h"
#include "nvrqueue.h"
#include "nvrsys.h"
#include "nvrsys.pb-c.h"
#include "nvrsys_in.h"
#include "nvrbrdapi.h"
#include "nvrcfg.h"
#include "nvrcfg_in.h"
#include "nvrcap_in.h"
#include "charconv.h"
#include "nvrpui_in.h"
#include "nvrvtductrl_in.h"
#include "nvrmpu.h"
#include "nvrmpu_in.h"
#include "nvrguard_in.h"
#include "nvrcrc32.h"
#include "nvrrec.h"
#include "nvrrec_in.h"
#include "nvrsystem.h"
#include "nvrguard.h"
#include "nvrproduct.h"
#include "nvrnetwork.h"
#include "nvrjnimsgdef.h"
#include "nvrjnistruct.h"
#include "gb28181app_share.h"
#include "pigeonapp_share.h"
#include <sys/vfs.h>
#include <fcntl.h>
#include "nvrnetwork_in.h"
#include "nvrlog.h"
#include "nvrdynamicplugin.h"
#include "sqlite3wrapper.h"
#include "nvrupgrade.h"
#include "openssl/aes.h"
#include "openssl/evp.h"
#include "openssl/sha.h"
#ifndef _QCOM_
#ifndef _SKYLATE_
#include "SDEF.h"
#endif
#endif

#define NVR_SYS_CFG  "NvrSysCfg"
#define NVR_SYS_AUTO_REBOOT_CFG  "NvrSysAutoRebootCfg"
#define NVR_SYS_POWER_WASTE_MODE_CFG "NvrSysPowerWasteModeInfo"
#define NVR_SYS_CUR_IO	"/tmp/.ioinfo"

#define NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG  "NvrSysPtzMaintainAutoRebootCfg"
#define NVR_SYS_LEN_AUTO_MAINTAIN_CFG  "NvrSysLenAutoMaintainCfg"

#define NVR_SYS_RECOVERY_KEY "NvrRecoveryFlag"
#define NVR_SYS_MAX_LED_NUM		21		///<单板led个数最大值 参考 LED_ID_GPS
#define NVR_SYS_STATIC_PROTECT_FLAG "/proc/sys/klsp/usb_esd"

#define NVR_SYS_ENCRYPT_KEY      1212
#define NVR_SYS_ENCRYPT_PARAM1   201601
#define NVR_SYS_ENCRYPT_PARAM2   12118
#define NVR_SYS_ENCRYPT_OFFSET   33

#define NVR_SYS_CFG_CHANGE_CB_MAX 16

#define NVR_SYS_BUTTON_SHUTDOWN_TIME 4  ///<关机长按时间 单位 (s)

#define NVR_SYS_GB_PLATFORM_MAX_NUM  2 	///<国标平台最大个数

#define NVR_SYS_HEALTH_MONTH_MAX_NUM 3	///<获取距离现在最近几个月最大的月数

#define NVR_SYS_HEALTH_FLAG_MINUTE_INTERVAL 5 ///<创建标志文件间隔时间的分钟数


#ifndef _QCOM_
#define NVR_EXPROT_CFG_DATABASE_TEMP "/tmp/nvrcfg_export.db"
#define NVR_EXPROT_CFG_CRC "/tmp/exportcfg_crc.dat"
///ipdt中cjson配置相关字段字符串
#define NVR_EXPORT_IPDT_CFG_FILE "/tmp/ipdtcfg_js.conf"
#define NVR_EXPORT_IPDT_CFG_BACKUP_FILE "/tmp/ipdtcfg_backup_js.conf"
#define NVR_EXPORT_IPDT_CFG_CRC_FILE "/tmp/ipdtcfg_crc_js.dat"
#define NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE "/tmp/ipdtcfg_backup_js.dat"
#define NVR_EXPORT_IPDT_CFG_CHECK_FILE "/tmp/ipdt_check.dat"
#else
#define NVR_EXPROT_CFG_DATABASE_TEMP "/data/nvrcfg_export.db"
#define NVR_EXPROT_CFG_CRC "/data/exportcfg_crc.dat"
#define NVR_EXPORT_IPDT_CFG_FILE "/data/ipdtcfg_js.conf"
#define NVR_EXPORT_IPDT_CFG_BACKUP_FILE "/data/ipdtcfg_backup_js.conf"
#define NVR_EXPORT_IPDT_CFG_CRC_FILE "/data/ipdtcfg_crc_js.dat"
#define NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE "/data/ipdtcfg_backup_js.dat"
#define NVR_EXPORT_IPDT_CFG_CHECK_FILE "/data/ipdt_check.dat"
#endif
SEMHANDLE g_hSysCfgRWSem;      		///<系统模块参数读写信号量
SEMHANDLE g_hSysCfgCBRWSem;         ///<系统模块回调读写信号量
SEMHANDLE g_hSysLogExportSem;       ///<调试日志导出信号量

TNvrSysCfg g_tNvrSysCfg;			///<系统模块配置

#define NVR_SYS_SHUTDOWN_CB_TASK_MAX 		50 		///<重启关机回调任务最大值

#define NVRSYS_ASSERT(p) \
if (NULL == p)			\
{	\
	NVRSYSERR("[%s]%s assert failed(line:%d)\n", __FILE__,__FUNCTION__, __LINE__);	\
	return NVR_ERR__ASSERT;												\
}	\

#define NVRSYSILLEGAL(min, val, max)  (((val) > (max)) || ((val) < (min)))			///<数值范围参数非法判断

///<ktcp(crn)相关配置及参数信息
#define NVR_SYS_KTCP_WORK_PATH 				"/proc/sys/net/crn/"
#define NVR_SYS_KTCP_ENABLE_CFG  			NVR_SYS_KTCP_WORK_PATH"vcp_enable"
#define NVR_SYS_KTCP_PORTS_RANGE_CFG 		NVR_SYS_KTCP_WORK_PATH"vcp_ports"
#define NVR_SYS_KTCP_MAX_BW_CFG  			NVR_SYS_KTCP_WORK_PATH"vcp_maxkbps"
#define NVR_SYS_KTCP_MIN_BW_CFG  			NVR_SYS_KTCP_WORK_PATH"vcp_minkbps"
#define NVR_SYS_KTCP_VER_INFO  				NVR_SYS_KTCP_WORK_PATH"vcp_version"
#define NVR_SYS_KTCP_STAT_INFO 				NVR_SYS_KTCP_WORK_PATH"vcp_stats"
#define NVR_SYS_KTCP_EXPIRE_INFO 			NVR_SYS_KTCP_WORK_PATH"vcp_expire"
#define NVR_SYS_GB_TRANSDATA_MAX_NUM   5                  ///<国标数据国标透明通道数据发送回调注册最大个数(SVR等)
///设备信息
typedef struct tagSysDevInfo
{
	u8   byDevTypeDefault;					  ///<是否使用默认设备型号(每个pid能力初始化时定义)
	char achDevType[NVR_MAX_STR32_LEN];       ///<设备型号
    char achDevSerialNum[NVR_MAX_STR32_LEN];  ///<设备序列号
    char achDevHWVer[NVR_MAX_STR16_LEN];      //硬件版本号
    u32  dwDevMDate;	  					  ///<设备生产日期
    char achDevRomVer[NVR_MAX_STR64_LEN];	  ///<设备rom版本号
    u32  dwPid;								  ///<设备pid
    char achIMEI[NVR_MAX_STR32_LEN];
}TSysDevInfo, *PTSysDevInfo;

/**GP安全配置*/
typedef struct tagNvrKtsmGpSecurityCfg
{
	BOOL32 bActive;                    ///<是否激活
    char achPin[NVR_MAX_STR64_LEN+1];  ///<pin码
    char achRes[3];                    ///<对齐保留位
    TNvrNetAddr tIpAddr;               ///<密管ip
    u16 wPort;                         ///<密管端口 
    char achUsrName[NVR_MAX_STR32_LEN+1]; ///<用户名
    char achRes2[3];                      ///<对齐保留位
    char achPwd[NVR_MAX_STR64_LEN+1];     ///<密码
    char achRes3[3];                      ///<对齐保留位
}TNvrKtsmGpSecurityCfg;
static u32 g_dwBrdPowerModeStat;				///<当前设置给系统的功耗模式状态
static u8 g_abyLedSup[NVR_SYS_MAX_LED_NUM];		///<led灯支持列表
static TNvrCapSysInfo g_tSysCap;				///<系统模块能力集信息
static TSysDevInfo g_tDevInfo;			///<设备信息
static u32 g_dwSlot;					///<用于标识IPC980两个从芯片对应的id号，nvrcap中能力集初始化时通过该值对两个从片设不同的默认ip
static TNvrKtsmGpSecurityCfg g_tKtsmGpCfg; ///<gp安全配置
static TNvrSysDevUpgradeDateInfo g_tLensUpgradeDate; ///<设备镜头升级数据信息
static TNvrSysDevUpgradeDateInfo g_tPowerUpgradeDate; ///<设备镜头升级数据信息
static PFNvrSysSystemDownCB g_apfSysTimingTaskTegInfo[NVR_SYS_SHUTDOWN_CB_TASK_MAX];	///<重启关机回调任务
static PFNvrSysRecoveryCB g_apfSysRecoveryTegInfo[NVR_SYS_SHUTDOWN_CB_TASK_MAX];	///<恢复出厂回调
static PFGbTransDataSend g_apfSysGbTransDataCB[NVR_SYS_GB_TRANSDATA_MAX_NUM];					///<设置国标透明通道数据发送回调
static void* g_pSysGbTransDataHandle[NVR_SYS_GB_TRANSDATA_MAX_NUM];					            ///<设置国标透明通道数据平台信息
static PFNvrSendData g_apfNvrSendDataCB[NVR_SYS_GB_TRANSDATA_MAX_NUM];					///<设置数据发送回调
static PFNvrSysLensUpgradeDateCB g_tLensUpgradeDateCB;                 ///<设置镜头升级数据传输回调
static PFNvrSysLensUpgradeStatusCB g_tLensUpgradeStatusCB;                 ///<设置镜头升级状态回调
static PFNvrSysPowerUpgradeDateCB g_tPowerUpgradeDateCB;                 ///<设置电源单片机升级数据传输回调
static PFNvrSysPowerUpgradeStatusCB g_tPowerUpgradeStatusCB;                 ///<设置电源单片机升级状态回调
static PFNvrSysGetEnvByMcuCB g_tGetEnvByMcuCB = NULL;                       ///<设置通过单片机获取环境信息回调
static PFNvrSysSlaveUpgradeStatusCB g_tSlaveUpgradeStatusCB;        ///<从机升级状态回调
static PFNvrSysSlaveNeedUpgradeCB 	g_tSlaveNeedUpgradeCB;        	///<从机是否需要升级回调
static PFNvrSysSlaveUpgradeCB 		g_tSlaveUpgradeCB;        		///<从机升级命令回调

static pfNvrSysFormatSdResultCB g_pfFormatSdResult = NULL;
static u32 g_dwContext = 0;
static HTIMERHANDLE  g_hUpdateLedTimer = NULL;		///<升级闪灯定时器
static HTIMERHANDLE  g_hTopWriteTimer = NULL;		///<Top日志记录
static PFNvrSysNotifyGbStateCB g_pfGbStateCB = NULL;	///<国标注册状态回调
static s32 g_nLogLinkNum = 0; ///<当前调试压缩日志文件被几个导出任务共

static u8 abyGbRegStat[NVR_SYS_GB_PLATFORM_MAX_NUM];	///<国标注册状态
static HTIMERHANDLE  g_hRegGbCbTimer = NULL;			///<注册国标回调定时器
static void NrvSysGbStatusChangeCallBack(u32 dwIndex, u32 dwState, void *pvContext);
static NvrSysRFIDDataCB g_apfRFIDCallback[NVR_APP_PROTO_MAX] = {0};//RFID上报探测数据回调函数注册数组
#ifdef _QCOM_
static HTIMERHANDLE  g_hExceptionPower = NULL;			///<异常重启回调定时器
static s32 NvrSysAutoWriteExcepitonRebootTimerCB( HTIMERHANDLE dwTimerId, void* param );
#endif
static NvrSysUpgradeByIpdtDataCB g_pfUpgradeByIpdtDCallback;//通过ipdt升级时,与ipdt交互数据回调函数
static BOOL32 g_bUpdate = FALSE; ///<获取包头，说明需要升级，则升级状态置为true

static NvrRadar2DTargetInfo s_atTargetInfo[5] ={{.byTargetNum = 0,},};
static u8 s_byReadIndex = 0;
///<单片机版本号
static char g_achSingleClipVer[NVR_MAX_STR32_LEN+1] = {0};
///<buf
static char g_achSqBuf[13] = {0};
static char g_achSoftVerSeted[NVR_MAX_STR64_LEN] = {""};
static char g_achCustomVersion[NVR_MAX_STR32_LEN] = {""};	///< 定制版本号，通过 NvrSysGetCustomVersion 获取，只在设备启动时初始化一次
static char g_achSoftVerExt[NVR_MAX_STR64_LEN] = {""};		///<扩展软件版本号(暂用于扩展二次开发APP版本号,追加在现有版本号后()内)
static char g_achCameraLens[NVR_MAX_STR64_LEN] = {0};       ///<镜头版本号
static PFNvrSysNotifyPlatStateCB g_pfNvrSysNotifyPlatStateCB = NULL;
static PFNvrSysBallCtrMaintainCB g_pfNvrSysBallCtrMaintainCB = NULL;

///回调函数函数指针
static PFNvrSysOspPrintfCallBack g_pfOspAppPrintf = NULL;
static BOOL32 g_bOspPrintfStart = FALSE;
static PTNvrDoubleList g_ptOspQueuePrintQueue = NULL;
static u32 g_dwCurPrintListNodeNum = 0;
static SEMHANDLE g_hNvrSysOspPrintSem = NULL; ///<osp打印读写信号量
static u32 g_dwNvrSysConID = 0;
static BOOL32 g_bStartOutputOspPrintf = FALSE;
static s32 NvrSysTopPrintTimerCallback( HTIMERHANDLE dwTimerId, void* param );
static NVRSTATUS NvrSysGetLogTar(const s8* pchPath, s8 *pchName, u32 dwBufLen);
BOOL32 g_bJudgeOperate = FALSE;
static PTNvrDoubleList g_ptQueueTarQueue = NULL;

typedef struct tagE2promResumeParam
{
    BOOL32 bExist;
    u32 dwCount;
    TNvrBrdPrdInfo tPrdInfo;
}TE2promResumeParam;

typedef struct tagNvrExportCfgInfo
{
	s32 nDatabaseCrc;							///数据库crc
	s32 nDevTypeCrc;							///<设备型号crc
}TNvrExportCfgInfo;


typedef struct tagNvrSysHealthMonthRecord
{
	u32 dwRecordNum;							///<记录数目
	u32 *pdwRecordKey;							///<对应的键值
}TNvrSysHealthMonthRecord;

typedef struct tagNvrSysHealthAllRecord
{
	TNvrSysHealthMonthRecord atRecord[NVR_SYS_HEALTH_RECORD_COUNT][NVR_SYS_HEALTH_TIME_COUNT];		
}TNvrSysHealthAllRecord;

typedef void (*PFNvrSysOspPrintfCB)(char*);

typedef struct tagTNvrAppSubscribeNode  
{  
	void * pHandle;
    ENvrTransDataType eTransDataType;                //    节点中的成员  
    struct tagTNvrAppSubscribeNode *pNext;        //    指向下一个节点的指针  
}TNvrAppSubscribeNode; 

typedef struct tagTNvrAppSubscribeHeadNode
{
    SEMHANDLE hAppSubcribeSem;
    TNvrAppSubscribeNode *pNext;
}TNvrAppSubscribeHeadNode;

static TNvrAppSubscribeHeadNode *g_tAppSubscribeHeadNode;

TNvrSysHealthAllRecord g_tHealhRecord;

/**
 *@brief  板级信息初始化
 *@param  void
 *@return NVR_OK:成功 错误码:失败，详见
 *@ref    nvrdef.h
 *@see
 *@note
 */
NVRSTATUS NvrSysBrdInfoInit(void);

/**
 *@brief  初始化系统模块默认配置
 *@param  void
 *@return NVR_OK:成功 错误码:失败，详见
 *@ref    nvrdef.h
 *@see
 *@note
 */
static NVRSTATUS NvrSysCfgDefaultInit(void);

/**
 *@brief  系统模块配置序列化
 *@param [out]   ptPbcSimple  保存序列化buf指针
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note
 */
static NVRSTATUS NvrSysCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple);

/**
 *@brief  系统配置proto结构体转化为全局(业务)结构体
 *@param  ptPbNvrSysCfg  系统配置proto结构体指针
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note
 */
static NVRSTATUS NvrSysCfgProtoToStruct(const TPbNvrSysCfg *ptPbNvrSysCfg);
/**
 *@brief  全局变量初始化
 *@see
 *@note
 */
static void NvrSysConstructInit();

/**
 *@brief  检测关机按钮
 *@see
 *@note
 */
static void NvrSysCheckShutDown();
static 	void NvrSysRecoveryNotify(TNvrSysRecoverParam *ptRecoveryParam);
static PFNvrGnssSetParamCB g_pfNvrGnssSetParamCB = NULL;				///<Gnss配置参数回调

static NVRSTATUS NvrSysPtzMaintainAutoRebootCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg);
static NVRSTATUS NvrSysPtzMaintainAutoRebootCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple);
static void NvrSysPtzMaintainAutoRebootDeal();
static NVRSTATUS NvrSysPtzMainTainAutoRebootInit();
static NVRSTATUS NvrSysCfgPowerWasteSave(void);

static NVRSTATUS NvrSysAutoRebootCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple);
static NVRSTATUS NvrSysAutoRebootCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg);
static void NvrSysSysShutdownNotify();
static NVRSTATUS NvrSysLenAutoMainTainInit();
static void NvrSysTimeRegfun(void);
static void NvrSysAutoRebootDeal();
static NVRSTATUS NvrSysLenAutoMaintainCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg);
static NVRSTATUS NvrSysLenAutoMaintainCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple);
static void NvrSysLenAutoMaintainDeal();
static NVRSTATUS NvrSysPowerWasteModeInit();
static NVRSTATUS NvrSysAutoRebootInit();
s32 NvrSysClearExportCfgFile( HTIMERHANDLE dwTimerId, void* param );
static s32 NvrSysDelayShutDownTimerCB( HTIMERHANDLE dwTimerId, void* param );
//static char** str_split(char* a_str, const char a_delim);
static 	void NvrSysOspPrintCallBack(char* pchBuffer);

static 	void NvrSysOspPrintThread();
static NVRSTATUS NvrSysTarLogThread(void);
static NVRSTATUS NvrSysTarLogNotify(TNvrSysTarInfo tTarInfo);

static u32 g_dwRecoveryFlag = 0;

static PFNvrSysParamChangeCB g_apSysParamChangeCB[NVR_SYS_CFG_CHANGE_CB_MAX] = {NULL};     ///<系统模块配置参数变化回调函数数组
static TNvrAutoRebootParam g_tAutoRebootParam;  ///<自动维护参数
static TNvrAutoRebootParam g_tPtzAutoRebootParam;  ///<云台维护自动维护参数
static TNvrAutoRebootParam g_tLenAutoMaintainParam;  ///<镜头维护自动维护参数
static TNvrSysPowerWasteModeInfo g_tPowerWasteModeParam; ///<功耗模式参数
static HTIMERHANDLE g_hExportCfgClearTimer = NULL; ///<清除导出文件定时器
static HTIMERHANDLE g_hDelayShutDownTimer = NULL; ///<延时关机定时器
static HTIMERHANDLE g_hExportLogClearTimer = NULL;
static pfJavaMsgCb g_javaMsgCb = NULL; ///<JNI callback
static void *g_pDynHandle = NULL;///<动态插件句柄
static TEdgeOsInterCapSysInfo g_tInterSysCap;///<内部能力集
static TNvrSysPackInfo g_tNvrSysPack;

static void NvrSysHealthCheckFlag();		///<检查更新系统健康标志守卫任务
static NVRSTATUS NvrSysJudgeShutdownStatus(char achFlagPath[]);
static void NvrSysCheckMuteDown();  ///<mute按键检测，长按4s后停止蜂鸣器告警
///<gp安全初始化
static void NvrSysInitKtsmGpSecurity(BOOL32 bReset);
///<gp安全设置
static NVRSTATUS NvrSysSaveKtsmGpSecurityCfg();

//国标透明通道链表尾部插入新节点
static NVRSTATUS NvrSysAppSubcribeNodeInsertBack(void *pHandle,ENvrTransDataType eTransDataType);

void NvrSysCfgChangeNotify(ENvrSysParamType eParamType, void *pParam, u32 dwDateLen)
{
    s32 i = 0;
	if (NVR_SYS_SYSTEM_TIME == eParamType || NVR_SYS_AUTO_TIME_AYNC == eParamType)
	{
		///<通知snmp
		NvrNetWorkSnmpNotfiy(NVR_SNMP_TIME_RESYNC);
	}
    OsApi_SemTake(g_hSysCfgCBRWSem);

    for(i = 0; i < NVR_SYS_CFG_CHANGE_CB_MAX; i++)
    {
        if(NULL != g_apSysParamChangeCB[i])
        {
        	NVRSYSDEBUG("NvrSysCfgChangeNotify eParamType:%d\n",eParamType);
            g_apSysParamChangeCB[i](eParamType, pParam, dwDateLen);
        }
    }

    OsApi_SemGive(g_hSysCfgCBRWSem);
}

NVRSTATUS NvrGnssSetParamCallBack(PFNvrGnssSetParamCB pfNvrGnssSetParamCB)
{
	NVRSYSMEMAPI();
	if (NULL == g_pfNvrGnssSetParamCB)
	{
		g_pfNvrGnssSetParamCB = pfNvrGnssSetParamCB;

		NVRSYSDEBUG("set Gnss param set callback\n");
	}
	return NVR_ERR__OK;
}



NVRSTATUS NvrSysNotifyPlatSetCallBack(PFNvrSysNotifyPlatStateCB pfNvrSysNotifyCB)
{
	NVRSYSMEMAPI();
	if (NULL == g_pfNvrSysNotifyPlatStateCB)
	{
		g_pfNvrSysNotifyPlatStateCB = pfNvrSysNotifyCB;

		NVRSYSDEBUG("set NotifyPlat set callback\n");
	}
	
	return NVR_ERR__OK;
}



NVRSTATUS NvrSysRegSysParamNotify(const PFNvrSysParamChangeCB pfParamChangeCB)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;    ///<返回值
    s32 i = 0;  ///<第一层循环变量
    BOOL32 bIsRegistered = FALSE;   ///<是否注册过 FALSE-没有注册过，TRUE-注册过
    BOOL32 bIsRegNumMax = TRUE;     ///<注册个数是否达到最大值，TRUE-注册个数最大，FALSE-还有注册个数
    u32 dwRegIndex = 0;             ///<记录可以注册的数组位置

    NVRSYS_ASSERT(pfParamChangeCB);
	OsApi_SemTake(g_hSysCfgRWSem);
    do
    {

        ///判断是否注册过
        for(i = 0; i < NVR_SYS_CFG_CHANGE_CB_MAX; i++)
        {
            if(g_apSysParamChangeCB[i] == pfParamChangeCB)
            {
                eRet = NVR_ERR__ALREADY_REGISTERED;     ///<被注册过，返回值
                NVRSYSDEBUG("NvrSysCfgChangeRegisterCB you have already register the func ret:%d.\n", eRet);
                bIsRegistered = TRUE;
                break;
            }
        }
        ///没有注册过判断是否还有空余位置用来注册
        if(!bIsRegistered)
        {
            ///判断是否还有注册空位
            for(i = 0; i < NVR_SYS_CFG_CHANGE_CB_MAX; i++)
            {
                ///还有没被注册的位置则记录下来
                if(NULL == g_apSysParamChangeCB[i])
                {
                    bIsRegNumMax = FALSE;   ///<注册个数没有达到最大
                    dwRegIndex = i;     ///<记录注册数组位置
                    break;
                }
            }
            if(bIsRegNumMax)
            {
                eRet = NVR_ERR__REGISTER_FULL;      ///<已经注册满了，返回值
                NVRSYSDEBUG("NvrSysCfgChangeRegisterCB register full.\n");
                break;
            }
            else
            {
                g_apSysParamChangeCB[dwRegIndex] = pfParamChangeCB;
            }
        }
        else
        {
            break;
        }

    }while(0);
    OsApi_SemGive(g_hSysCfgRWSem);
    return eRet;
}
void NvrSysDiskPowerOnThread()
{
#ifndef WIN32
    prctl(PR_SET_NAME, "NvrSysDiskPowerOnThread", 0, 0, 0);
#endif
    ///获取磁盘能力，是否进行延迟上电处理
    TNvrCapDmsrvInfo tNvrCapDmsrvInfo;
    u32 dwDiskIndex = 0;
    memset(&tNvrCapDmsrvInfo, 0, sizeof(tNvrCapDmsrvInfo));
    NvrCapGetCapParam(NVR_CAP_ID_DMSRV, &tNvrCapDmsrvInfo);

    for(dwDiskIndex = 0; dwDiskIndex < tNvrCapDmsrvInfo.tNvrCapDiskSlotCap.byInnerSlotNum; dwDiskIndex++)
    {
        NvrBrdApiHddPowerEn(dwDiskIndex, TRUE);
        OsApi_TaskDelay(tNvrCapDmsrvInfo.dwPowerOnDelayTime*1000);
    }

}

void NvrSysStaticProtectThread()
{
#ifndef WIN32
    prctl(PR_SET_NAME, "NvrSysStaticProtectThread", 0, 0, 0);
#endif

	s32 nFd = 0;
	s32 nNum = 0;
	s8* pPtr = NULL;
	s8 achTemp[NVR_MAX_STR16_LEN];
	mzero(achTemp);


	while(1)
	{

		nFd = open(NVR_SYS_STATIC_PROTECT_FLAG, O_RDWR);
		if (nFd < 0)
		{
			NVRSYSERR("open:%s faild errno:%d,%s\n",NVR_SYS_STATIC_PROTECT_FLAG, errno, strerror(errno));
			return ;
		}

		NVRSYSERR("start read flag\n");
		read(nFd, achTemp, sizeof(achTemp));		///<驱动阻塞
		nNum = strtoul(achTemp, &pPtr, 10);

		NVRSYSERR("read flag:%d\n", nNum);
		if (1 == nNum)
		{
			system("echo 0 > "NVR_SYS_STATIC_PROTECT_FLAG"");
			system("rmmod g_mass_storage");
			system("rmmod usb_f_mass_storage");
			system("rmmod libcomposite");
			system("rmmod configfs");
			system("rmmod udc_hisi");

			
			system("insmod /lib/modules/ko/udc-hisi.ko");
			system("insmod /lib/modules/ko/configfs.ko");
			system("insmod /lib/modules/ko/libcomposite.ko");
			system("insmod /lib/modules/ko/usb_f_mass_storage.ko");
			system("insmod /lib/modules/ko/g_mass_storage.ko file=/dev/mmcblk0p1 luns=1 stall=0  removable=1");
			
			
			NVRSYSERR("re install ko!!!\n");
		}

		close(nFd);

		OsApi_TaskDelay(1000);


	}

	return;


}


NVRSTATUS NvrSysDrvMoudleInit(u32 *pdwDevPid, u8 *pbyTestFlag,char *pchMac, u32 *pdwSlot)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrBrdPrdInfo tPrdInfo;
	TE2promResumeParam tE2promResumeParam = {0};
    FILE *fpFd  = NULL;
    char achDevType[NVR_MAX_STR32_LEN] = {""};
    u32 dwDevNameLen = 0;
    BOOL32 bRet = FALSE;
    u32 dwHSVER = 0;
    u32 dwCPU = 0;      //硬件细分型号SID
    u32 dwHVER = 0;

    mzero(tPrdInfo);
	mzero(tE2promResumeParam);

    NvrSysConstructInit();

    DebugLogRegist(DEBUG_LOG_MOD_SYS,"[NVRSYS]",NVR_SRV_DEBUGLOG_NAME);

    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_SYS, LOG_LEVEL_DEBUG, TRUE);  	///<开启能力集模块debug级别打印
    DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_DEBUG);   		///<开启标准输出打印

	NVRSYSIMP("DrvMoudleInit\n");

    /**drvlib模块初始化*/
    eRet = NvrBrdApiDrvModuleInit();
    if (NVR_ERR__OK != eRet)
    {
        NVRSYSERR("DrvModuleInit failed,ret:%d\n", eRet);
        return NVR_ERR__ERROR;
    }
    NVRSYSIMP("DrvModuleInit succ\n");

    /**获取e2prom信息*/
    eRet = NvrBrdApiBrdPinfoQuery(&tPrdInfo);
	if (NVR_ERR__OK != eRet)
    {
        NVRSYSERR("BrdPinfoQuery fail,ret:%d\n",eRet);
    }

    if(0 == tPrdInfo.dwPid || NVR_ERR__OK != eRet )
    {
        struct e2prom_info tE2promInfo;
        mzero(tE2promInfo);
        DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_BASIC);
        bRet = NvrProductGetE2promFromTestProduct(&tE2promInfo);
        tPrdInfo.dwProtect = tE2promInfo.protect;
        tPrdInfo.dwPid = tE2promInfo.pid;
        tPrdInfo.dwHwId = tE2promInfo.hwid;
        tPrdInfo.dwHwVer = tE2promInfo.hwver;
        tPrdInfo.dwHwSubVer = tE2promInfo.hwsubver;
        tPrdInfo.dwMdate = tE2promInfo.mdate;
        tPrdInfo.dwFlag0 = tE2promInfo.flag0;
        tPrdInfo.dwFlag1 = tE2promInfo.flag1;
        tPrdInfo.dwTestIp = tE2promInfo.testip;
        tPrdInfo.byMacNum = tE2promInfo.macnum;
        memcpy(tPrdInfo.abyDevsq, tE2promInfo.devsq, sizeof(tPrdInfo.abyDevsq));
        memcpy(tPrdInfo.abyFlowid, tE2promInfo.flowid, sizeof(tPrdInfo.abyFlowid));
#ifndef _QCOM_
        memcpy(tPrdInfo.aabyMacAddr, tE2promInfo.macaddr, 24);
#else
        ///物理网卡1
        memcpy(&tPrdInfo.aabyMacAddr[0], &tE2promInfo.macaddr[0], sizeof(u8)*6);
        ///物理网卡2
        memcpy(&tPrdInfo.aabyMacAddr[1], &tE2promInfo.macaddr[1], sizeof(u8)*6);
        ///wlan
        memcpy(tPrdInfo.aabyWlanMacAddr, &tE2promInfo.macaddr[2], sizeof(u8)*6);
        ///蓝牙
        memcpy(tPrdInfo.aabyBtMacAddr, &tE2promInfo.macaddr[3], sizeof(u8)*6);
#endif
        memcpy(tPrdInfo.abyUserData, tE2promInfo.userdata, sizeof(tPrdInfo.abyUserData));
        eRet = NvrBrdApiBrdPinfoUpdate(&tPrdInfo);
        if(NVR_ERR__OK != eRet || 0 == tPrdInfo.dwPid)  ///<生产测试更新e2失败或者获取的pid仍未0，则退出,防止生成默认配置，导致初始化异常
        {
            bRet = FALSE;
			eRet = NVR_ERR__ERROR;
            printf("Brd Pinfo Update Failed\n");
            NvrProductAckProductToolSetE2promInfo(bRet);
            printf("OSAPI_AckProductToolSetE2promFALSE\n");
            return eRet;
        }
        else
        {
            bRet = TRUE;
            printf("BrdEEPromUpgrade Succ\n");
            NvrProductAckProductToolSetE2promInfo(bRet);
            printf("OSAPI_AckProductToolSetE2promOK\n");
        }
    }
    else
	{
        memcpy(&(tE2promResumeParam.tPrdInfo),&tPrdInfo,sizeof(TNvrBrdPrdInfo));
        tE2promResumeParam.dwCount = 0;
        tE2promResumeParam.bExist = TRUE;
    	fpFd = fopen("/usr/config/e2pinfo.dat","wb");

    	/**写入e2pinfo.dat文件*/
    	if(NULL != fpFd )
    	{
    	    NVRSYSDEBUG("backup e2prom !\n");
        	fwrite(&tE2promResumeParam, 1, sizeof(tE2promResumeParam), fpFd );
    		fclose(fpFd );
    	}
	}
    snprintf(pchMac,13,"%.2X%.2X%.2X%.2X%.2X%.2X",
            tPrdInfo.aabyMacAddr[0][0],tPrdInfo.aabyMacAddr[0][1],tPrdInfo.aabyMacAddr[0][2],tPrdInfo.aabyMacAddr[0][3],tPrdInfo.aabyMacAddr[0][4],tPrdInfo.aabyMacAddr[0][5]);

	snprintf(g_achSqBuf,13,"%.2X%.2X%.2X%.2X%.2X%.2X",
            tPrdInfo.aabyMacAddr[0][3],tPrdInfo.aabyMacAddr[0][4],tPrdInfo.aabyMacAddr[0][5],tPrdInfo.aabyMacAddr[0][0],tPrdInfo.aabyMacAddr[0][1],tPrdInfo.aabyMacAddr[0][2]);
	
	g_achSqBuf[12] = '\0';

	NVRSYSDEBUG("Protect:	%lu\n",tPrdInfo.dwProtect);
	NVRSYSDEBUG("Pid:		0x%x\n",tPrdInfo.dwPid);
	NVRSYSDEBUG("HwId:		0x%x\n",tPrdInfo.dwHwId);
	NVRSYSDEBUG("HwVer:		%lu\n",tPrdInfo.dwHwVer);
	NVRSYSDEBUG("HwSubVer:	%lu\n",tPrdInfo.dwHwSubVer);
	NVRSYSDEBUG("Mdate:		%lu\n",tPrdInfo.dwMdate);
	NVRSYSDEBUG("Flag0:		0x%x\n",tPrdInfo.dwFlag0);
	NVRSYSDEBUG("Flag1:		0x%x\n",tPrdInfo.dwFlag1);
	NVRSYSDEBUG("TestIp:		%x\n",tPrdInfo.dwTestIp);
	NVRSYSDEBUG("Devsq:		%s\n",tPrdInfo.abyDevsq);
	NVRSYSDEBUG("Flowid:		%s\n",tPrdInfo.abyFlowid);
	NVRSYSDEBUG("MacNum:		%lu\n",tPrdInfo.byMacNum);
	NVRSYSDEBUG("MacAddr: %x:%x:%x:%x:%x:%x\n",
	tPrdInfo.aabyMacAddr[0][0],tPrdInfo.aabyMacAddr[0][1],tPrdInfo.aabyMacAddr[0][2],
	tPrdInfo.aabyMacAddr[0][3],tPrdInfo.aabyMacAddr[0][4],tPrdInfo.aabyMacAddr[0][5]);
	NVRSYSDEBUG("UserData:%s\n",tPrdInfo.abyUserData);

    *pdwDevPid = tPrdInfo.dwPid;
    *pbyTestFlag = (tPrdInfo.dwFlag0 & 0x00000001);     ///<Flag0中bit0表示生产测试标志位

    //DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_BASIC);
    //DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_SYS, LOG_LEVEL_DEBUG, FALSE);

	///设备名长度 21位为设备名称长度 最大32个字节  22~53为设备名
	if(0 != tPrdInfo.abyUserData[20] )
	{
		if(tPrdInfo.abyUserData[20] > NVR_MAX_STR32_LEN)
		{
			tPrdInfo.abyUserData[20] = NVR_MAX_STR32_LEN;
		}

		///userdata中第33位开始表示设备类型长度为32个字节
		memcpy(achDevType, &tPrdInfo.abyUserData[21], tPrdInfo.abyUserData[20]);

	    ///去除可能存在的回车字符 yueshichong 2017.4.20
	    dwDevNameLen = strlen(achDevType);
        NVRSYSDEBUG("userdata[20]:%u, dwDevNameLen:"FORMAT_U32"\n",tPrdInfo.abyUserData[20], dwDevNameLen);
	    if('\r' == achDevType[dwDevNameLen - 1] || '\n' == achDevType[dwDevNameLen - 1])
	    {
	        achDevType[dwDevNameLen - 1] = '\0';
	    }
	    strncpy(g_tDevInfo.achDevType, achDevType, sizeof(g_tDevInfo.achDevType) - 1);
	}

	g_tDevInfo.byDevTypeDefault = 1;

	memcpy(g_tDevInfo.achDevSerialNum, tPrdInfo.abyDevsq, sizeof(g_tDevInfo.achDevSerialNum));

    //获取HSVER
    if(0 != NvrBrdApiPinfoGetHsver(&dwHSVER))
    {
         NVRSYSERR("BrdPinfoGetHsver Failed\n");
    }
    //获取SID
    if(0 != NvrBrdApiBrdPinfoGetCPU(&dwCPU))
    {
         NVRSYSERR("BrdPinfoGetCPU Failed\n");
    }
    //获取HVER
    if(0 !=NvrBrdApiPinfoGetHver(&dwHVER))
    {
         NVRSYSERR("BrdPinfoGetHver Failed\n");
    }
    snprintf(g_tDevInfo.achDevHWVer,sizeof(g_tDevInfo.achDevHWVer), "%lu.%lu.%lu", dwCPU, dwHSVER, dwHVER);

    NVRSYSDEBUG("achDevHver:%s\n",g_tDevInfo.achDevHWVer);
	g_tDevInfo.dwDevMDate = tPrdInfo.dwMdate;

	eRet = NvrSysBrdInfoInit();
	*pdwSlot = g_dwSlot;

	g_tDevInfo.dwPid = *pdwDevPid;

    DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_ERR);   		///<调整标准输出打印级别
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_SYS, LOG_LEVEL_ERR, TRUE);

    return eRet;
}
NVRSTATUS NvrSysGetSqliteKey(char *pchKey,s32 *pnKeyLen)
{
	*pnKeyLen = strlen(g_achSqBuf);
	memcpy(pchKey,g_achSqBuf,*pnKeyLen+1);	
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysBrdInfoInit(void)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 i = 0;
	u16 wLedNum = 0;
    TBrdInfo tBrdInfo;
    TLedInfo tLedInfo;
	TSerialInfo tSerialInfo;


	mzero(tBrdInfo);
	eRet = NvrBrdApiBrdInfoQuery(&tBrdInfo);
    if (NVR_ERR__OK != eRet)
    {
        NVRSYSERR("query brd info failed,ret:%d\n", eRet);
        return NVR_ERR__ERROR;
    }

	wLedNum = tBrdInfo.wLedNum;

	NVRSYSERR("serial num:%d, led num:%d, hwmon Num num:%d, board name:%s\n",
		tBrdInfo.wSerialNum, tBrdInfo.wLedNum, tBrdInfo.wHwmonNum, tBrdInfo.achName);

	for (i = 0; i < wLedNum; i++)
	{
		mzero(tLedInfo);
		tLedInfo.dwNo = i;
		NvrBrdApiLedQueryInfo(&tLedInfo);
		NVRSYSERR("index:%d, ledinfo dwNo:%d, dwCab:%d, dwId:%d  MAX(%d), name:%s\n",
				i, tLedInfo.dwNo, tLedInfo.dwCab, tLedInfo.dwId, NVR_SYS_MAX_LED_NUM, tLedInfo.achName);
		if(tLedInfo.dwId >= NVR_SYS_MAX_LED_NUM )
		{
			continue;
		}
		g_abyLedSup[tLedInfo.dwId] = 1;		///<支持该类型led灯
	}


	for (i = 0; i < tBrdInfo.wSerialNum; i++)
	{
		mzero(tSerialInfo);
		tSerialInfo.dwNo = i;
		NvrBrdApiSerialQueryInfo(&tSerialInfo);
		
		NVRSYSERR("query serial info num:%d, No.%lu, type:%lu, usage:%lu, fix baidrate:%lu, name:%s\n",
			i,
			tSerialInfo.dwNo,
			tSerialInfo.dwType,
			tSerialInfo.dwUsage,
			tSerialInfo.dwFixBaudrate,
			tSerialInfo.achName);
	}

	g_dwSlot = tBrdInfo.nSlot;	///<获取主芯片对应的id号，目前只是用于IPC980，用于标识IPC980两个从芯片对应的id号


	return eRet;
}

NVRSTATUS NvrSysSetLedStatus(u32 dwId, u32 dwStatus)
{
    NVRSTATUS eRet = NVR_ERR__OK;

#if (defined _QCOM_) || (defined _HIS3559A_) || (defined _SKYLATE_)
	if (dwId < LED_ID_RUN || dwId > LED_ID_MPC)
#else
	if (dwId < LED_ID_RUN || dwId > LED_ID_MPC)
#endif
	{
		NVRSYSERR("led id invalid:%lu\n", dwId);
		return NVR_ERR__PARAM_INVALID;
	}

	NVRSYSDEBUG("ledid:%d sup:%d\n", dwId,  g_abyLedSup[dwId]);
	if (1 == g_abyLedSup[dwId])
	{
		eRet = NvrBrdApiLedSetStatus(dwId, dwStatus);
	}
	else
	{
		NVRSYSDEBUG("led:%d not support\n", dwId);
	}

	return eRet;
}

void NvrSysConstructInit()
{
    g_dwRecoveryFlag = 0;
    memset(g_abyLedSup, 0, sizeof(g_abyLedSup));
    memset(&g_tSysCap, 0, sizeof(g_tSysCap));
    memset(&g_tDevInfo, 0, sizeof(g_tDevInfo));
}

#ifdef _QCOM_
s32 NvrSysAutoWriteExcepitonRebootTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
	NvrSysWriteNormalPoweroff();
	OsApi_TimerSet(g_hRegGbCbTimer, 300*1000, NvrSysAutoWriteExcepitonRebootTimerCB, NULL);
	return 0;
}
#endif

NVRSTATUS NvrSysSetPingParam(TNvrSysPingParam *ptPingParam)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet =NETCBB_ERROR;
	
	if(NULL != ptPingParam)
	{
        if (TRUE == ptPingParam->bDisable)
        {
            NVRSYSDEBUG("TRUE == g_tNvrSysCfg.tAdvanceParam.tPingParam.bDisable\n");
#ifndef _QCOM_
            nRet = NetcbbPingFilterManage(NETCBB_FILTER_ICMP_ALL_DENY);
#else
			eRet = NvrSysManageIcmpFilter(NVR_FILTER_ICMP_ALL_DENY);
#endif
        }
        else
        {
            NVRSYSDEBUG("TRUE != g_tNvrSysCfg.tAdvanceParam.tPingParam.bDisable\n");
#ifndef _QCOM_
            nRet = NetcbbPingFilterManage(NETCBB_FILTER_ICMP_ALL_ACCESS);
#else
			eRet = NvrSysManageIcmpFilter(NVR_FILTER_ICMP_ALL_ACCESS);
#endif
        }
#ifndef _QCOM_
            NVRSYSDEBUG("NetcbbPingFilterManage ret:%d\n", nRet);
#else
			NVRSYSDEBUG("NvrSysManageIcmpFilter ret:%u\n", eRet);
#endif

        if (0 != memcmp(ptPingParam, &g_tNvrSysCfg.tAdvanceParam.tPingParam, sizeof (*ptPingParam)))
        {
    		g_tNvrSysCfg.tAdvanceParam.tPingParam = *ptPingParam;
    		eRet = NvrSysCfgSave();
    		if (NVR_ERR__OK != eRet)
    		{
    			NVRSYSERR("NvrSysSetKtcpParam save cfg failed ret:%d\n", eRet);
    		}
        }
	}
	else
		eRet = NVR_ERR__PARAM_INVALID;
	
	return eRet;
}


NVRSTATUS NvrSysGetMtu(u32 *pdwMtu)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL != pdwMtu)
		*pdwMtu = g_tNvrSysCfg.tAdvanceParam.dwMtu;
	else
		eRet = NVR_ERR__PARAM_INVALID;
	return eRet;
}


NVRSTATUS NvrSysGetPingParam(TNvrSysPingParam *ptPingParam)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL != ptPingParam)
		*ptPingParam = g_tNvrSysCfg.tAdvanceParam.tPingParam;
	else
		eRet = NVR_ERR__PARAM_INVALID;
	return eRet;
}

s32 NvrSysTopPrintTimerCallback( HTIMERHANDLE dwTimerId, void* param )
{
	s32 nRet = 0;
	char achTopFIlePath[NVR_MAX_STR128_LEN] = {
""};
	char achCmd[NVR_MAX_STR256_LEN] = {
""};
	char achBuffer[NVR_MAX_STR1024_LEN*6 +1] = {
""};;
#ifndef _QCOM_
	snprintf(achTopFIlePath, sizeof(achTopFIlePath), "%s", "/tmp/.topinfo");
#else
	snprintf(achTopFIlePath, sizeof(achTopFIlePath), "%s", "/sdcard/log/.topinfo");
#endif
	snprintf(achCmd, sizeof(achCmd), "top -b -n 1 > %s", achTopFIlePath);
	nRet = NvrSystem(achCmd);
	if(0 == nRet || 512 == nRet)
	{
		FILE *pFile = fopen(achTopFIlePath,"r");
		if(NULL != pFile)
		{
			fseek(pFile , 0 , SEEK_SET);
			fread(achBuffer, 1,6*NVR_MAX_STR1024_LEN, pFile);
			fclose(pFile);
			if(0 != strlen(achBuffer))
			{
				DebugLogMem(MEM_LOG_TOP, DEBUG_LOG_MOD_SYS, "%s\n", achBuffer);
			}
		}
		unlink(achTopFIlePath);		
	}
    OsApi_TimerSet(g_hTopWriteTimer, 12*60*1000, NvrSysTopPrintTimerCallback, NULL);	
	return 0;
}

NVRSTATUS NvrSysPortRecord(void)
{

	u32 dwSysPort[] = {23, 22, 21, 10021, 10023, 16666, 17230};
	EAppResult  eAppRet;
	eAppRet = AppBaseInit();
	NVRSYSDEBUG("AppBaseInit return %u\n", eAppRet);
    if (APP_ALREADY_INITED != eAppRet && APP_OK != eAppRet)
    {
        NVRSYSERR("AppBaseInit() failed %d\n", eAppRet);
		return NVR_ERR__ERROR;
    }

    TAppServerListenPortParam tParam;
    AppMemzero(&tParam, sizeof(tParam));
    //add到服务端口列表
    int i = 0;
	for (; i<(sizeof (dwSysPort)/sizeof (dwSysPort[0])); i++)
    {
        tParam.eType = APP_SERVER_LISTEN_PORT_TYPE_SYSTEM_RESERVED;
        tParam.bReused = FALSE;
        AppConvertStrtoNet("0.0.0.0", &tParam.tIpAddr);
        tParam.eSocketType = APP_SOCKET_TYPE_TCP;
        tParam.wPort = dwSysPort[i];
        //根据需要判断返回值
        eAppRet = AppRecordServerListenPort(&tParam);
		NVRSYSDEBUG("AppRecordServerListenPort: %u, return %u\n", dwSysPort[i], eAppRet);
    }
	return NVR_ERR__OK;
}




NVRSTATUS NvrReadKshieldErrLogThread(void)
{
#define KSHIELD_ERRLOG_PATH	"/tmp/kshield_err.log"

	NVRSTATUS eRet = NVR_ERR__OK;
	char aErrMsg[NVR_MAX_STR1024_LEN] = {};
	FILE *pFile = NULL;
	char *pchErrMsg = NULL;
	TNvrLogInfo tLogInfo;
	TNvrCapSysInfo tCapSys;
	mzero(tLogInfo);
	mzero(tCapSys);

	NvrCapGetCapParam(NVR_CAP_ID_SYS, &tCapSys);
	strncpy(tLogInfo.achSourceId, tCapSys.tNvrCapSysBasic.achDevSrcName, sizeof (tLogInfo.achSourceId));
	NVRSYSDEBUG("tLogInfo.achSourceId: %s\n", tLogInfo.achSourceId);
	tLogInfo.eLogType = NVR_LOG_KSHIELD_SIGN_EXCEPTION;

	int ret = -1;
	ret = access(KSHIELD_ERRLOG_PATH, F_OK);
	if (0 != ret) {
		NVRSYSDEBUG("access %s:%s\n", KSHIELD_ERRLOG_PATH, strerror(errno));
		creat(KSHIELD_ERRLOG_PATH, S_IRWXU);
	}

	int nty_fd = -1;
	int watch_fd = -1;
	int epoll_fd = -1;
	struct epoll_event ep_evin;
	struct epoll_event ep_evout;
	int fds_num = 0;
	ssize_t read_len;
	struct stat file_stat;
	size_t file_len = 0;
	char buff[1024] = {};

	nty_fd = inotify_init();
	if (-1 == nty_fd) {
		NVRSYSDEBUG("inotify_init error :%s\n", strerror(errno));
		return -1;
	}
	watch_fd = inotify_add_watch(nty_fd, KSHIELD_ERRLOG_PATH, IN_MODIFY);
	if (-1 == watch_fd) {
		NVRSYSDEBUG("inotify_add_watch error :%s\n", strerror(errno));
		ret = -1;
		goto ending;
	}
	NVRSYSDEBUG("nty_fd:%d, watch_fd:%d\n", nty_fd, watch_fd);

	epoll_fd = epoll_create(1);
	if (-1 == epoll_fd) {
		NVRSYSDEBUG("epoll_create :%s\n", strerror(errno));
		ret = -1;
		goto ending;
	}

	//ep_evin.events = EPOLLIN | EPOLLET;
	ep_evin.events = EPOLLIN;
	ep_evin.data.fd = nty_fd; //for ep_evout, the value is given to ep_evout.data.fd
	ret = epoll_ctl(epoll_fd, EPOLL_CTL_ADD, nty_fd, &ep_evin);
	if (-1 == ret) {
		NVRSYSDEBUG("epoll_ctl error :%s\n", strerror(errno));
		ret = -1;
		goto ending;
	}

    ret = stat(KSHIELD_ERRLOG_PATH, &file_stat);
	if (0 != ret) {
		NVRSYSDEBUG("stat %s :%s\n", KSHIELD_ERRLOG_PATH, strerror(errno));
		ret = -1;
		goto ending;
	}
	file_len = file_stat.st_size;

	pFile = fopen(KSHIELD_ERRLOG_PATH, "r");
	if (NULL == pFile) {
		NVRSYSDEBUG("open file error :%s\n", strerror(errno));
		ret = -1;
		goto ending;
	}

	NVRSYSDEBUG("%s size : %zu\n", KSHIELD_ERRLOG_PATH, file_len);
	while (1) {
		ret = fseek(pFile, 0, SEEK_CUR);  // 3516av200不能获取追加内容
		while (1) {
			if (NULL != fgets(aErrMsg, sizeof (aErrMsg), pFile))
			{
				NVRSYSDEBUG("%s", aErrMsg);
				NVRSYSERR("%s", aErrMsg);
				pchErrMsg = strstr(aErrMsg, "[");
				if (NULL != pchErrMsg)
				{
					tLogInfo.dwLogTime = 0;
					NVRSYSDEBUG("%s", pchErrMsg);
					sprintf(tLogInfo.achLogDatail, "%s", pchErrMsg);
					///<facecontrast文件相关的日志会导致签名异常，因为这些日志是算法模块启动时动态创建的
					///<临时修改方案：facecontrast文件相关的日暂时不记录到签名异常日志
					if(NULL == strstr(aErrMsg, "/opt/nvr/facecontrast") && 
						NULL == strstr(aErrMsg, "vaapi-sdk-update.sh") &&
						NULL == strstr(aErrMsg, "vaapi-sdk-rollback.sh"))
					{
						NvrLogWrite(&tLogInfo);
					}
				}
			}
			else 
			{
				//error or end of file
				break;
			}
		}

		//start watch
		fds_num = epoll_wait(epoll_fd, &ep_evout, 5, -1);
		NVRSYSDEBUG("events is %x\n", ep_evout.events);
		if (-1 == fds_num) {
			NVRSYSDEBUG("epoll_wait : %s\n", strerror(errno));
			// interrupted by a signal handler
			if (EINTR == errno) {
				break;	
			}
		} else if (0 == fds_num) {
			NVRSYSDEBUG("timeout\n");
		}
		if (nty_fd != ep_evout.data.fd) {
			NVRSYSDEBUG("not the right fd : %d\n", ep_evout.data.fd);
			//return -1;
		}
		malloc(1024);
		NVRSYSDEBUG("ep_evout %#x\n", ep_evout.events);
		struct inotify_event nty_ev;
		read_len = read(ep_evout.data.fd, &nty_ev, sizeof (nty_ev));
	}
	NVRSYSDEBUG("end loop\n");
	malloc(1024);
	ret = 0;
ending:
	if (NULL != pFile) {
		fclose(pFile);
	}
	if (-1 != epoll_fd) {
		close(epoll_fd);
	}
	if (nty_fd != -1) {
		if (watch_fd != -1) {
			inotify_rm_watch(nty_fd, watch_fd);
		}
		close(nty_fd);
	}
	return ret;
}


NVRSTATUS NvrSysInit(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 dwSysCfgBuflen = 0;
    u8  *pbySysCfgBuf = NULL;    				///<获取配置时保存配置数据的buf
	TPbNvrSysCfg *ptPbNvrSysCfg = NULL;
    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;    		///<分配器
    TNvrPbAllocData tPbAllocData; 				///<分配器上下文
    ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型
	TOspCpuInfo tCpuInfo;
	TOspMemInfo tMemInfo;
    TNvrCapSysDefCfg tNvrSysDefaultCfg;	
	TNvrCapSortwareCapInfo tCapSoftware;
	s8 achDevType[NVR_MAX_STR32_LEN];
    s32 i = 0;
    FILE *pFile = NULL;
    char achIpcCfg[NVR_MAX_STR128_LEN] = {0};
    //u32 dwReocrdNum = 0;
    
	mzero(tCpuInfo);
	mzero(tMemInfo);
    mzero(g_tInterSysCap);
    
	mzero(tPbAlocator);
	mzero(achDevType);
	mzero(tPbAllocData);
    mzero(tNvrSysDefaultCfg);
    for(i = 0 ;i < NVR_SYS_SHUTDOWN_CB_TASK_MAX; i++)
    {
        g_apfSysTimingTaskTegInfo[i] = NULL;
    }

    ///内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_DEBUG);         ///<调整标准输出打印级别
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_SYS, LOG_LEVEL_DEBUG, TRUE);

	///< 定制版本号
	eRet = NvrSysGetCustomVersion(g_achCustomVersion);
	NVRSYSDEBUG("NvrSysGetCustomVersion ret = %d, g_achCustomVersion = %s\n", eRet, g_achCustomVersion);

    OspSetOspPrintfCallBack(NvrSysOspPrintCallBack, NULL);

	g_tAppSubscribeHeadNode = (TNvrAppSubscribeHeadNode *)NVRALLOC(sizeof(TNvrAppSubscribeHeadNode));
    if(NULL == g_tAppSubscribeHeadNode)
    {
        NVRSYSERR(" malloc SubscribeHeadNode failed\n");
        return NVR_ERR__MALLOC_FAILED;
    }
    g_tAppSubscribeHeadNode->pNext = NULL;
    if( !OsApi_SemBCreate(&g_tAppSubscribeHeadNode->hAppSubcribeSem) )
    {
	    NVRSYSERR("  create hSubcribeSem failed\n");
		return NVR_ERR__ERROR;
    }

    //创建系统模块参数读写信号量
    if(!OsApi_SemBCreate(&g_hSysCfgRWSem))
    {
        NVRSYSERR("OsApi_SemBCreate g_hSysCfgInfoSem failed \n");
        return NVR_ERR__ERROR;
    }

    //创建系统模块参数回调函数读写信号量
    if(!OsApi_SemBCreate(&g_hSysCfgCBRWSem))
    {
        NVRSYSERR("OsApi_SemBCreate g_hSysCfgInfoSem failed \n");
        return NVR_ERR__ERROR;
    }

	//创建日志导出信号量
    if(!OsApi_SemBCreate(&g_hSysLogExportSem))
    {
        NVRSYSERR("OsApi_SemBCreate g_hSysLogExportSem failed \n");
        return NVR_ERR__ERROR;
    }

	eRet = NvrCapGetCapParam(NVR_CAP_ID_SYS, (void*)&g_tSysCap);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get sys cap failed, ret:%d\n", eRet);
    	return eRet;
    }
	///创建top日志记录
	if (0 != OsApi_TimerNew(&g_hTopWriteTimer))
	{
		NVRSYSERR("create delay g_hTopWriteTimer  failed\n");
		return NVR_ERR__ERROR;
	}
	
    OsApi_TimerSet(g_hTopWriteTimer, 12*60*1000, NvrSysTopPrintTimerCallback, NULL);

	char achDevSoftVer[NVR_MAX_STR64_LEN] = {0};      ///<设备软件版本号
	if (0 != strlen(g_achCustomVersion))
	{
		snprintf(achDevSoftVer, sizeof(achDevSoftVer), "%s%s %s %s %s", NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE, g_achCustomVersion,__DATE__,__TIME__);
	}
	else
	{
		snprintf(achDevSoftVer, sizeof(achDevSoftVer), "%s%s %s %s", NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE,__DATE__,__TIME__);
	}
	///<设置包头校验方式
    NvrUpdateSetkgHeadType(g_tSysCap.tNvrCapSysBasic.ePkgType, achDevSoftVer);

	///<设置最大fd能力
	if(1024 != g_tSysCap.tNvrCapSysBasic.wFdlimit)
	{
		struct rlimit tRlimit;
		struct rlimit tRlimitNew;
		if (0 == getrlimit(RLIMIT_NOFILE, &tRlimit))
		{
			DebugLogPrint(DEBUG_LOG_MOD_FILE_ONLY, LOG_LEVEL_FILE,"Default RLIMIT_NOFILE: Softlimit=%d, Hardlimit=%d. Set RLIMIT_NOFILE values :%u!\n", tRlimit.rlim_cur, tRlimit.rlim_max, g_tSysCap.tNvrCapSysBasic.wFdlimit);
			tRlimitNew.rlim_cur = g_tSysCap.tNvrCapSysBasic.wFdlimit;
			tRlimitNew.rlim_max = tRlimit.rlim_max;
			if (0 != setrlimit(RLIMIT_NOFILE, &tRlimitNew)) 
			{
				DebugLogPrint(DEBUG_LOG_MOD_FILE_ONLY, LOG_LEVEL_FILE, "setrlimit error, change to default!\n");
				tRlimitNew.rlim_cur = tRlimit.rlim_cur;
				tRlimitNew.rlim_max = tRlimit.rlim_max;
				setrlimit(RLIMIT_NOFILE, &tRlimitNew);
			}
		}
	}
	
    //eRet = NvrCapGetCapParam(NVR_CAP_ID_DEF_CFG, (void*)&tNvrDefaultCfg);
	eRet = NvrCapGetDefCfgParam(NVR_CAP_DEF_CFG_ID_SYS, (void*)&tNvrSysDefaultCfg);
    if(NVR_ERR__OK != eRet)
    {
    	NVRSYSERR("get sys cap failed, ret:%d\n", eRet);
        return eRet;
    }

	eRet = NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &g_tInterSysCap);
    if(NVR_ERR__OK != eRet)
    {
    	NVRSYSERR("get sys inter cap failed, ret:%d\n", eRet);
        return eRet;
    }	

	snprintf(achIpcCfg, sizeof(achIpcCfg), "/usr/config/ipccfg.conf");
	///<如果是IPC老设备,则需要更改usrdata
	if(0 == access(achIpcCfg, 0))	///<存在ipc配置则为原ipc设备
	{
		TNvrBrdPrdInfo tPrdInfo;
		TNvrBrdPrdInfo tPrdInfoNew;
		char achDataTemp[NVR_MAX_STR64_LEN] = {0}; 		
		u8 byDataLen = 0;
		
		mzero(tPrdInfo);
		mzero(tPrdInfoNew);
		
		 /**获取e2prom信息*/
	    eRet = NvrBrdApiBrdPinfoQuery(&tPrdInfo);
		if (NVR_ERR__OK != eRet)
	    {
	        NVRSYSERR("BrdPinfoQuery fail,ret:%d\n",eRet);
	    }
	    else
	    {
			memcpy(achDataTemp, (char *)(tPrdInfo.abyUserData + 17), 31);	///<ipc设备名称最长31字节
			byDataLen = tPrdInfo.abyUserData[16];

			memcpy(&tPrdInfoNew, &tPrdInfo, sizeof(TNvrBrdPrdInfo));
			mzero(tPrdInfoNew.abyUserData);

			NVRSYSDEBUG("byDataLen:%d, strlen:%d !!\n", byDataLen, strlen(achDataTemp));
			if((0 < byDataLen)  &&
			   (0 < strlen(achDataTemp)) &&
			   (strlen(achDataTemp) < 33))
			{
				///<符合原ipc设备型号,修改usrdata
				NVRSYSDEBUG("ipc dev type, change usrdata, datalen:[%d] !!\n", tPrdInfo.abyUserData[16]);
				////<先拷贝设备型号
				memcpy(g_tDevInfo.achDevType, (char *)(tPrdInfo.abyUserData + 17), MIN(sizeof(g_tDevInfo.achDevType), tPrdInfo.abyUserData[16]));

				///<设备型号
				tPrdInfoNew.abyUserData[20] = strlen(achDataTemp);
				memcpy(&tPrdInfoNew.abyUserData[21], &tPrdInfo.abyUserData[17], tPrdInfoNew.abyUserData[20]);

				///<升级校验包头信息
				tPrdInfoNew.abyUserData[3] = strlen((const char*)(tPrdInfo.abyUserData+50));
				memcpy(tPrdInfoNew.abyUserData+4, tPrdInfo.abyUserData+50, tPrdInfoNew.abyUserData[3]);

				///<通道数低位
				tPrdInfoNew.abyUserData[1] = 1;

				///<盘位
				tPrdInfoNew.abyUserData[2] = 0;		///<To Do
				
				eRet = NvrBrdApiBrdPinfoUpdate(&tPrdInfoNew);
				NVRSYSDEBUG("NvrBrdApiBrdPinfoUpdate ret=%d !!\n", eRet);
			}
			else
			{
				NVRSYSDEBUG("dev usr data already change !!\n");
			}
	    }
	}

	///<如果是IPC老设备,则需要更改设备名称
	if(NVR_SYS_UPGRADE_PKG_HEAD_TYPE_IPC == g_tSysCap.tNvrCapSysBasic.ePkgType)
	{
		mzero(g_tDevInfo.achDevType);

		TNvrBrdPrdInfo tPrdInfo;
		mzero(tPrdInfo);

		 /**获取e2prom信息*/
	    eRet = NvrBrdApiBrdPinfoQuery(&tPrdInfo);
		if (NVR_ERR__OK != eRet)
	    {
	        NVRSYSERR("BrdPinfoQuery fail,ret:%d\n",eRet);
	    }
	    else
	    {
			memcpy(g_tDevInfo.achDevType, (char *)(tPrdInfo.abyUserData + 17), MIN(sizeof(g_tDevInfo.achDevType), tPrdInfo.abyUserData[16]));
	    }
	}

    
	///<如果是有定制设备类型，则从能力中获取默认设备名
	if(tNvrSysDefaultCfg.tNvrSysCfg.byDefaultDevType)
	{
		memset(g_tDevInfo.achDevType, 0 ,NVR_MAX_STR32_LEN);
		strncpy(g_tDevInfo.achDevType, tNvrSysDefaultCfg.tNvrSysCfg.achDevType,sizeof(g_tDevInfo.achDevType));
		snprintf(g_tDevInfo.achDevType, NVR_MAX_STR32_LEN, "%s", tNvrSysDefaultCfg.tNvrSysCfg.achDevType);
	}
	///如果之前e2prom中userdata里设备名为空，则从能力中获取默认设备名
	if(0 == strlen(g_tDevInfo.achDevType))
	{
		strncpy(g_tDevInfo.achDevType, tNvrSysDefaultCfg.tNvrSysCfg.achDevType,sizeof(g_tDevInfo.achDevType));
	}


/*	printf("tNvrSysDefaultCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum:%u\n",tNvrSysDefaultCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum);

	for(i = 0 ; i < tNvrSysDefaultCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum; i++)
	{
		printf("abySyncTypeEnable[%d]:%d\n", i, tNvrSysDefaultCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable[i]);
		printf("aeSyncTypePri[%d]:%d\n", i, tNvrSysDefaultCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i]);		
	}
*/
	NvrSysGetRomVer((u8*)g_tDevInfo.achDevRomVer);

    ///创建恢复出厂字段
    eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_RECOVERY_KEY, &g_dwRecoveryFlag);
    NVRSYSFLASH("NVRCfgGetParam %s g_dwRecoveryFlag:0x%x eRet:%u\n", NVR_SYS_RECOVERY_KEY, g_dwRecoveryFlag, eRet);
    if(NVR_ERR__OK != eRet)
    {
        g_dwRecoveryFlag = g_dwRecoveryFlag + (0x01 << NVR_CFG_RESET_NO);
        NVRSYSFLASH("NVRCfgGetParam failed g_dwRecoveryFlag:0x%x\n", g_dwRecoveryFlag);
        eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_RECOVERY_KEY, &g_dwRecoveryFlag, sizeof(g_dwRecoveryFlag));
        if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NVRCfgSetParam recovery param failed ret:%d\n", eRet);
			return NVR_ERR__ERROR;
		}
    }

   	do
	{
	    NvrSysGetRecoveryFlag(&eRecoveryType,NVR_BASE_PARAM_CFG_RESET);
		///初始化系统模块配置参数，获取失败则创建默认配置
		eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, NVR_SYS_CFG, &dwSysCfgBuflen);
        if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
		{
			NVRSYSDEBUG("get unpack size :%lu \n", dwSysCfgBuflen);

			pbySysCfgBuf = (u8 *)NVRALLOC(dwSysCfgBuflen);
			if(NULL == pbySysCfgBuf)
			{
				NVRSYSERR("malloc SysCfgBuf failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}

			eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_CFG, pbySysCfgBuf);
			if(NVR_ERR__OK == eRet)
			{
				ptPbNvrSysCfg = tpb_nvr_sys_cfg__unpack(tPbcSimple.allocator, dwSysCfgBuflen, pbySysCfgBuf); 			///<反序列化动作
			}
			else
			{
				NVRSYSERR("NVRCfgGetParam get %s failed\n", NVR_SYS_CFG);
				break;
			}
			NvrSysCfgProtoToStruct(ptPbNvrSysCfg);


	 		tpb_nvr_sys_cfg__free_unpacked(ptPbNvrSysCfg, tPbcSimple.allocator);	///<释放ptPbNvrSysCfg

            NvrSysInitKtsmGpSecurity(FALSE);
		}
		else
		{

			NVRSYSDEBUG("create default cfg\n");
            ///<ktsm恢复
            NvrSysInitKtsmGpSecurity(TRUE);

			///获取配置失败，创建默认配置
			NvrSysCfgDefaultInit();

			///序列化动作
			eRet = NvrSysCfgStructToProto(&tPbcSimple);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysCfgStructToProto failed ret:%d\n", eRet);
				break;
			}

			///默认配置写入配置文件中
			eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_CFG, tPbcSimple.data, tPbcSimple.len);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
				break;
			}
			
		}
	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	///释放pbySysCfgBuf空间
	if(pbySysCfgBuf != NULL)
	{
	   NVRFREE(pbySysCfgBuf);
	   pbySysCfgBuf = NULL;
	}

	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("success\n");
	}
	else
	{
	   NVRSYSDEBUG("failed ret:%d\n", eRet);
	}
#ifndef __I18N__

    //执法仪设备信息，用户信息从jni层获取
    TNvrCapHwCapInfo tHwCapInfoDsj;
    mzero(tHwCapInfoDsj);
    NvrCapGetCapParam(NVR_CAP_ID_HW, &tHwCapInfoDsj);
    if (NVR_DEV_TYPE_DSJ == tHwCapInfoDsj.eDevType)
    {
        TNvrJniMsgInfo tCBInfo;
        mzero(tCBInfo);

        ///硬件版本
        T_DSJ_COM_PARAM tzfyParam = {{0}};
        memset(&tzfyParam, 0, sizeof(T_DSJ_COM_PARAM));
        tzfyParam.nMsgID = MSG_GET_SYS_HARDWARE_VER;

        tCBInfo.pData = &tzfyParam;
        tCBInfo.wMsgType = EV_NOTIFY_JAVA_MSG;
        tCBInfo.nSize = sizeof(T_DSJ_COM_PARAM);
        NvrSysSndJniMsg(&tCBInfo, __FUNCTION__, __LINE__);
        snprintf(g_tDevInfo.achDevHWVer, sizeof(g_tDevInfo.achDevHWVer), "%s", tzfyParam.chData);

        ///设备序列号
        T_DSJ_COM_PARAMEX tzfyParamEx;
        T_DSJ_INFO_LOCAL tParam;
        mzero(tParam);
        mzero(tCBInfo);
        memset(&tzfyParamEx, 0, sizeof(T_DSJ_COM_PARAMEX));
        tzfyParamEx.nMsgID = EV_JNIMSG_DSJ_TYPE_GET_INFO;
        snprintf(tzfyParamEx.chPwd, sizeof(tzfyParamEx.chPwd), "##_kedacom_ipw_##");

        tCBInfo.pData = &tzfyParamEx;
        tCBInfo.wMsgType = EV_NOTIFY_JAVA_MSG;
        tCBInfo.nSize = sizeof(T_DSJ_COM_PARAMEX);
        NvrSysSndJniMsg(&tCBInfo, __FUNCTION__, __LINE__);
        memcpy(&tParam, tzfyParamEx.chData, sizeof(T_DSJ_INFO_LOCAL));
        snprintf(g_tDevInfo.achDevSerialNum, sizeof(g_tDevInfo.achDevSerialNum), "%s", tParam.cSerialAll);
        snprintf(g_tDevInfo.achIMEI, sizeof(g_tDevInfo.achIMEI), "%s", tParam.cIMEI);
		
        NVRSYSDEBUG("DevRomVer:%s DevHWVer:%s DevSerialNum:%s \n", g_tDevInfo.achDevRomVer, g_tDevInfo.achDevHWVer, g_tDevInfo.achDevSerialNum);
    }
#endif
	///<ktcp参数设置
	if(!access(NVR_SYS_KTCP_WORK_PATH, F_OK))
	{
		///<设置ktcp能力集
		g_tSysCap.bKTCPSup = TRUE;
		g_tSysCap.tNvrCapAdvance.tKtcpCapInfo.byKtcpSup = TRUE;
		eRet = NvrCapSetCapParam(NVR_CAP_ID_SYS, (void *)&g_tSysCap);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("set NVR_CAP_ID_SYS cap failed, eRet:%d\n", eRet);
			return eRet;
		}

		///<ktcp端口应用范围和带宽应用范围参数，界面暂不支持设置，业务不进行设置，采用crn默认值--2020-08-04
		NvrSysSetKtcpParam(&g_tNvrSysCfg.tAdvanceParam.tKtcpParam); ///<仅设置使能
		
		///<屏蔽ssh，ftp，telnet端口
		///<先获取默认值
		TNvrSysKtcpParam tKtcpParam;
		s8 achKtcpPorts[NVR_MAX_STR64_LEN];
		s8 achCommand[NVR_MAX_STR128_LEN];
		
		mzero(tKtcpParam);
		NvrSysGetKtcpParam(&tKtcpParam);
        u8 byStrlen = strlen(tKtcpParam.achKtcpPorts);
        NVRSYSIMP("ports:%s, strlen:%u\n", tKtcpParam.achKtcpPorts, byStrlen);
        if(tKtcpParam.achKtcpPorts[byStrlen-1] == '\n')
        {
            tKtcpParam.achKtcpPorts[byStrlen-1] = '\0';
        }
        NVRSYSERR("ports:%s", tKtcpParam.achKtcpPorts);
        snprintf(achKtcpPorts, NVR_MAX_STR64_LEN, "%s,#17230,#16666,#2277,#22,#23,#10023", tKtcpParam.achKtcpPorts);
        NVRSYSERR("ktcpports:%s\n", achKtcpPorts);
        
#ifndef _QCOM_
        s32 nRet = 0;
        snprintf(achCommand, 128, "echo \"%s\" > %s", achKtcpPorts, NVR_SYS_KTCP_PORTS_RANGE_CFG);
        NVRSYSIMP("ktcp command:%s\n", achCommand);
        nRet = NvrSystem(achCommand);
        NVRSYSERR("command:%s nRet:%d\n", achCommand,nRet);
#else		
		TNvrSuperFile tSuperFile;
		mzero(tSuperFile);
		///<ktcp端口范围
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_PORTS_RANGE_CFG);
		snprintf(tSuperFile.szContent, NVR_MAX_STR1024_LEN, "%s", achKtcpPorts);
		NvrSysWriteSuperFile(&tSuperFile);
#endif

	}
	///时间模块初始化
	eRet = NvrSysTimeInit();
	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("NvrSysTimeInit success\n");
	}
	else
	{
	   NVRSYSDEBUG("NvrSysTimeInit failed ret:%d\n", eRet);
	}
    eRet = NvrSysAutoRebootInit();
	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("NvrSysAutoRebootInit success\n");
	}
	else
	{
	   NVRSYSDEBUG("NvrSysAutoRebootInit failed ret:%d\n", eRet);
	}


	eRet = NvrSysPtzMainTainAutoRebootInit();
	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("NvrSysPtzMaintainAutoRebootInit success\n");
	}
	else
	{
	   NVRSYSDEBUG("NvrSysPtzMaintainAutoRebootInit failed ret:%d\n", eRet);
	}

	
	eRet =  NvrSysLenAutoMainTainInit();
	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("NvrSysLenAutoMainTainInit success\n");
	}
	else
	{
	   NVRSYSDEBUG("NvrSysLenAutoMainTainInit failed ret:%d\n", eRet);
	}
	eRet =  NvrSysPowerWasteModeInit();
	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("NvrSysPowerWasteModeInit success\n");
	}
	else
	{
	   NVRSYSDEBUG("NvrSysPowerWasteModeInit failed ret:%d\n", eRet);
	}
	TNvrCapHwCapInfo tHwCapInfo;
	mzero(tHwCapInfo);
	eRet = NvrCapGetCapParam(NVR_CAP_ID_HW, (void*)&tHwCapInfo);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get sys cap   NVR_CAP_ID_HW failed, ret:%d\n", eRet);
    	return eRet;
    }

    if(FALSE == g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport)
    {
        TNvrCapNvrRecInfo tRecCapInfo;
        mzero(tRecCapInfo);
    	eRet = NvrCapGetCapParam(NVR_CAP_ID_REC, &tRecCapInfo);
        if(NVR_ERR__OK != eRet)
        {
    		NVRSYSERR("get sys cap   NVR_CAP_ID_REC failed, ret:%d\n", eRet);
        	return eRet;
        }
        tRecCapInfo.abyNvrCapRecStreamCap[ENVR_REC_THIRD_STREAM_PRIORITY] = FALSE;
    	eRet = NvrCapSetCapParam(NVR_CAP_ID_REC, &tRecCapInfo);
        if(NVR_ERR__OK != eRet)
        {
    		NVRSYSERR("set sys cap failed, ret:%d\n", eRet);
        	return eRet;
        }
    }

	strncpy(g_tNvrSysCfg.tNvrSysParam.achPreDevType, tNvrSysDefaultCfg.tNvrSysCfg.achPreDevType,sizeof(g_tNvrSysCfg.tNvrSysParam.achPreDevType));

	///<注册给守卫线程，周期性检测任务
	TNvrGuardTaskParam tTaskParam;
	tTaskParam.dwCheckTime = 1;
	tTaskParam.bEnable = TRUE;

	///支持关机检测
	if(NVR_CAP_SUPPORT == tHwCapInfo.byShutdownButtonCheck)
	{
		///开启关机按钮检测
	    tTaskParam.pfGuardDealCB = NvrSysCheckShutDown;
	    strncpy(tTaskParam.achTaskName,"NvrSysCheckShutDown",sizeof(tTaskParam.achTaskName));
	    NvrGuardTaskRegist(&tTaskParam);
	}

    strncpy(tTaskParam.achTaskName,"NvrSysAutoRebootDeal",sizeof(tTaskParam.achTaskName));
    tTaskParam.pfGuardDealCB = NvrSysAutoRebootDeal;
    NvrGuardTaskRegist(&tTaskParam);

	
    strncpy(tTaskParam.achTaskName,"NvrSysPtzMaintainAutoRebootDeal",sizeof(tTaskParam.achTaskName));
    tTaskParam.pfGuardDealCB = NvrSysPtzMaintainAutoRebootDeal;
    NvrGuardTaskRegist(&tTaskParam);
	
	///镜头维护检测
	mzero(tCapSoftware);	
	NvrCapGetCapParam(NVR_CAP_ID_SOFTWARE, &tCapSoftware);
	if(NVR_CAP_SUPPORT == tCapSoftware.bySupLenMaintain)
	{
		tTaskParam.pfGuardDealCB = NvrSysLenAutoMaintainDeal;
		strncpy(tTaskParam.achTaskName,"NvrSysLenAutoMaintainDeal",sizeof(tTaskParam.achTaskName));
		NvrGuardTaskRegist(&tTaskParam);
	}

	///<支持静音按键，长按4s消除告警声音
	if(NVR_CAP_SUPPORT == tHwCapInfo.byMuteButtonSup)
	{
		tTaskParam.dwCheckTime = 1;  ///<1s检测一次
		tTaskParam.bEnable = TRUE;
		tTaskParam.pfGuardDealCB = NvrSysCheckMuteDown;
	    strncpy(tTaskParam.achTaskName,"NvrSysCheckMuteDown",sizeof(tTaskParam.achTaskName));
	    NvrGuardTaskRegist(&tTaskParam);
	}


	NvrSysTimeRegfun();

	///首次调用获取cpu 内存会耗时，故先调用一次，避免页面卡顿
	OsApi_GetCpuInfo(&tCpuInfo);
	OsApi_GetMemInfo(&tMemInfo);

	///初始化升级服务器地址和端口
	NvrSysSetUpgradeServerInfo(&g_tNvrSysCfg.tUpgradeServerParam);

/*
    ///张从静要求暂时屏蔽升级电灯功能
    if (0 != OsApi_TimerNew(&g_hUpdateLedTimer))
	{
		NVRSYSERR("NvrSysInit create g_hUpdateLedTimer failed\n");
		return NVR_ERR__ERROR;
	}*/	

    ///获取磁盘能力，是否进行延迟上电处理
    TNvrCapDmsrvInfo tNvrCapDmsrvInfo;
    memset(&tNvrCapDmsrvInfo, 0, sizeof(tNvrCapDmsrvInfo));
    NvrCapGetCapParam(NVR_CAP_ID_DMSRV, &tNvrCapDmsrvInfo);
    if(tNvrCapDmsrvInfo.byPowerOnDelaySup)
    {
        ///创建上电线程
        //创建SD卡状态检测线程
        if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSysDiskPowerOnThread, "NvrSysDiskPowerOnThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
        {
            NVRSYSERR("NvrSysDiskPowerOnThread create failed\n");
            return NVR_ERR__PARAM_INVALID;
        }
    }

	if (NVR_CAP_SUPPORT == tHwCapInfo.bySupStaicProtect)
	{
		//创建静电检测保护线程
		if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSysStaticProtectThread, "NvrSysStaticProtectThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
		{
			NVRSYSERR("NvrSysStaticProtectThread create failed\n");
			return NVR_ERR__PARAM_INVALID;
		}
	}


	///<初始化gps
	//NvrNetSetGpsEnabled(g_tNvrSysCfg.tGeographyPosParam.bOpenGps, g_tNvrSysCfg.tGeographyPosParam.eGpsLocMode);
	
#ifdef _QCOM_
		if (0 != OsApi_TimerNew(&g_hExceptionPower))
		{
			NVRSYSERR("create reg cb cb failed\n");
			return NVR_ERR__ERROR;
		}
		OsApi_TimerSet(g_hExceptionPower, 300*1000, NvrSysAutoWriteExcepitonRebootTimerCB, NULL);
#endif


	///<系统健康初始化
	if(TRUE == g_tSysCap.tSysHealth.bSupSysHealth)
	{	
		do 
		{
			///<初始化数据库
			eRet = NvrSysHealthInit(g_tSysCap.tSysHealth.achDbPath);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysHealthInit failed, eRet = %d, back up SysHealth.db!!\n", eRet);
				break;			
			}

			///<判断上次关机状态
			if(0 == access(g_tSysCap.tSysHealth.achFlagPath, 0))
			{
				eRet = NvrSysJudgeShutdownStatus(g_tSysCap.tSysHealth.achFlagPath);
				if(NVR_ERR__OK != eRet)
				{
					NVRSYSERR("NvrSysJudgeShutdownStatus err, eRet = %d, back up SysHealth.db !!\n", eRet);
					break;
				}
			}

		}while(0);

		if(NVR_ERR__OK != eRet)
		{
			eRet = NvrSysHealthDbBackUp(g_tSysCap.tSysHealth.achFlagPath);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysHealthDbBackUp failed, eRet = %d !!\n", eRet);
			}
		}
		
		if(NVR_ERR__OK == eRet)
		{

			if(0 != access(g_tSysCap.tSysHealth.achFlagPath, 0))
			{
				pFile = fopen(g_tSysCap.tSysHealth.achFlagPath, "w+");
				if(NULL == pFile)
				{
					NVRSYSERR(
"open %s failed !!\n", g_tSysCap.tSysHealth.achFlagPath);
				}
				else
				{				
					fclose(pFile);
					pFile = NULL;
				}
			}
			
			///<更新标志内容
			eRet = NvrSysHealthUpdateFlag(NVR_SYS_HEALTH_FLAG_REGULARLY_UPDATE, NULL);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysHealthUpdateFlag failed, eRet = %d !!\n", eRet);
			}

			
			///<注册守卫任务,定时更新
			TNvrGuardTaskParam tHealthTaskParam;
			mzero(tHealthTaskParam);
			tHealthTaskParam.dwCheckTime = NVR_SYS_HEALTH_FLAG_MINUTE_INTERVAL*60;
			tHealthTaskParam.bEnable = TRUE;
			

			tHealthTaskParam.pfGuardDealCB = NvrSysHealthCheckFlag;
			strncpy(tHealthTaskParam.achTaskName,"NvrSysHealthCheckFlag",sizeof(tHealthTaskParam.achTaskName));
			NvrGuardTaskRegist(&tHealthTaskParam);
		}
		else
		{
			eRet = NVR_ERR__OK;
		}
	}

    if (TRUE == g_tSysCap.tNvrCapAdvance.tAdvancePingInfo.byPingSup)
    {
        NvrSysSetPingParam(&g_tNvrSysCfg.tAdvanceParam.tPingParam);
    }

	if(!g_tSysCap.bSecurityLevel)
	{
		g_tNvrSysCfg.tNvrSysParam.eSecurityLevel = NVR_DES_SECURITY_LEVEL_MAX;
	}
	
	if(g_tSysCap.bSecurityLevel)
	{
		NvrSystem("iptables -I INPUT -p tcp --dport 80 -j REJECT");		
		NvrSystem("iptables -I INPUT -p tcp --dport 443 -j REJECT");
	}
	NvrSysPortRecord();
	NvrQueueCreate(&g_ptQueueTarQueue);
	NvrSysTarLogInit();
    DebugLogSetToStdioPrintf(DEBUG_LOG_MOD_SYS, LOG_LEVEL_ERR);			///<调整标准输出打印级别
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_SYS, LOG_LEVEL_ERR, TRUE);
	return eRet;
}

NVRSTATUS NvrSysTarLogInit(void)
{
	if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSysTarLogThread, "NvrSysTarLogThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
	{
		NVRSYSERR("NvrSysTarLogThread create failed\n");
		return NVR_ERR__PARAM_INVALID;
	}
	return NVR_ERR__OK;
}

NVRSTATUS NvrReadKshieldErrLogInit(void)
{

	//创建签名保护日志检测线程
	if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrReadKshieldErrLogThread, "NvrReadKshieldErrLogThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
	{
		NVRSYSERR("NvrReadKshieldErrLogThread create failed\n");
		return NVR_ERR__PARAM_INVALID;
	}
	return NVR_ERR__OK;
}

void NvrSysHealthCheckFlag()
{
	NVRSTATUS eRet = NVR_ERR__OK;
	FILE *pFile = NULL;
	TNvrSysHealthFlag tFlag;

	mzero(tFlag);

	pFile = fopen( g_tSysCap.tSysHealth.achFlagPath, "rb+");
	if(NULL == pFile)
	{
		NVRSYSERR("open %s failed !!\n",  g_tSysCap.tSysHealth.achFlagPath);
		return;
	}

	fread(&tFlag, sizeof(tFlag), 1, pFile);

	fclose(pFile);
	pFile = NULL;

	if(NVR_SYS_HEALTH_FLAG_REGULARLY_UPDATE == tFlag.eFlagtype)
	{
		eRet = NvrSysHealthUpdateFlag(NVR_SYS_HEALTH_FLAG_REGULARLY_UPDATE, NULL);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthUpdateFlag %s failed, eRet = %d !!\n", g_tSysCap.tSysHealth.achFlagPath);
		}
	}

	return;
}


NVRSTATUS NvrSysHealthUpdateFlag(ESysHealthFlagType eFlag, TNvrSysShutDownInfo *ptInfo)
{
	NVRSYSMEMAPI();
	FILE *pFile = NULL;
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrSysHealthFlag tFlag;
	TNvrBrokenDownTime tUtcTime;	///<UTC时间
	u32 dwUtcTime = 0;

	mzero(tFlag);
	mzero(tUtcTime);

	pFile = fopen(g_tSysCap.tSysHealth.achFlagPath, "rb+");
	if(NULL == pFile)
	{
		NVRSYSERR(
"open %s failed !!\n", g_tSysCap.tSysHealth.achFlagPath);
		return NVR_ERR__ERROR;
	}
	
	eRet = NvrSysGetSystemTime(&tUtcTime);
	if(NVR_ERR__OK != eRet)
	{
		fclose(pFile);
		NVRSYSERR("Get utc time err, eRet = %d !!\n", eRet);
		return eRet;
	}
	
	eRet = NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT, &dwUtcTime, &tUtcTime);
	if(NVR_ERR__OK != eRet)
	{
		fclose(pFile);
		NVRSYSERR("NvrSysTimeBdtUintSwitch failed, eRet = %d !!\n", eRet);
		return eRet;
	}
	
	tFlag.dwUtcTime = dwUtcTime;
	tFlag.eFlagtype = eFlag;
	if(NULL != ptInfo)
	{
		memcpy(tFlag.achDescription, ptInfo->achDescription, MIN((strlen(ptInfo->achDescription)+1),(sizeof(tFlag.achDescription))));
		memcpy(tFlag.achOperator, ptInfo->achOperator, MIN((strlen(ptInfo->achOperator)+1),(sizeof(tFlag.achOperator))));
	}
	
	fwrite(&tFlag, sizeof(tFlag), 1, pFile);
	fclose(pFile);

	return NVR_ERR__OK;
}


NVRSTATUS NvrSysJudgeShutdownStatus(char achFlagPath[])
{
	FILE *pFile = NULL;
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrSysHealthFlag tFlag;
	ESysHeathRecordType eRecordType = NVR_SYS_HEALTH_RECORD_POWER_DOWN;
	BOOL32 bAbNormal = FALSE;
	char achFileName[NVR_MAX_STR512_LEN] = {0};
	struct dirent *pDirent = NULL;
	DIR *pDir;
	struct stat tFileInfo;
	s32 nRet = 0;
	time_t dwTime = 0;
	u32 dwDelayTime = 0;

	mzero(tFlag);
	mzero(tFileInfo);
	
	do 
	{
		pFile = fopen(achFlagPath, "rb+");
		if(NULL == pFile)
		{
			NVRSYSERR("open %s failed !!\n", achFlagPath);
			eRet = NVR_ERR__ERROR;
			break;
		}

		fread(&tFlag, sizeof(tFlag), 1, pFile);

		fclose(pFile);
		pFile = NULL;

		///< 判断是否是通过 ipdt 重启
		pFile = fopen(NVR_SYS_HEALTH_IPDT_FLAG_FILE, "r");
		if(NULL != pFile)
		{
			tFlag.eFlagtype = NVR_SYS_HEALTH_FLAG_NORMAL_RESTART;
			snprintf(tFlag.achOperator, sizeof(tFlag.achOperator), "admin");
			fgets(tFlag.achDescription, sizeof(tFlag.achDescription), pFile);
			fclose(pFile);
			pFile = NULL;
			remove(NVR_SYS_HEALTH_IPDT_FLAG_FILE);
		}

		if(NVR_SYS_HEALTH_FLAG_REGULARLY_UPDATE == tFlag.eFlagtype)
		{
			///<非正常关机,判断是异常重启还是掉电关机
			///<如果是HIS平台,会在/usr/log下生成debug_output文件,如果是高通平台,会在/data/tombstones目录下生成tombstone_*文件
			///<读取这些文件最近的改动时间,与标志中记录的时间进行对比
			if(0 == access("/usr/log",0))
			{
				pDir = opendir("/usr/log");
				if(NULL == pDir)
				{
					NVRSYSERR("Open dir %s failed !!\n", "/usr/log");
					eRet = NVR_ERR__ERROR;
					break;
				}

				///<获取包含"debug_output"字符的文件最新的改动时间
				while((pDirent = readdir(pDir)) != NULL)
				{
					if(0 == strcmp(pDirent->d_name, ".") || 0 == strcmp(pDirent->d_name,".."))	///<当前路径和上层路径
					{
						continue;
					}
					else if(pDirent->d_type == 8)	///<文件
					{
						if(NULL != strstr(pDirent->d_name, "debug_output"))
						{
							mzero(achFileName);
							snprintf(achFileName, sizeof(achFileName), "/usr/log/%s", pDirent->d_name);
							mzero(tFileInfo);
							nRet = stat(achFileName, &tFileInfo);
							if(-1 == nRet)
							{
								NVRSYSERR("Get [%s] stat failed !!\n", achFileName);
								return NVR_ERR__ERROR;
							}

							if(dwTime < tFileInfo.st_mtime)
							{
								dwTime = tFileInfo.st_mtime;
							}
						}
					}
				}

				if(tFlag.dwUtcTime > dwTime)
				{
					dwDelayTime = tFlag.dwUtcTime - dwTime;
				}
				else
				{
					dwDelayTime = dwTime - tFlag.dwUtcTime;
				}

				NVRSYSDEBUG("last modify time:[%lu], flag utc time:[%lu], delaytime:[%lu] !!\n", dwTime, tFlag.dwUtcTime, dwDelayTime);
				
				if(dwDelayTime <= NVR_SYS_HEALTH_FLAG_MINUTE_INTERVAL * 60)
				{
					bAbNormal = TRUE;
				}

				NVRSYSDEBUG("open /usr/log, bAbNormal:[%d] !!\n", bAbNormal);
				closedir(pDir);
				
			}
			else if(0 == access("/data/tombstones",0))
			{
				pDir = opendir("/data/tombstones");
				if(NULL == pDir)
				{
					NVRSYSERR("Open dir %s failed !!\n", "/data/tombstones");
					eRet = NVR_ERR__ERROR;
					break;
				}

				///<获取包含"debug_output"字符的文件最新的改动时间
				while((pDirent = readdir(pDir)) != NULL)
				{
					if(0 == strcmp(pDirent->d_name, ".") || 0 == strcmp(pDirent->d_name,".."))	///<当前路径和上层路径
					{
						continue;
					}
					else if(pDirent->d_type == 8)	///<文件
					{
						if(NULL != strstr(pDirent->d_name, "tombstone"))
						{
							mzero(achFileName);
							snprintf(achFileName, sizeof(achFileName), "/data/tombstones/%s", pDirent->d_name);
							
							mzero(tFileInfo);
							nRet = stat(achFileName, &tFileInfo);
							if(-1 == nRet)
							{
								NVRSYSERR("Get [%s] stat failed !!\n", achFileName);
								return NVR_ERR__ERROR;
							}

							if(dwTime < tFileInfo.st_mtime)
							{
								dwTime = tFileInfo.st_mtime;
							}
						}
					}
				}

				if(tFlag.dwUtcTime > dwTime)
				{
					dwDelayTime = tFlag.dwUtcTime - dwTime;
				}
				else
				{
					dwDelayTime = dwTime - tFlag.dwUtcTime;
				}

				NVRSYSDEBUG("last modify time:[%lu], flag utc time:[%lu], delaytime:[%lu] !!\n", dwTime, tFlag.dwUtcTime, dwDelayTime);
				
				if(dwDelayTime <= NVR_SYS_HEALTH_FLAG_MINUTE_INTERVAL * 60)
				{
					bAbNormal = TRUE;
				}

				NVRSYSDEBUG("open /data/tombstones, bAbNormal:[%d] !!\n", bAbNormal);
				closedir(pDir);
			}

			if(TRUE == bAbNormal)
			{
				eRecordType = NVR_SYS_HEALTH_RECORD_ABNORMAL_RESTART;
				tFlag.dwUtcTime = dwTime;
				snprintf(tFlag.achDescription, sizeof(tFlag.achDescription), "Abnormal Restart");
			}
			else
			{
				eRecordType = NVR_SYS_HEALTH_RECORD_POWER_DOWN;
				snprintf(tFlag.achDescription, sizeof(tFlag.achDescription), "Power Down");
			}
			
			snprintf(tFlag.achOperator, sizeof(tFlag.achOperator), "Local");
		}
		else 
		{
			if(NVR_SYS_HEALTH_FLAG_NORMAL_SHUTDOWN == tFlag.eFlagtype)
			{
				eRecordType = NVR_SYS_HEALTH_RECORD_NORMAL_SHUTDOWN;
			}
			else if(NVR_SYS_HEALTH_FLAG_NORMAL_RESTART == tFlag.eFlagtype)
			{
				eRecordType = NVR_SYS_HEALTH_RECORD_NORMAL_RESTART;
			}
		}
	
		eRet = NvrSysHealthAddRecord(tFlag.dwUtcTime, eRecordType, tFlag.achDescription, tFlag.achOperator);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthAddRecord failed, eRet = %d !!\n", eRet);
			break;
		}	
	
	}while(0);

	return eRet;
}

///<获取系统健康状态信息
NVRSTATUS NvrSysGetHealthInfo(ESysHealthTimeType eTimeType, TNvrSysHealthInfo *ptHealthInfo)
{
	NVRSYSMEMAPI();
	NVRSYSMEMAPI();
	u32 i = 0, j = 0; 
	NVRSTATUS eRet = NVR_ERR__OK;
	ASSERTSURE(ptHealthInfo);
	
	///<读出最近的记录
	j = eTimeType;
	for(i = 0; i < NVR_SYS_HEALTH_RECORD_COUNT; i++)
	{
		if(NULL != g_tHealhRecord.atRecord[i][j].pdwRecordKey)
		{
			NVRFREE(g_tHealhRecord.atRecord[i][j].pdwRecordKey);
			g_tHealhRecord.atRecord[i][j].pdwRecordKey = NULL;
		}
			
		eRet = NvrSysHealthGetRecord(i, j+1, &g_tHealhRecord.atRecord[i][j].dwRecordNum, &g_tHealhRecord.atRecord[i][j].pdwRecordKey);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthGetRecord failed, eRet = %d !!\n", eRet);
			return eRet;
		}		
	}

	for(i = 0; i < NVR_SYS_HEALTH_RECORD_COUNT; i++)
	{
		ptHealthInfo->adwRecordNum[i] = g_tHealhRecord.atRecord[i][eTimeType].dwRecordNum;
	}

	return NVR_ERR__OK;
}

NVRSTATUS NvrSysGetHealthDetails(ESysHealthTimeType eTimeType, ESysHeathRecordType eRecordType, u32 dwStartIndex, u32 *pdwDetailNum, TNvrSysHealthDetail atDetail[])
{
	//u32 dwPriKey = 0;
	u32 i = 0, j = 0;
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYSDEBUG("dwRecordNum:[%lu], dwStartIndex:[%lu], *pdwDetailNum:[%lu] !!\n",
				g_tHealhRecord.atRecord[eRecordType][eTimeType].dwRecordNum, dwStartIndex, *pdwDetailNum);

	if(NULL == g_tHealhRecord.atRecord[eRecordType][eTimeType].pdwRecordKey)
	{
		NVRSYSERR("pdwRecordKey is null !!\n");
		return NVR_ERR__ERROR;		
	}

	if(dwStartIndex >= g_tHealhRecord.atRecord[eRecordType][eTimeType].dwRecordNum)
	{
		NVRSYSERR("dwStartIndex too big, err !!\n");
		*pdwDetailNum = 0;
		return NVR_ERR__ERROR;
	}
	else if(dwStartIndex + *pdwDetailNum > g_tHealhRecord.atRecord[eRecordType][eTimeType].dwRecordNum)
	{
		NVRSYSDEBUG("change *pdwDetailNum !!\n");
		*pdwDetailNum = g_tHealhRecord.atRecord[eRecordType][eTimeType].dwRecordNum - dwStartIndex;
	}

	
	j = *pdwDetailNum - 1;
	for(i = dwStartIndex; i < (dwStartIndex + *pdwDetailNum); i++)
	{		
		eRet = NvrSysHealthGetRecordDetailByKey(g_tHealhRecord.atRecord[eRecordType][eTimeType].pdwRecordKey[i], &atDetail[j]);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthGetRecordDetailByKey failed, eRet = %d !!\n", eRet);
			break;
		}
		//snprintf(atDetail[j].achSrc, sizeof(atDetail[j].achSrc), "Local");		
		NVRSYSDEBUG("Prikey:[%lu] !!\n", g_tHealhRecord.atRecord[eRecordType][eTimeType].pdwRecordKey[i]);
		j--;
	}

	
	return eRet;
}

/**
* @brief		获取寿命状态详情
* @param[out]	TNvrLifeStat *ptLifeStat 寿命状态
* @return 	成功		  NVR_ERR__OK
			失败		参考错误码
* @ref		nvrdef.h
*/
NVRSTATUS NvrSysGetLifeStat(TNvrLifeStat *ptLifeStat)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;

	return eRet;
}


void NrvSysGbStatusChangeCallBack(u32 dwIndex, u32 dwState, void *pvContext)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i=  0;
	BOOL32 bLight = FALSE;
	if (dwIndex >= NVR_SYS_GB_PLATFORM_MAX_NUM)
	{
		NVRSYSDEBUG("index invalid:%lu\n", dwIndex);
		return;
	}

	TNvrCapHwCapInfo tHwCapInfo;
	mzero(tHwCapInfo);
	eRet = NvrCapGetCapParam(NVR_CAP_ID_HW, (void*)&tHwCapInfo);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get sys cap   NVR_CAP_ID_HW failed, ret:%d\n", eRet);
    	return;
    }

	NVRSYSDEBUG("index:%lu stat:%lu\n", dwIndex, dwState);

	///<gb注册成功的状态（改枚举为app内部定义，业务无法访问app头文件）
	if (4 == dwState)
	{
		abyGbRegStat[dwIndex] = 1;
	}
	else
	{
		abyGbRegStat[dwIndex] = 0;
	}

	///<有一个平台注册成功则点亮
	for (i = 0; i < NVR_SYS_GB_PLATFORM_MAX_NUM; i++)
	{
		if (abyGbRegStat[i] != 0)
		{
			bLight = TRUE;
			break;
		}
	}

	NVRSYSDEBUG("set led link:%d\n", bLight);
	if (bLight)
	{
		if (g_pfGbStateCB)
		{
			g_pfGbStateCB();
		}
		if (tHwCapInfo.bySupPlatformRegLight)
		{
			NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_ON);
		}
	}
	else
	{
		if (tHwCapInfo.bySupPlatformRegLight)
		{
			NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_OFF);
		}
	}
	
	return;
}


NVRSTATUS NvrSysRegGbCBInit(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	EAppResult eAppRet = APP_OK;
	EAppResult eShareRet = APP_OK;
	u32 dwGbStatueCBId = 0;
	u32 dwPlatIndex = 0;
	BOOL32 bIsRegPlatSuc = FALSE;
	BOOL32 bLight = FALSE;

	TGbMgmSetRegStatusMdyCbParam tGbSetRegStatusCbParam;

	mzero(tGbSetRegStatusCbParam);


	///<注册平台注册状态变化的回调通知
	tGbSetRegStatusCbParam.bSet = TRUE;
	tGbSetRegStatusCbParam.pfCb = NrvSysGbStatusChangeCallBack;
    eAppRet = AppRunShareAction(GBMANAGEMENT_SET_REG_STATUS_MDY_CALLBACK, &tGbSetRegStatusCbParam, &dwGbStatueCBId, NULL, &eShareRet);
    if(APP_OK != eAppRet || APP_OK != eShareRet)
	{
		NVRSYSERR("set reg mdy failed apireg:%d share ret:%d\n", eAppRet, eShareRet);
		return NVR_ERR__ERROR;
	}

	NVRSYSDEBUG("set reg mdy success\n");

	///<获取注册状态
	for (i = 0; i < NVR_SYS_GB_PLATFORM_MAX_NUM; i++)
	{
		bIsRegPlatSuc = FALSE;
		dwPlatIndex = i;
		eAppRet = AppRunShareAction(GBMANAGEMENT_GET_REG_STATUS, &dwPlatIndex, &bIsRegPlatSuc, NULL, &eShareRet);
		if(APP_OK != eAppRet || APP_OK != eShareRet)
		{
			NVRSYSERR("get RegStatus failed, %d, %d\n", eAppRet, eShareRet);
			continue;
		}

		NVRSYSDEBUG("reg index:%lu stat:%d\n", i, bIsRegPlatSuc);

		if (bIsRegPlatSuc)
		{
			abyGbRegStat[i] = 1;
		}
		else
		{
			abyGbRegStat[i] = 0;
		}
	}

	TNvrCapHwCapInfo tHwCapInfo;
	mzero(tHwCapInfo);
	eRet = NvrCapGetCapParam(NVR_CAP_ID_HW, (void*)&tHwCapInfo);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get sys cap   NVR_CAP_ID_HW failed, ret:%d\n", eRet);
    	return eRet;
    }

	///<初始化注册灯光，有一个平台注册成功则点亮
	for (i = 0; i < NVR_SYS_GB_PLATFORM_MAX_NUM; i++)
	{
		if (abyGbRegStat[i] != 0)
		{
			bLight = TRUE;
			break;
		}
	}

	NVRSYSDEBUG("set led link:%d\n", bLight);
	if (bLight)
	{
		if (g_pfGbStateCB)
		{
			g_pfGbStateCB();
		}
		if (tHwCapInfo.bySupPlatformRegLight)
		{
			NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_ON);
		}
	}
	else
	{
		if (tHwCapInfo.bySupPlatformRegLight)
		{
			NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_OFF);
		}
	}

	return eRet;
}


s32 NvrSysLedLIghtCB( HTIMERHANDLE dwTimerId, void* param )
{
    static BOOL32 bLight = FALSE;
    if(FALSE == bLight)
    {
        bLight = TRUE;
        NvrSysSetPowerLedStat(TRUE);
    }
    else
    {
        bLight = FALSE;
        NvrSysSetPowerLedStat(FALSE);

    }
    OsApi_TimerSet(g_hUpdateLedTimer, 1*1000, NvrSysLedLIghtCB, NULL);
    return 0;
}


void NvrSysStartLedLight(BOOL32 bOpen)
{
	NVRSYSMEMAPI();
    ///张丛静要求 屏蔽暂时
    return;
    if( TRUE == bOpen)
    {
        OsApi_TimerSet(g_hUpdateLedTimer, 1*1000, NvrSysLedLIghtCB, NULL);
    }
    else
    {
        OsApi_TimerStop(g_hUpdateLedTimer);
        NvrSysSetPowerLedStat(TRUE);
    }
}

void NvrSysCheckShutDown()
{
    static u32 m_dwCount = 0;   ///计数
    u32 dwStatus = 0;           ///获取按钮状态
    static u32 dwMaxCheckCount = 0xff;

    ///避免重复获取
    if(0xff == dwMaxCheckCount)
    {
        TNvrCapHwCapInfo tHwCapInfo;
        mzero(tHwCapInfo);
	    NvrCapGetCapParam(NVR_CAP_ID_HW, &tHwCapInfo);
        dwMaxCheckCount = tHwCapInfo.byShutdownButtonCount;
    }

    NvrBrdApiBrdButtonGetStatus(BUTTON_ID_RST, &dwStatus);
    if(BUTTON_STATE_OFF == dwStatus)
    {
        m_dwCount++;
        NVRSYSDEBUG("dwMaxCheckCount :%lu\n");
        if(dwMaxCheckCount == m_dwCount)
        {
        	///<若复位按钮是恢复出厂则恢复,否则默认关机
        	if(g_tInterSysCap.bySupResetRecovery == NVR_CAP_SUPPORT)
    		{
				TNvrSysRecoverParam tRecoverParam;

				mzero(tRecoverParam);

				NVRSYSDEBUG("recovery\n");
				///<完全恢复出厂
		        tRecoverParam.dwRecoverCfgType |= (0x01 << NVR_CFG_RESET_ALL);				
		        NVRSTATUS eRet = NvrSysRecovery(&tRecoverParam);
		        if ( NVR_ERR__OK != eRet)
	            {
	                NVRSYSERR("NvrSysRecovery failed! eRet:%d\n",eRet);
	            }
				else
				{
					NVRSYSDEBUG("recovery suc\n");
					NvrSysReboot(NULL);
				}
    		}
			else
			{
	            void *pPluginHandle = NULL;
	            void (*pFunc)() = NULL;
	            pPluginHandle = dlopen(NVR_ROOT_PATH"bin/lib/libnvrgui.so", RTLD_LAZY|RTLD_GLOBAL);
	            if (NULL == pPluginHandle)
	            {
	                NVRSYSDEBUG("dlopen failed libnvrgui !\n");
	            }
	            else
	            {
	                NVRSYSDEBUG("dlopen succ !\n");
	                pFunc = dlsym(pPluginHandle, "ShutdownNotify");
	                if (NULL == pFunc)
	                {
	                    NVRSYSERR("dlsym ResolutionChangedNotify failed \n");
	                }
	                else
	                {
	                    ///通知GUI
	                    pFunc();
	                    OsApi_TaskDelay(4000);
	                    NVRSYSDEBUG("dlsym ResolutionChangedNotify succ \n");
	                }
	            }
	            TNvrSysShutDownInfo tInfo;
	            mzero(tInfo);

	            snprintf(tInfo.achOperator, sizeof(tInfo.achOperator), "Local");
	            NvrSysShutDown(&tInfo);
			}
        }
    }
    else
    {
        m_dwCount = 0;
    }
    return;
}


NVRSTATUS NvrSysStartApp()
{
    NVRSTATUS eRet = NVR_ERR__OK;
	char achBuffer[NVR_MAX_STR1024_LEN] = {""};
#ifndef WIN32
	u32 i = 0;
	u32 j = 0;
	BOOL32 bVsipExist = FALSE;//配置中是否有visp
	u32 index = 0; //配置中vsip所在的位置
	TNvrTimeCfg tTimeCfg;
	mzero(tTimeCfg);
    void *pAppHandle = NULL;
    void (*pAppStart)() = NULL;
    struct dirent    *pDirent = NULL;
    DIR   *dpDir = NULL;
    s8    achDirPath[NVR_MAX_STR256_LEN] = NVR_APP_LIB;
    s8    achLibPath[NVR_MAX_STR256_LEN] = "";
    static BOOL32 s_bAppStart = FALSE;
	TNvrCapSysInfo tCapSys;
	mzero(tCapSys);

    if (s_bAppStart)
    {
        NVRSYSDEBUG("app started,no to start again\n");
        return NVR_ERR__OK;
    }

#ifdef _QCOM_
	mzero(achDirPath);
	memcpy(achDirPath, NvrPuiGetApkShareLibPath(), NVR_MAX_STR256_LEN);
	if (0 == strlen(achDirPath))
	{
		NVRSYSERR("applib dir null line:%d,\n", __LINE__);
		return NVR_ERR__ERROR;
	}
#endif
	NvrCapGetDefaultCfgKeyLine(NVR_DEFAULT_CAP_CFG, "LIB_APP_NOT_LOAD", achBuffer, NVR_MAX_STR1024_LEN);
    ///打开目录
    if(NULL == (dpDir = opendir(achDirPath)))
    {
        NVRSYSERR("opendir:%s faild line:%d,errno:%d,%s\n",achDirPath, __LINE__, errno, strerror(errno));

        return NVR_ERR__ERROR;
    }
    ///读取目录下文件

	mzero(tCapSys);
	NvrCapGetCapParam(NVR_CAP_ID_SYS, &tCapSys);
	/*tCapSys.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = FALSE;
	tCapSys.tNvrCapAdvance.byVsipAppSup = FALSE;
	NvrCapSetCapParam(NVR_CAP_ID_SYS, &tCapSys);*/
    while(NULL != (pDirent = readdir(dpDir)))
    {
    	NVRSYSDEBUG("pDirent->d_name:%s\n", pDirent->d_name);
        ///仅区分是否so文件
        if(NULL != strstr(pDirent->d_name,"app.so"))
        {
        	if(NULL != strstr(achBuffer, pDirent->d_name))
			{
				NVRSYSERR("LIB %s not need loading!\n", pDirent->d_name);
				continue;
			}

			///检测到有vsip库 放开vsip校时能力支持
			/*if(0 == strcmp(pDirent->d_name,"libvsipapp.so"))
			{
				NVRSYSDEBUG("set byVsipAppSup = TRUE");
				tCapSys.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = TRUE;
				tCapSys.tNvrCapAdvance.byVsipAppSup = TRUE;
				NvrCapSetCapParam(NVR_CAP_ID_SYS, &tCapSys);
				if(!g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipAppEnable)
				{
					continue;
				}
			}*/

			if (0 == strcmp(pDirent->d_name, "libvsipapp.so"))
			{
				NvrSysGetTimeParam(&tTimeCfg);
				if (tCapSys.tNvrCapAdvance.byVsipAppSup)
				{
					NVRSYSDEBUG("byVsipAppSup = TRUE");
					for(i = 0; i < tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum; i++)
					{

						if(NVR_TIME_AUTO_SYNC_P_VSIP == tTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i])
						{
							bVsipExist = TRUE;
							index = i;
							break;
						}
					}
					if (!g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipAppEnable)
					{
						//关闭能力
						tCapSys.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = FALSE;
						NvrCapSetCapParam(NVR_CAP_ID_SYS, (void *)&tCapSys);
						//如果存在visp配置则删除
						if(bVsipExist)
						{
							if(index != tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum - 1)
							{
								for(j = index; j < tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum - 1; j++) 
								{
									tTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[j] = tTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[j+1];
									
								}
							}
							tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum--;
							g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum--;
						}				
						NvrSysSetAutoSyncTimeParam(&tTimeCfg.tAutoSyncParam,FALSE);
						continue;
					}
					else
					{
						if (!tCapSys.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP])
						{
							//开启能力
							tCapSys.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = TRUE;
							g_tSysCap.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = TRUE;
							NvrCapSetCapParam(NVR_CAP_ID_SYS, (void *)&tCapSys);
							//如果不存在vsip配置则添加在最后
							if(!bVsipExist)
							{						
								g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum] = NVR_TIME_AUTO_SYNC_P_VSIP;
								g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum++;
								tTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum] = NVR_TIME_AUTO_SYNC_P_VSIP;
								tTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum++;
								NvrSysSetAutoSyncTimeParam(&tTimeCfg.tAutoSyncParam,FALSE);
							}
						}
					}				
				}
				else
				{
					NVRSYSDEBUG("byVsipAppSup = FALSE");
					continue;
				}
			}

			///打开该链接文件
			snprintf(achLibPath, sizeof(achLibPath), "%s/%s", achDirPath, pDirent->d_name);
			pAppHandle = dlopen(achLibPath, RTLD_LAZY | RTLD_GLOBAL);
			if (NULL == pAppHandle)
			{
				NVRSYSERR("dlopen app err,%s,line:%d,error:%s\n", achLibPath, __LINE__, dlerror());
				eRet = NVR_ERR__ERROR;
			}
			else
			{
				NVRSYSIMP("dlopen %s succ\n", achLibPath);
				///获取函数地址指针
				pAppStart = (void (*)())dlsym(pAppHandle, "StartApp");
				if (NULL == pAppStart)
				{
					NVRSYSERR("dlsym StartApp err,line:%d,error:%s\n", __LINE__, dlerror());

					eRet = NVR_ERR__ERROR;
				}
				else
				{
					NVRSYSIMP("dlsym StartApp succ,begin to startapp\n");
					//启动app
					pAppStart();
				}
			}
		}
	}
    closedir(dpDir);
    s_bAppStart = TRUE;

	DmSrvAppStartFinishNotify();
    NVRSYSIMP("ret:%d\n",eRet);

#endif
    return eRet;
}




NVRSTATUS NvrSysCfgDefaultInit(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
    ENvrCapClassId eCapClassId = NVR_CAP_ID_DEF_CFG;  ///<能力集id
    TNvrCapDefaultCfg tNvrCapDefCfg;      ///<默认配置能力集
    TEdgeOsInterCapSysInfo tInterSysCap;
	u8 i = 0;
	mzero(tNvrCapDefCfg);

	eRet = NvrCapGetCapParam(eCapClassId, (void*)&tNvrCapDefCfg);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get defcap failed, ret:%d\n",eRet);
    	return eRet;
    }

	mzero(g_tNvrSysCfg);

	g_tNvrSysCfg.tNvrSysParam = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg;

    mzero(tInterSysCap);
    NvrCapGetInterCapParam(EDGEOS_IN_CAP_ID_SYS, &tInterSysCap);
    NVRSYSIMP("bySupGpioExtCardToDevName:%u\n",tInterSysCap.bySupGpioExtCardToDevName);
    
	if(tInterSysCap.bySupGpioExtCardToDevName)  
	{
		///<获取槽位号
		u8 abyDevName[(NVR_SYS_MAX_DEVNANE_LEN+1)*2];		///<设备名称unicode编码，1~32个字符
		u32 dwDevNameLen = sizeof(abyDevName);
		
		s8 achDevNameUtf8[NVR_MAX_STR128_LEN] = {0};
		s8 achDevNameUtf8Tmp[NVR_MAX_STR128_LEN] = {0};
		s32 nSlotIndex = 0;
		if(!CharConvConvertUnicodetoUtf8(g_tNvrSysCfg.tNvrSysParam.abyDevName, g_tNvrSysCfg.tNvrSysParam.wDevNameLen, achDevNameUtf8Tmp, sizeof(achDevNameUtf8Tmp)))
		{
			NVRSYSIMP("devname utf8:%s\n", achDevNameUtf8Tmp);
			eRet = NvrBrdApiGetExGpioInput(0, 0, &nSlotIndex);
			NVRSYSIMP("get gpio slot index:%d\n", nSlotIndex);
			snprintf(achDevNameUtf8, NVR_MAX_STR128_LEN,"%s-index%d", achDevNameUtf8Tmp, nSlotIndex);
			NVRSYSIMP("devname :%s\n", achDevNameUtf8);
			if(!CharConvConvertUtf8toUnicode(achDevNameUtf8, abyDevName, &dwDevNameLen))
			{
				NVRSYSIMP("unicode name len %lu\n", dwDevNameLen);
				memcpy(g_tNvrSysCfg.tNvrSysParam.abyDevName, abyDevName, sizeof(abyDevName));
				g_tNvrSysCfg.tNvrSysParam.wDevNameLen = dwDevNameLen;
			}
		}
	}

	g_tNvrSysCfg.tNvrSysZeroChnEncParam = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg;
	g_tNvrSysCfg.tNvrTimeParam = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg;
	g_tNvrSysCfg.tAdvanceParam = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg;
	g_tNvrSysCfg.tAudCallBindingParam = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam;
	g_tNvrSysCfg.tNvrSysParam.eLanguage = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eLanguage;
	g_tNvrSysCfg.tNvrSysParam.eSecurityLevel = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eSecurityLevel;

    NVRSYSDEBUG("bandingnum:%u,supptcalldevnum:%u,enable:%d-%d,bmix:%d,baec:%d eLanguage:%d\n",g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum,g_tNvrSysCfg.tAudCallBindingParam.wSupptCallDevNum,
        g_tNvrSysCfg.tAudCallBindingParam.atCallDev[0].bEnable,g_tNvrSysCfg.tAudCallBindingParam.atCallDev[1].bEnable,
        g_tNvrSysCfg.tAudCallBindingParam.bCallMix,g_tNvrSysCfg.tAudCallBindingParam.bDevAec, g_tNvrSysCfg.tNvrSysParam.eLanguage);
    for(i = 0;i<g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum;i++)
	{
	    NVRSYSDEBUG("===chnid:%u\n",g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId[i]);
	}
	g_tNvrSysCfg.tManualEventParam = tNvrCapDefCfg.tSysDefCfg.tManualEventParam;
	g_tNvrSysCfg.tGeographyPosParam = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam;
	g_tNvrSysCfg.tUpgradeServerParam = tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam;
	g_tNvrSysCfg.tVehicleParam = tNvrCapDefCfg.tSysDefCfg.tVehicleParam;
	g_tNvrSysCfg.tLanEncIdBindParam = tNvrCapDefCfg.tSysDefCfg.tLanEncIdBindParam;
	memcpy(&g_tNvrSysCfg.tBallCtrMatinParam, &tNvrCapDefCfg.tSysDefCfg.tBallCtrMatinParam, sizeof(g_tNvrSysCfg.tBallCtrMatinParam));

	return eRet;
}

NVRSTATUS NvrSysCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
	NVRSYS_ASSERT(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	u32 adwSyncTypeEnable[NVR_TIME_AUTO_SYNC_TYPE_MAX];

	mzero(adwSyncTypeEnable)

	u32 dwBufLen = 0;
	TPbNvrSysCfg tPbNvrSysCfg = TPB_NVR_SYS_CFG__INIT;
	TPbNvrSysParam tPbNvrSysParam = TPB_NVR_SYS_PARAM__INIT;
	TPbNvrSysZeroChnEncParam tPbNvrSysZeroChnEncParam = TPB_NVR_SYS_ZERO_CHN_ENC_PARAM__INIT;
	TPbNvrSysVidRes tPbNvrSysVidRes = TPB_NVR_SYS_VID_RES__INIT;
	TPbNvrTimeParam tPbNvrTimeParam = TPB_NVR_TIME_PARAM__INIT;
	TPbNvrSummerTimeParam tPbNvrSummerTimeParam = TPB_NVR_SUMMER_TIME_PARAM__INIT;
	TPbNvrSummerTime tPbNvrSummerTimeBegin = TPB_NVR_SUMMER_TIME__INIT;
	TPbNvrSummerTime tPbNvrSummerTimeEnd = TPB_NVR_SUMMER_TIME__INIT;
	TPbNvrAutoSyncTimeParam tPbNvrAutoSyncTimeParam = TPB_NVR_AUTO_SYNC_TIME_PARAM__INIT;
	TPbNvrNtpParam tPbNvrNtpParam = TPB_NVR_NTP_PARAM__INIT;
	TPbNvrTimeAutoAdaptParam tPbNvrTimeAutoAdaptParam = TPB_NVR_TIME_AUTO_ADAPT_PARAM__INIT;
	TPbNvrSysUdpReTranParam tPbNvrSysUdpReTranParam = TPB_NVR_SYS_UDP_RE_TRAN_PARAM__INIT;
	TPbNvrSysKtcpParam tPbNvrSysKtcpParam = TPB_NVR_SYS_KTCP_PARAM__INIT;
	TPbNvrSysMrtcParam tPbNvrSysMrtcParam = TPB_NVR_SYS_MRTC_PARAM__INIT;
    TPbNvrSysPingParam tPbNvrSysPingParam = TPB_NVR_SYS_PING_PARAM__INIT;
	TPbNvrSysCustomPlugDownload tPbNvrSysCustomPlugDownload = TPB_NVR_SYS_CUSTOM_PLUG_DOWNLOAD__INIT;
	TPbNvrSysAdvanceSysParam tPbNvrSysAdvanceSysParam = TPB_NVR_SYS_ADVANCE_SYS_PARAM__INIT;
	TPbNvrSysAdvanceParam tPbNvrSysAdvanceParam = TPB_NVR_SYS_ADVANCE_PARAM__INIT;
	TPbNvrSysVehicleParam tPbNvrSysVehicleParam = TPB_NVR_SYS_VEHICLE_PARAM__INIT;
	TPbNvrBallCtrMatinParam tPbNvrBallCtrMatinParam = TPB_NVR_BALL_CTR_MATIN_PARAM__INIT;
	TPbNvrSysLanEncIdBindParam lan_encid_bind_param = TPB_NVR_SYS_LAN_ENC_ID_BIND_PARAM__INIT;

	///系统参数赋值
	tPbNvrSysParam.has_dev_name = TRUE;
	tPbNvrSysParam.dev_name.data = g_tNvrSysCfg.tNvrSysParam.abyDevName;
	tPbNvrSysParam.dev_name.len = (u32)g_tNvrSysCfg.tNvrSysParam.wDevNameLen;
	tPbNvrSysParam.has_dev_name_len = TRUE;
	tPbNvrSysParam.dev_name_len = g_tNvrSysCfg.tNvrSysParam.wDevNameLen;
	tPbNvrSysParam.has_dev_num = TRUE;
	tPbNvrSysParam.dev_num = g_tNvrSysCfg.tNvrSysParam.dwDevNum;
	tPbNvrSysParam.has_language = TRUE;
	tPbNvrSysParam.language = g_tNvrSysCfg.tNvrSysParam.eLanguage;
	tPbNvrSysParam.has_auto_logout = TRUE;
	tPbNvrSysParam.auto_logout = g_tNvrSysCfg.tNvrSysParam.eAutoLogout;
	tPbNvrSysParam.has_audio_listen = TRUE;
	tPbNvrSysParam.audio_listen = g_tNvrSysCfg.tNvrSysParam.eAudioListen;
	tPbNvrSysParam.has_menu_tran = TRUE;
	tPbNvrSysParam.menu_tran = g_tNvrSysCfg.tNvrSysParam.dwMenuTran;
	tPbNvrSysParam.has_boot_guide_enable = TRUE;
	tPbNvrSysParam.boot_guide_enable = g_tNvrSysCfg.tNvrSysParam.byBootGuideEnable;
	tPbNvrSysParam.has_opt_pwd_enable = TRUE;
	tPbNvrSysParam.opt_pwd_enable = g_tNvrSysCfg.tNvrSysParam.byOptPwdEnable;
	tPbNvrSysParam.has_delay_shutdown_time = TRUE;
	tPbNvrSysParam.delay_shutdown_time = g_tNvrSysCfg.tNvrSysParam.dwDelayShutdownTime;
	tPbNvrSysParam.has_security_level = TRUE;
	tPbNvrSysParam.security_level = g_tNvrSysCfg.tNvrSysParam.eSecurityLevel;

	///零通道编码参数赋值
	tPbNvrSysZeroChnEncParam.has_enable = TRUE;
	tPbNvrSysZeroChnEncParam.enable = g_tNvrSysCfg.tNvrSysZeroChnEncParam.byEnable;
	tPbNvrSysVidRes.has_width = TRUE;
	tPbNvrSysVidRes.width = g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wWidth;
	tPbNvrSysVidRes.has_height = TRUE;
	tPbNvrSysVidRes.height= g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wHeight;
	tPbNvrSysZeroChnEncParam.has_frame_rate = TRUE;
	tPbNvrSysZeroChnEncParam.frame_rate = g_tNvrSysCfg.tNvrSysZeroChnEncParam.eFrameRate;
	tPbNvrSysZeroChnEncParam.has_bit_rate = TRUE;
	tPbNvrSysZeroChnEncParam.bit_rate = g_tNvrSysCfg.tNvrSysZeroChnEncParam.eBitRate;
	tPbNvrSysZeroChnEncParam.has_video_src = TRUE;
	tPbNvrSysZeroChnEncParam.video_src = g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVideoSrc;
	tPbNvrSysZeroChnEncParam.enc_res = &tPbNvrSysVidRes;
	tPbNvrSysZeroChnEncParam.has_enc_type = TRUE;
	tPbNvrSysZeroChnEncParam.enc_type = g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVidEncType;

	///时间参数赋值
	tPbNvrTimeParam.has_time_zone = TRUE;
	tPbNvrTimeParam.time_zone = g_tNvrSysCfg.tNvrTimeParam.swTimeZone;


	tPbNvrSummerTimeParam.has_enable = TRUE;
	tPbNvrSummerTimeParam.enable = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.byEnable;
	tPbNvrSummerTimeParam.has_offset = TRUE;
	tPbNvrSummerTimeParam.offset = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.eOffSet;
	tPbNvrSummerTimeBegin.has_month = TRUE;
	tPbNvrSummerTimeBegin.month = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byMonth;
	tPbNvrSummerTimeBegin.has_week_of_month = TRUE;
	tPbNvrSummerTimeBegin.week_of_month = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byWeekOfMonth;
	tPbNvrSummerTimeBegin.has_day = TRUE;
	tPbNvrSummerTimeBegin.day = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byDay;
	tPbNvrSummerTimeBegin.has_hour = TRUE;
	tPbNvrSummerTimeBegin.hour = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byHour;
	tPbNvrSummerTimeEnd.has_month = TRUE;
	tPbNvrSummerTimeEnd.month = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byMonth;
	tPbNvrSummerTimeEnd.has_week_of_month = TRUE;
	tPbNvrSummerTimeEnd.week_of_month = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byWeekOfMonth;
	tPbNvrSummerTimeEnd.has_day = TRUE;
	tPbNvrSummerTimeEnd.day = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byDay;
	tPbNvrSummerTimeEnd.has_hour = TRUE;
	tPbNvrSummerTimeEnd.hour = g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byHour;
	tPbNvrSummerTimeParam.begin = &tPbNvrSummerTimeBegin;
	tPbNvrSummerTimeParam.end = &tPbNvrSummerTimeEnd;
	tPbNvrTimeParam.summer_param = &tPbNvrSummerTimeParam;

	tPbNvrAutoSyncTimeParam.has_enable = TRUE;
	tPbNvrAutoSyncTimeParam.enable = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byEnable;
	tPbNvrAutoSyncTimeParam.has_sync_type = TRUE;
	tPbNvrAutoSyncTimeParam.sync_type = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType;
	tPbNvrAutoSyncTimeParam.has_ntp_enable = TRUE;
	tPbNvrAutoSyncTimeParam.ntp_enable = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byNtpEnable;
	tPbNvrAutoSyncTimeParam.has_type = FALSE;
	//tPbNvrAutoSyncTimeParam.type = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eType;		///<保留废弃配置
	tPbNvrTimeAutoAdaptParam.has_src_num = TRUE;
	tPbNvrTimeAutoAdaptParam.src_num = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum;
	tPbNvrTimeAutoAdaptParam.n_sync_type_pri = NVR_TIME_AUTO_SYNC_TYPE_MAX;
	tPbNvrTimeAutoAdaptParam.sync_type_pri = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri;
	tPbNvrTimeAutoAdaptParam.n_sync_type_enable = NVR_TIME_AUTO_SYNC_TYPE_MAX;
	for (i = 0; i < NVR_TIME_AUTO_SYNC_TYPE_MAX; i++)
	{
		adwSyncTypeEnable[i] = (u32)g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable[i];
	}
	tPbNvrTimeAutoAdaptParam.sync_type_enable = (uint32_t *)adwSyncTypeEnable;
	tPbNvrTimeAutoAdaptParam.has_adapt_lock_time = TRUE;
	tPbNvrTimeAutoAdaptParam.adapt_lock_time = (uint32_t)g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.dwAdaptLockTime;

	tPbNvrNtpParam.has_server_port = TRUE;
	tPbNvrNtpParam.server_port = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwServerPort;
	tPbNvrNtpParam.has_interval = TRUE;
	tPbNvrNtpParam.interval = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwInterval;
	tPbNvrNtpParam.server_ip = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIp;
	tPbNvrNtpParam.backupserver_ip = g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIPBackUp;       ///<新增：备用服务器域名或IP
	tPbNvrAutoSyncTimeParam.ntp_sync = &tPbNvrNtpParam;
	tPbNvrAutoSyncTimeParam.auto_adpt = &tPbNvrTimeAutoAdaptParam;
	tPbNvrTimeParam.auto_sync_param = &tPbNvrAutoSyncTimeParam;

	///高级配置
	///udp重传参数
	tPbNvrSysUdpReTranParam.has_enable = TRUE;
	tPbNvrSysUdpReTranParam.enable = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.byEnable;
	tPbNvrSysUdpReTranParam.has_first_check_point = TRUE;
	tPbNvrSysUdpReTranParam.first_check_point = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwFirstCheckPoint;
	tPbNvrSysUdpReTranParam.has_second_check_point = TRUE;
	tPbNvrSysUdpReTranParam.second_check_point = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwSecondCheckPoint;
	tPbNvrSysUdpReTranParam.has_third_check_point = TRUE;
	tPbNvrSysUdpReTranParam.third_check_point = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwThirdCheckPoint;
	tPbNvrSysUdpReTranParam.has_overdue_discard = TRUE;
	tPbNvrSysUdpReTranParam.overdue_discard = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwOverdueDiscard;

	tPbNvrSysAdvanceSysParam.has_disk_prerecord = TRUE;
	tPbNvrSysAdvanceSysParam.disk_prerecord = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDiskPreRecord;
	tPbNvrSysAdvanceSysParam.has_third_enc_sup = TRUE;
	tPbNvrSysAdvanceSysParam.third_enc_sup = g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport;
	tPbNvrSysAdvanceSysParam.has_close_audio = TRUE;
	tPbNvrSysAdvanceSysParam.close_audio = g_tNvrSysCfg.tAdvanceParam.tSysParam.bCloseAudio;
    tPbNvrSysAdvanceSysParam.has_double_aud_sup = TRUE;
	tPbNvrSysAdvanceSysParam.double_aud_sup = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioSupport;
	tPbNvrSysAdvanceSysParam.has_audcall_enctype = TRUE;
	tPbNvrSysAdvanceSysParam.audcall_enctype = g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType;
	tPbNvrSysAdvanceSysParam.has_blk_size = TRUE;
	tPbNvrSysAdvanceSysParam.blk_size = g_tNvrSysCfg.tAdvanceParam.tSysParam.eBlkSize;
	tPbNvrSysAdvanceSysParam.has_ptz_ctrl_time = TRUE;
	tPbNvrSysAdvanceSysParam.ptz_ctrl_time = g_tNvrSysCfg.tAdvanceParam.tSysParam.wPtzCtrlTime;
	tPbNvrSysAdvanceSysParam.has_alarm_delay_time = TRUE;
	tPbNvrSysAdvanceSysParam.alarm_delay_time = g_tNvrSysCfg.tAdvanceParam.tSysParam.wAlarmDelayTime;
	tPbNvrSysAdvanceSysParam.has_double_aud_mix = TRUE;
	tPbNvrSysAdvanceSysParam.double_aud_mix = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioMix;
	tPbNvrSysAdvanceSysParam.has_dynamic_plugin = TRUE;
	tPbNvrSysAdvanceSysParam.dynamic_plugin = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable;
	tPbNvrSysAdvanceSysParam.has_dynamic_plugin = TRUE;
	tPbNvrSysAdvanceSysParam.dynamic_plugin = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable;
	tPbNvrSysAdvanceSysParam.has_stream_extern_head = TRUE;
	tPbNvrSysAdvanceSysParam.stream_extern_head = g_tNvrSysCfg.tAdvanceParam.tSysParam.bStreamExternHeadEnable;
	tPbNvrSysAdvanceSysParam.has_vsip_app_enable = TRUE;
	tPbNvrSysAdvanceSysParam.vsip_app_enable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipAppEnable;
	tPbNvrSysAdvanceSysParam.has_vsip_clt_enable = TRUE;
	tPbNvrSysAdvanceSysParam.vsip_clt_enable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipCltEnable;
	tPbNvrSysAdvanceSysParam.has_temp_meas_enable = TRUE;
	tPbNvrSysAdvanceSysParam.temp_meas_enable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bThermeasTempEnable;
	tPbNvrSysAdvanceSysParam.has_smooth_snd_enable = TRUE;
	tPbNvrSysAdvanceSysParam.smooth_snd_enable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable;
	tPbNvrSysAdvanceSysParam.has_smooth_snd_rate = TRUE;
	tPbNvrSysAdvanceSysParam.smooth_snd_rate = g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate;

	
	tPbNvrSysCustomPlugDownload.has_enable = TRUE;
	tPbNvrSysCustomPlugDownload.enable = g_tNvrSysCfg.tAdvanceParam.tPlugDownload.byEnable;
	
	tPbNvrSysCustomPlugDownload.has_custom_plug_url = TRUE;
	tPbNvrSysCustomPlugDownload.custom_plug_url.data = (uint8_t *)g_tNvrSysCfg.tAdvanceParam.tPlugDownload.achCustomPlugUrl;
	tPbNvrSysCustomPlugDownload.custom_plug_url.len = sizeof(g_tNvrSysCfg.tAdvanceParam.tPlugDownload.achCustomPlugUrl);

	tPbNvrSysKtcpParam.has_enable = TRUE;
	tPbNvrSysKtcpParam.enable = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.bKtcpEnable;
	tPbNvrSysKtcpParam.has_conge_type = TRUE;
	tPbNvrSysKtcpParam.conge_type = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.eCongeType;

	tPbNvrSysKtcpParam.has_start_port = TRUE;
	tPbNvrSysKtcpParam.start_port = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wStartPort;
	tPbNvrSysKtcpParam.has_end_port = TRUE;
	tPbNvrSysKtcpParam.end_port = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wEndPort;
	
	tPbNvrSysKtcpParam.has_bandwidth_min = TRUE;
	tPbNvrSysKtcpParam.bandwidth_min = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMin;
	tPbNvrSysKtcpParam.has_bandwidth_max = TRUE;
	tPbNvrSysKtcpParam.bandwidth_max = g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMax;

	tPbNvrSysMrtcParam.has_enable = TRUE;
	tPbNvrSysMrtcParam.enable = g_tNvrSysCfg.tAdvanceParam.tMrtcParam.bMrtcEnable;
	tPbNvrSysMrtcParam.has_conge_type = TRUE;
	tPbNvrSysMrtcParam.conge_type = g_tNvrSysCfg.tAdvanceParam.tMrtcParam.eCongeType;

    tPbNvrSysPingParam.has_disable = TRUE;
    tPbNvrSysPingParam.disable = g_tNvrSysCfg.tAdvanceParam.tPingParam.bDisable;
        

	tPbNvrSysAdvanceParam.udp_re_tran_param = &tPbNvrSysUdpReTranParam;
	tPbNvrSysAdvanceParam.advance_sys_param = &tPbNvrSysAdvanceSysParam;
	tPbNvrSysAdvanceParam.plug_download = &tPbNvrSysCustomPlugDownload;
	tPbNvrSysAdvanceParam.ktcp_param = &tPbNvrSysKtcpParam;
	tPbNvrSysAdvanceParam.mrtc_param = &tPbNvrSysMrtcParam;
    tPbNvrSysAdvanceParam.ping_param = &tPbNvrSysPingParam;

	tPbNvrSysAdvanceParam.has_mtu = TRUE;
	tPbNvrSysAdvanceParam.mtu = g_tNvrSysCfg.tAdvanceParam.dwMtu;
	tPbNvrSysAdvanceParam.has_not_rtcp_port_share = TRUE;
	tPbNvrSysAdvanceParam.not_rtcp_port_share = g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare;

	///音频绑定
	TPbNvrSysCallDev tPbNvrSysCallDev = TPB_NVR_SYS_CALL_DEV__INIT;
	TPbNvrSysCallDev atPbNvrSysCallDev[NVR_MAX_AUD_CALL_BINDING_DEV_NUM];
	TPbNvrSysCallDev *aptPbNvrSysCallDev[NVR_MAX_AUD_CALL_BINDING_DEV_NUM] = {NULL};
	TPbNvrSysAudCallBindingParam tTPbTNvrSysAudCallBindingParam = TPB_NVR_SYS_AUD_CALL_BINDING_PARAM__INIT;
	for (i = 0; i < NVR_MAX_AUD_CALL_BINDING_DEV_NUM; i++)
	{
		atPbNvrSysCallDev[i] = tPbNvrSysCallDev;
		aptPbNvrSysCallDev[i] = &atPbNvrSysCallDev[i];
	}
	tTPbTNvrSysAudCallBindingParam.has_call_binding_num = TRUE;
	tTPbTNvrSysAudCallBindingParam.call_binding_num = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum;
	tTPbTNvrSysAudCallBindingParam.has_suppt_call_dev_num = TRUE;
	tTPbTNvrSysAudCallBindingParam.suppt_call_dev_num = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.wSupptCallDevNum;
	tTPbTNvrSysAudCallBindingParam.has_call_mix = TRUE;
	tTPbTNvrSysAudCallBindingParam.call_mix = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.bCallMix;
	tTPbTNvrSysAudCallBindingParam.has_dev_aec = TRUE;
	tTPbTNvrSysAudCallBindingParam.dev_aec = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.bDevAec;
	tTPbTNvrSysAudCallBindingParam.n_call_binding_chn_id = NVR_MAX_CHN_NUM;
	//tTPbTNvrSysAudCallBindingParam.call_binding_chn_id = (uint32_t *)g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId;
	///<因为数组的类型不同,一个u16， 一个是u32，不能直接赋值地址，需逐个赋值
	tTPbTNvrSysAudCallBindingParam.call_binding_chn_id = (uint32_t*)NVRALLOC(sizeof(uint32_t)*NVR_MAX_CHN_NUM);

	for(i = 0; i < NVR_MAX_CHN_NUM;i++)
	{
		tTPbTNvrSysAudCallBindingParam.call_binding_chn_id[i] = g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId[i];
	}

	tTPbTNvrSysAudCallBindingParam.n_call_dev = NVR_MAX_AUD_CALL_BINDING_DEV_NUM;

    NVRSYSDEBUG("bandingnum:%u,supptcalldevnum:%u,bmix:%d,baec:%d\n",tTPbTNvrSysAudCallBindingParam.call_binding_num,tTPbTNvrSysAudCallBindingParam.suppt_call_dev_num,
        tTPbTNvrSysAudCallBindingParam.call_mix,tTPbTNvrSysAudCallBindingParam.dev_aec);
    for(i = 0;i<tTPbTNvrSysAudCallBindingParam.call_binding_num;i++)
	{
	    NVRSYSDEBUG("chnid:%u\n",tTPbTNvrSysAudCallBindingParam.call_binding_chn_id[i]);
	}

	for (i = 0; i < tTPbTNvrSysAudCallBindingParam.n_call_dev; i++)
	{
		atPbNvrSysCallDev[i].has_enable = TRUE;
		atPbNvrSysCallDev[i].enable = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].bEnable;
		atPbNvrSysCallDev[i].has_unicode_len = TRUE;
		atPbNvrSysCallDev[i].unicode_len = (uint32_t)g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].dwUnicodeLen;
		atPbNvrSysCallDev[i].has_dev_unicode_name = TRUE;
		atPbNvrSysCallDev[i].dev_unicode_name.data = (uint8_t *)g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName;
		atPbNvrSysCallDev[i].dev_unicode_name.len = (size_t)g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].dwUnicodeLen;
	}
	tTPbTNvrSysAudCallBindingParam.call_dev = aptPbNvrSysCallDev;

	///事件
	TPbNvrSysEventDetail tPbNvrSysEventDetail = TPB_NVR_SYS_EVENT_DETAIL__INIT;
	TPbNvrSysEventDetail atPbNvrSysEventDetail[NVR_SYS_MAX_MANUAL_EVENT_NUM];
	TPbNvrSysEventDetail* aptPbNvrSysEventDetail[NVR_SYS_MAX_MANUAL_EVENT_NUM] = {NULL};
	TPbNvrSysManualEventParam tPbNvrSysManualEventParam = TPB_NVR_SYS_MANUAL_EVENT_PARAM__INIT;
	for (i = 0; i < NVR_SYS_MAX_MANUAL_EVENT_NUM; i++)
	{
		atPbNvrSysEventDetail[i] = tPbNvrSysEventDetail;
		aptPbNvrSysEventDetail[i] = &atPbNvrSysEventDetail[i];
	}

	tPbNvrSysManualEventParam.has_event_num = TRUE;
	tPbNvrSysManualEventParam.event_num = g_tNvrSysCfg.tManualEventParam.wEventNum;
	tPbNvrSysManualEventParam.has_trg_event_snap = TRUE;
	tPbNvrSysManualEventParam.trg_event_snap = g_tNvrSysCfg.tManualEventParam.bTrgEventSnap;
	tPbNvrSysManualEventParam.n_event_list = NVR_SYS_MAX_MANUAL_EVENT_NUM;
	for (i = 0; i < tPbNvrSysManualEventParam.n_event_list; i++)
	{
		atPbNvrSysEventDetail[i].has_event_name_len = TRUE;
		atPbNvrSysEventDetail[i].event_name_len = g_tNvrSysCfg.tManualEventParam.atEventList[i].dwEventNameLen;
		atPbNvrSysEventDetail[i].has_event_name = TRUE;
		atPbNvrSysEventDetail[i].event_name.data = (uint8_t *)g_tNvrSysCfg.tManualEventParam.atEventList[i].achEventName;
		atPbNvrSysEventDetail[i].event_name.len = g_tNvrSysCfg.tManualEventParam.atEventList[i].dwEventNameLen;

		atPbNvrSysEventDetail[i].has_desc_len = TRUE;
		atPbNvrSysEventDetail[i].desc_len = g_tNvrSysCfg.tManualEventParam.atEventList[i].dwDescLen;
		atPbNvrSysEventDetail[i].has_desc = TRUE;
		atPbNvrSysEventDetail[i].desc.data = (uint8_t *)g_tNvrSysCfg.tManualEventParam.atEventList[i].achDesc;
		atPbNvrSysEventDetail[i].desc.len = g_tNvrSysCfg.tManualEventParam.atEventList[i].dwDescLen;

		atPbNvrSysEventDetail[i].has_default_ = TRUE;
		atPbNvrSysEventDetail[i].default_ = g_tNvrSysCfg.tManualEventParam.atEventList[i].bDefault;
	}
	tPbNvrSysManualEventParam.event_list = aptPbNvrSysEventDetail;

	///地理位置
	TPbNvrSysLimitSpeedParam tPbNvrSysLimitSpeedParam = TPB_NVR_SYS_LIMIT_SPEED_PARAM__INIT;
	TPbNvrSysGeographyPosParam tPbNvrSysGeographyPosParam = TPB_NVR_SYS_GEOGRAPHY_POS_PARAM__INIT;

	tPbNvrSysGeographyPosParam.has_open_gps = TRUE;
	tPbNvrSysGeographyPosParam.open_gps = g_tNvrSysCfg.tGeographyPosParam.bOpenGps;
	tPbNvrSysGeographyPosParam.has_gps_loc_mode = TRUE;
	tPbNvrSysGeographyPosParam.gps_loc_mode = g_tNvrSysCfg.tGeographyPosParam.eGpsLocMode;
	tPbNvrSysGeographyPosParam.has_enhance_agps = TRUE;
	tPbNvrSysGeographyPosParam.enhance_agps = g_tNvrSysCfg.tGeographyPosParam.bEnhanceAgps;
	tPbNvrSysGeographyPosParam.has_nty_ipc = FALSE;
	//tPbNvrSysGeographyPosParam.nty_ipc = g_tNvrSysCfg.tGeographyPosParam.bNtyIpc;
	tPbNvrSysGeographyPosParam.n_upload_pubsec = 0;
	tPbNvrSysGeographyPosParam.upload_pubsec = NULL;

	tPbNvrSysLimitSpeedParam.has_enable = TRUE;
	tPbNvrSysLimitSpeedParam.enable = g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bEnable;
	tPbNvrSysLimitSpeedParam.has_limit_value = TRUE;
	tPbNvrSysLimitSpeedParam.limit_value = g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.dwLimitValue;
	tPbNvrSysLimitSpeedParam.has_over_speed_report = TRUE;
	tPbNvrSysLimitSpeedParam.over_speed_report = g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bOverSpeedReport;
	tPbNvrSysGeographyPosParam.limit_speed = &tPbNvrSysLimitSpeedParam;

	///升级服务器
	TPbNvrSysUpgradeServerParam tPbNvrSysUpgradeServerParam = TPB_NVR_SYS_UPGRADE_SERVER_PARAM__INIT;

	tPbNvrSysUpgradeServerParam.has_auto_upgrade = TRUE;
	tPbNvrSysUpgradeServerParam.auto_upgrade = g_tNvrSysCfg.tUpgradeServerParam.bAutoUpgrade;
	tPbNvrSysUpgradeServerParam.has_server_ip = TRUE;
	tPbNvrSysUpgradeServerParam.server_ip.data = (uint8_t *)g_tNvrSysCfg.tUpgradeServerParam.achServerIP;
	tPbNvrSysUpgradeServerParam.server_ip.len = sizeof(g_tNvrSysCfg.tUpgradeServerParam.achServerIP);
	tPbNvrSysUpgradeServerParam.has_server_port = TRUE;
	tPbNvrSysUpgradeServerParam.server_port = g_tNvrSysCfg.tUpgradeServerParam.wServerPort;

	///车载配置
	tPbNvrSysVehicleParam.has_car_plate_num_len = TRUE;
	tPbNvrSysVehicleParam.car_plate_num_len = g_tNvrSysCfg.tVehicleParam.dwCarPlateNumLen;
	tPbNvrSysVehicleParam.has_car_plate_num = TRUE;
	tPbNvrSysVehicleParam.car_plate_num.data = (uint8_t *)g_tNvrSysCfg.tVehicleParam.achCarPlateNum;
	tPbNvrSysVehicleParam.car_plate_num.len = g_tNvrSysCfg.tVehicleParam.dwCarPlateNumLen;
	tPbNvrSysVehicleParam.has_shutdown_poe_sup = TRUE;
	tPbNvrSysVehicleParam.shutdown_poe_sup = g_tNvrSysCfg.tVehicleParam.bShutdownPoeSup;
	tPbNvrSysVehicleParam.has_sync_lable = TRUE;
	tPbNvrSysVehicleParam.sync_lable = g_tNvrSysCfg.tVehicleParam.bSyncLable;
	tPbNvrSysVehicleParam.has_poe_power_enable = TRUE;
	tPbNvrSysVehicleParam.poe_power_enable = g_tNvrSysCfg.tVehicleParam.bPoePowerEnable;
	tPbNvrSysVehicleParam.has_flameout_alarm_enable = TRUE;
	tPbNvrSysVehicleParam.flameout_alarm_enable = g_tNvrSysCfg.tVehicleParam.bFlameoutAlarmEnable;
	tPbNvrSysVehicleParam.has_ptzout_power_enable = TRUE;
	tPbNvrSysVehicleParam.ptzout_power_enable = g_tNvrSysCfg.tVehicleParam.bPtzOutPowerEnable;
	tPbNvrSysVehicleParam.has_close_ptzout_power_enable = TRUE;
	tPbNvrSysVehicleParam.close_ptzout_power_enable = g_tNvrSysCfg.tVehicleParam.bClosePtzOutPowerEnable;

	///布控球维护配置
	tPbNvrBallCtrMatinParam.has_shut_down_enable = TRUE;
	tPbNvrBallCtrMatinParam.shut_down_enable = g_tNvrSysCfg.tBallCtrMatinParam.byShutDownEnable;
	tPbNvrBallCtrMatinParam.has_light_enable = TRUE;
	tPbNvrBallCtrMatinParam.light_enable = g_tNvrSysCfg.tBallCtrMatinParam.byLightEnbale;
	tPbNvrBallCtrMatinParam.has_vehicle_mode_enable = TRUE;
	tPbNvrBallCtrMatinParam.vehicle_mode_enable = g_tNvrSysCfg.tBallCtrMatinParam.byVehicleModeEnbale;
	
	///<信息报送配置
	TPbNvrSysMessageSubmitParam tPbNvrSysMessageSubmitParam = TPB_NVR_SYS_MESSAGE_SUBMIT_PARAM__INIT;

	tPbNvrSysMessageSubmitParam.has_upload_plat_form_gps = TRUE;
	tPbNvrSysMessageSubmitParam.upload_plat_form_gps = g_tNvrSysCfg.tMessageSubmitParam.bUploadPlatformGps;
	tPbNvrSysMessageSubmitParam.n_upload_pubsec = NVR_MAX_PUBSEC_APP_NUM;
	//NVRSYSDEBUG("NVR_MAX_PUBSEC_APP_NUM !!\n");

	uint32_t abUploadPubsec[NVR_MAX_PUBSEC_APP_NUM] = {0};
	for(i = 0; i < NVR_MAX_PUBSEC_APP_NUM; i++)
	{
		abUploadPubsec[i] = (uint32_t)g_tNvrSysCfg.tMessageSubmitParam.abUploadPubsec[i];
		
	}
	tPbNvrSysMessageSubmitParam.upload_pubsec = abUploadPubsec;
	tPbNvrSysMessageSubmitParam.has_nty_ipc_gps = TRUE;
	tPbNvrSysMessageSubmitParam.nty_ipc_gps = g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcGps;
	tPbNvrSysMessageSubmitParam.has_send_gps_interval = TRUE;
	tPbNvrSysMessageSubmitParam.send_gps_interval = g_tNvrSysCfg.tMessageSubmitParam.wSendGpsInterval;
	tPbNvrSysMessageSubmitParam.has_nty_ipc_net = TRUE;
	tPbNvrSysMessageSubmitParam.nty_ipc_net = g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcNet;

	TPbNvrSysEncChnSwitchInfo tPbEthInfo = TPB_NVR_SYS_ENC_CHN_SWITCH_INFO__INIT;
	TPbNvrSysEncChnSwitchInfo tPbEthInfolist[NVR_MAX_ETH_NUM];
	TPbNvrSysEncChnSwitchInfo *aptPbEncChnSwitchInfo[NVR_MAX_ETH_NUM];
	lan_encid_bind_param.n_eth_info_list = NVR_MAX_ETH_NUM;
	lan_encid_bind_param.eth_info_list = aptPbEncChnSwitchInfo;
	for(i = 0; i < NVR_MAX_ETH_NUM; i++)
	{
		aptPbEncChnSwitchInfo[i] = &tPbEthInfolist[i];
		tPbEthInfolist[i] = tPbEthInfo;
		tPbEthInfolist[i].n_enc_id_switch = NVR_MAX_LCAM_CHN_NUM;
		tPbEthInfolist[i].enc_id_switch = g_tNvrSysCfg.tLanEncIdBindParam.tEthInfo[i].abEncidSwitch;
	}

	tPbNvrSysCfg.nvr_sys_param = &tPbNvrSysParam;
	tPbNvrSysCfg.nvr_sys_zero_chn_enc_param = &tPbNvrSysZeroChnEncParam;
	tPbNvrSysCfg.nvr_time_param = &tPbNvrTimeParam;
	tPbNvrSysCfg.nvr_sys_advance_param = &tPbNvrSysAdvanceParam;
	tPbNvrSysCfg.aud_call_binding_param = &tTPbTNvrSysAudCallBindingParam;
	tPbNvrSysCfg.manual_event_param = &tPbNvrSysManualEventParam;
	tPbNvrSysCfg.geography_pos_param = &tPbNvrSysGeographyPosParam;
	tPbNvrSysCfg.upgrade_server_param = &tPbNvrSysUpgradeServerParam;
	tPbNvrSysCfg.vehicle_param = &tPbNvrSysVehicleParam;
	tPbNvrSysCfg.message_submit_param = &tPbNvrSysMessageSubmitParam;
	tPbNvrSysCfg.ball_ctr_matin_param = &tPbNvrBallCtrMatinParam;
	tPbNvrSysCfg.lan_encid_bind_param = &lan_encid_bind_param;
		
    ///获取结构体序列化后的二进制buffer大小
	dwBufLen = tpb_nvr_sys_cfg__get_packed_size(&tPbNvrSysCfg);


    NVRSYSDEBUG("get pack size :%lu \n", dwBufLen);

	///为ptPbcSimple->data申请内存，外部释放
	ptPbcSimple->data = NVRALLOC(dwBufLen);
	if(NULL == ptPbcSimple->data)
	{
	   NVRSYSERR("malloc pack buffer failed.\n");
	   NVRFREE(tTPbTNvrSysAudCallBindingParam.call_binding_chn_id);
	   return NVR_ERR__ERROR;
	}

    ///序列化tPbNvrSysCfg到buffer中
    ptPbcSimple->len = tpb_nvr_sys_cfg__pack(&tPbNvrSysCfg, ptPbcSimple->data);
    if(dwBufLen != ptPbcSimple->len)
    {
        NVRSYSERR("pack buffer failed, pack len:%lu \n", ptPbcSimple->len);
        eRet = NVR_ERR__ERROR;
    }
	NVRFREE(tTPbTNvrSysAudCallBindingParam.call_binding_chn_id);
	return eRet;
}

NVRSTATUS NvrSysCfgProtoToStruct(const TPbNvrSysCfg *ptPbNvrSysCfg)
{
	NVRSYS_ASSERT(ptPbNvrSysCfg);

	u32 i = 0;
	u32 j = 0;
	u32 dwNtpTime = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
    ENvrCapClassId eCapClassId = NVR_CAP_ID_DEF_CFG; 	///<能力集id
    TNvrCapDefaultCfg tNvrCapDefCfg;      				///<默认配置能力集

	mzero(tNvrCapDefCfg);

	eRet = NvrCapGetCapParam(eCapClassId, (void*)&tNvrCapDefCfg);
    if(NVR_ERR__OK != eRet)
    {
		NVRSYSERR("get defcap failed, ret:%d\n",eRet);
    	return eRet;
    }

	NVRSYSIMP("start...\n");


	if (NULL != ptPbNvrSysCfg->nvr_sys_param)
	{
		///系统参数转换
		if (ptPbNvrSysCfg->nvr_sys_param->has_dev_name)
		{
			if (NULL != ptPbNvrSysCfg->nvr_sys_param->dev_name.data)
			{
				memcpy(g_tNvrSysCfg.tNvrSysParam.abyDevName, ptPbNvrSysCfg->nvr_sys_param->dev_name.data,  ptPbNvrSysCfg->nvr_sys_param->dev_name.len);
			}
			else
			{
				memcpy(g_tNvrSysCfg.tNvrSysParam.abyDevName, tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.abyDevName, sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.abyDevName));
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tNvrSysParam.abyDevName, tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.abyDevName, sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.abyDevName));
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_dev_name_len)
		{
			g_tNvrSysCfg.tNvrSysParam.wDevNameLen = ptPbNvrSysCfg->nvr_sys_param->dev_name_len;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.wDevNameLen = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.wDevNameLen;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_dev_num)
		{
			g_tNvrSysCfg.tNvrSysParam.dwDevNum = ptPbNvrSysCfg->nvr_sys_param->dev_num;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.dwDevNum = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.dwDevNum;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_language)
		{
			g_tNvrSysCfg.tNvrSysParam.eLanguage = ptPbNvrSysCfg->nvr_sys_param->language;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.eLanguage = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eLanguage;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_auto_logout)
		{
			g_tNvrSysCfg.tNvrSysParam.eAutoLogout = ptPbNvrSysCfg->nvr_sys_param->auto_logout;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.eAutoLogout = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eAutoLogout;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_audio_listen)
		{
			g_tNvrSysCfg.tNvrSysParam.eAudioListen = ptPbNvrSysCfg->nvr_sys_param->audio_listen;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.eAudioListen = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eAudioListen;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_menu_tran)
		{
			g_tNvrSysCfg.tNvrSysParam.dwMenuTran = ptPbNvrSysCfg->nvr_sys_param->menu_tran;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.dwMenuTran = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.dwMenuTran;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_boot_guide_enable)
		{
			g_tNvrSysCfg.tNvrSysParam.byBootGuideEnable = ptPbNvrSysCfg->nvr_sys_param->boot_guide_enable;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.byBootGuideEnable = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.byBootGuideEnable;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_opt_pwd_enable)
		{
			g_tNvrSysCfg.tNvrSysParam.byOptPwdEnable = ptPbNvrSysCfg->nvr_sys_param->opt_pwd_enable;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.byOptPwdEnable = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.byOptPwdEnable;
		}

		if (ptPbNvrSysCfg->nvr_sys_param->has_delay_shutdown_time)
		{
			g_tNvrSysCfg.tNvrSysParam.dwDelayShutdownTime = ptPbNvrSysCfg->nvr_sys_param->delay_shutdown_time;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.dwDelayShutdownTime = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.dwDelayShutdownTime;
		}

		if(ptPbNvrSysCfg->nvr_sys_param->has_security_level)
		{
			g_tNvrSysCfg.tNvrSysParam.eSecurityLevel = ptPbNvrSysCfg->nvr_sys_param->security_level;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysParam.eSecurityLevel = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg.eSecurityLevel;
		}
	}
	else	///<使用默认配置
	{
		g_tNvrSysCfg.tNvrSysParam = tNvrCapDefCfg.tSysDefCfg.tNvrSysCfg;
	}

	if (NULL != ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param)
	{
		///零通道编码参数
		if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->has_enable)
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.byEnable = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enable;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.byEnable;
		}

		if (NULL != ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_res)
		{
			if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_res->has_width)
			{
				g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wWidth = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_res->width;
			}
			else
			{
				g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wWidth = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.tEncRes.wWidth;
			}

			if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_res->has_height)
			{
				g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wHeight = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_res->height;
			}
			else
			{
				g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes.wHeight = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.tEncRes.wHeight;
			}
		}
		else		///<使用默认配置
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.tEncRes = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.tEncRes;
		}

		if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->has_frame_rate)
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eFrameRate = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->frame_rate;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eFrameRate = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.eFrameRate;
		}

		if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->has_bit_rate)
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eBitRate = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->bit_rate;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eBitRate = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.eBitRate;
		}

		if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->has_video_src)
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVideoSrc = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->video_src;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVideoSrc = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.eVideoSrc;
		}
		if (ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->has_enc_type)
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVidEncType = ptPbNvrSysCfg->nvr_sys_zero_chn_enc_param->enc_type;
		}
		else
		{
			g_tNvrSysCfg.tNvrSysZeroChnEncParam.eVidEncType = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg.eVidEncType;
		}

	}
	else			///<使用默认配置
	{
		g_tNvrSysCfg.tNvrSysZeroChnEncParam = tNvrCapDefCfg.tSysDefCfg.tNvrSysZeroChnEncCfg;
	}


	if (NULL != ptPbNvrSysCfg->nvr_time_param)
	{
		///时间参数转换
		if (ptPbNvrSysCfg->nvr_time_param->has_time_zone)
		{
			g_tNvrSysCfg.tNvrTimeParam.swTimeZone = ptPbNvrSysCfg->nvr_time_param->time_zone;
		}
		else
		{
			g_tNvrSysCfg.tNvrTimeParam.swTimeZone = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.swTimeZone;
		}

		if (NULL != ptPbNvrSysCfg->nvr_time_param->summer_param)
		{
			if (ptPbNvrSysCfg->nvr_time_param->summer_param->has_enable)
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.byEnable = ptPbNvrSysCfg->nvr_time_param->summer_param->enable;
			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.byEnable;
			}

			if (ptPbNvrSysCfg->nvr_time_param->summer_param->has_offset)
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.eOffSet = ptPbNvrSysCfg->nvr_time_param->summer_param->offset;
			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.eOffSet = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.eOffSet;
			}

			if (NULL != ptPbNvrSysCfg->nvr_time_param->summer_param->begin)
			{
				if (ptPbNvrSysCfg->nvr_time_param->summer_param->begin->has_month)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byMonth = ptPbNvrSysCfg->nvr_time_param->summer_param->begin->month;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byMonth = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tBegin.byMonth;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->begin->has_week_of_month)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byWeekOfMonth = ptPbNvrSysCfg->nvr_time_param->summer_param->begin->week_of_month;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byWeekOfMonth = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tBegin.byWeekOfMonth;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->begin->has_day)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byDay = ptPbNvrSysCfg->nvr_time_param->summer_param->begin->day;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byDay = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tBegin.byDay;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->begin->has_hour)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byHour = ptPbNvrSysCfg->nvr_time_param->summer_param->begin->hour;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin.byHour = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tBegin.byHour;
				}
			}
			else		///<使用默认配置
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tBegin = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tBegin;
			}

			if (NULL != ptPbNvrSysCfg->nvr_time_param->summer_param->end)
			{
				if (ptPbNvrSysCfg->nvr_time_param->summer_param->end->has_month)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byMonth = ptPbNvrSysCfg->nvr_time_param->summer_param->end->month;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byMonth = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tEnd.byMonth;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->end->has_week_of_month)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byWeekOfMonth = ptPbNvrSysCfg->nvr_time_param->summer_param->end->week_of_month;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byWeekOfMonth = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tEnd.byWeekOfMonth;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->end->has_day)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byDay = ptPbNvrSysCfg->nvr_time_param->summer_param->end->day;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byDay = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tEnd.byDay;
				}

				if (ptPbNvrSysCfg->nvr_time_param->summer_param->end->has_hour)
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byHour = ptPbNvrSysCfg->nvr_time_param->summer_param->end->hour;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd.byHour = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tEnd.byHour;
				}
			}
			else		///<使用默认配置
			{
				g_tNvrSysCfg.tNvrTimeParam.tSummerParam.tEnd = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam.tEnd;
			}
		}
		else		///<使用默认配置
		{
			g_tNvrSysCfg.tNvrTimeParam.tSummerParam = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tSummerParam;
		}

		if (NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param)
		{
			if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->has_enable)
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byEnable = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->enable;
			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.byEnable;
			}

			if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->has_sync_type)
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->sync_type;

				///<如果能力变为不支持，配置改为自动校时
				//if(!g_tSysCap.tNvrCapTime.abySyncTypeSup[ptPbNvrSysCfg->nvr_time_param->auto_sync_param->sync_type])
				//{
				//	g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType = NVR_TIME_AUTO_SYNC_AUTO_ADAPT;
				//}
			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.eSyncType;
			}

			if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->has_ntp_enable)
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byNtpEnable = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_enable;
			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.byNtpEnable = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.byNtpEnable;
			}

			if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->has_type)
			{
				///<有这项配置说明为老的校时版本升级到新校时,新版改项不会序列化到proto
				if ((EmPbNvrSysSynctimeType)NVR_SYS_AUTO_SYNCTIME_NTP == ptPbNvrSysCfg->nvr_time_param->auto_sync_param->type)
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType = NVR_TIME_AUTO_SYNC_NTP;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eSyncType = NVR_TIME_AUTO_SYNC_AUTO_ADAPT;
				}

				NVRSYSERR("*** time sync update adapt, old type:%d ***\n", ptPbNvrSysCfg->nvr_time_param->auto_sync_param->type);

				//g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eType = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->type;
			}
			else
			{
				//g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.eType = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.eType;
			}

			

			if (NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt)
			{
				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->has_src_num)
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->src_num;

					//if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param && NULL != ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param && ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_vsip_app_enable)
					//{
						//if (!ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->vsip_app_enable)
						//{
							//if (g_tSysCap.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP])
							//{
							//	g_tSysCap.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_P_VSIP] = FALSE;
							//	g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum--;
							//}
						//}
					//}
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum;
				}

				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_pri > 0 && NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->sync_type_pri)
				{
					for (i = 0; i < MIN(ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_pri, NVR_TIME_AUTO_SYNC_TYPE_MAX); i++)
					{
						//if (g_tSysCap.tNvrCapTime.abySyncTypeSup[ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->sync_type_pri[i]])
						//{
							g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i] = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->sync_type_pri[i];
					//	}
						//else
						//{
							///能力改变，优先级列表按默认配置
						//	for(j = 0; j < tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum; ++j)
						//	{
						//		g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[j] = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[j];
						//	}
					//	g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.bySrcNum;
					//		break;
					//	}
						//if(0 == g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i])
					//	{
					//		dwNtpTime++;
					//		if(g_tSysCap.tNvrCapTime.abySyncTypeSup[NVR_TIME_AUTO_SYNC_NTP])
					//		{
						//		if(dwNtpTime > 1)
						//		{
						//			g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i] = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i];
						//		}
						//	}
						//	else
						//	{
						//		g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i] = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri[i];
						//	}
					//	}
					}
                    
                    ///<aeSyncTypePri 下标为bySrcNum索引
                    if(ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_pri<g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum)
                    {
                        g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.bySrcNum = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_pri;
                    }
				}
				else
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.aeSyncTypePri, tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri, sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.aeSyncTypePri));
				}


				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_enable > 0 && NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->sync_type_enable)
				{
					for (i = 0; i < MIN(ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->n_sync_type_enable, NVR_TIME_AUTO_SYNC_TYPE_MAX); i++)
					{
						g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable[i] = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->sync_type_enable[i];						
					}
				}
				else
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable, tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable, sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.abySyncTypeEnable));
				}


				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->has_adapt_lock_time)
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.dwAdaptLockTime = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->auto_adpt->adapt_lock_time;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt.dwAdaptLockTime = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt.dwAdaptLockTime;
				}

			}
			else
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tAutoAdpt = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tAutoAdpt;
			}

			if (NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync)
			{
				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->has_server_port)
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwServerPort = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->server_port;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwServerPort = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.dwServerPort;
				}

				if (ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->has_interval)
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwInterval = ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->interval;
				}
				else
				{
					g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.dwInterval = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.dwInterval;
				}

				if (NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->server_ip)
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIp, ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->server_ip, strlen(ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->server_ip)+1);
				}
				else
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIp, tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.achServerIp, strlen(tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.achServerIp)+1);
				}
				 ///<新增：备用NTP服务器
				if (NULL != ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->backupserver_ip)
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIPBackUp, ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->backupserver_ip, strlen(ptPbNvrSysCfg->nvr_time_param->auto_sync_param->ntp_sync->backupserver_ip)+1);
				}
				else
				{
					memcpy(g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync.achServerIPBackUp, tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.achServerIPBackUp, strlen(tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync.achServerIPBackUp)+1);
				}
			}
			else///<使用默认配置
			{
				g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam.tNtpSync = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam.tNtpSync;
			}
		}
		else	///<使用默认配置
		{
			g_tNvrSysCfg.tNvrTimeParam.tAutoSyncParam = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg.tAutoSyncParam;
		}
	}
	else		///<使用默认配置
	{
		g_tNvrSysCfg.tNvrTimeParam = tNvrCapDefCfg.tSysDefCfg.tNvrTimeCfg;
	}

	///高级配置参数转换
	if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param)
	{
		///udp重传参数转换
		if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param)
		{
			if (ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->has_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.byEnable = ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam.byEnable;
			}

			if (ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->has_first_check_point)
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwFirstCheckPoint = ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->first_check_point;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwFirstCheckPoint = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam.dwFirstCheckPoint;
			}

			if (ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->has_second_check_point)
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwSecondCheckPoint = ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->second_check_point;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwSecondCheckPoint = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam.dwSecondCheckPoint;
			}

			if (ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->has_third_check_point)
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwThirdCheckPoint = ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->third_check_point;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwThirdCheckPoint = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam.dwThirdCheckPoint;
			}

			if (ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->has_overdue_discard)
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwOverdueDiscard = ptPbNvrSysCfg->nvr_sys_advance_param->udp_re_tran_param->overdue_discard;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam.dwOverdueDiscard = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam.dwOverdueDiscard;
			}
		}
		else
		{
			g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tUdpReTranParam;
		}

		if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param)
		{
 			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_disk_prerecord)
			{

				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDiskPreRecord = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->disk_prerecord;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDiskPreRecord = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bDiskPreRecord;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_third_enc_sup)
			{


				g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->third_enc_sup;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bThirdEncSupport;
			}
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_close_audio)
			{


				g_tNvrSysCfg.tAdvanceParam.tSysParam.bCloseAudio = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->close_audio;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bCloseAudio = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bCloseAudio;
			}
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_double_aud_sup)
			{


				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioSupport= ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->double_aud_sup;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioSupport = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bDoubleAudioSupport;
			}
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_audcall_enctype)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->audcall_enctype;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.eAudType;
			}
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_blk_size)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.eBlkSize = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->blk_size;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.eBlkSize = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.eBlkSize;
			}

			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_ptz_ctrl_time)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.wPtzCtrlTime = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->ptz_ctrl_time;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.wPtzCtrlTime = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.wPtzCtrlTime;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_alarm_delay_time)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.wAlarmDelayTime = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->alarm_delay_time;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.wAlarmDelayTime = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.wAlarmDelayTime;
			}	

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_double_aud_mix)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioMix = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->double_aud_mix;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDoubleAudioMix = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bDoubleAudioMix;
			}	
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_dynamic_plugin)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->dynamic_plugin;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bDynamicPluginEnable;
			}
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_stream_extern_head)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bStreamExternHeadEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->stream_extern_head;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bStreamExternHeadEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bStreamExternHeadEnable;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_vsip_app_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipAppEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->vsip_app_enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipAppEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bVsipAppEnable;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_smooth_snd_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->smooth_snd_enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bSmoothSndEnable;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_smooth_snd_rate)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->smooth_snd_rate;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.dwSmoothRate;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_vsip_clt_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipCltEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->vsip_clt_enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bVsipCltEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bVsipCltEnable;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->has_temp_meas_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bThermeasTempEnable = ptPbNvrSysCfg->nvr_sys_advance_param->advance_sys_param->temp_meas_enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bThermeasTempEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bThermeasTempEnable;
			}
		}
		else
		{
			g_tNvrSysCfg.tAdvanceParam.tSysParam.bDiskPreRecord = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bDiskPreRecord;
			g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.bThirdEncSupport;
			g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.eAudType;
			g_tNvrSysCfg.tAdvanceParam.tSysParam.eBlkSize = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.eBlkSize;	
			g_tNvrSysCfg.tAdvanceParam.tSysParam.wPtzCtrlTime = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tSysParam.wPtzCtrlTime;
		}
		if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param->plug_download)
		{
			if(ptPbNvrSysCfg->nvr_sys_advance_param->plug_download->has_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tPlugDownload.byEnable = ptPbNvrSysCfg->nvr_sys_advance_param->plug_download->enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tPlugDownload.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.byEnable; 		
			}
			if(ptPbNvrSysCfg->nvr_sys_advance_param->plug_download->has_custom_plug_url)
			{
				memcpy(g_tNvrSysCfg.tAdvanceParam.tPlugDownload.achCustomPlugUrl,ptPbNvrSysCfg->nvr_sys_advance_param->plug_download->custom_plug_url.data,ptPbNvrSysCfg->nvr_sys_advance_param->plug_download->custom_plug_url.len);
			}
			else
			{
				memcpy(g_tNvrSysCfg.tAdvanceParam.tPlugDownload.achCustomPlugUrl,tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.achCustomPlugUrl,sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.achCustomPlugUrl));
			}
		}
		else
		{
			g_tNvrSysCfg.tAdvanceParam.tPlugDownload.byEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.byEnable;			
			memcpy(g_tNvrSysCfg.tAdvanceParam.tPlugDownload.achCustomPlugUrl,tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.achCustomPlugUrl,sizeof(tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPlugDownload.achCustomPlugUrl));
		}
		
		if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param)
		{
			if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_enable)
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.bKtcpEnable = ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->enable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.bKtcpEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.bKtcpEnable; 		
			}

            if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_conge_type)
			{
                g_tNvrSysCfg.tAdvanceParam.tKtcpParam.eCongeType = ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->conge_type;
			}
			else
			{
                g_tNvrSysCfg.tAdvanceParam.tKtcpParam.eCongeType = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.eCongeType;
			}

			if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_start_port)
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wStartPort = (u16)ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->start_port;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wStartPort = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.wStartPort;
			}
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_end_port)
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wEndPort = (u16)ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->end_port;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.wEndPort = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.wEndPort;
			}
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_bandwidth_min)
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMin = ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->bandwidth_min;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMin = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.dwBandwidthMin;
			}
			
			if(ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->has_bandwidth_max)
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMax = ptPbNvrSysCfg->nvr_sys_advance_param->ktcp_param->bandwidth_max;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tKtcpParam.dwBandwidthMax = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam.dwBandwidthMax;
			}
		}
		else
		{
			memcpy(&g_tNvrSysCfg.tAdvanceParam.tKtcpParam, &tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tKtcpParam, sizeof(TNvrSysKtcpParam));
		}

		if(NULL != ptPbNvrSysCfg->nvr_sys_advance_param->mrtc_param)
		{
			if(ptPbNvrSysCfg->nvr_sys_advance_param->mrtc_param->has_enable)
			{
                g_tNvrSysCfg.tAdvanceParam.tMrtcParam.bMrtcEnable = ptPbNvrSysCfg->nvr_sys_advance_param->mrtc_param->enable;
			}
			else
			{
                g_tNvrSysCfg.tAdvanceParam.tMrtcParam.bMrtcEnable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tMrtcParam.bMrtcEnable;
			}

            if(ptPbNvrSysCfg->nvr_sys_advance_param->mrtc_param->has_conge_type)
			{
                g_tNvrSysCfg.tAdvanceParam.tMrtcParam.eCongeType = ptPbNvrSysCfg->nvr_sys_advance_param->mrtc_param->conge_type;
			}
			else
			{
                g_tNvrSysCfg.tAdvanceParam.tMrtcParam.eCongeType = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tMrtcParam.eCongeType;
			}

		}
        else
        {
            memcpy(&g_tNvrSysCfg.tAdvanceParam.tMrtcParam, &tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tMrtcParam, sizeof(TNvrSysMrtcParam));
        }

        if (NULL != ptPbNvrSysCfg->nvr_sys_advance_param->ping_param)
        {
            if(ptPbNvrSysCfg->nvr_sys_advance_param->ping_param->has_disable)
			{
				g_tNvrSysCfg.tAdvanceParam.tPingParam.bDisable = ptPbNvrSysCfg->nvr_sys_advance_param->ping_param->disable;
			}
			else
			{
				g_tNvrSysCfg.tAdvanceParam.tPingParam.bDisable = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPingParam.bDisable;
			}
        }
        else
        {
            memcpy(&g_tNvrSysCfg.tAdvanceParam.tPingParam, &tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.tPingParam, sizeof(TNvrSysPingParam));
        }
		if(ptPbNvrSysCfg->nvr_sys_advance_param->has_mtu)
		{
			g_tNvrSysCfg.tAdvanceParam.dwMtu = ptPbNvrSysCfg->nvr_sys_advance_param->mtu;
		}
		else
		{
			g_tNvrSysCfg.tAdvanceParam.dwMtu = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.dwMtu;
		}
		if(ptPbNvrSysCfg->nvr_sys_advance_param->has_not_rtcp_port_share)
		{
			g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare = ptPbNvrSysCfg->nvr_sys_advance_param->not_rtcp_port_share;
		}
		else
		{
			g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg.bNotRtcpPortShare;
		}
	}
	else
	{
		g_tNvrSysCfg.tAdvanceParam = tNvrCapDefCfg.tSysDefCfg.tNvrAdvanceCfg;
	}

	///音频绑定
	if (NULL != ptPbNvrSysCfg->aud_call_binding_param)
	{
	    NVRSYSDEBUG("=====get aud binding param succ====\n"); ///<临时添加验证问题
		if(ptPbNvrSysCfg->aud_call_binding_param->has_call_binding_num)
		{
			g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum = (u16)ptPbNvrSysCfg->aud_call_binding_param->call_binding_num;
		}
		else
		{
			g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.wCallBindingNum;
		}
		NVRSYSDEBUG("=====get aud bind num:%u====\n", g_tNvrSysCfg.tAudCallBindingParam.wCallBindingNum);

		if (ptPbNvrSysCfg->aud_call_binding_param->has_suppt_call_dev_num)
		{
			g_tNvrSysCfg.tAudCallBindingParam.wSupptCallDevNum = (u16)ptPbNvrSysCfg->aud_call_binding_param->suppt_call_dev_num;
		}
		else
		{
			g_tNvrSysCfg.tAudCallBindingParam.wSupptCallDevNum = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.wSupptCallDevNum;
		}
		NVRSYSDEBUG("=====get aud bind wSupptCallDevNum num:%u====\n", g_tNvrSysCfg.tAudCallBindingParam.wSupptCallDevNum);

		if (ptPbNvrSysCfg->aud_call_binding_param->has_call_mix)
		{
			g_tNvrSysCfg.tAudCallBindingParam.bCallMix = (BOOL32)ptPbNvrSysCfg->aud_call_binding_param->call_mix;
		}
		else
		{
			g_tNvrSysCfg.tAudCallBindingParam.bCallMix = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.bCallMix;
		}
		NVRSYSDEBUG("=====get aud bind bmix:%u====\n", g_tNvrSysCfg.tAudCallBindingParam.bCallMix);
		if (ptPbNvrSysCfg->aud_call_binding_param->has_dev_aec)
		{
			g_tNvrSysCfg.tAudCallBindingParam.bDevAec = ptPbNvrSysCfg->aud_call_binding_param->dev_aec;
		}
		else
		{
			g_tNvrSysCfg.tAudCallBindingParam.bDevAec = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.bDevAec;
		}
		NVRSYSDEBUG("=====get aud bind bDevAec:%u====\n", g_tNvrSysCfg.tAudCallBindingParam.bDevAec);

		if (ptPbNvrSysCfg->aud_call_binding_param->n_call_binding_chn_id > 0 && NULL != ptPbNvrSysCfg->aud_call_binding_param->call_binding_chn_id)
		{
			for (i = 0; i < ptPbNvrSysCfg->aud_call_binding_param->n_call_binding_chn_id; i++)
			{
				g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId[i] = (u16)ptPbNvrSysCfg->aud_call_binding_param->call_binding_chn_id[i];
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId, tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.awCallBindingChnId, sizeof(tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.awCallBindingChnId));
		}
		for (i = 0; i < ptPbNvrSysCfg->aud_call_binding_param->n_call_binding_chn_id; i++)
		{
			NVRSYSDEBUG("=====get aud bind chnid:%u====\n", g_tNvrSysCfg.tAudCallBindingParam.awCallBindingChnId[i]);
		}

		if (NULL != ptPbNvrSysCfg->aud_call_binding_param->call_dev && ptPbNvrSysCfg->aud_call_binding_param->n_call_dev > 0)
		{
			for (i = 0; i < ptPbNvrSysCfg->aud_call_binding_param->n_call_dev; i++)
			{
				if (NULL != ptPbNvrSysCfg->aud_call_binding_param->call_dev[i])
				{
					if (ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->has_enable)
					{
						g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].bEnable = ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->enable;
					}
					else
					{
						g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].bEnable = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].bEnable;
					}

					if (ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->has_unicode_len)
					{
						g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].dwUnicodeLen = ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->unicode_len;
					}
					else
					{
						g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].dwUnicodeLen = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].dwUnicodeLen;
					}

					if (ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->has_dev_unicode_name)
					{
						if(NULL != ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->dev_unicode_name.data && ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->dev_unicode_name.len > 0)
						{
							memcpy(g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName, ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->dev_unicode_name.data, ptPbNvrSysCfg->aud_call_binding_param->call_dev[i]->dev_unicode_name.len);
						}
						else
						{
							memcpy(g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName, tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName, sizeof(tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName));
						}
					}
					else
					{
						memcpy(g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName, tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName, sizeof(tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i].achDevUnicodeName));
					}

				}
				else
				{
					g_tNvrSysCfg.tAudCallBindingParam.atCallDev[i] = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev[i];
				}
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tAudCallBindingParam.atCallDev, tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev, sizeof(tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam.atCallDev));
		}
	}
	else
	{
	    NVRSYSDEBUG("=====get aud binding param failed====\n"); ///<临时添加验证问题
		g_tNvrSysCfg.tAudCallBindingParam = tNvrCapDefCfg.tSysDefCfg.tAudCallBindingParam;
	}

	///事件
	if (NULL != ptPbNvrSysCfg->manual_event_param)
	{
		if (ptPbNvrSysCfg->manual_event_param->has_event_num)
		{
			g_tNvrSysCfg.tManualEventParam.wEventNum = ptPbNvrSysCfg->manual_event_param->event_num;
		}
		else
		{
			g_tNvrSysCfg.tManualEventParam.wEventNum = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.wEventNum;
		}


		if (ptPbNvrSysCfg->manual_event_param->has_trg_event_snap)
		{
			g_tNvrSysCfg.tManualEventParam.bTrgEventSnap = ptPbNvrSysCfg->manual_event_param->trg_event_snap;
		}
		else
		{
			g_tNvrSysCfg.tManualEventParam.bTrgEventSnap = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.bTrgEventSnap;
		}


		if (NULL != ptPbNvrSysCfg->manual_event_param->event_list && ptPbNvrSysCfg->manual_event_param->n_event_list > 0)
		{
			for (i = 0; i < ptPbNvrSysCfg->manual_event_param->n_event_list; i++)
			{
				if (NULL != ptPbNvrSysCfg->manual_event_param->event_list[i])
				{
					if (ptPbNvrSysCfg->manual_event_param->event_list[i]->has_event_name_len)
					{
						g_tNvrSysCfg.tManualEventParam.atEventList[i].dwEventNameLen = ptPbNvrSysCfg->manual_event_param->event_list[i]->event_name_len;
					}
					else
					{
						g_tNvrSysCfg.tManualEventParam.atEventList[i].dwEventNameLen = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].dwEventNameLen;
					}

					if (ptPbNvrSysCfg->manual_event_param->event_list[i]->has_event_name)
					{
						if (NULL != ptPbNvrSysCfg->manual_event_param->event_list[i]->event_name.data && ptPbNvrSysCfg->manual_event_param->event_list[i]->event_name.len > 0)
						{
							memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achEventName, ptPbNvrSysCfg->manual_event_param->event_list[i]->event_name.data, ptPbNvrSysCfg->manual_event_param->event_list[i]->event_name.len);
						}
						else
						{
							memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achEventName, tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achEventName, sizeof(tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achEventName));
						}

					}
					else
					{
						memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achEventName, tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achEventName, sizeof(tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achEventName));
					}

					if (ptPbNvrSysCfg->manual_event_param->event_list[i]->has_desc_len)
					{

						g_tNvrSysCfg.tManualEventParam.atEventList[i].dwDescLen = ptPbNvrSysCfg->manual_event_param->event_list[i]->desc_len;
					}
					else
					{
						g_tNvrSysCfg.tManualEventParam.atEventList[i].dwDescLen = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].dwDescLen;
					}

					if (ptPbNvrSysCfg->manual_event_param->event_list[i]->has_desc)
					{
						if (NULL != ptPbNvrSysCfg->manual_event_param->event_list[i]->desc.data && ptPbNvrSysCfg->manual_event_param->event_list[i]->desc.len > 0)
						{
							memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achDesc, ptPbNvrSysCfg->manual_event_param->event_list[i]->desc.data, ptPbNvrSysCfg->manual_event_param->event_list[i]->desc.len);
						}
						else
						{
							memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achDesc, tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achDesc, sizeof(tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achDesc));
						}

					}
					else
					{
						memcpy(g_tNvrSysCfg.tManualEventParam.atEventList[i].achDesc, tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achDesc, sizeof(tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].achDesc));
					}

					if (ptPbNvrSysCfg->manual_event_param->event_list[i]->has_default_)
					{
						g_tNvrSysCfg.tManualEventParam.atEventList[i].bDefault = ptPbNvrSysCfg->manual_event_param->event_list[i]->default_;
					}
					else
					{
						g_tNvrSysCfg.tManualEventParam.atEventList[i].bDefault = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i].bDefault;
					}


				}
				else
				{
					g_tNvrSysCfg.tManualEventParam.atEventList[i] = tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList[i];
				}
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tManualEventParam.atEventList, tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList, sizeof(tNvrCapDefCfg.tSysDefCfg.tManualEventParam.atEventList));
		}
	}
	else
	{
		g_tNvrSysCfg.tManualEventParam = tNvrCapDefCfg.tSysDefCfg.tManualEventParam;
	}

	///地理位置
	if (NULL != ptPbNvrSysCfg->geography_pos_param)
	{
		if (ptPbNvrSysCfg->geography_pos_param->has_open_gps)
		{
			g_tNvrSysCfg.tGeographyPosParam.bOpenGps = ptPbNvrSysCfg->geography_pos_param->open_gps;
		}
		else
		{
			g_tNvrSysCfg.tGeographyPosParam.bOpenGps = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.bOpenGps;
		}

		if (ptPbNvrSysCfg->geography_pos_param->has_gps_loc_mode)
		{
			g_tNvrSysCfg.tGeographyPosParam.eGpsLocMode = ptPbNvrSysCfg->geography_pos_param->gps_loc_mode;
		}
		else
		{
			g_tNvrSysCfg.tGeographyPosParam.eGpsLocMode = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.eGpsLocMode;
		}

		/*
		if (ptPbNvrSysCfg->geography_pos_param->has_nty_ipc)
		{
			g_tNvrSysCfg.tGeographyPosParam.bNtyIpc = ptPbNvrSysCfg->geography_pos_param->nty_ipc;
		}
		else
		{
			g_tNvrSysCfg.tGeographyPosParam.bNtyIpc = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.bNtyIpc;
		}
		*/
		
		/*
		if (ptPbNvrSysCfg->geography_pos_param->n_upload_pubsec > 0 && NULL != ptPbNvrSysCfg->geography_pos_param->upload_pubsec)
		{
			for (i = 0; i < ptPbNvrSysCfg->geography_pos_param->n_upload_pubsec; i++)
			{
				g_tNvrSysCfg.tGeographyPosParam.abUploadPubsec[i] = ptPbNvrSysCfg->geography_pos_param->upload_pubsec[i];
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tGeographyPosParam.abUploadPubsec, tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.abUploadPubsec, sizeof(tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.abUploadPubsec));
		}
		*/

		if (ptPbNvrSysCfg->geography_pos_param->limit_speed)
		{
			if (ptPbNvrSysCfg->geography_pos_param->limit_speed->has_enable)
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bEnable = ptPbNvrSysCfg->geography_pos_param->limit_speed->enable;
			}
			else
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bEnable = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.tLimitSpeed.bEnable;
			}


			if (ptPbNvrSysCfg->geography_pos_param->limit_speed->has_limit_value)
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.dwLimitValue = ptPbNvrSysCfg->geography_pos_param->limit_speed->limit_value;
			}
			else
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.dwLimitValue = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.tLimitSpeed.dwLimitValue;
			}


			if (ptPbNvrSysCfg->geography_pos_param->limit_speed->has_over_speed_report)
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bOverSpeedReport = ptPbNvrSysCfg->geography_pos_param->limit_speed->over_speed_report;
			}
			else
			{
				g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed.bOverSpeedReport = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.tLimitSpeed.bOverSpeedReport;
			}
		}
		else
		{
			g_tNvrSysCfg.tGeographyPosParam.tLimitSpeed = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam.tLimitSpeed;
		}

	}
	else
	{
		g_tNvrSysCfg.tGeographyPosParam = tNvrCapDefCfg.tSysDefCfg.tGeographyPosParam;
	}
	///升级服务器
	if(NULL != ptPbNvrSysCfg->upgrade_server_param)
	{
		if(ptPbNvrSysCfg->upgrade_server_param->has_auto_upgrade)
		{
			g_tNvrSysCfg.tUpgradeServerParam.bAutoUpgrade = ptPbNvrSysCfg->upgrade_server_param->auto_upgrade;
		}
		else
		{
			g_tNvrSysCfg.tUpgradeServerParam.bAutoUpgrade = tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam.bAutoUpgrade;
		}

		if(ptPbNvrSysCfg->upgrade_server_param->has_server_ip)
		{
			memcpy(g_tNvrSysCfg.tUpgradeServerParam.achServerIP, ptPbNvrSysCfg->upgrade_server_param->server_ip.data,
				ptPbNvrSysCfg->upgrade_server_param->server_ip.len);
		}
		else
		{
			memcpy(g_tNvrSysCfg.tUpgradeServerParam.achServerIP, tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam.achServerIP,
				sizeof(tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam.achServerIP));
		}

		if(ptPbNvrSysCfg->upgrade_server_param->has_server_port)
		{
			g_tNvrSysCfg.tUpgradeServerParam.wServerPort = ptPbNvrSysCfg->upgrade_server_param->server_port;
		}
		else
		{
			g_tNvrSysCfg.tUpgradeServerParam.wServerPort = tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam.wServerPort;
		}

	}
	else
	{
		g_tNvrSysCfg.tUpgradeServerParam = tNvrCapDefCfg.tSysDefCfg.tUpgradeServerParam;
	}

	if(NULL != ptPbNvrSysCfg->vehicle_param)
	{
		if (ptPbNvrSysCfg->vehicle_param->has_car_plate_num_len)
		{
			g_tNvrSysCfg.tVehicleParam.dwCarPlateNumLen = ptPbNvrSysCfg->vehicle_param->car_plate_num_len;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.dwCarPlateNumLen = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.dwCarPlateNumLen;
		}

		if (ptPbNvrSysCfg->vehicle_param->has_car_plate_num)
		{
			if (NULL != ptPbNvrSysCfg->vehicle_param->car_plate_num.data && ptPbNvrSysCfg->vehicle_param->car_plate_num.len > 0)
			{
				memcpy(g_tNvrSysCfg.tVehicleParam.achCarPlateNum, ptPbNvrSysCfg->vehicle_param->car_plate_num.data, ptPbNvrSysCfg->vehicle_param->car_plate_num.len);
			}
			else
			{
				memcpy(g_tNvrSysCfg.tVehicleParam.achCarPlateNum, tNvrCapDefCfg.tSysDefCfg.tVehicleParam.achCarPlateNum, sizeof(tNvrCapDefCfg.tSysDefCfg.tVehicleParam.achCarPlateNum));
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tVehicleParam.achCarPlateNum, tNvrCapDefCfg.tSysDefCfg.tVehicleParam.achCarPlateNum, sizeof(tNvrCapDefCfg.tSysDefCfg.tVehicleParam.achCarPlateNum));
		}

		if (ptPbNvrSysCfg->vehicle_param->has_shutdown_poe_sup)
		{
			g_tNvrSysCfg.tVehicleParam.bShutdownPoeSup = ptPbNvrSysCfg->vehicle_param->shutdown_poe_sup;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bShutdownPoeSup = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bShutdownPoeSup;
		}

		if (ptPbNvrSysCfg->vehicle_param->has_poe_power_enable)
		{
			g_tNvrSysCfg.tVehicleParam.bPoePowerEnable = ptPbNvrSysCfg->vehicle_param->poe_power_enable;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bPoePowerEnable = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bPoePowerEnable;
		}
		if (ptPbNvrSysCfg->vehicle_param->has_flameout_alarm_enable)
		{
			g_tNvrSysCfg.tVehicleParam.bFlameoutAlarmEnable = ptPbNvrSysCfg->vehicle_param->flameout_alarm_enable;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bFlameoutAlarmEnable = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bFlameoutAlarmEnable;
		}

		if (ptPbNvrSysCfg->vehicle_param->has_ptzout_power_enable)
		{
			g_tNvrSysCfg.tVehicleParam.bPtzOutPowerEnable = ptPbNvrSysCfg->vehicle_param->ptzout_power_enable;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bPtzOutPowerEnable = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bPtzOutPowerEnable;
		}
		if (ptPbNvrSysCfg->vehicle_param->has_close_ptzout_power_enable)
		{
			g_tNvrSysCfg.tVehicleParam.bClosePtzOutPowerEnable = ptPbNvrSysCfg->vehicle_param->close_ptzout_power_enable;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bClosePtzOutPowerEnable = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bClosePtzOutPowerEnable;
		}

		if (ptPbNvrSysCfg->vehicle_param->has_sync_lable)
		{
			g_tNvrSysCfg.tVehicleParam.bSyncLable = ptPbNvrSysCfg->vehicle_param->sync_lable;
		}
		else
		{
			g_tNvrSysCfg.tVehicleParam.bSyncLable = tNvrCapDefCfg.tSysDefCfg.tVehicleParam.bSyncLable;
		}
	}
	else
	{
		g_tNvrSysCfg.tVehicleParam = tNvrCapDefCfg.tSysDefCfg.tVehicleParam;
	}

	if(NULL != ptPbNvrSysCfg->message_submit_param)
	{
		if(ptPbNvrSysCfg->message_submit_param->has_upload_plat_form_gps)
		{
			g_tNvrSysCfg.tMessageSubmitParam.bUploadPlatformGps = ptPbNvrSysCfg->message_submit_param->upload_plat_form_gps;
		}
		else
		{
			g_tNvrSysCfg.tMessageSubmitParam.bUploadPlatformGps = tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam.bUploadPlatformGps;
		}
		
		if(ptPbNvrSysCfg->message_submit_param->n_upload_pubsec > 0 && NULL != ptPbNvrSysCfg->message_submit_param->upload_pubsec)
		{
			for(i = 0; i < ptPbNvrSysCfg->message_submit_param->n_upload_pubsec; i++)
			{
				g_tNvrSysCfg.tMessageSubmitParam.abUploadPubsec[i] = ptPbNvrSysCfg->message_submit_param->upload_pubsec[i];
			}
		}
		else
		{
			memcpy(g_tNvrSysCfg.tMessageSubmitParam.abUploadPubsec, tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam.abUploadPubsec, sizeof(g_tNvrSysCfg.tMessageSubmitParam.abUploadPubsec));
		}

		if(ptPbNvrSysCfg->message_submit_param->has_nty_ipc_gps)
		{
			g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcGps = ptPbNvrSysCfg->message_submit_param->nty_ipc_gps;
		}
		else
		{
			g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcGps = tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam.bNtyIpcGps;
		}

		if(ptPbNvrSysCfg->message_submit_param->has_send_gps_interval)
		{
			g_tNvrSysCfg.tMessageSubmitParam.wSendGpsInterval = ptPbNvrSysCfg->message_submit_param->send_gps_interval;
		}
		else
		{
			g_tNvrSysCfg.tMessageSubmitParam.wSendGpsInterval = tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam.wSendGpsInterval;
		}

		if(ptPbNvrSysCfg->message_submit_param->has_nty_ipc_net)
		{
			g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcNet = ptPbNvrSysCfg->message_submit_param->nty_ipc_net;
		}
		else
		{
			g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcNet = tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam.bNtyIpcNet;
		}
	}
	else
	{
		g_tNvrSysCfg.tMessageSubmitParam = tNvrCapDefCfg.tSysDefCfg.tMessageSubmitParam;
	}

	///<以下为兼容之前Gps信息报送的配置
	if(NULL != ptPbNvrSysCfg->geography_pos_param)
	{
		if(ptPbNvrSysCfg->geography_pos_param->has_nty_ipc)
		{
			g_tNvrSysCfg.tMessageSubmitParam.bNtyIpcGps = ptPbNvrSysCfg->geography_pos_param->nty_ipc;
		}

		if(ptPbNvrSysCfg->geography_pos_param->n_upload_pubsec > 0 && NULL != ptPbNvrSysCfg->geography_pos_param->upload_pubsec)
		{
			for(i = 0; i < ptPbNvrSysCfg->geography_pos_param->n_upload_pubsec; i++)
			{
				g_tNvrSysCfg.tMessageSubmitParam.abUploadPubsec[i] = ptPbNvrSysCfg->geography_pos_param->upload_pubsec[i];
			}
		}

	}

	if(NULL != ptPbNvrSysCfg->ball_ctr_matin_param)
	{
		if(ptPbNvrSysCfg->ball_ctr_matin_param->has_shut_down_enable)
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byShutDownEnable = ptPbNvrSysCfg->ball_ctr_matin_param->shut_down_enable;
		}
		else
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byShutDownEnable = tNvrCapDefCfg.tSysDefCfg.tBallCtrMatinParam.byShutDownEnable;
		}
		if(ptPbNvrSysCfg->ball_ctr_matin_param->has_light_enable)
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byLightEnbale = ptPbNvrSysCfg->ball_ctr_matin_param->light_enable;
		}
		else
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byLightEnbale = tNvrCapDefCfg.tSysDefCfg.tBallCtrMatinParam.byLightEnbale;
		}
		if(ptPbNvrSysCfg->ball_ctr_matin_param->has_vehicle_mode_enable)
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byVehicleModeEnbale = ptPbNvrSysCfg->ball_ctr_matin_param->vehicle_mode_enable;
		}
		else
		{
			g_tNvrSysCfg.tBallCtrMatinParam.byVehicleModeEnbale = tNvrCapDefCfg.tSysDefCfg.tBallCtrMatinParam.byVehicleModeEnbale;
		}
	}
	else
	{
		memcpy(&g_tNvrSysCfg.tBallCtrMatinParam, &tNvrCapDefCfg.tSysDefCfg.tBallCtrMatinParam, sizeof(g_tNvrSysCfg.tBallCtrMatinParam));
	}
	

	if(NULL != ptPbNvrSysCfg->lan_encid_bind_param && ptPbNvrSysCfg->lan_encid_bind_param->n_eth_info_list > 0)
	{
		for (i = 0; i < ptPbNvrSysCfg->lan_encid_bind_param->n_eth_info_list; i++)
		{
			if (NULL != ptPbNvrSysCfg->lan_encid_bind_param->eth_info_list[i] &&
				ptPbNvrSysCfg->lan_encid_bind_param->eth_info_list[i]->n_enc_id_switch > 0 &&
				NULL != ptPbNvrSysCfg->lan_encid_bind_param->eth_info_list[i]->enc_id_switch)
			{
				for (j = 0; j < ptPbNvrSysCfg->lan_encid_bind_param->eth_info_list[i]->n_enc_id_switch; j++)
				{
					g_tNvrSysCfg.tLanEncIdBindParam.tEthInfo[i].abEncidSwitch[j] = ptPbNvrSysCfg->lan_encid_bind_param->eth_info_list[i]->enc_id_switch[j];
				}
			}
			else
			{
				g_tNvrSysCfg.tLanEncIdBindParam.tEthInfo[i] = tNvrCapDefCfg.tSysDefCfg.tLanEncIdBindParam.tEthInfo[i];
			}
		}
	}
	else
	{
		memcpy(&g_tNvrSysCfg.tLanEncIdBindParam, &tNvrCapDefCfg.tSysDefCfg.tLanEncIdBindParam, sizeof(g_tNvrSysCfg.tLanEncIdBindParam));
	}

	NVRSYSIMP("end\n");

	return eRet;
}


NVRSTATUS NvrSysCfgSave(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文

	mzero(tPbAlocator);
	mzero(tPbAllocData)

	///内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	do
	{
		///序列化动作
		eRet = NvrSysCfgStructToProto(&tPbcSimple);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysCfgStructToProto failed ret:%d\n", eRet);
			break;
		}

		///配置写入配置文件中
		eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_CFG, tPbcSimple.data, tPbcSimple.len);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
			break;
		}

	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	return eRet;
}

NVRSTATUS NvrSysSetSecurity(u8 bySecurity)
{
	TNvrSysParam tSysParam;
	mzero(tSysParam);
	NvrSysGetSysParam(&tSysParam);
	tSysParam.eSecurityLevel = bySecurity;
	NvrSysSetSysParam(&tSysParam);
	return NVR_ERR__OK;
}


NVRSTATUS NvrSysSetSysParam(PTNvrSysParam ptNvrSysParam)
{
	NVRSYSMEMAPI();
	NVRSYS_ASSERT(ptNvrSysParam);
	NVRSTATUS eRet = NVR_ERR__OK;
	char achDevName[NVR_SYS_MAX_DEVNANE_LEN*4+1];
	u8 abyNvrChnName[NVR_MAX_STR64_LEN];
	s32 nRet = 0;
	u32 dwNameLen = 0;
	TNvrCapHwCapInfo tHwCapInfo;
	BOOL32 bSecuChange = FALSE; ///<加密级别发生变化,航天12院35114项目定制
	TNvrOsdParam tOsdParam;

	mzero(tOsdParam);
	mzero(tHwCapInfo);
	mzero(achDevName);
	mzero(abyNvrChnName);

	OsApi_SemTake(g_hSysCfgRWSem);

	do
	{

		nRet = CharConvConvertUnicodetoUtf8(ptNvrSysParam->abyDevName, ptNvrSysParam->wDevNameLen, achDevName, sizeof(achDevName));
		if (0 != nRet)
		{
			NVRSYSERR("CharConvConvertUnicodetoUtf8 failed\n");
		}

		NVRSYSDEBUG("devname:%s, namelen:%d, devnum:%lu, language:%d, logout:%d, audio listen:%d, tran:%d, bootguide:%d, optpwd:%d delay shutdown time:%lu eSecurityLevel:%u\n",
		achDevName,
		ptNvrSysParam->wDevNameLen,
		ptNvrSysParam->dwDevNum,
		ptNvrSysParam->eLanguage,
		ptNvrSysParam->eAutoLogout,
		ptNvrSysParam->eAudioListen,
		ptNvrSysParam->dwMenuTran,
		ptNvrSysParam->byBootGuideEnable,
		ptNvrSysParam->byOptPwdEnable,
		ptNvrSysParam->dwDelayShutdownTime, 
		ptNvrSysParam->eSecurityLevel);

		if(NVRSYSILLEGAL(g_tSysCap.tNvrCapSysBasic.tDevNum.nMinValue, ptNvrSysParam->dwDevNum, g_tSysCap.tNvrCapSysBasic.tDevNum.nMaxValue)
		|| NVRSYSILLEGAL(g_tSysCap.tNvrCapSysBasic.tDevNameLen.nMinValue * 2, ptNvrSysParam->wDevNameLen, (g_tSysCap.tNvrCapSysBasic.tDevNameLen.nMaxValue + 1)*2)
		|| NVRSYSILLEGAL(g_tSysCap.tNvrCapSysBasic.tMenuTran.nMinValue, ptNvrSysParam->dwMenuTran, g_tSysCap.tNvrCapSysBasic.tMenuTran.nMaxValue))
		{
			NVRSYSERR("param illgegal\n");

			eRet = NVR_ERR__PARAM_INVALID;
			break;
		}

		if(ptNvrSysParam->eSecurityLevel != g_tNvrSysCfg.tNvrSysParam.eSecurityLevel)
		{
			bSecuChange = TRUE;
		}

		g_tNvrSysCfg.tNvrSysParam = *ptNvrSysParam;

		eRet = NvrSysCfgSave();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
			break;
		}

		eRet = NvrCapGetCapParam(NVR_CAP_ID_HW, (void *)&tHwCapInfo);
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("get sys cap   NVR_CAP_ID_HW failed, ret:%d\n", eRet);
			break;
		}

		if (NVR_DEV_TYPE_IPC == tHwCapInfo.eDevType || NVR_DEV_TYPE_PTZ == tHwCapInfo.eDevType)
		{
			dwNameLen = sizeof(abyNvrChnName);
			CharConvConvertUtf8toUnicode(achDevName, abyNvrChnName, &dwNameLen);
			NvrPuiModifyNvrChnName(0, abyNvrChnName, dwNameLen, NULL, NULL);
		}

	}while(0);


	OsApi_SemGive(g_hSysCfgRWSem);

    if(NVR_ERR__OK == eRet)
    {
        NvrSysCfgChangeNotify(NVR_SYS_SYSTEM_PARAM, ptNvrSysParam, sizeof(TNvrSysParam));
		if(bSecuChange)
		{
			NvrPuiGetDevParam(0, 0, NVR_PUI_OSD_PARAM, &tOsdParam);
			NvrPuiSetDevParam(0, 0, NVR_PUI_OSD_PARAM, &tOsdParam);
		}
    }

	return eRet;
}

NVRSTATUS NvrSysGetSysParam(PTNvrSysParam ptNvrSysParam)
{
	NVRSYSMEMAPI();
	NVRSYS_ASSERT(ptNvrSysParam);
	char achDevName[NVR_SYS_MAX_DEVNANE_LEN*4+1];
	s32 nRet = 0;

	mzero(achDevName);

	OsApi_SemTake(g_hSysCfgRWSem);

	*ptNvrSysParam = g_tNvrSysCfg.tNvrSysParam;

	OsApi_SemGive(g_hSysCfgRWSem);
	nRet = CharConvConvertUnicodetoUtf8(ptNvrSysParam->abyDevName, ptNvrSysParam->wDevNameLen, achDevName, sizeof(achDevName));
	if (0 != nRet)
	{
		NVRSYSERR("CharConvConvertUnicodetoUtf8 failed\n");
	}

	NVRSYSDEBUG("devname:%s, namelen:%d, devnum:%lu, language:%d, logout:%d, audio listen:%d, tran:%d, bootguide:%d, optpwd:%d, delay shutdown time:%lu achPreDevType:%s eSecurityLevel%u\n",
	achDevName,
	ptNvrSysParam->wDevNameLen,
	ptNvrSysParam->dwDevNum,
	ptNvrSysParam->eLanguage,
	ptNvrSysParam->eAutoLogout,
	ptNvrSysParam->eAudioListen,
	ptNvrSysParam->dwMenuTran,
	ptNvrSysParam->byBootGuideEnable,
	ptNvrSysParam->byOptPwdEnable,
	ptNvrSysParam->dwDelayShutdownTime,
	ptNvrSysParam->achPreDevType, 
	ptNvrSysParam->eSecurityLevel);

	return NVR_ERR__OK;
}
NVRSTATUS NvrSysPowerWasteDefaultCfgInit(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;	
	TNvrCapDefaultCfg tDefCfg;

	mzero(tDefCfg);
	
	eRet = NvrCapGetCapParam(NVR_CAP_ID_DEF_CFG, &tDefCfg);
	if (NVR_ERR__OK != eRet)
	{
		NVRSYSDEBUG("get cap def cfg failed ret:%d\n", eRet);
		return eRet;
	}
	g_tPowerWasteModeParam = tDefCfg.tSysDefCfg.tPowerWasteModeParam;
	
	
	NVRSYSDEBUG("default power waste mode cfg init success\n");
	
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysPowerWasteModeCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
	ASSERTSURE(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;

	u32 dwBufLen = 0;
	u32 i = 0;
	u32 j = 0;
	//u32 k = 0;

	TNvrPointerNode *ptNodeHead = NULL; 	///<保存malloc出来的指针队列头结点
	
	TPbNvrSysPowerWasteModeInfo* ptTPbNvrSysPowerWasteModeInfo = NULL;
	TPbNvrSysSleepOfModeInfo* ptTPbNvrSysSleepOfModeInfo = NULL;
	TPbNvrSysSleepInfo* ptTPbNvrSysSleepInfo = NULL;
	TPbNvrSysLowPowerSleepInfo* ptTPbNvrSysLowPowerSleepInfo = NULL;
	TPbNvrSysTimedSleepInfo* ptTPbNvrSysTimedSleepInfo = NULL;
	TPbNvrSysSleepTimeOfDay* ptTPbNvrSysSleepTimeOfDay = NULL;
	TPbNvrSysTimeSeg* ptTPbNvrSysTimeSeg = NULL;
	
	TPbNvrSysPowerWasteModeInfo tTPbNvrSysPowerWasteModeInfo = TPB_NVR_SYS_POWER_WASTE_MODE_INFO__INIT;
	TPbNvrSysSleepOfModeInfo tTPbNvrSysSleepOfModeInfo = TPB_NVR_SYS_SLEEP_OF_MODE_INFO__INIT;
	TPbNvrSysSleepInfo tTPbNvrSysSleepInfo = TPB_NVR_SYS_SLEEP_INFO__INIT;
	TPbNvrSysLowPowerSleepInfo tTPbNvrSysLowPowerSleepInfo = TPB_NVR_SYS_LOW_POWER_SLEEP_INFO__INIT;
	TPbNvrSysTimedSleepInfo tTPbNvrSysTimedSleepInfo = TPB_NVR_SYS_TIMED_SLEEP_INFO__INIT;
	TPbNvrSysSleepTimeOfDay tTPbNvrSysSleepTimeOfDay = TPB_NVR_SYS_SLEEP_TIME_OF_DAY__INIT;
	TPbNvrSysTimeSeg tTPbNvrSysTimeSeg = TPB_NVR_SYS_TIME_SEG__INIT;
	do 
	{
		ptTPbNvrSysPowerWasteModeInfo = (TPbNvrSysPowerWasteModeInfo*)NVRALLOC(sizeof(TPbNvrSysPowerWasteModeInfo));
		if (NULL == ptTPbNvrSysPowerWasteModeInfo)
		{
			NVRNETFLASHERR("malloc ptTPbNvrSysPowerWasteModeInfo failed\n");
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysPowerWasteModeInfo);

		*ptTPbNvrSysPowerWasteModeInfo = tTPbNvrSysPowerWasteModeInfo;		///<初始化
		ptTPbNvrSysPowerWasteModeInfo->has_power_waste_mode = TRUE;
		ptTPbNvrSysPowerWasteModeInfo->power_waste_mode = g_tPowerWasteModeParam.ePowerWasteMode;

		///<全功耗模式休眠设置开始
		ptTPbNvrSysSleepOfModeInfo = (TPbNvrSysSleepOfModeInfo *)NVRALLOC(sizeof(TPbNvrSysSleepOfModeInfo));
		if (NULL == ptTPbNvrSysSleepOfModeInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysSleepOfModeInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepOfModeInfo);

		*ptTPbNvrSysSleepOfModeInfo = tTPbNvrSysSleepOfModeInfo;			///<初始化protobuf结构体

		ptTPbNvrSysPowerWasteModeInfo->full_waste_sleep = ptTPbNvrSysSleepOfModeInfo;

		///<低电休眠配置开始
		ptTPbNvrSysLowPowerSleepInfo = (TPbNvrSysLowPowerSleepInfo *)NVRALLOC(sizeof(TPbNvrSysLowPowerSleepInfo));
		if (NULL == ptTPbNvrSysLowPowerSleepInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysLowPowerSleepInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysLowPowerSleepInfo);

		*ptTPbNvrSysLowPowerSleepInfo = tTPbNvrSysLowPowerSleepInfo;			///<初始化protobuf结构体

		ptTPbNvrSysSleepOfModeInfo->low_power_sleep = ptTPbNvrSysLowPowerSleepInfo;
		ptTPbNvrSysLowPowerSleepInfo->has_enable = TRUE;
		ptTPbNvrSysLowPowerSleepInfo->enable = g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.bEnable;
		ptTPbNvrSysLowPowerSleepInfo->has_sleep_power_threshold = TRUE;
		ptTPbNvrSysLowPowerSleepInfo->sleep_power_threshold = g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold;
		///<低电休眠配置结束
		///<定时休眠开始
		ptTPbNvrSysTimedSleepInfo = (TPbNvrSysTimedSleepInfo *)NVRALLOC(sizeof(TPbNvrSysTimedSleepInfo));
		if (NULL == ptTPbNvrSysTimedSleepInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysTimedSleepInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimedSleepInfo);

		*ptTPbNvrSysTimedSleepInfo = tTPbNvrSysTimedSleepInfo;			///<初始化protobuf结构体

		ptTPbNvrSysSleepOfModeInfo->timed_sleep = ptTPbNvrSysTimedSleepInfo;
		ptTPbNvrSysTimedSleepInfo->has_enable = TRUE;
		ptTPbNvrSysTimedSleepInfo->enable = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.bEnable;
		
		ptTPbNvrSysTimedSleepInfo->n_sleep_of_week = NVR_WEEK_MAX;
		ptTPbNvrSysTimedSleepInfo->sleep_of_week = (TPbNvrSysSleepTimeOfDay **)NVRALLOC(ptTPbNvrSysTimedSleepInfo->n_sleep_of_week*sizeof(TPbNvrSysSleepTimeOfDay *));
		if (NULL == ptTPbNvrSysTimedSleepInfo->sleep_of_week)
		{
			NVRNETFLASHERR("malloc TPbNvrSysSleepTimeOfDay * failed\n");
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimedSleepInfo->sleep_of_week);
		for (i = 0; i < ptTPbNvrSysTimedSleepInfo->n_sleep_of_week; i++)
		{
			ptTPbNvrSysSleepTimeOfDay = (TPbNvrSysSleepTimeOfDay *)NVRALLOC(sizeof(TPbNvrSysSleepTimeOfDay));
			if (NULL == ptTPbNvrSysSleepTimeOfDay)
			{
				NVRNETFLASHERR("%lu malloc ptTPbNvrSysSleepTimeOfDay failed\n", i);
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}
			NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepTimeOfDay);

			*ptTPbNvrSysSleepTimeOfDay = tTPbNvrSysSleepTimeOfDay;			///<初始化protobuf结构体

			ptTPbNvrSysTimedSleepInfo->sleep_of_week[i] = ptTPbNvrSysSleepTimeOfDay;

			ptTPbNvrSysSleepTimeOfDay->has_num = TRUE;
			ptTPbNvrSysSleepTimeOfDay->num = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum;

			ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day = NVR_SYS_MAX_TIME_SEG;
			ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day = (TPbNvrSysTimeSeg **)NVRALLOC(ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day*sizeof(TPbNvrSysTimeSeg *));
			if (NULL == ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day)
			{
				NVRNETFLASHERR("malloc TPbNvrSysTimeSeg * failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}
			NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day);
			
			for (j = 0; j < ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day; j++)
			{
				ptTPbNvrSysTimeSeg = (TPbNvrSysTimeSeg *)NVRALLOC(sizeof(TPbNvrSysTimeSeg));
				if (NULL == ptTPbNvrSysTimeSeg)
				{
					NVRNETFLASHERR("%lu malloc ptTPbNvrSysTimeSeg failed\n", i);
					eRet = NVR_ERR__MALLOC_FAILED;
					break;
				}
				NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimeSeg);

				*ptTPbNvrSysTimeSeg = tTPbNvrSysTimeSeg;			///<初始化protobuf结构体

				ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day[j] = ptTPbNvrSysTimeSeg;

				ptTPbNvrSysTimeSeg->has_start_time = TRUE;
				ptTPbNvrSysTimeSeg->start_time = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime;
				ptTPbNvrSysTimeSeg->has_end_time = TRUE;
				ptTPbNvrSysTimeSeg->end_time = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime;
				ptTPbNvrSysTimeSeg->has_sleep_snap_mode = TRUE;
				ptTPbNvrSysTimeSeg->sleep_snap_mode = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode;
				ptTPbNvrSysTimeSeg->has_snap_time_interval = TRUE;
				ptTPbNvrSysTimeSeg->snap_time_interval = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval;
				ptTPbNvrSysTimeSeg->has_link_ptz_mode = TRUE;
				ptTPbNvrSysTimeSeg->link_ptz_mode = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode;
				ptTPbNvrSysTimeSeg->has_preset_num = TRUE;
				ptTPbNvrSysTimeSeg->preset_num = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum;
				ptTPbNvrSysTimeSeg->has_path_num = TRUE;
				ptTPbNvrSysTimeSeg->path_num = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum;
				ptTPbNvrSysTimeSeg->has_upload_post_mode = TRUE;
				ptTPbNvrSysTimeSeg->upload_post_mode = g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode;

			}
		}

		
		///<定时休眠结束
		///<全功耗模式休眠设置结束

		
		///<低功耗模式休眠配置开始
		ptTPbNvrSysSleepOfModeInfo = (TPbNvrSysSleepOfModeInfo *)NVRALLOC(sizeof(TPbNvrSysSleepOfModeInfo));
		if (NULL == ptTPbNvrSysSleepOfModeInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysSleepOfModeInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepOfModeInfo);

		*ptTPbNvrSysSleepOfModeInfo = tTPbNvrSysSleepOfModeInfo;			///<初始化protobuf结构体

		ptTPbNvrSysPowerWasteModeInfo->low_waste_sleep = ptTPbNvrSysSleepOfModeInfo;
		///<低电休眠配置开始
		ptTPbNvrSysLowPowerSleepInfo = (TPbNvrSysLowPowerSleepInfo *)NVRALLOC(sizeof(TPbNvrSysLowPowerSleepInfo));
		if (NULL == ptTPbNvrSysLowPowerSleepInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysLowPowerSleepInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysLowPowerSleepInfo);

		*ptTPbNvrSysLowPowerSleepInfo = tTPbNvrSysLowPowerSleepInfo;			///<初始化protobuf结构体

		ptTPbNvrSysSleepOfModeInfo->low_power_sleep = ptTPbNvrSysLowPowerSleepInfo;
		ptTPbNvrSysLowPowerSleepInfo->has_enable = TRUE;
		ptTPbNvrSysLowPowerSleepInfo->enable = g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.bEnable;
		ptTPbNvrSysLowPowerSleepInfo->has_sleep_power_threshold = TRUE;
		ptTPbNvrSysLowPowerSleepInfo->sleep_power_threshold = g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold;
		///<低电休眠配置结束
		///<定时休眠开始
		ptTPbNvrSysTimedSleepInfo = (TPbNvrSysTimedSleepInfo *)NVRALLOC(sizeof(TPbNvrSysTimedSleepInfo));
		if (NULL == ptTPbNvrSysTimedSleepInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysTimedSleepInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimedSleepInfo);

		*ptTPbNvrSysTimedSleepInfo = tTPbNvrSysTimedSleepInfo;			///<初始化protobuf结构体

		ptTPbNvrSysSleepOfModeInfo->timed_sleep = ptTPbNvrSysTimedSleepInfo;

		ptTPbNvrSysTimedSleepInfo->has_enable = TRUE;
		ptTPbNvrSysTimedSleepInfo->enable = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.bEnable;
		ptTPbNvrSysTimedSleepInfo->n_sleep_of_week = NVR_WEEK_MAX;
		ptTPbNvrSysTimedSleepInfo->sleep_of_week = (TPbNvrSysSleepTimeOfDay **)NVRALLOC(ptTPbNvrSysTimedSleepInfo->n_sleep_of_week*sizeof(TPbNvrSysSleepTimeOfDay *));
		if (NULL == ptTPbNvrSysTimedSleepInfo->sleep_of_week)
		{
			NVRNETFLASHERR("malloc TPbNvrSysSleepTimeOfDay * failed\n");
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimedSleepInfo->sleep_of_week);
		for (i = 0; i < ptTPbNvrSysTimedSleepInfo->n_sleep_of_week; i++)
		{
			ptTPbNvrSysSleepTimeOfDay = (TPbNvrSysSleepTimeOfDay *)NVRALLOC(sizeof(TPbNvrSysSleepTimeOfDay));
			if (NULL == ptTPbNvrSysSleepTimeOfDay)
			{
				NVRNETFLASHERR("%lu malloc ptTPbNvrSysSleepTimeOfDay failed\n", i);
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}
			NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepTimeOfDay);

			*ptTPbNvrSysSleepTimeOfDay = tTPbNvrSysSleepTimeOfDay;			///<初始化protobuf结构体

			ptTPbNvrSysTimedSleepInfo->sleep_of_week[i] = ptTPbNvrSysSleepTimeOfDay;

			ptTPbNvrSysSleepTimeOfDay->has_num = TRUE;
			ptTPbNvrSysSleepTimeOfDay->num = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum;
			ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day = NVR_SYS_MAX_TIME_SEG;
			ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day = (TPbNvrSysTimeSeg **)NVRALLOC(ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day*sizeof(TPbNvrSysTimeSeg *));
			if (NULL == ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day)
			{
				NVRNETFLASHERR("malloc TPbNvrSysTimeSeg * failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}
			NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day);
			
			for (j = 0; j < ptTPbNvrSysSleepTimeOfDay->n_sleep_time_of_day; j++)
			{
				ptTPbNvrSysTimeSeg = (TPbNvrSysTimeSeg *)NVRALLOC(sizeof(TPbNvrSysTimeSeg));
				if (NULL == ptTPbNvrSysTimeSeg)
				{
					NVRNETFLASHERR("%lu malloc ptTPbNvrSysTimeSeg failed\n", i);
					eRet = NVR_ERR__MALLOC_FAILED;
					break;
				}
				NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysTimeSeg);

				*ptTPbNvrSysTimeSeg = tTPbNvrSysTimeSeg;			///<初始化protobuf结构体

				ptTPbNvrSysSleepTimeOfDay->sleep_time_of_day[j] = ptTPbNvrSysTimeSeg;

				ptTPbNvrSysTimeSeg->has_start_time = TRUE;
				ptTPbNvrSysTimeSeg->start_time = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime;
				ptTPbNvrSysTimeSeg->has_end_time = TRUE;
				ptTPbNvrSysTimeSeg->end_time = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime;
				ptTPbNvrSysTimeSeg->has_sleep_snap_mode = TRUE;
				ptTPbNvrSysTimeSeg->sleep_snap_mode = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode;
				ptTPbNvrSysTimeSeg->has_snap_time_interval = TRUE;
				ptTPbNvrSysTimeSeg->snap_time_interval = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval;
				ptTPbNvrSysTimeSeg->has_link_ptz_mode = TRUE;
				ptTPbNvrSysTimeSeg->link_ptz_mode = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode;
				ptTPbNvrSysTimeSeg->has_preset_num = TRUE;
				ptTPbNvrSysTimeSeg->preset_num = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum;
				ptTPbNvrSysTimeSeg->has_path_num = TRUE;
				ptTPbNvrSysTimeSeg->path_num = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum;
				ptTPbNvrSysTimeSeg->has_upload_post_mode = TRUE;
				ptTPbNvrSysTimeSeg->upload_post_mode = g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode;

			}

			
		}
		
		///<定时休眠结束
		///<低功耗模式休眠配置结束
		///<休眠配置开始		
		ptTPbNvrSysSleepInfo = (TPbNvrSysSleepInfo *)NVRALLOC(sizeof(TPbNvrSysSleepInfo));
		if (NULL == ptTPbNvrSysSleepInfo)
		{
			NVRNETFLASHERR("%lu malloc ptTPbNvrSysSleepInfo failed\n", i);
			eRet = NVR_ERR__MALLOC_FAILED;
			break;
		}
		NvrSrvRecordPointer(&ptNodeHead, (void *)ptTPbNvrSysSleepInfo);

		*ptTPbNvrSysSleepInfo = tTPbNvrSysSleepInfo;			///<初始化protobuf结构体

		ptTPbNvrSysPowerWasteModeInfo->sleep_info = ptTPbNvrSysSleepInfo;
		ptTPbNvrSysSleepInfo->has_enable = TRUE;
		ptTPbNvrSysSleepInfo->enable = g_tPowerWasteModeParam.tSleepInfo.bEnable;
		ptTPbNvrSysSleepInfo->has_delay_time = TRUE;
		ptTPbNvrSysSleepInfo->delay_time = g_tPowerWasteModeParam.tSleepInfo.dwSleepDelayTime;
		///<休眠配置结束
		///<获取结构体序列化后的二进制buffer大小
		dwBufLen = tpb_nvr_sys_power_waste_mode_info__get_packed_size(ptTPbNvrSysPowerWasteModeInfo);

		NVRSYSDEBUG("get pack size :%lu \n", dwBufLen);

		if (dwBufLen > 0)
		{
			///<为ptPbcSimple->data申请内存，外部释放
			ptPbcSimple->data = NVRALLOC(dwBufLen);
			if(NULL == ptPbcSimple->data)
			{
			   NVRSYSERR("malloc pack buffer failed.\n");
			   eRet = NVR_ERR__MALLOC_FAILED;
			   break;
			}

			///序列化tPbNvrWifiMangerAll到buffer中
			ptPbcSimple->len = tpb_nvr_sys_power_waste_mode_info__pack(ptTPbNvrSysPowerWasteModeInfo, ptPbcSimple->data);
			if(dwBufLen != ptPbcSimple->len)
			{
				NVRSYSERR("pack buffer failed, pack len:%lu \n", ptPbcSimple->len);
				eRet = NVR_ERR__ERROR;
				break;
			}
		}

		
	}while(0);

	///free掉序列化过程分配的所有指针
	NvrSrvFreePointer(&ptNodeHead);
	///free掉记录指针列表的头指针

	if(NULL != ptNodeHead)
	{
		NVRFREE(ptNodeHead);
		ptNodeHead = NULL;
	}


	return eRet;
}

NVRSTATUS NvrSysPowerWasteModeCfgProtoToStruct(const TPbNvrSysPowerWasteModeInfo* ptPbNvrSysCfg)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	u32 j = 0;
	TNvrCapDefaultCfg tNvrCapDefCfg;				   ///<默认配置能力集
    ENvrCapClassId eCapClassId = NVR_CAP_ID_DEF_CFG; 	///<能力集id
	mzero(tNvrCapDefCfg);
	
	eRet = NvrCapGetCapParam(eCapClassId, (void*)&tNvrCapDefCfg);
   if(NVR_ERR__OK != eRet)
   {
	   NVRSYSERR("get defcap failed, ret:%d\n",eRet);
	   return eRet;
   }
   NVRSYSDEBUG("NvrSysPowerWasteModeCfgProtoToStruct start!!!!!!!!!!!!!");

    #if 1
	///<功耗模式
	if(ptPbNvrSysCfg->has_power_waste_mode)
	{
		g_tPowerWasteModeParam.ePowerWasteMode = ptPbNvrSysCfg->power_waste_mode;
	}
	else
	{
		g_tPowerWasteModeParam.ePowerWasteMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.ePowerWasteMode;
	}
	if(NULL != ptPbNvrSysCfg->full_waste_sleep)
	{
		if(NULL != ptPbNvrSysCfg->full_waste_sleep->low_power_sleep)
		{
			if(ptPbNvrSysCfg->full_waste_sleep->low_power_sleep->has_enable)
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.bEnable = ptPbNvrSysCfg->full_waste_sleep->low_power_sleep->enable;
			}
			else
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.bEnable = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.bEnable;
			}
			if(ptPbNvrSysCfg->full_waste_sleep->low_power_sleep->has_sleep_power_threshold)
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold = ptPbNvrSysCfg->full_waste_sleep->low_power_sleep->sleep_power_threshold;
			}
			else
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold;
			}
		}
		else
		{
			g_tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo;
		}
		#if 1
		if(NULL != ptPbNvrSysCfg->full_waste_sleep->timed_sleep)
		{
			if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->has_enable)
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.bEnable = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->enable;
			}
			else
			{
				g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.bEnable = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.bEnable;
			}
			if(NULL != ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week && ptPbNvrSysCfg->full_waste_sleep->timed_sleep->n_sleep_of_week > 0)
			{
				for(i = 0; i < ptPbNvrSysCfg->full_waste_sleep->timed_sleep->n_sleep_of_week; i++)
				{
					if(NULL != ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i])
					{
						if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->has_num)
						{
							g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->num;
						}
						else
						{
							g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum;
						}
						if(NULL != ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day && ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->n_sleep_time_of_day > 0)
						{
							for(j = 0; j < ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->n_sleep_time_of_day; j++)
							{
								if(NULL != ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j])
								{
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_start_time)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->start_time;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_end_time)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->end_time;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_sleep_snap_mode)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->sleep_snap_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_snap_time_interval)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->snap_time_interval;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_link_ptz_mode)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->link_ptz_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_preset_num)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->preset_num;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_path_num)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->path_num;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum;
									}
									if(ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_upload_post_mode)
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode = ptPbNvrSysCfg->full_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->upload_post_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode;
									}
								}
								else
								{
									g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j] = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j];
								}
							}
						}
						else
						{
						
							memcpy(g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay, tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay, sizeof(g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay));
						}
					}
					else
					{
						g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i] = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i];
					}
				}
			}
			else
			{			
				memcpy(g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek, tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek, sizeof(g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek));

			}
		}
		else
		{
			g_tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo;
		}
		#endif
	}
	else
	{
		g_tPowerWasteModeParam.tFullPowerSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo;
	}
	if(NULL != ptPbNvrSysCfg->low_waste_sleep)
	{
		if(NULL != ptPbNvrSysCfg->low_waste_sleep->low_power_sleep)
		{
			if(ptPbNvrSysCfg->low_waste_sleep->low_power_sleep->has_enable)
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.bEnable = ptPbNvrSysCfg->low_waste_sleep->low_power_sleep->enable;
			}
			else
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.bEnable = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.bEnable;
			}
			if(ptPbNvrSysCfg->low_waste_sleep->low_power_sleep->has_sleep_power_threshold)
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold = ptPbNvrSysCfg->low_waste_sleep->low_power_sleep->sleep_power_threshold;
			}
			else
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tLowPowerSleepInfo.dwSleepPowerThreshold;
			}
		}
		else
		{
			g_tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tLowPowerSleepInfo;
		}
		#if 1
		if(NULL != ptPbNvrSysCfg->low_waste_sleep->timed_sleep)
		{
			if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->has_enable)
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.bEnable = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->enable;
			}
			else
			{
				g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.bEnable = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.bEnable;
			}
			if(NULL != ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week && ptPbNvrSysCfg->low_waste_sleep->timed_sleep->n_sleep_of_week > 0)
			{
				for(i = 0; i < ptPbNvrSysCfg->low_waste_sleep->timed_sleep->n_sleep_of_week; i++)
				{
					if(NULL != ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i])
					{
						if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->has_num)
						{
							g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->num;
						}
						else
						{
							g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tFullPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].byNum;
						}
						if(NULL != ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day && ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->n_sleep_time_of_day > 0)
						{
							for(j = 0; j < ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->n_sleep_time_of_day; j++)
							{
								if(NULL != ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j])
								{
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_start_time)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->start_time;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwStartTime;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_end_time)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->end_time;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwEndTime;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_sleep_snap_mode)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->sleep_snap_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eSleepSnapMode;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_snap_time_interval)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->snap_time_interval;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].dwSnapTimeInterval;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_link_ptz_mode)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->link_ptz_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eLinkPTZMode;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_preset_num)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->preset_num;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].wPresetNum;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_path_num)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->path_num;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].byPathNum;
									}
									if(ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->has_upload_post_mode)
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode = ptPbNvrSysCfg->low_waste_sleep->timed_sleep->sleep_of_week[i]->sleep_time_of_day[j]->upload_post_mode;
									}
									else
									{
										g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j].eUploadPostMode;
									}
								}
								else
								{
									g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j] = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay[j];
								}
							}
						}
						else
						{
							memcpy(g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay, tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay, sizeof(g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i].atSleepTimeOfDay));

						}
					}
					else
					{
						g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i] = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek[i];
					}
				}
			}
			else
			{					
				memcpy(g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek, tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek, sizeof(g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo.atSleepOfWeek));
			}
		}
		else
		{
			g_tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo.tTimedSleepInfo;
		}
		#endif
	}
	else
	{
		g_tPowerWasteModeParam.tLowPowerSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tLowPowerSleepInfo;
	}
		
	
	if(NULL != ptPbNvrSysCfg->sleep_info)
	{
		if(ptPbNvrSysCfg->sleep_info->has_enable)
		{
			g_tPowerWasteModeParam.tSleepInfo.bEnable = ptPbNvrSysCfg->sleep_info->enable;
		}
		else
		{
			g_tPowerWasteModeParam.tSleepInfo.bEnable = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tSleepInfo.bEnable;
		}
		if(ptPbNvrSysCfg->sleep_info->has_delay_time)
		{
			g_tPowerWasteModeParam.tSleepInfo.dwSleepDelayTime = ptPbNvrSysCfg->sleep_info->delay_time;
		}
		else
		{
			g_tPowerWasteModeParam.tSleepInfo.dwSleepDelayTime = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tSleepInfo.dwSleepDelayTime;
		}
	}
	else
	{
		g_tPowerWasteModeParam.tSleepInfo = tNvrCapDefCfg.tSysDefCfg.tPowerWasteModeParam.tSleepInfo;
	}

#endif
    return NVR_ERR__OK;
}

///设置功耗模式给小系统
NVRSTATUS NvrSysBrdWorkPwrStateCtrl(BOOL32 bSnap)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	u32 j = 0;
	static BOOL32 bFirst = TRUE;///<是否是第一次设置
	TNvrCapSortwareCapInfo tCapSoftware;
	mzero(tCapSoftware);
	///<如果支持功耗模式，把模式设置给小系统
	NvrCapGetCapParam(NVR_CAP_ID_SOFTWARE, &tCapSoftware);
	if(NVR_CAP_NONSUPPORT == tCapSoftware.bySupPowerWasteMode)
	{
		return NVR_ERR__OK;
	}
	///<模式变化设置给小系统（全功耗:0,低功耗+不开启抓拍:1,低功耗+开启抓拍:2）
	if (NVR_FULL_POWER_MODE == g_tPowerWasteModeParam.ePowerWasteMode)
	{	
		//去重
		if(0 != g_dwBrdPowerModeStat || bFirst)
		{
			g_dwBrdPowerModeStat = 0;			
			NVRSYSDEBUG("NvrSysBrdWorkPwrStateCtrl00 BrdSysWorkPwrStateCtrl 0\n");
			eRet =  NvrBrdSysWorkPwrStateCtrl(0);
			if (NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysBrdWorkPwrStateCtrl BrdSysWorkPwrStateCtrl failed ret:%d\n", eRet);
				eRet = NVR_ERR__ERROR;				
				return eRet;
			}
			

			
		}
		
	}
	else if(NVR_LOW_POWER_MODE == g_tPowerWasteModeParam.ePowerWasteMode)
	{
		NVRSYSDEBUG("NvrSysBrdWorkPwrStateCtrl snap:%d\n",bSnap);
		//去重
		if(!bSnap)
		{
			if(1 != g_dwBrdPowerModeStat || bFirst)
			{
				g_dwBrdPowerModeStat = 1;				
				NVRSYSDEBUG("NvrSysBrdWorkPwrStateCtrl11 BrdSysWorkPwrStateCtrl 1\n");
				eRet =  NvrBrdSysWorkPwrStateCtrl(1);
				if (NVR_ERR__OK != eRet)
				{
					NVRSYSERR("NvrSysBrdWorkPwrStateCtrl BrdSysWorkPwrStateCtrl failed ret:%d\n", eRet);
					eRet = NVR_ERR__ERROR;					
					return eRet;
				}
			}
		}
		else
		{
			if(2 != g_dwBrdPowerModeStat || bFirst)
			{
				g_dwBrdPowerModeStat = 2;				
				NVRSYSDEBUG("NvrSysBrdWorkPwrStateCtrl22 BrdSysWorkPwrStateCtrl 2\n");
				eRet =  NvrBrdSysWorkPwrStateCtrl(2);
				if (NVR_ERR__OK != eRet)
				{
					NVRSYSERR("NvrSysBrdWorkPwrStateCtrl BrdSysWorkPwrStateCtrl failed ret:%d\n", eRet);
					eRet = NVR_ERR__ERROR;					
					return eRet;
				}
				
			}
		
		}
		
	}
	bFirst = FALSE;
	return eRet;
	
}

NVRSTATUS NvrSysSetSysPowerWasteParam(PTNvrSysPowerWasteModeInfo ptNvrSysParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	BOOL32 modeChange = FALSE;///功耗模式是否修改	
	static BOOL32 bFirst = TRUE;///<是否是第一次设置
	OsApi_SemTake(g_hSysCfgRWSem);
	//判断功耗模式是否修改
	if(ptNvrSysParam->ePowerWasteMode != g_tPowerWasteModeParam.ePowerWasteMode || bFirst)
	{
		modeChange = TRUE;
	}
	g_tPowerWasteModeParam = *ptNvrSysParam;

	//保存配置
	eRet = NvrSysCfgPowerWasteSave();
	

	OsApi_SemGive(g_hSysCfgRWSem);

	if(NVR_ERR__OK == eRet)
	{
		///<参数变化，回调通知
		if(modeChange)
		{	NVRSYSDEBUG("NvrSysSetSysPowerWasteParam mode change mode:%d\n",ptNvrSysParam->ePowerWasteMode);	
			NvrSysCfgChangeNotify(NVR_SYS_POWER_WASTE_MODE_PARAM, ptNvrSysParam, sizeof(TNvrSysPowerWasteModeInfo));
			bFirst = FALSE;
		}

	}

    return eRet;

}


NVRSTATUS NvrSysGetSysPowerWasteParam(PTNvrSysPowerWasteModeInfo ptNvrSysParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptNvrSysParam);


	OsApi_SemTake(g_hSysCfgRWSem);

	*ptNvrSysParam = g_tPowerWasteModeParam;

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}
NVRSTATUS NvrSysCfgPowerWasteSave(void)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);   
	ProtobufCAllocator	tPbAlocator;			
	TNvrPbAllocData tPbAllocData;				

	mzero(tPbAlocator);
	mzero(tPbAllocData)

	///内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	do
	{
	
		NVRSYSDEBUG("NvrSysPowerWasteModeCfgStructToProto start\n");
		eRet = NvrSysPowerWasteModeCfgStructToProto(&tPbcSimple);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("LcamIspCfgSave LcamIspCfgStructToProto failed ret:%d\n", eRet);
			break;
		}
		NVRSYSDEBUG("NvrSysPowerWasteModeCfgStructToProto endend\n");
		eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_POWER_WASTE_MODE_CFG, tPbcSimple.data, tPbcSimple.len);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
			break;
		}

	}while(0);

	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("NvrSysCfgSave all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	return eRet;
}


NVRSTATUS NvrSysPowerWasteModeInit()
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 dwCfgBuflen = 0;
	u8	*pbyCfgBuf = NULL;					///<获取配置时保存配置数据的buf
	
	TPbNvrSysPowerWasteModeInfo *ptPbNvrSysCfg = NULL;
	ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;	

	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    
	ProtobufCAllocator	tPbAlocator;			
	TNvrPbAllocData tPbAllocData;				
	mzero(tPbAlocator);
	mzero(tPbAllocData);
	///内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	NvrSysGetRecoveryFlag(&eRecoveryType, NVR_BASE_PARAM_CFG_RESET);


	///初始化告警基本配置参数，获取失败则创建默认配置
	eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, NVR_SYS_POWER_WASTE_MODE_CFG, &dwCfgBuflen);
	if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
	{
		NVRSYSDEBUG("get unpack size :%lu \n", dwCfgBuflen);

		pbyCfgBuf = (u8 *)NVRALLOC(dwCfgBuflen);
		if(NULL == pbyCfgBuf)
		{
			NVRSYSERR("malloc SysCfgBuf failed\n");
			return NVR_ERR__MALLOC_FAILED;
		}

		eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_POWER_WASTE_MODE_CFG, pbyCfgBuf);
		if(NVR_ERR__OK == eRet)
		{
			
			NVRSYSDEBUG("NVRCfgGetParam get cfg succ\n");

			ptPbNvrSysCfg = tpb_nvr_sys_power_waste_mode_info__unpack(tPbcSimple.allocator, dwCfgBuflen, pbyCfgBuf);			///<·′DòáD?ˉ?ˉ×÷

			if (NULL != ptPbNvrSysCfg)
			{
				NvrSysPowerWasteModeCfgProtoToStruct(ptPbNvrSysCfg);

				tpb_nvr_sys_power_waste_mode_info__free_unpacked(ptPbNvrSysCfg, tPbcSimple.allocator); ///<êí·?ptPbLcamIspParamAll
			}
			else
			{
				NVRSYSDEBUG("power waste mode param all unpack failed, use default cfg\n");
				
				NvrSysPowerWasteDefaultCfgInit();
				
				eRet = NvrSysCfgPowerWasteSave();
				if(NVR_ERR__OK != eRet)
				{
					NVRSYSERR("save cfg failed ret:%d\n", eRet);
					return eRet;
				}		
			}
		}
		else
		{
			NVRSYSERR("NVRCfgGetParam get %s failed\n", NVR_SYS_POWER_WASTE_MODE_CFG);
		}

		///释放pbyCfgBuf空间
		if(pbyCfgBuf != NULL)
		{
		   NVRFREE(pbyCfgBuf);
		   pbyCfgBuf = NULL;
		}

	}
	else
	{
		NVRSYSDEBUG("create default cfg!!!!!!\n");
		
		///获取配置失败，创建默认配置
		#if 1
		eRet = NvrSysPowerWasteDefaultCfgInit();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("default cfg init failed ret:%d\n", eRet);
			return eRet;
		}		
		#endif
		///默认配置写入配置文件中
		eRet = NvrSysCfgPowerWasteSave();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
			return eRet;
		}		

	}

	return eRet;
}



NVRSTATUS NvrSysSetSoftExtendVer(char *pchExVer)
{
	NVRSYSMEMAPI();
	TNvrSysDevInfo tSysDEvInfo;
	mzero(tSysDEvInfo);
	if (NULL != pchExVer)
	{
		snprintf(g_achSoftVerExt, sizeof(g_achSoftVerExt), "%s", pchExVer);
		NVRSYSDEBUG("g_achSoftVerExt:%s\n",g_achSoftVerExt);
	}
	NvrSysGetDevInfo(&tSysDEvInfo);
	///<通知ipdt软件版本号修改(ipdt拿到ENvrSysParamType后，会调用NvrSysGetDevInfo重新获取)
	NvrSysCfgChangeNotify(NVR_SYS_SOFTVER_EXT,&tSysDEvInfo,sizeof(TNvrSysDevInfo));
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysGetCustomVersion(s8 *achCustomVersionBuf_32)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(achCustomVersionBuf_32);

	do
	{
		memset(achCustomVersionBuf_32, 0, NVR_MAX_STR32_LEN);
		eRet = NvrCapGetDefaultCfgKeyValue(NVR_DEFAULT_CAP_CFG, "CUSTOM_VERSION", achCustomVersionBuf_32, NVR_MAX_STR32_LEN);
	} while(0);

	return eRet;
}

NVRSTATUS NvrSysGetEnvInfo(PTNvrSysEnvInfo ptNvrSysEnvInfo)
{
	NVRSYS_ASSERT(ptNvrSysEnvInfo);
    s32 nRet = 0;
    THwmonStat tHwmonStat;

    if (g_tGetEnvByMcuCB)
    {
        return g_tGetEnvByMcuCB(ptNvrSysEnvInfo);
    }

    memset(&tHwmonStat, 0, sizeof(tHwmonStat));

    //获取温度传感器的温度
    tHwmonStat.dwId = HWMON_ID_TEMP(0);
    nRet = BrdHwmonGetStatus(&tHwmonStat);
    if (0 != nRet)
    {
        NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus T errno=%d\n", nRet);
    }
    NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus TEMP dwCur=%d\n", tHwmonStat.tEntry.tTemp.dwCur);
    ptNvrSysEnvInfo->nTemprature = tHwmonStat.tEntry.tTemp.dwCur/1000;

    //获取湿度传感器的湿度
    tHwmonStat.dwId = HWMON_ID_HUMIDITY(0);
    nRet = BrdHwmonGetStatus(&tHwmonStat);
    if (0 != nRet)
    {
        NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus H errno=%d\n", nRet);
    }
    NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus H dwCur=%d\n", tHwmonStat.tEntry.tHumidity.cur);
    ptNvrSysEnvInfo->nHumidity   = tHwmonStat.tEntry.tHumidity.cur;

    //获取气压传感器的气压
    tHwmonStat.dwId = HWMON_ID_PRESSURE(0);
    nRet = BrdHwmonGetStatus(&tHwmonStat);
    if (0 != nRet)
    {
        NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus P errno=%d\n", nRet);
    }
    NVRSYSDEBUG("NvrSysGetEnvInfo:BrdHwmonGetStatus P dwCur=%d\n", tHwmonStat.tEntry.tPressure.dwCur);   
    ptNvrSysEnvInfo->nDevKpa     = tHwmonStat.tEntry.tPressure.dwCur/10;
	NVRSYSDEBUG("env temprature:%d,humidity:%d,kpa:%d\n",
	ptNvrSysEnvInfo->nTemprature,
	ptNvrSysEnvInfo->nHumidity,
	ptNvrSysEnvInfo->nDevKpa);

	return NVR_ERR__OK;
}

NVRSTATUS NvrSysGetEnvInfoByMcuRegistCB(PFNvrSysGetEnvByMcuCB pfCallBack)
{
    NVRSYSMEMAPI();
    g_tGetEnvByMcuCB = pfCallBack;

    return NVR_ERR__OK;
}
void NvrSysConvertDateStr(const s8 *pchInput, s8 *pchOutput) {  
	NVRSYS_ASSERT(pchInput);
	NVRSYS_ASSERT(pchOutput);
    // 提取年份、月份和日期
    s8 *pchFormat = "yyyymmdd";  ///<日期格式
    s8 achYear[5];  
    s8 achMonth[3];  
    s8 achDay[3];  
    if(strlen(pchFormat) != strlen(pchInput))
    {
        NVRSYSDEBUG("strlen pchInput illegal\n", strlen(pchInput));
        return;
    }
    strncpy(achYear, pchInput, 4);    // 提取前4个字符作为年份  
    achYear[4] = '\0';             // 添加字符串结束符  
      
    strncpy(achMonth, pchInput + 4, 2); // 提取第5到6个字符作为月份  
    achMonth[2] = '\0';            // 添加字符串结束符  
      
    strncpy(achDay, pchInput + 6, 2);  // 提取第7到8个字符作为日期  
    achDay[2] = '\0';              // 添加字符串结束符  
      
    // 拼接成日月年格式  
    snprintf(pchOutput, strlen(pchFormat)+1, "%s%s%s", achDay, achMonth, achYear); 
    return;
}
void NvrSysConvertDate(const u32 dwInputDate, u32 *pdwOutputDate) 
{  
	NVRSYS_ASSERT(pdwOutputDate);
#ifdef __I18N__
    u32 dwDay = 0;
    u32 dwMonth = 0;
    u32 dwYear = 0;
    
    ///<年月日改为日月年
    dwDay = dwInputDate % 100;
    dwMonth = (dwInputDate % 10000 - dwDay)/100;
    dwYear = dwInputDate /10000;
    
    *pdwOutputDate = dwDay*1000000+dwMonth*10000 + dwYear;
#else

    *pdwOutputDate = dwInputDate;
#endif
    NVRSYSDEBUG("OutputDate:%08lu\n", *pdwOutputDate);
    return;
}

NVRSTATUS NvrSysGetDevInfo(PTNvrSysDevInfo ptNvrSysDevInfo)
{
	NVRSYS_ASSERT(ptNvrSysDevInfo);

	TOspCpuInfo tCpuInfo;
	TOspMemInfoEx tMemInfo;
	TNvrCapSysInfo tCapSys;
	
	mzero(tCpuInfo);
	mzero(tMemInfo);
	mzero(tCapSys);

	OsApi_GetCpuInfo(&tCpuInfo);
	OspGetMemInfoEx(&tMemInfo);
	NvrCapGetCapParam(NVR_CAP_ID_SYS, &tCapSys);
	memcpy(ptNvrSysDevInfo->achDevType, g_tDevInfo.achDevType, sizeof(ptNvrSysDevInfo->achDevType));
	memcpy(ptNvrSysDevInfo->achDevSerialNum, g_tDevInfo.achDevSerialNum, sizeof(ptNvrSysDevInfo->achDevSerialNum));
	memcpy(ptNvrSysDevInfo->achIMEI, g_tDevInfo.achIMEI, sizeof(ptNvrSysDevInfo->achIMEI));
	//ptNvrSysDevInfo->dwDevMDate = g_tDevInfo.dwDevMDate;
    NvrSysConvertDate(g_tDevInfo.dwDevMDate, &ptNvrSysDevInfo->dwDevMDate);
	ptNvrSysDevInfo->dwPid = g_tDevInfo.dwPid;
    ///硬件版本号
    strncpy(ptNvrSysDevInfo->achDevHWVer, g_tDevInfo.achDevHWVer,sizeof(ptNvrSysDevInfo->achDevHWVer));	
	///<扩展软件版本号(暂用于扩展二次开发APP版本号,追加在(daily)或定制版本号后)
	if(0 != strlen(g_achSoftVerSeted))
	{
		if(0 != strlen(g_achSoftVerExt))
		{
			snprintf(ptNvrSysDevInfo->achDevSoftVer, sizeof(ptNvrSysDevInfo->achDevSoftVer), "%s(%s)", g_achSoftVerSeted, g_achSoftVerExt);
		}
		else
		{
			strncpy(ptNvrSysDevInfo->achDevSoftVer, g_achSoftVerSeted,sizeof(ptNvrSysDevInfo->achDevSoftVer));
		}
	}
	else
	{
		if(0 != strlen(g_achSoftVerExt))
		{
			if (0 != strlen(g_achCustomVersion))
			{
				snprintf(ptNvrSysDevInfo->achDevSoftVer, sizeof(ptNvrSysDevInfo->achDevSoftVer), "%s%s %s (%s) %s %s",
					NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE, g_achCustomVersion, g_achSoftVerExt,__DATE__,__TIME__);
			}
			else
			{
				snprintf(ptNvrSysDevInfo->achDevSoftVer, sizeof(ptNvrSysDevInfo->achDevSoftVer), "%s%s (%s) %s %s",
					NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE, g_achSoftVerExt,__DATE__,__TIME__);
			}
		}
		else
		{
			if (0 != strlen(g_achCustomVersion))
			{
				snprintf(ptNvrSysDevInfo->achDevSoftVer, sizeof(ptNvrSysDevInfo->achDevSoftVer), "%s%s %s %s %s",
					NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE, g_achCustomVersion,__DATE__,__TIME__);
			}
			else
			{
				snprintf(ptNvrSysDevInfo->achDevSoftVer, sizeof(ptNvrSysDevInfo->achDevSoftVer), "%s%s %s %s",
					NVR_SYS_SOFT_VER, NVR_SYS_VER_TYPE,__DATE__,__TIME__);
			}
		}
	}
	strncpy(ptNvrSysDevInfo->achDevRomVer, g_tDevInfo.achDevRomVer,sizeof(ptNvrSysDevInfo->achDevRomVer));
	ptNvrSysDevInfo->byCpuUsed = 100 - tCpuInfo.m_byIdlePercent;
	if( 0 != tMemInfo.m_dwPhysicsSize)
	{
		ptNvrSysDevInfo->byMemUsed = (tMemInfo.m_dwPhysicsSize-tMemInfo.m_dwBuffers-tMemInfo.m_dwCached-tMemInfo.m_dwFreeSize-tMemInfo.m_dwSwapCached)*100/tMemInfo.m_dwPhysicsSize;
	}	
	snprintf(ptNvrSysDevInfo->achSingleClipVer,NVR_MAX_STR32_LEN,"%s",g_achSingleClipVer);
	snprintf(ptNvrSysDevInfo->achCameraLensVer,sizeof(ptNvrSysDevInfo->achCameraLensVer),"%s",g_achCameraLens);
	NvrNetGetAllMbnetVer(ptNvrSysDevInfo->achMbnetVer);
	if(NVR_CAP_SUPPORT == tCapSys.tNvrCapSysBasic.bySupIspVer)
	{
		NvrPuiGetIspVerCode(ptNvrSysDevInfo->achIspVer);
	}
	///<获取ktcp状态
	if(!access(NVR_SYS_KTCP_WORK_PATH, F_OK))
	{
		
		TNvrSysKtcpParam tKtcpParam;
		mzero(tKtcpParam);
		NvrSysGetKtcpParam(&tKtcpParam);
		snprintf(ptNvrSysDevInfo->achKtcpVer, sizeof(ptNvrSysDevInfo->achKtcpVer), "%s", tKtcpParam.achKtcpVer);
		NVRSYSIMP("ktcp ver%s\n", ptNvrSysDevInfo->achKtcpVer);
		snprintf(ptNvrSysDevInfo->achKtcpRunningState, sizeof(ptNvrSysDevInfo->achKtcpRunningState), "%s", tKtcpParam.achKtcpRunningState);
		NVRSYSIMP("ktcp stat:%s\n", ptNvrSysDevInfo->achKtcpRunningState);
	}
	NVRSYSDEBUG("dev:%s, serialnum:%s, mdata:%lu, softver:%s, romver:%s, cpuused:%d, memused:%d achDevHWVer:%s,clipver:%s,lensver:%s,achMbnetVer:%s\n",
	ptNvrSysDevInfo->achDevType,
	ptNvrSysDevInfo->achDevSerialNum,
	ptNvrSysDevInfo->dwDevMDate,
	ptNvrSysDevInfo->achDevSoftVer,
	ptNvrSysDevInfo->achDevRomVer,
	ptNvrSysDevInfo->byCpuUsed,
	ptNvrSysDevInfo->byMemUsed,
	ptNvrSysDevInfo->achDevHWVer,
	ptNvrSysDevInfo->achSingleClipVer,
	ptNvrSysDevInfo->achCameraLensVer,
	ptNvrSysDevInfo->achMbnetVer);

	return NVR_ERR__OK;
}
/**
 *@brief        设置芯片版本号
 *@param[in]	char *pSingleClipVer 单边机版本号 32位，(如果多个单片机，建议复用该字段，中间用标志隔离区分下，如pow:1.2_up:1.1)
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 *@see
 *@note         版本号为32位，可以填null，null则不显示或不支持
 */
NVRSTATUS NvrSysSetClipVer(char *pSingleClipVer)
{	
	NVRSYSMEMAPI();
	if(NULL != pSingleClipVer)
	{
		snprintf(g_achSingleClipVer,NVR_MAX_STR32_LEN,"%s",pSingleClipVer);
		NVRSYSDEBUG("singleclipver:%s-%s\n",g_achSingleClipVer,pSingleClipVer);
	}
	return NVR_ERR__OK;	
}
/**
 *@brief        设置镜头版本号
 *@param[in]	char *pCameraLensVer
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 *@see
 */
NVRSTATUS NvrSysSetCameraLensVer(char *pCameraLensVer)
{	
	NVRSYSMEMAPI();
	if(NULL != pCameraLensVer)
	{
		snprintf(g_achCameraLens, sizeof(g_achCameraLens), "%s", pCameraLensVer);
		NVRSYSDEBUG("singleclipver:%s-%s\n",g_achCameraLens,pCameraLensVer);
	}
	return NVR_ERR__OK;	
}


static u8 NvrSysConvertFpsEnumToValue(const ENvrSysFrameRate eFrameRate)
{
    u8 byFrameRate = 25;    ///<返回值默认25

	NVRSYSDEBUG("eFrameRate:%d.\n", eFrameRate);

    switch(eFrameRate)
    {
        case NVR_SYS_FRAME_RATE_1FPS:
            byFrameRate = 1;
            break;
        case NVR_SYS_FRAME_RATE_5FPS:
            byFrameRate = 5;
            break;
        case NVR_SYS_FRAME_RATE_6FPS:
            byFrameRate = 6;
            break;
        case NVR_SYS_FRAME_RATE_8FPS:
            byFrameRate = 8;
            break;
        case NVR_SYS_FRAME_RATE_10FPS:
            byFrameRate = 10;
            break;
        case NVR_SYS_FRAME_RATE_15FPS:
            byFrameRate = 15;
            break;
        case NVR_SYS_FRAME_RATE_20FPS:
            byFrameRate = 20;
            break;
        case NVR_SYS_FRAME_RATE_25FPS:
            byFrameRate = 25;
            break;
        case NVR_SYS_FRAME_RATE_30FPS:
            byFrameRate = 30;
            break;
        default:
            byFrameRate = 25;
            break;
    }

    return byFrameRate;
}
static u16 NvrSysConvertBitRateEnumToValue(const ENvrSysBitRate eBitRate)
{
    u16 wBitRate = 1024;    ///<返回值默认1024

	NVRSYSDEBUG("eBitRate:%d.\n", eBitRate);
    switch(eBitRate)
    {
        case NVR_SYS_BIT_RATE_256K:
            wBitRate = 256;
            break;
        case NVR_SYS_BIT_RATE_512K:
            wBitRate = 512;
            break;

        case NVR_SYS_BIT_RATE_1M:
            wBitRate = 1024;
            break;
        case NVR_SYS_BIT_RATE_2M:
            wBitRate = 2*1024;
            break;

        case NVR_SYS_BIT_RATE_4M:
            wBitRate = 4*1024;
            break;
        default:
            wBitRate = 1024;
            break;
    }

    return wBitRate;
}
NVRSTATUS NvrSysSetSysZeroChnEncParam(PTNvrSysZeroChnEncParam ptNvrSysZeroChnEncParam)
{
	NVRSYSMEMAPI();
	NVRSYS_ASSERT(ptNvrSysZeroChnEncParam);
    TNvrMpuVidEncParam tVidEncParam;
    TNvrMpuVidEncBindInputParam tVidEncBindInput;
	NVRSTATUS eRet = NVR_ERR__OK;

    mzero(tVidEncParam);
    mzero(tVidEncBindInput);

	do
	{
		///绑定编码通道
		tVidEncBindInput.eEncSourceType = NVR_MPU_VID_ENC_SOURCE_DIS;
		if (NVR_SYS_VIDEO_SRC_HDMI == ptNvrSysZeroChnEncParam->eVideoSrc)
		{
			tVidEncBindInput.x.tDisParam.eVidDisDev = NVR_VID_DIS_DEV_HDMI;
			tVidEncBindInput.x.tDisParam.dwDisDevId = 0;
		}
		else if (NVR_SYS_VIDEO_SRC_VGA == ptNvrSysZeroChnEncParam->eVideoSrc)
		{
			tVidEncBindInput.x.tDisParam.eVidDisDev = NVR_VID_DIS_DEV_VGA;
			tVidEncBindInput.x.tDisParam.dwDisDevId = 0;
		}
		else if(NVR_SYS_VIDEO_SRC_HDMI2 == ptNvrSysZeroChnEncParam->eVideoSrc)
		{
			tVidEncBindInput.x.tDisParam.eVidDisDev = NVR_VID_DIS_DEV_HDMI;
			tVidEncBindInput.x.tDisParam.dwDisDevId = 1;
		}
		else if (NVR_SYS_VIDEO_SRC_VGA2 == ptNvrSysZeroChnEncParam->eVideoSrc)
		{
			tVidEncBindInput.x.tDisParam.eVidDisDev = NVR_VID_DIS_DEV_VGA;
			tVidEncBindInput.x.tDisParam.dwDisDevId = 1;
		}
		eRet = NvrMpuSetVidEncBindInput(0, &tVidEncBindInput);
		if(NVR_ERR__OK!=eRet)
		{
			NVRSYSERR("==NvrMpuSetVidEncBindInput failed:eRet:%d\n", eRet);
			break;
		}
	
		tVidEncParam.eEncType = ptNvrSysZeroChnEncParam->eVidEncType;
		tVidEncParam.byFrameRate = NvrSysConvertFpsEnumToValue(ptNvrSysZeroChnEncParam->eFrameRate);
        tVidEncParam.wBitRate = NvrSysConvertBitRateEnumToValue(ptNvrSysZeroChnEncParam->eBitRate);
        tVidEncParam.tVidEncRes.wWidth = ptNvrSysZeroChnEncParam->tEncRes.wWidth;
        tVidEncParam.tVidEncRes.wHeight = ptNvrSysZeroChnEncParam->tEncRes.wHeight;
        tVidEncParam.bEnable = ptNvrSysZeroChnEncParam->byEnable;
        eRet = NvrMpuSetVidEncParam(0, &tVidEncParam);
        if(NVR_ERR__OK == eRet)
        {
    		///<通知pui零通道编码使能状态
    		NvrPuiSetZeroChnParam(ptNvrSysZeroChnEncParam);

        	OsApi_SemTake(g_hSysCfgRWSem);
            NVRSYSERR("set enc param success.\n");
    		g_tNvrSysCfg.tNvrSysZeroChnEncParam = *ptNvrSysZeroChnEncParam;
	   		eRet = NvrSysCfgSave();
    		if (NVR_ERR__OK != eRet)
    		{
    			NVRSYSERR("save cfg failed ret:%d\n", eRet);
                OsApi_SemGive(g_hSysCfgRWSem);
    			break;
    		}

            OsApi_SemGive(g_hSysCfgRWSem);
        }
        else
        {
            NVRSYSERR("set enc param failed(ret=%d).\n", eRet);
            break;
        }
	}while(0);


	NVRSYSDEBUG("en:%d, height:%d, width:%d, bit:%d, frame:%d, videosrc:%d enctype:%d \n",
	ptNvrSysZeroChnEncParam->byEnable,
	ptNvrSysZeroChnEncParam->tEncRes.wHeight,
	ptNvrSysZeroChnEncParam->tEncRes.wWidth,
	ptNvrSysZeroChnEncParam->eBitRate,
	ptNvrSysZeroChnEncParam->eFrameRate,
	ptNvrSysZeroChnEncParam->eVideoSrc,
	ptNvrSysZeroChnEncParam->eVidEncType);

	return eRet;
}

NVRSTATUS NvrSysGetSysZeroChnEncParam(PTNvrSysZeroChnEncParam ptNvrSysZeroChnEncParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptNvrSysZeroChnEncParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	*ptNvrSysZeroChnEncParam = g_tNvrSysCfg.tNvrSysZeroChnEncParam;

	OsApi_SemGive(g_hSysCfgRWSem);

	NVRSYSDEBUG("en:%d, height:%d, width:%d, bit:%d, frame:%d, videosrc:%d,enctype:%d\n",
	ptNvrSysZeroChnEncParam->byEnable,
	ptNvrSysZeroChnEncParam->tEncRes.wHeight,
	ptNvrSysZeroChnEncParam->tEncRes.wWidth,
	ptNvrSysZeroChnEncParam->eBitRate,
	ptNvrSysZeroChnEncParam->eFrameRate,
	ptNvrSysZeroChnEncParam->eVideoSrc,
	ptNvrSysZeroChnEncParam->eVidEncType);

	return eRet;
}
/**
 *@brief        设置音频呼叫绑定关系
 *@param		PTNvrSysAudCallBindingParam 音频呼叫绑定参数
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 *@see
 *@note
 */
NVRSTATUS NvrSysSetAudCallBindingParam(PTNvrSysAudCallBindingParam ptAudCallBindingParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 i = 0;
	TNvrSysAudCallBindingParam tAudCallBindingParam;
	mzero(tAudCallBindingParam);


	NVRSYS_ASSERT(ptAudCallBindingParam);

	eRet = NvrSysGetAudCallBindingParam(&tAudCallBindingParam);
	if(NVR_ERR__OK != eRet)
	{
		NVRSYSERR("NvrSysGetAudCallBindingParam failed eret:%d\n",eRet);
		
	}
	OsApi_SemTake(g_hSysCfgRWSem);
	do
	{
	    NVRSYSDEBUG("bandingnum:%u,supptcalldevnum:%u,eable:%d-%d,bmix:%d,baec:%d\n",ptAudCallBindingParam->wCallBindingNum,ptAudCallBindingParam->wSupptCallDevNum,
        ptAudCallBindingParam->atCallDev[0].bEnable,ptAudCallBindingParam->atCallDev[1].bEnable,
        ptAudCallBindingParam->bCallMix,ptAudCallBindingParam->bDevAec);
	    for(i = 0;i<ptAudCallBindingParam->wCallBindingNum;i++)
	    {
	        NVRSYSDEBUG("chnid:%u-",ptAudCallBindingParam->awCallBindingChnId[i]);
	    }
	    NVRSYSDEBUG("\n");
		if(ptAudCallBindingParam->bDevAec != tAudCallBindingParam.bDevAec)
		{
			eRet = NvrPuiSetAudCallBindingParam(ptAudCallBindingParam->bDevAec);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrPuiSetAudCallBindingParam  failed ret:%d\n", eRet);
			}
		}

	    g_tNvrSysCfg.tAudCallBindingParam = *ptAudCallBindingParam;
		

	    eRet = NvrSysCfgSave();
	    if(NVR_ERR__OK != eRet)
	    {
	        NVRSYSERR("save cfg failed ret:%d\n", eRet);
	    }
	}while(0);
	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

/**
 *@brief        获取音频呼叫绑定关系
 *@param[out]	PTNvrSysAudCallBindingParam 音频呼叫绑定参数
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 *@see
 *@note
 */
NVRSTATUS NvrSysGetAudCallBindingParam(PTNvrSysAudCallBindingParam ptAudCallBindingParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 i = 0;


	NVRSYS_ASSERT(ptAudCallBindingParam);


    OsApi_SemTake(g_hSysCfgRWSem);

     *ptAudCallBindingParam = g_tNvrSysCfg.tAudCallBindingParam;

    NVRSYSDEBUG("bandingnum:%u,supptcalldevnum:%u,enable:%d-%d,bmix:%d,baec:%d\n",ptAudCallBindingParam->wCallBindingNum,ptAudCallBindingParam->wSupptCallDevNum,
        ptAudCallBindingParam->atCallDev[0].bEnable,ptAudCallBindingParam->atCallDev[1].bEnable,
        ptAudCallBindingParam->bCallMix,ptAudCallBindingParam->bDevAec);
    for(i = 0;i<ptAudCallBindingParam->wCallBindingNum;i++)
	{
	    NVRSYSDEBUG("chnid:%u-",ptAudCallBindingParam->awCallBindingChnId[i]);
	}
	NVRSYSDEBUG("\n");
	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

NVRSTATUS NvrSysSetManualEventParam(PTNvrSysManualEventParam ptManualEvent)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 i = 0;


	NVRSYS_ASSERT(ptManualEvent);


    OsApi_SemTake(g_hSysCfgRWSem);
	do
	{
	    NVRSYSDEBUG("event num:%d, event snap:%d\n", ptManualEvent->wEventNum, ptManualEvent->bTrgEventSnap);

	    for(i = 0; i<ptManualEvent->wEventNum; i++)
	    {
	        NVRSYSDEBUG("index:%d name:%s len:%lu desc:%s len:%lu default:%d\n", i, ptManualEvent->atEventList[i].achEventName, ptManualEvent->atEventList[i].dwEventNameLen, ptManualEvent->atEventList[i].achDesc, ptManualEvent->atEventList[i].dwDescLen, ptManualEvent->atEventList[i].bDefault);
	    }

	    g_tNvrSysCfg.tManualEventParam = *ptManualEvent;


	    eRet = NvrSysCfgSave();
	    if(NVR_ERR__OK != eRet)
	    {
	        NVRSYSERR("save cfg failed ret:%d\n", eRet);
	    }
	}while(0);
	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

NVRSTATUS NvrSysGetManualEventParam(PTNvrSysManualEventParam ptManualEvent)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 i = 0;
    char achEventName[NVR_MAX_STR32_LEN+2];
    char achDesc[NVR_MAX_STR128_LEN+2];

	mzero(achEventName);
	mzero(achDesc);

	NVRSYS_ASSERT(ptManualEvent);


    OsApi_SemTake(g_hSysCfgRWSem);

	*ptManualEvent = g_tNvrSysCfg.tManualEventParam;

	NVRSYSDEBUG("event num:%d, event snap:%d\n", ptManualEvent->wEventNum, ptManualEvent->bTrgEventSnap);

	for(i = 0; i<ptManualEvent->wEventNum; i++)
	{

		mzero(achEventName);
		mzero(achDesc);

		CharConvConvertUnicodetoUtf8((u8*)ptManualEvent->atEventList[i].achEventName, ptManualEvent->atEventList[i].dwEventNameLen, achEventName, sizeof(achEventName));
		CharConvConvertUnicodetoUtf8((u8*)ptManualEvent->atEventList[i].achDesc, ptManualEvent->atEventList[i].dwDescLen, achDesc, sizeof(achDesc));

		NVRSYSDEBUG("index:%d name:%s len:%lu desc:%s len:%lu default:%d\n", i, achEventName, ptManualEvent->atEventList[i].dwEventNameLen, achDesc, ptManualEvent->atEventList[i].dwDescLen, ptManualEvent->atEventList[i].bDefault);
	}

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

#if 0
NVRSTATUS NvrSysSetGeographyPosParam(PTNvrSysGeographyPosParam ptGeographyPos)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptGeographyPos);


    OsApi_SemTake(g_hSysCfgRWSem);


	do
	{

		NVRSYSDEBUG("gps open:%lu mode:%d enhance:%lu limit speed enable:%lu value:%lu report:%lu\n",
					ptGeographyPos->bOpenGps,
					ptGeographyPos->eGpsLocMode,
					ptGeographyPos->bEnhanceAgps,
					ptGeographyPos->tLimitSpeed.bEnable,
					ptGeographyPos->tLimitSpeed.dwLimitValue,
					ptGeographyPos->tLimitSpeed.bOverSpeedReport);

		if (ptGeographyPos->bOpenGps != g_tNvrSysCfg.tGeographyPosParam.bOpenGps || ptGeographyPos->eGpsLocMode != g_tNvrSysCfg.tGeographyPosParam.eGpsLocMode)
		{
			NvrNetSetGpsEnabled(ptGeographyPos->bOpenGps, ptGeographyPos->eGpsLocMode);	///<目前高通只支持卫星定位
		}


		g_tNvrSysCfg.tGeographyPosParam = *ptGeographyPos;


		eRet = NvrSysCfgSave();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
			break;
		}

		///to do 配置实际的gps开关和模式
		NVRSYSINFO("try to call g_pfNvrGnssSetParamCB !!\n ");
		if(NULL != g_pfNvrGnssSetParamCB)
		{
			NVRSYSDEBUG("Call the g_pfNvrGnssSetParamCB !!\n");
			g_pfNvrGnssSetParamCB(ptGeographyPos);
		}

	}
	while(0);

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

NVRSTATUS NvrSysGetGeographyPosParam(PTNvrSysGeographyPosParam ptGeographyPos)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptGeographyPos);


    OsApi_SemTake(g_hSysCfgRWSem);

	*ptGeographyPos = g_tNvrSysCfg.tGeographyPosParam;

	NVRSYSDEBUG("gps open:%lu mode:%d enhance:%lu limit speed enable:%lu value:%lu report:%lu\n",
				ptGeographyPos->bOpenGps,
				ptGeographyPos->eGpsLocMode,
				ptGeographyPos->bEnhanceAgps,
				ptGeographyPos->tLimitSpeed.bEnable,
				ptGeographyPos->tLimitSpeed.dwLimitValue,
				ptGeographyPos->tLimitSpeed.bOverSpeedReport);


	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}
#endif

NVRSTATUS NvrSysGetMessageSubmitParam(PTNvrSysMessageSubmitParam ptMessageSubmit)
{
	NVRSYS_ASSERT(ptMessageSubmit);
	
	OsApi_SemTake(g_hSysCfgRWSem);

	memcpy(ptMessageSubmit, &g_tNvrSysCfg.tMessageSubmitParam, sizeof(TNvrSysMessageSubmitParam));

	NVRSYSDEBUG("GetMessageSubmitParam UploadPlatformGps:%d UploadPubsec[0]:%d UploadPubsec[1]:%d NtyIpcGps:%d, SendGpsInterval:%d NtyIpcNet:%d !!\n",
				ptMessageSubmit->bUploadPlatformGps,
				ptMessageSubmit->abUploadPubsec[0],
				ptMessageSubmit->abUploadPubsec[1],
				ptMessageSubmit->bNtyIpcGps,
				ptMessageSubmit->wSendGpsInterval,
				ptMessageSubmit->bNtyIpcNet);

	OsApi_SemGive(g_hSysCfgRWSem);

	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSetMessageSubmitParam(PTNvrSysMessageSubmitParam ptMessageSubmit)
{
	NVRSYSMEMAPI();
	NVRSYS_ASSERT(ptMessageSubmit);
	
	OsApi_SemTake(g_hSysCfgRWSem);

	NVRSYSDEBUG("SetMessageSubmitParam UploadPlatformGps:%d UploadPubsec[0]:%d UploadPubsec[1]:%d NtyIpcGps:%d, SendGpsInterval:%d NtyIpcNet:%d !!\n",
			ptMessageSubmit->bUploadPlatformGps,
			ptMessageSubmit->abUploadPubsec[0],
			ptMessageSubmit->abUploadPubsec[1],
			ptMessageSubmit->bNtyIpcGps,
			ptMessageSubmit->wSendGpsInterval,
			ptMessageSubmit->bNtyIpcNet);
			
	memcpy( &g_tNvrSysCfg.tMessageSubmitParam, ptMessageSubmit, sizeof(TNvrSysMessageSubmitParam));

	OsApi_SemGive(g_hSysCfgRWSem);

	NvrMessageSubmitSetTimer(ptMessageSubmit->wSendGpsInterval);

	return NVR_ERR__OK;
	
}

NVRSTATUS NvrSysGetVehicleParam(PTNvrSysVehicleParam ptVehicleParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptVehicleParam);


	OsApi_SemTake(g_hSysCfgRWSem);

	*ptVehicleParam = g_tNvrSysCfg.tVehicleParam;

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}



NVRSTATUS NvrSysSetVehicleParam(PTNvrSysVehicleParam ptVehicleParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptVehicleParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	do
	{
		NVRSYSDEBUG("carplate str len:%d, carplate:%s synclable:%d shutdown poesup:%d\n", ptVehicleParam->dwCarPlateNumLen, ptVehicleParam->achCarPlateNum, ptVehicleParam->bSyncLable, ptVehicleParam->bShutdownPoeSup);

		///<参数变化通知osd
		if (g_tNvrSysCfg.tVehicleParam.bSyncLable != ptVehicleParam->bSyncLable || (0 != memcmp(g_tNvrSysCfg.tVehicleParam.achCarPlateNum, ptVehicleParam->achCarPlateNum, sizeof(ptVehicleParam->achCarPlateNum))))
		{
			NvrPuiOsdLableSyncNty();
		}

		g_tNvrSysCfg.tVehicleParam = *ptVehicleParam;

		eRet = NvrSysCfgSave();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
		}
	}while(0);

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

NVRSTATUS NvrSysSetUdpReTranParam(PTNvrSysUdpReTranParam ptNvrSysUdpReTranParam)
{
	NVRSYSMEMAPI();
	NVRSYS_ASSERT(ptNvrSysUdpReTranParam);
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYSDEBUG("en:%d, first:%lu, second:%lu, third:%lu, discard:%lu\n",
	ptNvrSysUdpReTranParam->byEnable,
	ptNvrSysUdpReTranParam->dwFirstCheckPoint,
	ptNvrSysUdpReTranParam->dwSecondCheckPoint,
	ptNvrSysUdpReTranParam->dwThirdCheckPoint,
	ptNvrSysUdpReTranParam->dwOverdueDiscard);

	if (NVRSYSILLEGAL(g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tFirstCheckPoint.nMinValue ,ptNvrSysUdpReTranParam->dwFirstCheckPoint, g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tFirstCheckPoint.nMaxValue) ||
		NVRSYSILLEGAL(g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tSecondCheckPoint.nMinValue ,ptNvrSysUdpReTranParam->dwSecondCheckPoint, g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tSecondCheckPoint.nMaxValue) ||
		NVRSYSILLEGAL(g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tThirdCheckPoint.nMinValue ,ptNvrSysUdpReTranParam->dwThirdCheckPoint, g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tThirdCheckPoint.nMaxValue) ||
		NVRSYSILLEGAL(g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tOverdueDiscard.nMinValue ,ptNvrSysUdpReTranParam->dwOverdueDiscard, g_tSysCap.tNvrCapAdvance.tNvrCapUdpReTran.tOverdueDiscard.nMaxValue) ||
		!(ptNvrSysUdpReTranParam->dwFirstCheckPoint < ptNvrSysUdpReTranParam->dwSecondCheckPoint && ptNvrSysUdpReTranParam->dwSecondCheckPoint < ptNvrSysUdpReTranParam->dwThirdCheckPoint))
	{
		NVRSYSERR("param illgegal\n");
		return NVR_ERR__PARAM_INVALID;
	}

	OsApi_SemTake(g_hSysCfgRWSem);

	do
	{
		if (NVR_SYS_DISABLE != ptNvrSysUdpReTranParam->byEnable)
		{
			ptNvrSysUdpReTranParam->byEnable = NVR_SYS_ENABLE;
		}

		g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam = *ptNvrSysUdpReTranParam;

		eRet = NvrSysCfgSave();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
			break;
		}

		///通知vtdu
		NvrVtduCtrlUdpRetransParamChangeNty();

	}while(0);


	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;

}

NVRSTATUS NvrSysSetCustomPlugParam(PTNvrSysCustomPlugDownload ptNvrSysCustomPlugParam)
{
	NVRSYS_ASSERT(ptNvrSysCustomPlugParam);
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYSDEBUG("en:%d, CustomPlugUrl:%s\n",
	ptNvrSysCustomPlugParam->byEnable,
	ptNvrSysCustomPlugParam->achCustomPlugUrl);

	

	OsApi_SemTake(g_hSysCfgRWSem);

	if (NVR_SYS_DISABLE != ptNvrSysCustomPlugParam->byEnable)
	{
		ptNvrSysCustomPlugParam->byEnable = NVR_SYS_ENABLE;
	}

	g_tNvrSysCfg.tAdvanceParam.tPlugDownload = *ptNvrSysCustomPlugParam;

	eRet = NvrSysCfgSave();
	if (NVR_ERR__OK != eRet)
	{
		NVRSYSERR("save cfg failed ret:%d\n", eRet);
        OsApi_SemGive(g_hSysCfgRWSem);
		return eRet;
	}



	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;

}


NVRSTATUS NvrSysGetUdpReTranParam(PTNvrSysUdpReTranParam ptNvrSysUdpReTranParam)
{

	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptNvrSysUdpReTranParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	*ptNvrSysUdpReTranParam = g_tNvrSysCfg.tAdvanceParam.tUdpReTranParam;

	OsApi_SemGive(g_hSysCfgRWSem);

	NVRSYSDEBUG("en:%d, first:%lu, second:%lu, third:%lu, discard:%lu\n",
	ptNvrSysUdpReTranParam->byEnable,
	ptNvrSysUdpReTranParam->dwFirstCheckPoint,
	ptNvrSysUdpReTranParam->dwSecondCheckPoint,
	ptNvrSysUdpReTranParam->dwThirdCheckPoint,
	ptNvrSysUdpReTranParam->dwOverdueDiscard);

	return eRet;
}
NVRSTATUS NvrSysGetCustomPlugParam(PTNvrSysCustomPlugDownload ptNvrSysCustomPlugParam)
{

	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptNvrSysCustomPlugParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	*ptNvrSysCustomPlugParam = g_tNvrSysCfg.tAdvanceParam.tPlugDownload;

	OsApi_SemGive(g_hSysCfgRWSem);

	NVRSYSDEBUG("en:%d, CustomPlugUrl:%s\n",
	ptNvrSysCustomPlugParam->byEnable,
	ptNvrSysCustomPlugParam->achCustomPlugUrl);

	return eRet;
}

NVRSTATUS NvrSysSetMrtcParam(const TNvrSysMrtcParam *ptMrtcParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	
	if(NULL != ptMrtcParam)
	{
		g_tNvrSysCfg.tAdvanceParam.tMrtcParam = *ptMrtcParam;
		NVRSYSDEBUG("mrtc: %d, %d\n", g_tNvrSysCfg.tAdvanceParam.tMrtcParam.bMrtcEnable, g_tNvrSysCfg.tAdvanceParam.tMrtcParam.eCongeType);
		eRet = NvrSysCfgSave();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysSetMrtcParam save cfg failed ret:%d\n", eRet);
		}
	}
	else
	{
		eRet = NVR_ERR__PARAM_INVALID;
	}
	
	return eRet;
}

NVRSTATUS NvrSysGetMrtcParam(TNvrSysMrtcParam *ptMrtcParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL != ptMrtcParam)
	{
		*ptMrtcParam = g_tNvrSysCfg.tAdvanceParam.tMrtcParam;  ///<仅获取使能，带宽范围，其他参数从crn直接获取
	}
	else
	{
		eRet = NVR_ERR__PARAM_INVALID;
	}
	
	return eRet;
}


NVRSTATUS NvrSysSetKtcpParam(const TNvrSysKtcpParam *ptKtcpParam)
{	
	///<仅设置使能
	NVRSTATUS eRet = NVR_ERR__OK;
	
	if(NULL != ptKtcpParam)
	{
		g_tNvrSysCfg.tAdvanceParam.tKtcpParam = *ptKtcpParam;
        NvrVtduCtrlKtcpEnable(ptKtcpParam->bKtcpEnable);
#if 0
#ifndef _QCOM_
		s8 achCommand[64] = {0};
		///<ktcp使能
		snprintf(achCommand, 64, "echo %d > %s", ptKtcpParam->bKtcpEnable, NVR_SYS_KTCP_ENABLE_CFG);
		NVRSYSIMP("ktcp command:%s\n", achCommand);
		NvrSystem(achCommand);

		///<业务不设置端口范围和带宽范围，采用crn默认值
		#if 0
		///<ktcp端口范围
		snprintf(achCommand, 64, "echo %u-%u > %s", ptKtcpParam->wStartPort, ptKtcpParam->wEndPort, NVR_SYS_KTCP_PORTS_RANGE_CFG);
		NVRSYSIMP("ktcp command:%s\n", achCommand);
		NvrSystem(achCommand);
		///<ktcp带宽范围
		snprintf(achCommand, 64, "echo %lu > %s", ptKtcpParam->dwBandwidthMin, NVR_SYS_KTCP_MIN_BW_CFG);
		NVRSYSIMP("ktcp command:%s\n", achCommand);
		NvrSystem(achCommand);
		snprintf(achCommand, 64, "echo %lu > %s", ptKtcpParam->dwBandwidthMax, NVR_SYS_KTCP_MAX_BW_CFG);
		NVRSYSIMP("ktcp command:%s\n", achCommand);
		NvrSystem(achCommand);
		#endif
#else
		TNvrSuperFile tSuperFile;
		mzero(tSuperFile);
		///<ktcp使能
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_ENABLE_CFG);
		snprintf(tSuperFile.szContent, NVR_MAX_STR1024_LEN, "%d", ptKtcpParam->bKtcpEnable);
		NvrSysWriteSuperFile(&tSuperFile);
		
		///<业务不设置端口范围和带宽范围，采用crn默认值
		#if 0
		///<ktcp端口范围
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_PORTS_RANGE_CFG);
		snprintf(tSuperFile.szContent, NVR_MAX_STR1024_LEN, "%u-%u", ptKtcpParam->wStartPort, ptKtcpParam->wEndPort);
		NvrSysWriteSuperFile(&tSuperFile);
		///<ktcp带宽范围
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_MIN_BW_CFG);
		snprintf(tSuperFile.szContent, NVR_MAX_STR1024_LEN, "%d", ptKtcpParam->dwBandwidthMin);
		NvrSysWriteSuperFile(&tSuperFile);
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_MAX_BW_CFG);
		snprintf(tSuperFile.szContent, NVR_MAX_STR1024_LEN, "%d", ptKtcpParam->dwBandwidthMax);
		NvrSysWriteSuperFile(&tSuperFile);
		#endif
#endif
#endif
		eRet = NvrSysCfgSave();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysSetKtcpParam save cfg failed ret:%d\n", eRet);
		}
	}
	else
		eRet = NVR_ERR__PARAM_INVALID;
	
	return eRet;
}

NVRSTATUS NvrSysGetKtcpParam(TNvrSysKtcpParam *ptKtcpParam)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL != ptKtcpParam)
	{
		*ptKtcpParam = g_tNvrSysCfg.tAdvanceParam.tKtcpParam;  ///<仅获取使能，带宽范围，其他参数从crn直接获取
#if 0
#ifndef _QCOM_
		s8 achCommand[128] = {0};
		FILE *fp = NULL;
		s8 achResults[64] = {0};
		///<获取端口范围字符串
		snprintf(achCommand, 128, "cat %s", NVR_SYS_KTCP_PORTS_RANGE_CFG);
		fp = popen(achCommand, "r");
		if(!fp)
		{
			NVRSYSERR("popen command:%s failed%s\n", achCommand, strerror(errno));
		}
		else
		{
			fgets(achResults, sizeof(achResults), fp);
			pclose(fp);
			fp = NULL;
		}
		snprintf(ptKtcpParam->achKtcpPorts, sizeof(ptKtcpParam->achKtcpVer), "%s", achResults);
		
		NVRSYSIMP("ktcp ports%s,tmpstr:%s, connamd:%s\n", ptKtcpParam->achKtcpPorts,achResults, achCommand);
		///<获取ktcp版本号
		snprintf(achCommand, 128, "cat %s", NVR_SYS_KTCP_VER_INFO);
		fp = popen(achCommand, "r");
		if(!fp)
		{
			NVRSYSERR("popen command:%s failed%s\n", achCommand, strerror(errno));
		}
		else
		{
			fgets(achResults, sizeof(achResults), fp);
			pclose(fp);
			fp = NULL;
		}
		snprintf(ptKtcpParam->achKtcpVer, sizeof(ptKtcpParam->achKtcpVer), "%s", achResults);
		
		NVRSYSIMP("ktcp ver%s,tmpstr:%s, connamd:%s\n", ptKtcpParam->achKtcpVer,achResults, achCommand);
		///<获取ktcp运行状态
		snprintf(achCommand, 64, "cat %s", NVR_SYS_KTCP_STAT_INFO);
		fp = popen(achCommand, "r");
		if(!fp)
		{
			NVRSYSERR("popen command:%s failed%s\n", achCommand, strerror(errno));
		}
		else
		{
			memset(achResults, 0, sizeof(achResults));
			fgets(achResults, sizeof(achResults), fp);
			pclose(fp);
			fp = NULL;
		}
		if(ptKtcpParam->bKtcpEnable)
		{
			snprintf(ptKtcpParam->achKtcpRunningState, sizeof(ptKtcpParam->achKtcpRunningState), "Running-%s", achResults);
		}
		else
		{
			snprintf(ptKtcpParam->achKtcpRunningState, sizeof(ptKtcpParam->achKtcpRunningState), "Stopped-%s", achResults);
		}
		NVRSYSIMP("ktcp stat:%s,tmpstr:%s,command:%s\n", ptKtcpParam->achKtcpRunningState, achResults, achCommand);
		///<获取授权到期时间
		snprintf(achCommand, 64, "cat %s", NVR_SYS_KTCP_EXPIRE_INFO);
		fp = popen(achCommand, "r");
		if(!fp)
		{
			NVRSYSERR("popen command:%s failed%s\n", achCommand, strerror(errno));
		}
		else
		{
			memset(achResults, 0, sizeof(achResults));
			fgets(achResults, sizeof(achResults), fp);
			pclose(fp);
			fp = NULL;
		}
		snprintf(ptKtcpParam->achKtcpExpireTime, sizeof(ptKtcpParam->achKtcpExpireTime), "%s", achResults);
		NVRSYSIMP("ktcp expire time:%s,tmpstr:%s,command:%s\n",ptKtcpParam->achKtcpExpireTime, achResults, achCommand);
#else
		TNvrSuperFile tSuperFile;
		mzero(tSuperFile);

        ///<获取ktcp是否启用
        snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_ENABLE_CFG);
        mzero(tSuperFile.szContent);  ///<需要清空szContent,不清空发现read返回有脏数据
        NvrSysReadSuperFile(&tSuperFile);
        ptKtcpParam->bKtcpEnable = atoi(tSuperFile.szContent);///<获取一次开关配置，防止人为通过底层命令开关配置更新不及时
        if(ptKtcpParam->bKtcpEnable != g_tNvrSysCfg.tAdvanceParam.tKtcpParam.bKtcpEnable)
        {
            g_tNvrSysCfg.tAdvanceParam.tKtcpParam.bKtcpEnable = ptKtcpParam->bKtcpEnable;
        }
		///<获取端口范围字符串
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_PORTS_RANGE_CFG);
		NvrSysReadSuperFile(&tSuperFile);
		snprintf(ptKtcpParam->achKtcpPorts, sizeof(ptKtcpParam->achKtcpPorts), "%s", tSuperFile.szContent);
		NVRSYSIMP("ktcp ports:%s\n", ptKtcpParam->achKtcpPorts);
		///<获取ktcp版本号
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_VER_INFO);
		mzero(tSuperFile.szContent);  ///<需要清空szContent,不清空发现read返回有脏数据
		NvrSysReadSuperFile(&tSuperFile);
		snprintf(ptKtcpParam->achKtcpVer, sizeof(ptKtcpParam->achKtcpVer), "%s", tSuperFile.szContent);
		NVRSYSIMP("ktcp ver:%s\n", ptKtcpParam->achKtcpVer);
		///<获取ktcp运行状态
		snprintf(tSuperFile.szFilePath, NVR_MAX_STR256_LEN, "%s", NVR_SYS_KTCP_STAT_INFO);
		mzero(tSuperFile.szContent);  ///<需要清空szContent,不清空发现read返回有脏数据
		NvrSysReadSuperFile(&tSuperFile);
		if(ptKtcpParam->bKtcpEnable)
		{
			snprintf(ptKtcpParam->achKtcpRunningState, sizeof(ptKtcpParam->achKtcpRunningState), "Running-%s", tSuperFile.szContent);
		}
		else
		{
			snprintf(ptKtcpParam->achKtcpRunningState, sizeof(ptKtcpParam->achKtcpRunningState), "Stopped-%s", tSuperFile.szContent);
		}
		///<获取授权到期时间
		snprintf(tSuperFile.szFilePath, sizeof(tSuperFile.szFilePath), "%s", NVR_SYS_KTCP_EXPIRE_INFO);
		mzero(tSuperFile.szContent);  ///<需要清空szContent,不清空发现read返回有脏数据
		NvrSysReadSuperFile(&tSuperFile);
		snprintf(ptKtcpParam->achKtcpExpireTime, sizeof(ptKtcpParam->achKtcpExpireTime), "%s", tSuperFile.szContent);
		NVRSYSIMP("ktcp expire time:%s\n", ptKtcpParam->achKtcpExpireTime);
		
#endif
#endif		
	}
	else
		eRet = NVR_ERR__PARAM_INVALID;
	return eRet;
}

NVRSTATUS NvrSysMtuInit()
{
	NvrSysSetMtuForce();
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSetMtuForce(void)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
#ifndef _QCOM_	
	s32 nRet = 0;
	s32 i = 0;
	TNvrNetWorkMode tNetWorkMode;
	TNvrNetEthParam tNetParam;
	TNvrCapHwCapInfo tHwCap;
	mzero(tNetWorkMode);
	mzero(tNetParam);
	mzero(tHwCap);

	NvrNetworkGetNetWorkMode(&tNetWorkMode);
	if (NVR_NET_WORK_MODE_MUL_ADDR == tNetWorkMode.eNetWorkMode)
	{
		NvrCapGetCapParam(NVR_CAP_ID_HW,&tHwCap);
		for(i = 0; i < tHwCap.dwEthNum; i++)
		{
			NvrNetworkGetEthParam(i, &tNetParam);
			nRet = NetcbbIfaceSetMtu(tNetParam.achNetName, NETCBB_PROTO_TYPE_IPV4, g_tNvrSysCfg.tAdvanceParam.dwMtu);
			NVRSYSDEBUG("set achNetName:%s NETCBB_PROTO_TYPE_IPV4 mtu :%lu nRet:%d\n", tNetParam.achNetName, g_tNvrSysCfg.tAdvanceParam.dwMtu, nRet);
		}
	}
	else
	{
		NvrNetworkGetBondParam(tNetWorkMode.eNetWorkMode, &tNetParam);
		nRet = NetcbbIfaceSetMtu(tNetParam.achNetName, NETCBB_PROTO_TYPE_IPV4, g_tNvrSysCfg.tAdvanceParam.dwMtu);
		NVRSYSDEBUG("set achNetName:%s NETCBB_PROTO_TYPE_IPV4 mtu :%lu nRet:%d\n", tNetParam.achNetName, g_tNvrSysCfg.tAdvanceParam.dwMtu, nRet);
	}
#else
    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
	TNvrNetMtu tNetMtu;
	mzero(tNetMtu);
    mzero(tMsgData);
    mzero(tCallback);
    tMsgData.nMsgID = MSG_SET_SYS_NET_MTU;
    NVRSYSFILE("mtu:%d\n", g_tNvrSysCfg.tAdvanceParam.dwMtu);	
    tCallback.wMsgType = JNIMSG_TYPE_SYS;
    tCallback.pData=&tMsgData;
	ENvrSysNetType eType = NVR_IFACE_ETH;
	tNetMtu.nMtu = (s32)g_tNvrSysCfg.tAdvanceParam.dwMtu;
	for(eType = NVR_IFACE_ETH; eType <= NVR_IFACE_WLAN; eType++ )
	{
		tNetMtu.eNetType = eType;
	    memcpy(tMsgData.chData, &tNetMtu, sizeof(tNetMtu));
	    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);
	    if(NVR_JNI_ERR == tMsgData.errCD)
	    {
	        NVRSYSFILE("NVR_JNI_ERR\n");
	        eRet = NVR_ERR__ERROR;
			break;
	    }
	}
#endif
	return eRet;
}

NVRSTATUS NvrSysSetMtu(u32 dwMtu)
{	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(g_tNvrSysCfg.tAdvanceParam.dwMtu != dwMtu)
	{
		g_tNvrSysCfg.tAdvanceParam.dwMtu = dwMtu;
		eRet = NvrSysSetMtuForce();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysSetMtuForce failed ret:%d\n", eRet);
		}
		eRet = NvrSysCfgSave();
		if (NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysSetMtu save cfg failed ret:%d\n", eRet);
		}
	}
	return eRet;
}



NVRSTATUS NvrSysSetNetAdvanceParam(TNvrSysNetAdvanceParam *ptNetAdvanceParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYSDEBUG("NvrSysSetNetAdvanceParam udp enable:%d,first:%d,second:%d,third:%d,over:%d,url enable:%d,url:%s ktcp:%d port:%u~%u bandwidth %lu~%lu dwMtu:%lu mrtc:%d notrtcpportshare:%d\n",
					ptNetAdvanceParam->tUdpReTranParam.byEnable,
					ptNetAdvanceParam->tUdpReTranParam.dwFirstCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwSecondCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwThirdCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwOverdueDiscard,
					ptNetAdvanceParam->tPlugDownload.byEnable,
					ptNetAdvanceParam->tPlugDownload.achCustomPlugUrl,
					ptNetAdvanceParam->tKtcpParam.bKtcpEnable,
					ptNetAdvanceParam->tKtcpParam.wStartPort,
					ptNetAdvanceParam->tKtcpParam.wEndPort,
					ptNetAdvanceParam->tKtcpParam.dwBandwidthMin,
					ptNetAdvanceParam->tKtcpParam.dwBandwidthMax,
					ptNetAdvanceParam->dwMtu,
					ptNetAdvanceParam->tMrtcParam.bMrtcEnable,
					ptNetAdvanceParam->bNotRtcpPortShare);
    NVRSYSDEBUG("ptNetAdvanceParam->tPingParam.bDisable %u\n", ptNetAdvanceParam->tPingParam.bDisable);

    eRet = NvrSysSetUdpReTranParam(&ptNetAdvanceParam->tUdpReTranParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetUdpReTranParam failed eret:%d\n",eRet);
		return eRet;
	}
	
	eRet = NvrSysSetCustomPlugParam(&ptNetAdvanceParam->tPlugDownload);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetCustomPlugParam failed eret:%d\n",eRet);
		return eRet;
	}
	
	eRet = NvrSysSetKtcpParam(&ptNetAdvanceParam->tKtcpParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetKtcpParam failed eret:%d\n", eRet);
		return eRet;
	}

	eRet = NvrSysSetMrtcParam(&ptNetAdvanceParam->tMrtcParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetMrtcParam failed eret:%d\n", eRet);
		return eRet;
	}

	eRet = NvrVtduSetSysParam(ptNetAdvanceParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrVtduSetSysParam failed eret:%d\n", eRet);
		return eRet;
	}
	
    eRet = NvrSysSetPingParam(&ptNetAdvanceParam->tPingParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetPingParam failed eret:%d\n", eRet);
		return eRet;
	}

    eRet = NvrSysSetMtu(ptNetAdvanceParam->dwMtu);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetMtu failed eret:%d\n", eRet);
		return eRet;
	}
	g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable = ptNetAdvanceParam->bSmoothSndEnable;
	g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate = ptNetAdvanceParam->dwSmoothRate;
	g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare = ptNetAdvanceParam->bNotRtcpPortShare;
	NvrVtduSetSmoothSndParam(g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable, g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate);
	eRet = NvrSysCfgSave();

    return eRet;
}

NVRSTATUS NvrSysGetNetAdvanceParam(TNvrSysNetAdvanceParam *ptNetAdvanceParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    eRet = NvrSysGetUdpReTranParam(&ptNetAdvanceParam->tUdpReTranParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysSetUdpReTranParam failed eret:%d\n",eRet);
		return eRet;
	}

    eRet = NvrSysGetKtcpParam(&ptNetAdvanceParam->tKtcpParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysGetKtcpParam failed eret:%d\n",eRet);
		return eRet;
	}

	eRet = NvrSysGetMrtcParam(&ptNetAdvanceParam->tMrtcParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysGetMrtcParam failed eret:%d\n",eRet);
		return eRet;
	}


	eRet = NvrSysGetCustomPlugParam(&ptNetAdvanceParam->tPlugDownload);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysGetCustomPlugParam failed eret:%d\n",eRet);
		return eRet;
	}
    eRet = NvrSysGetPingParam(&ptNetAdvanceParam->tPingParam);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysGetPingParam failed eret:%d\n",eRet);
		return eRet;
	}

    eRet = NvrSysGetMtu(&ptNetAdvanceParam->dwMtu);
	if(NVR_ERR__OK != eRet)
	{
	    NVRSYSERR("NvrSysGetMtu failed eret:%d\n",eRet);
		return eRet;
	}
	ptNetAdvanceParam->bSmoothSndEnable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bSmoothSndEnable;
	ptNetAdvanceParam->dwSmoothRate = g_tNvrSysCfg.tAdvanceParam.tSysParam.dwSmoothRate;
	ptNetAdvanceParam->bNotRtcpPortShare = g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare;
	
	NVRSYSDEBUG("NvrSysGetNetAdvanceParam udp enable:%d,first:%d,second:%d,third:%d,over:%d,url enable:%d,url:%s bKtcpEnable:%d port:%u~%u bandwidth:%lu~%lu kbps dwMtu:%lu notrtcpportshare:%d\n",
					ptNetAdvanceParam->tUdpReTranParam.byEnable,
					ptNetAdvanceParam->tUdpReTranParam.dwFirstCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwSecondCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwThirdCheckPoint,
					ptNetAdvanceParam->tUdpReTranParam.dwOverdueDiscard,
					ptNetAdvanceParam->tPlugDownload.byEnable,
					ptNetAdvanceParam->tPlugDownload.achCustomPlugUrl,
					ptNetAdvanceParam->tKtcpParam.bKtcpEnable,
					ptNetAdvanceParam->tKtcpParam.wStartPort,
					ptNetAdvanceParam->tKtcpParam.wEndPort,
					ptNetAdvanceParam->tKtcpParam.dwBandwidthMin,
					ptNetAdvanceParam->tKtcpParam.dwBandwidthMax,
					ptNetAdvanceParam->dwMtu,
					ptNetAdvanceParam->bNotRtcpPortShare);
	NVRSYSDEBUG("tPingParam bDisable : %u\n", ptNetAdvanceParam->tPingParam.bDisable);
	
	return eRet;
}

NVRSTATUS NvrSysGetNotRtcpPortShareFlag(BOOL32 *pbNotRtcpPortShare)
{
    NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL != pbNotRtcpPortShare)
		*pbNotRtcpPortShare = g_tNvrSysCfg.tAdvanceParam.bNotRtcpPortShare;
	else
		eRet = NVR_ERR__PARAM_INVALID;
	return eRet;
}

NVRSTATUS NvrSysSetAdvanceSysParam(const TNvrSysAdvanceSysParam *ptSysParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptSysParam);
	ENvrSysAudCallEncType eTmpAudType = NVR_SYS_AUDCALL_ENC_TYPE_MAX;
	BOOL32 byLastDynEnable = FALSE;
		
	OsApi_SemTake(g_hSysCfgRWSem);

	NVRSYSDEBUG("bDiskPreRecord:%d bThirdEncSupport:%d,bcloseaud:%d,bDoubleAudSupport:%d, audtype:%d eBlkSize:%d wAlarmDelayTime:%u bWebTelnetEnable:%d bStreamExternHeadEnable:%d\n",
				ptSysParam->bDiskPreRecord,
				ptSysParam->bThirdEncSupport,
				ptSysParam->bCloseAudio,
				ptSysParam->bDoubleAudioSupport,
				ptSysParam->eAudType,
				ptSysParam->eBlkSize,
				ptSysParam->wAlarmDelayTime,
				ptSysParam->bWebTelnetEnable,
				ptSysParam->bStreamExternHeadEnable);


    ///第三码流关闭，之前为第三码流，则默认成主流
    if(FALSE == ptSysParam->bThirdEncSupport && TRUE == g_tNvrSysCfg.tAdvanceParam.tSysParam.bThirdEncSupport)
    {
        s32 i = 0;
        TNvrRecChnCfg atChnCfg[NVR_MAX_CHN_NUM + NVR_MAX_COMPOSECHN_NUM];
        mzero(atChnCfg);
        NvrRecGetChnCfg(0, NVR_MAX_CHN_NUM + NVR_MAX_COMPOSECHN_NUM, atChnCfg);
        for(i = 0; i < NVR_MAX_CHN_NUM + NVR_MAX_COMPOSECHN_NUM; i++)
        {
            if(ENVR_REC_THIRD_STREAM_PRIORITY == atChnCfg[i].eRecStream)
            {
                atChnCfg[i].eRecStream = ENVR_REC_MAIN_PRIORITY;
            }
        }
        NvrRecSetChnCfg(NVR_MAX_CHN_NUM + NVR_MAX_COMPOSECHN_NUM, atChnCfg);
    }

	byLastDynEnable = g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable;
	eTmpAudType = g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType;
	g_tNvrSysCfg.tAdvanceParam.tSysParam = *ptSysParam;

	///硬盘预录开关
	if(ptSysParam->bDiskPreRecord)
	{
		NvrRecSetHdPreRecState(TRUE);
	}
	else
	{
		NvrRecSetHdPreRecState(FALSE);
	}
	///<如果不支持双路音频支持功能
	if(NVR_CAP_NONSUPPORT == g_tSysCap.tNvrCapAdvance.byDoubleAudSup)
	{
		///第三码流支持
		NvrPuiSetStreamAdvanceParam(ptSysParam->bThirdEncSupport,0,ptSysParam->bCloseAudio,ptSysParam->abCloseChnAudio);
	}
	else
	{
		///第三码流支持
		NvrPuiSetStreamAdvanceParam(ptSysParam->bThirdEncSupport,ptSysParam->bDoubleAudioSupport,ptSysParam->bCloseAudio,ptSysParam->abCloseChnAudio);
	}
	///<音频呼叫格式设置,格式改变才设置
	if(g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType != eTmpAudType)
	{
		eRet = NvrMpuSetAudEncType(ptSysParam->eAudType);
		if(NVR_ERR__OK != eRet)
		{
			///<失败恢复之前编码格式配置
			g_tNvrSysCfg.tAdvanceParam.tSysParam.eAudType = eTmpAudType;
			NVRSYSERR("set aud enc type failed");
		}
	}

	NvrPuiSetPtzCtrlTime(ptSysParam->wPtzCtrlTime);
    NvrVtduCtrlStreamExtHead(ptSysParam->bStreamExternHeadEnable);

    ///保存配置
    NvrSysCfgSave();

	OsApi_SemGive(g_hSysCfgRWSem);

	//动态插件由关闭变成开启，需要开启
	if(TRUE == g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable && byLastDynEnable == FALSE)
	{
		NvrSysStartDynamicLoad();
	}
	
	//动态插件由开启变成关闭，需要关闭
	if(FALSE == g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable && byLastDynEnable == TRUE)
	{
		NvrSysStopDynamicLoad();
	}

	NVRSYSDEBUG("set dyn cur %u,last %u\n",g_tNvrSysCfg.tAdvanceParam.tSysParam.bDynamicPluginEnable,byLastDynEnable);

	return eRet;
}


NVRSTATUS NvrSysStartDynamicLoad()
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
#ifndef WIN32
#ifndef _QCOM_
    void (*pApp)() = NULL;

	//每次stop会dlclose句柄,所以start需要重新创建
	g_pDynHandle = dlopen(NVR_DYNAMICPLUGIN_LIB_PATH,RTLD_LAZY|RTLD_GLOBAL);
	if (NULL == g_pDynHandle)
	    {
	        NVRSYSERR("dlopen app err,%s,line:%d,error:%s\n",NVR_DYNAMICPLUGIN_LIB_PATH,__LINE__,dlerror());
	        eRet = NVR_ERR__ERROR;
	    }
	    else
	    {
	        NVRSYSIMP("dlopen %s succ\n",NVR_DYNAMICPLUGIN_LIB_PATH);
	        ///获取函数地址指针
		pApp = (void (*)())dlsym(g_pDynHandle,"NvrDynamicStartPlugin");
        if (NULL == pApp)
	        {
	            NVRSYSERR("dlsym NvrDynamicStartPlugin err,line:%d,error:%s\n",__LINE__,dlerror());

	            eRet = NVR_ERR__ERROR;
	        }
	        else
	        {
	            NVRSYSIMP("dlsym NvrDynamicStartPlugin succ,begin to startapp\n");
	            //启动app
	        pApp();
	    }
	}
		
	NVRSYSIMP("ret:%d\n",eRet);
#else
	///高通直接编译进去
	NvrDynamicStartPlugin();
#endif
#endif
    return eRet;
}

NVRSTATUS NvrSysStopDynamicLoad()
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
#ifndef WIN32
#ifndef _QCOM_
    void (*pApp)() = NULL;

	//先获得函数符号
	if(g_pDynHandle != NULL)
	{
	    NVRSYSIMP("dlopen %s succ\n",NVR_DYNAMICPLUGIN_LIB_PATH);
	    ///获取函数地址指针
		pApp = (void (*)())dlsym(g_pDynHandle,"NvrDynamicStopPlugin");
        if (NULL == pApp)
        {
            NVRSYSERR("dlsym NvrDynamicStopPlugin err,line:%d,error:%s\n",__LINE__,dlerror());

            eRet = NVR_ERR__ERROR;
        }
		else
		{
		    NVRSYSIMP("dlsym NvrDynamicStopPlugin succ,begin to startapp\n");
		    //启动app
	        pApp();
	    }	

		dlclose(g_pDynHandle);
		g_pDynHandle = NULL;
	}
		
	NVRSYSIMP("ret:%d\n",eRet);
#else
	///高通直接编译进去
	NvrDynamicStopPlugin();
#endif
#endif
    return eRet;
}


NVRSTATUS NvrSysGetAdvanceSysParam(TNvrSysAdvanceSysParam *ptSysParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	NVRSYS_ASSERT(ptSysParam);	
	char achUrlSoPath[NVR_MAX_STR256_LEN] = {""};
	TNvrCapSortwareCapInfo tCapSoftware;
	TNvrCapSysInfo tSysCap;
	mzero(tCapSoftware);
	mzero(tSysCap);
	OsApi_SemTake(g_hSysCfgRWSem);

	*ptSysParam = g_tNvrSysCfg.tAdvanceParam.tSysParam;
	///<如果不支持双路音频支持功能
	if(NVR_CAP_NONSUPPORT == g_tSysCap.tNvrCapAdvance.byDoubleAudSup)
	{
		ptSysParam->bDoubleAudioSupport = 0;
	}

	NVRSYSDEBUG("bDiskPreRecord:%d bThirdEncSupport:%d,bcloseAud:%d,doubleaudsup:%d,eAudType:%d eBlkSize:%d wAlarmDelayTime:%u,bstreamexthead:%d\n",
				ptSysParam->bDiskPreRecord,
				ptSysParam->bThirdEncSupport,
				ptSysParam->bCloseAudio,
				ptSysParam->bDoubleAudioSupport,
				ptSysParam->eAudType,
				ptSysParam->eBlkSize,
				ptSysParam->wAlarmDelayTime,
				ptSysParam->bStreamExternHeadEnable);
	
	OsApi_SemGive(g_hSysCfgRWSem);
	NvrCapGetCapParam(NVR_CAP_ID_SOFTWARE, &tCapSoftware);
	///<根据高级配置中的配置来调整界面能力
	if(ptSysParam->bCloseAudio)
	{
		tCapSoftware.bySupAudCfg = NVR_CAP_NONSUPPORT;
	}
	else
	{
		tCapSoftware.bySupAudCfg = NVR_CAP_SUPPORT;
	}
	NvrCapSetCapParam(NVR_CAP_ID_SOFTWARE, &tCapSoftware);
	NvrCapGetCapParam(NVR_CAP_ID_SYS, &tSysCap);
	snprintf(achUrlSoPath, sizeof(achUrlSoPath), "%s", NVR_DYNAMICPLUGIN_LIB_PATH);	
	if(0 != access(achUrlSoPath, F_OK))
	{
		tSysCap.tNvrCapAdvance.byDynamicPluginSup = NVR_CAP_NONSUPPORT;
	}
	NvrCapSetCapParam(NVR_CAP_ID_SYS, &tSysCap);
	return eRet;


}

NVRSTATUS NvrSysFrameRateSwitch(ENvrSysFrameRate eFrameRate, u8* pbyFrameRate)
{
	NVRSYS_ASSERT(pbyFrameRate);

	switch(eFrameRate)
	{
		case NVR_SYS_FRAME_RATE_1FPS:
		{
			*pbyFrameRate = 1;
		}
		break;
		case NVR_SYS_FRAME_RATE_5FPS:
		{
			*pbyFrameRate = 5;
		}
		break;
		case NVR_SYS_FRAME_RATE_6FPS:
		{
			*pbyFrameRate = 6;
		}
		break;
		case NVR_SYS_FRAME_RATE_8FPS:
		{
			*pbyFrameRate = 8;
		}
		break;
		case NVR_SYS_FRAME_RATE_10FPS:
		{
			*pbyFrameRate = 10;
		}
		break;
		case NVR_SYS_FRAME_RATE_15FPS:
		{
			*pbyFrameRate = 15;
		}
		break;
		case NVR_SYS_FRAME_RATE_20FPS:
		{
			*pbyFrameRate = 20;
		}
		break;
		case NVR_SYS_FRAME_RATE_25FPS:
		{
			*pbyFrameRate = 25;
		}
		break;
		case NVR_SYS_FRAME_RATE_30FPS:
		{
			*pbyFrameRate = 30;
		}
		break;

		default:
		{
			NVRSYSERR("param invalid:%d\n", eFrameRate);
			return NVR_ERR__PARAM_INVALID;
		}

	}

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysBitRateSwitch(ENvrSysBitRate eBitRate, u16* pwBitRate)
{
	NVRSYS_ASSERT(pwBitRate);

	switch(eBitRate)
	{
		case NVR_SYS_BIT_RATE_256K:
		{
			*pwBitRate = 256;
		}
		break;
		case NVR_SYS_BIT_RATE_512K:
		{
			*pwBitRate = 512;
		}
		break;
		case NVR_SYS_BIT_RATE_1M:
		{
			*pwBitRate = 1024;
		}
		break;
		case NVR_SYS_BIT_RATE_2M:
		{
			*pwBitRate = 2*1024;
		}
		break;
		case NVR_SYS_BIT_RATE_4M:
		{
			*pwBitRate = 4*1024;
		}
		break;

		default:
		{
			NVRSYSERR("param invalid:%d\n", eBitRate);
			return NVR_ERR__PARAM_INVALID;
		}

	}

	return NVR_ERR__OK;
}


NVRSTATUS NvrGetRecoveryMode(ENvrCfgRecoveryType *peRecoveryType)
{
	NVRSYSMEMAPI();
    ENvrCfgRecoveryType eRecoveryMode = NVR_CFG_RESET_NO;
    ///如果为完全恢复
    if(g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_ALL)
    {
        eRecoveryMode = NVR_CFG_RESET_ALL;
    }

    else if(g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_NO)
    {
        eRecoveryMode = NVR_CFG_RESET_NO;
    }
    else if(g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_SIMPLE)
    {
        eRecoveryMode = NVR_CFG_RESET_SIMPLE;
    }

    *peRecoveryType = eRecoveryMode;

    NVRSYSDEBUG("Recovery Mode:%d\n",eRecoveryMode);

    return NVR_ERR__OK;
}


NVRSTATUS NvrSysGetRecoveryFlag(ENvrCfgRecoveryType *peRecoveryType, ENvrRecoveryModuleType eRecoveryModuleType)
{
    if(NULL == peRecoveryType)
    {
        return NVR_ERR__ERROR;
    }
    ///如果为完全恢复
    if(g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_ALL)
    {
        *peRecoveryType = NVR_CFG_RESET_ALL;
    }

    ///如果为不恢复
    else if(g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_NO)
    {
        *peRecoveryType = NVR_CFG_RESET_NO;
    }
    ///简单恢复且置位
    if((g_dwRecoveryFlag & 0x01 << eRecoveryModuleType) && (g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_SIMPLE))
    {
        *peRecoveryType = NVR_CFG_RESET_SIMPLE;
    }
    ///简单恢复，但未置位
    else if((!(g_dwRecoveryFlag & 0x01 << eRecoveryModuleType)) && (g_dwRecoveryFlag & 0x01 << NVR_CFG_RESET_SIMPLE))
    {
        *peRecoveryType = NVR_CFG_RESET_NO;
    }
    return NVR_ERR__OK;
}

NVRSTATUS NvrSysClearRecoveryFlag()
{
    NVRSTATUS eRet = NVR_ERR__OK;

    g_dwRecoveryFlag = 0;

    ///默认不恢复
    g_dwRecoveryFlag = 0x01 << NVR_CFG_RESET_NO;

    NVRSYSIMP("NVRCfgSetParam begin NvrSysClearRecoveryFlag !\n");

    eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_RECOVERY_KEY, &g_dwRecoveryFlag, sizeof(g_dwRecoveryFlag));
    if(NVR_ERR__OK != eRet)
    {
        NVRSYSERR("NVRCfgSetParam faild eRet:%d\n", eRet);
        return NVR_ERR__ERROR;
    }

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysRecovery(TNvrSysRecoverParam *ptRecoveryParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
	static BOOL32 bFirstInit = FALSE;
	static SEMHANDLE hRecoverySem;
	ENvrSysNotifyPlatStateType eNotifyType;
	TNvrSysNotifyPlatRestoreInfo tNotifyRestore;

	mzero(tNotifyRestore);

	if(TRUE == g_bJudgeOperate)
		return  NVR_ERR__SVRSF_SERVER_IS_BURN;

	if(FALSE == bFirstInit)
	{
		OsApi_SemBCreate(&hRecoverySem);
		bFirstInit = TRUE;
	}

	///<通知上联平台恢复默认
	eNotifyType = NVR_SYS_NOTIFY_PLAT_TYPE_RESTORE_DEF;
	tNotifyRestore.bRestore = TRUE;
	if(NULL != g_pfNvrSysNotifyPlatStateCB)
		g_pfNvrSysNotifyPlatStateCB(eNotifyType, (void *)&tNotifyRestore);

	NvrNetWorkSnmpNotfiy(NVR_SNMP_RESET);
	//先触发简单恢复的先生效，等待重启
	OsApi_SemTake(hRecoverySem);

    NVRSYSERR("param:%x \n", ptRecoveryParam->dwRecoverCfgType);
    g_dwRecoveryFlag = ptRecoveryParam->dwRecoverCfgType;
    eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_RECOVERY_KEY, &(ptRecoveryParam->dwRecoverCfgType), sizeof(u32));
    NVRSYSFLASH("NVRCfgSetParam %s param: 0x%x, eRet:%u\n", NVR_SYS_RECOVERY_KEY, ptRecoveryParam->dwRecoverCfgType, eRet);
    if(NVR_ERR__OK != eRet)
    {
        NVRSYSIMP("recovery param: %x\n", ptRecoveryParam->dwRecoverCfgType);
		OsApi_SemGive(hRecoverySem);
        return NVR_ERR__ERROR;
    }
    ///ipdt配置无需清空，业务网络参数恢复默认后，会通知ipdt重新生成ipdt配置。业务优先感知完全恢复出厂标志，不读取ipdt配置，
    ///故无需删除ipdt配置
#ifdef _QCOM_
    NvrSystem("rm -rf /mnt/sdcard/config/airp.db");
#endif

	///完全恢复出厂删除证书
	if(ptRecoveryParam->dwRecoverCfgType & (0x01 << NVR_CFG_RESET_ALL))
	{
		char achRmFile[NVR_MAX_STR256_LEN] = {""};
		snprintf(achRmFile, sizeof(achRmFile), "%s%s",NVR_HTTPS_ROOT_PATH,"server.crt");
		unlink(achRmFile);
		snprintf(achRmFile, sizeof(achRmFile), "%s%s",NVR_HTTPS_ROOT_PATH,"server.key");
		unlink(achRmFile);
	}

    ///<对于多芯片的多源设备，主片恢复出厂， 需要对从片进行恢复出厂
    NvrPuiRecoveryDev(ptRecoveryParam->dwRecoverCfgType);    
	NvrSysRecoveryNotify(ptRecoveryParam);
    OsApi_SemGive(hRecoverySem);
    return NVR_ERR__OK;
}


NVRSTATUS NvrSysWriteNormalPoweroff()
{
	NVRSYSMEMAPI();
#ifdef _QCOM_
	FILE *pFile = NULL;
	u32 dwCurTime = 0;
	TNvrBrokenDownTime tUtcTime;
	mzero(tUtcTime);
	NvrSysGetSystemTime(&tUtcTime);
	NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT, &dwCurTime, &tUtcTime);
	fopen(NVR_EXCEPITON_REBOOT_FLAG_INFO,"w");
	if(NULL != pFile)
	{
		fwrite(&dwCurTime, sizeof(dwCurTime), 1, pFile);
		fclose(pFile);
	}
	else
		NVRSYSERR("fopen /sdcard/config/poweroff.data faild!\n");
#endif
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysClearExcepitonRebootFile()
{
	NVRSYSMEMAPI();
#ifdef _QCOM_
	///清除异常日志信息
	unlink(NVR_EXCEPITON_REBOOT_FLAG_INFO);
	return NVR_ERR__OK;
#endif
	return NVR_ERR__OK;

}


NVRSTATUS NvrSysReadNormalPoweroff(u32 *pdwExceptionShutDownTime)
{
	NVRSYSMEMAPI();
#ifdef _QCOM_
	FILE *pFile = NULL;
	u32 dwCurTime = 0;
	fopen(NVR_EXCEPITON_REBOOT_FLAG_INFO,"r");
	if(NULL != pFile)
	{
		fread(&dwCurTime,  1, sizeof(dwCurTime), pFile);
		fclose(pFile);
	}
	else
		NVRSYSERR("fopen %s faild!\n", NVR_EXCEPITON_REBOOT_FLAG_INFO);
#endif
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysReboot(TNvrSysShutDownInfo *ptInfo)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	static SEMHANDLE hRebootSem = NULL;
	static BOOL32 bFirstInit = FALSE;
	s8 byThreadName[NVR_MAX_STR128_LEN];
	TWdParams tParams;
	TNvrSysShutDownInfo tInfo;
	ENvrSysNotifyPlatStateType eNotifyType;
	TNvrSysNotifyPlatRestartInfo tNotifyRestart;
	

	if(TRUE == g_bJudgeOperate)
		return NVR_ERR__SVRSF_SERVER_IS_BURN;

	mzero(tInfo);
	mzero(tParams);
	mzero(byThreadName);
	mzero(tNotifyRestart);
	
	tParams.nTimeout = 1;

	NVRSYSIMP("call SysHwReset begin!\n");

	///记录调用关机线程名
	prctl(PR_GET_NAME, byThreadName, 0, 0, 0);
    NVRSYSFLASH("call reboot pid:%lu, name:%s\n",getpid(), byThreadName);

	if(FALSE == bFirstInit)
	{
		OsApi_SemBCreate(&hRebootSem);
		bFirstInit = TRUE;
	}

	///<通知snmp
	NvrNetWorkSnmpNotfiy(NVR_SNMP_REBOOT);
	NvrNetWorkSnmpNotfiy(NVR_SNMP_SEND_DELETE_DEV);
    NVRSYSFLASH("shut down notify\n");
    NvrSysSysShutdownNotify();
    
    NVRSYSFLASH("take reboot sem\n");
	//防止多条重启信令同时过来
	OsApi_SemTake(hRebootSem);
    
    ///<清空重启次数标记
    NvrSystem("echo 0 > /usr/config/reboot_cnt");	
#ifdef _QCOM_
		NvrSysClearExcepitonRebootFile();
#endif

	if(TRUE == g_tSysCap.tSysHealth.bSupSysHealth)
	{
		if(NULL == ptInfo)
		{
			snprintf(tInfo.achOperator, sizeof(tInfo.achOperator), "Local");
			eRet = NvrSysHealthUpdateFlag(NVR_SYS_HEALTH_FLAG_NORMAL_RESTART, &tInfo);			
		}
		else
		{
			eRet = NvrSysHealthUpdateFlag(NVR_SYS_HEALTH_FLAG_NORMAL_RESTART, ptInfo);
		}
		
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthUpdateFlag failed, eRet = %d !!\n", eRet);
		}
	}
    

    NVRSYSFLASH("slave dev deal\n");
    ///<重启从片,升级时不重启从片
    if(g_bUpdate)
    {
		if((0 != access(NVR_ROOT_PATH"config/update.ini", 0)) &&
			(0 != access(NVR_ROOT_PATH"config/update_cgi.ini", 0)))
		{
			///<没有检测的升级文件，重启则把从片一并重启掉
			NvrPuiRebootSlaveDev();
		}
    }
	else
	{
		NvrPuiRebootSlaveDev();
	}
	
	///<重启前先停止喂狗，防止reboot阻塞，导致重启失败
	NvrGuardFeedSwitch(FALSE);


    NVRSYSFLASH("notify plat\n");
    ///<通知重启
	eNotifyType = NVR_SYS_NOTIFY_PLAT_TYPE_RESTART;
	tNotifyRestart.bRestart = TRUE;
	if(NULL != g_pfNvrSysNotifyPlatStateCB)
		g_pfNvrSysNotifyPlatStateCB(eNotifyType, (void *)&tNotifyRestart);

	///日志备份
	NVRSYSFLASH("log back up\n");
	DebugLogBackUpStart(TRUE);
    NVRSYSFLASH("do HwReset 1\n");
    eRet = NvrBrdSysHwReset(1);
    if(NVR_ERR__OK != eRet)
    {
    	OsApi_SemGive(hRebootSem);
        NVRSYSFLASH("call SysHwReset faild ret:%d!\n", eRet);
        return NVR_ERR__ERROR;
    }
	NVRSYSIMP("call SysHwReset succ!\n");
	OsApi_SemGive(hRebootSem);
    return NVR_ERR__OK;
}


NVRSTATUS NvrSysNotifyPlatInitState(BOOL32 bSuc)
{
	ENvrSysNotifyPlatStateType eNotifyType;
	TNvrSysNotifyPlatInitInfo tNotifyInit;

	mzero(tNotifyInit);

	eNotifyType = NVR_SYS_NOTIFY_PLAT_TYPE_INIT;
	tNotifyInit.bSuc = bSuc;
	if(NULL != g_pfNvrSysNotifyPlatStateCB)
		g_pfNvrSysNotifyPlatStateCB(eNotifyType, (void *)&tNotifyInit);


	 return NVR_ERR__OK;
}


NVRSTATUS NvrSysShutDown(TNvrSysShutDownInfo *ptInfo)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0;
    static SEMHANDLE hRebootSem;
    static BOOL32 bFirstInit = FALSE;
    //BOOL32 bGuardOpen = FALSE;
    s8 byThreadName[NVR_MAX_STR128_LEN];
    TWdParams tParams;
    mzero(tParams);
	mzero(byThreadName);

	if(TRUE == g_bJudgeOperate)
		return NVR_ERR__SVRSF_SERVER_IS_BURN;

    tParams.nTimeout = 1;

	///记录调用关机线程名
	prctl(PR_GET_NAME, byThreadName, 0, 0, 0);
    NVRSYSFLASH("call shutdown pid:%lu, name:%s\n",getpid(), byThreadName);


    if(FALSE == bFirstInit)
    {
        OsApi_SemBCreate(&hRebootSem);
        bFirstInit = TRUE;
    }
    
    NVRSYSFLASH("shut down notify\n");
    NvrSysSysShutdownNotify();

	///日志备份
	NVRSYSFLASH("log back up begin\n");
	DebugLogBackUpStart(TRUE);

    //防止多条重启信令同时过来
    NVRSYSFLASH("take reboot sem\n");
    OsApi_SemTake(hRebootSem);

    //防止重启之前内存数据未写入FLASH
    NVRSYSFLASH("sync\n");
    NvrSystem("sync");

    //防止没有同步结束就重启，导致分区有问题，引起重启时不能解压缩,添加2s延时在重启
    OsApi_TaskDelay(5*1000);
    
    ///<清空重启次数标记
    NvrSystem("echo 0 > /usr/config/reboot_cnt");	
#ifdef _QCOM_
		NvrSysClearExcepitonRebootFile();
#endif

	TNvrCapSysInfo tSysCap;
	mzero(tSysCap);

    NvrCapGetCapParam(NVR_CAP_ID_SYS,(void *)&tSysCap);

	if(TRUE == tSysCap.tSysHealth.bSupSysHealth)
	{
		eRet = NvrSysHealthUpdateFlag(NVR_SYS_HEALTH_FLAG_NORMAL_SHUTDOWN, ptInfo);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("NvrSysHealthUpdateFlag failed, eRet = %d !!\n", eRet);
		}
	}
	#ifdef _QCOM_	
	TNvrLogInfo tLogInfo;
	mzero(tLogInfo);
	tLogInfo.eLogType = NVR_LOG_CLOSE_DEV;
	sprintf(tLogInfo.achSourceId, "NVR");
	sprintf(tLogInfo.achLogDatail, "shut down");
	NvrLogWrite(&tLogInfo);
	#endif
    if(TRUE == tSysCap.tNvrCapSysBasic.byPowerOffShutdown)
    {    
        nRet = NvrSystem("poweroff");
        NVRSYSFLASH("exc poweroff nRet:%d\n", nRet);
        if(0 != nRet)
        {
            eRet = NVR_ERR__ERROR;
            OsApi_SemGive(hRebootSem);
        }
    }
    else
    {
        NVRSYSFLASH("exc NvrBrdSetShutdown\n");
        if(NVR_ERR__OK != NvrBrdSetShutdown())
        {
            eRet = NVR_ERR__ERROR;
            OsApi_SemGive(hRebootSem);
        }

    }

    return eRet;
}

NVRSTATUS NvrSysAccStatNotify(ENvrSysAccStat eStat)
{
	NVRSYSMEMAPI();
	TNvrSysParam tNvrSysParam;

	mzero(tNvrSysParam);

	NvrSysGetSysParam(&tNvrSysParam);


	NVRSYSINFO("stat:%d time:%lu \n", eStat, tNvrSysParam.dwDelayShutdownTime);

	if(NULL == g_hDelayShutDownTimer)
	{
		///创建检测定时器
		if (0 != OsApi_TimerNew(&g_hDelayShutDownTimer))
		{
			NVRSYSERR("create delay shutdown timer failed\n");
			return NVR_ERR__ERROR;
		}
	}

	switch(eStat)
	{
		case NVR_SYS_ACC_STAT_POWEROFF:
		{
			OsApi_TimerSet(g_hDelayShutDownTimer, tNvrSysParam.dwDelayShutdownTime*1000, NvrSysDelayShutDownTimerCB, NULL);
		}
		break;

		case NVR_SYS_ACC_STAT_POWERON:
		{
			OsApi_TimerStop(g_hDelayShutDownTimer);
		}
		break;

		default:
		{
			NVRSYSERR("stat:%d invalid", eStat);
			return NVR_ERR__ERROR;
		}
		break;
	}


	return NVR_ERR__OK;
}


s32 NvrSysClearExportCfgFile( HTIMERHANDLE dwTimerId, void* param )
{
	s32 nRet = 0;
	nRet = NvrSystem("rm -rf /tmp/*_cfg_*.tar");
	if(0 != nRet)
	{
        NVRSYSERR("rm -rf /tmp/*_cfg_*.tar failed\n");
		return nRet;
	}
	///停止定时器
    OsApi_TimerStop(g_hExportCfgClearTimer);
	return nRet;
}

s32 NvrSysDelayShutDownTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
	u32 i = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrRecChnCfg atRecChnCfg[NVR_MAX_LCAM_CHN_NUM];

	mzero(atRecChnCfg);

	NVRSYSINFO("delay shutdown start\n");

	///关机前停止录像
	NvrRecGetChnCfg(0, NVR_MAX_LCAM_CHN_NUM, atRecChnCfg);
	for (i = 0; i < NVR_MAX_LCAM_CHN_NUM; i++)
	{
		atRecChnCfg[i].eRecMode = ENVR_REC_MODE_STOP;
	}
    NvrRecSetChnMemCfg(NVR_MAX_LCAM_CHN_NUM, atRecChnCfg);

	TNvrSysShutDownInfo tInfo;
    mzero(tInfo);

    snprintf(tInfo.achOperator, sizeof(tInfo.achOperator), "Local");
	eRet = NvrSysShutDown(&tInfo);
	if (NVR_ERR__OK != eRet)
	{
		NVRSYSINFO("shutdown error ret:%d", eRet);
	}

	return 0;
}

void NvrSysGetAlllogTarSize(long long unsigned int *pqwAllSize, char *pchDirPath)
{
#ifndef WIN32
	struct dirent	 *pDirent = NULL;
	struct stat statbuf;
	DIR   *dpDir = NULL;
	char achFileName[NVR_MAX_STR512_LEN] = {""};
	///打开目录
	if(NULL == (dpDir = opendir(pchDirPath)))
	{
		printf("opendir:%s faild line:%d,errno:%d,%s\n",pchDirPath, __LINE__, errno, strerror(errno));
		return;
	}

	*pqwAllSize = 0;
	
	///读取目录下文件
	while(NULL != (pDirent = readdir(dpDir)))
	{
		///必须为内存日志打包文件
		if(NULL == strstr(pDirent->d_name,".tar"))
			continue;
		memset(&statbuf, 0, sizeof(statbuf));
		sprintf(achFileName,"%s/%s", pchDirPath, pDirent->d_name);
		
		if(0 == stat(achFileName, &statbuf))
			*pqwAllSize += statbuf.st_size;		
		
	}
	closedir(dpDir);	
	
#endif
}

void NvrSysGetAllFileSize(long long unsigned int *pqwAllSize, char *pchDirPath)
{
#ifndef WIN32
	struct dirent	 *pDirent = NULL;
	struct stat statbuf;
	DIR   *dpDir = NULL;
	char achFileName[NVR_MAX_STR512_LEN] = {""};
	///打开目录
	if(NULL == (dpDir = opendir(pchDirPath)))
	{
		printf("opendir:%s faild line:%d,errno:%d,%s\n",pchDirPath, __LINE__, errno, strerror(errno));
		return;
	}

	*pqwAllSize = 0;
	
	///读取目录下文件
	while(NULL != (pDirent = readdir(dpDir)))
	{
		if(NULL == strstr(pDirent->d_name,".tar") && NULL == strstr(pDirent->d_name,".db"))
			continue;
		memset(&statbuf, 0, sizeof(statbuf));
		sprintf(achFileName,"%s/%s", pchDirPath, pDirent->d_name);
		
		if(0 == stat(achFileName, &statbuf))
			*pqwAllSize += statbuf.st_size;		
		
	}
	closedir(dpDir);	
	
#endif
}

u64 NvrSysGetDirSize(char *pchDirPath)
{
	u64 qwSize = 0;
#ifndef WIN32
	struct dirent *pDirent= NULL;
	struct stat statbuf;
	DIR   *dpDir = NULL;
	char achFileName[NVR_MAX_STR512_LEN] = {""};

	if(NULL == (dpDir = opendir(pchDirPath)))
	{
		printf("opendir:%s faild line:%d,errno:%d,%s\n",pchDirPath, __LINE__, errno, strerror(errno));
		return 0;
	}

	while(NULL != (pDirent= readdir(dpDir)))			
	{				
		memset(&statbuf, 0, sizeof(statbuf));
		sprintf(achFileName,"%s/%s", pchDirPath, pDirent->d_name);		
		if(pDirent->d_type == DT_REG)//普通文件				
		{	
			stat(achFileName,&statbuf);
			qwSize += statbuf.st_size;
		}				
		else if(pDirent->d_type == DT_DIR)//目录文件				
		{	
			//把 . 和.. 过滤
			if(strcmp(pDirent->d_name,".") == 0 || strcmp(pDirent->d_name,"..") == 0)
			{
				continue;
			}
			//把子目录的大小获取
			qwSize +=  NvrSysGetDirSize(achFileName);
		}			
	}	
	
	closedir(dpDir);
#endif
	return qwSize;
}	

u64 NvrSysGetPackDirSize(s8 *pchDirPath, s8 *pchInclude, s8 *pchExcude)
{
	u64 qwSize = 0;
#ifndef WIN32
	int in = 0;

	struct dirent *pDirent= NULL;
	struct stat statbuf;
	DIR   *dpDir = NULL;
	s8 achFileName[NVR_MAX_STR512_LEN] = {""};
	s8 achTmp[NVR_MAX_STR512_LEN] = {""};
	s8 achTmpStr[NVR_MAX_STR256_LEN] = {""};
	
	BOOL32 bInclude = FALSE;
 	s8* pDelimiter = " ";
	BOOL32 bHasIncludeStr = FALSE;
	BOOL32 bHasExcudeStr = FALSE;
	s8 *pchTemInclude[NVR_MAX_PACK_LOG_FILE_NUM];
	s8 *pchTemExcude[NVR_MAX_PACK_LOG_FILE_NUM];	
	s8 *buf = NULL;
	s8 *outer_ptr = NULL;

	if(NULL == (dpDir = opendir(pchDirPath)))
	{
		return 0;
	}

	if(pchInclude && 0 != strlen(pchInclude))
	{
		memcpy(achTmpStr, pchInclude, sizeof(achTmpStr));
		buf = achTmpStr;
		bHasIncludeStr = TRUE;		
		while((pchTemInclude[in] = strtok_r(buf, pDelimiter, &outer_ptr))!=NULL)
		{
			in++;
			buf=NULL;
		}
	}

	if(pchExcude && 0 != strlen(pchExcude))
	{
		memcpy(achTmpStr, pchExcude, sizeof(achTmpStr));
		in = 0;
		buf = achTmpStr;
		outer_ptr = NULL;
		bHasExcudeStr = TRUE;		
		while((pchTemExcude[in] = strtok_r(buf, pDelimiter, &outer_ptr))!=NULL)
		{
			in++;
			buf=NULL;
		}
	}

	while(NULL != (pDirent= readdir(dpDir)))			
	{				
		bInclude = FALSE;
		memset(&statbuf, 0, sizeof(statbuf));		
		snprintf(achTmp, NVR_MAX_STR512_LEN, "./%s", pDirent->d_name);	
		snprintf(achFileName, NVR_MAX_STR512_LEN, "%s/%s", pchDirPath, pDirent->d_name);			
		if(pDirent->d_type == DT_REG) //普通文件 			
		{	
			if(!bHasIncludeStr && !bHasExcudeStr)
			{
				bInclude = TRUE;
			}
			else
			{
				if(bHasIncludeStr)
				{
					if(NULL != strstr(pchInclude, achTmp))
					{
						bInclude = TRUE;						
						NVRSYSDEBUG("pchInclude:%s pDirent->d_name :%s\n", pchInclude, achTmp);
					}
					else
					{
						for(in = 0; in < NVR_MAX_PACK_LOG_FILE_NUM; in++)
						{						
							if(NULL == pchTemInclude[in])
							{
								break;
							}
							
							if(NULL != strstr(achTmp, pchTemInclude[in]))
							{
								bInclude =	TRUE;								
								break;
							}
						}						
					}

				}

				if(bHasExcudeStr && (pchExcude && NULL != strstr(pchExcude, achTmp)))
				{				
					bInclude = FALSE;	
				}

			}
			
			if(bInclude)
			{
				stat(achFileName, &statbuf);
				qwSize += statbuf.st_size;				
				NVRSYSDEBUG("pDirent->d_name :%s\n", pDirent->d_name);
			}
			
		}				
		else if(pDirent->d_type == DT_DIR)//目录文件				
		{	
			//把 . 和.. 过滤
			if(strcmp(pDirent->d_name, ".") == 0 || strcmp(pDirent->d_name, "..") == 0)
			{
				continue;
			}
			NVRSYSDEBUG("dir pDirent->d_name :%s\n", pDirent->d_name);

			//把子目录的大小获取
			if(!bHasIncludeStr && !bHasExcudeStr)
			{
				bInclude = TRUE;
			}
			else
			{
				if(bHasIncludeStr && (pchInclude && NULL != strstr(pchInclude, achTmp)))
				{
					bInclude = TRUE;
				}
				
				if(bHasExcudeStr && (pchExcude && NULL != strstr(pchExcude, achTmp)))
				{
					bInclude = FALSE;
				}

			}
			
			if(bInclude)
			{
				qwSize +=  NvrSysGetDirSize(achFileName);
			}
			
		}			
	}
	
	closedir(dpDir);	
#endif
	return qwSize;
}	



s32 NvrSysClearExportLogFile( HTIMERHANDLE dwTimerId, void* param )
{
	TNvrCapSysBasic tNvrCapSysBasic;
	s8 achCommand[NVR_MAX_STR1024_LEN] = {0};
	mzero(tNvrCapSysBasic);
	NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC,&tNvrCapSysBasic);
	s32 nRet = 0;
	
	NVRSYSDEBUG("NvrSysClearExportLogFile rm tar\n");
	snprintf(achCommand, sizeof(achCommand), "rm -rf %s*_log_*", tNvrCapSysBasic.achLoghExportPath);
	nRet = NvrSystem(achCommand);
	if(0 != nRet)
	{
        NVRSYSERR("rm -rf  *_log_* failed\n");
		return nRet;
	}
	g_nLogLinkNum = 0;
	OsApi_SemTake(g_hSysLogExportSem);
	mzero(g_tNvrSysPack);
	OsApi_SemGive(g_hSysLogExportSem);
	///停止定时器
    OsApi_TimerStop(g_hExportLogClearTimer);
	return nRet;
}

void NvrSysGetAllLogName(const s8 *pchPath,s8 *pchLogName)
{
	struct dirent	 *pDirent = NULL;
	DIR   *dpDir = NULL;
	TNvrCapSysBasic tNvrCapSysBasic;
	mzero(tNvrCapSysBasic);
	NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC,&tNvrCapSysBasic);
	s8 achLogName[NVR_MAX_STR512_LEN]={""};
	///打开目录
	if(NULL == (dpDir = opendir(pchPath)))
	{
		return;
	}
	
	///读取目录下文件
	while(NULL != (pDirent = readdir(dpDir)))
	{
		if(NULL != strstr(tNvrCapSysBasic.achLogNotExport, pDirent->d_name) || NULL != strstr(pDirent->d_name, "mem"))///<先把mem.tar包和根据能力不需要打包的剔除
		{
			continue;
		}
		else if(strstr(pDirent->d_name, "log") || strstr(pDirent->d_name, "txt")) ///<打包log日志文件和txt日志
		{
			mzero(achLogName);
			snprintf(achLogName, sizeof(achLogName), "%s/%s ", pchPath, pDirent->d_name);
			strcat(pchLogName, achLogName);
		}
	}
	closedir(dpDir);	
}

NVRSTATUS NvrSysGetLogTar(const s8* pchPath, s8 *pchName, u32 dwBufLen)
{
	NVRSTATUS eRet = NVR_ERR__ERROR;
	struct dirent	 *pDirent = NULL;
	DIR   *dpDir = NULL;

	///打开目录
	if(NULL == (dpDir = opendir(pchPath)))
	{
		return eRet;
	}
	
	///读取目录下文件
	while(NULL != (pDirent = readdir(dpDir)))
	{
		if(NULL != strstr(pDirent->d_name, "_log_"))
		{
			NVRSYSDEBUG("pDirent->d_name:%s\n", pDirent->d_name);
			snprintf(pchName, dwBufLen, "%s", pDirent->d_name);
			eRet =  NVR_ERR__OK;
			break;
		}
	}
	closedir(dpDir);
	return eRet;

}

void NvrSysGetPackLogName(s8* pchDirPath, s8* pchInclude, s8* pchExcude, s8* pchLogName)
{
#ifndef WIN32
		int in = 0;
		struct dirent *pDirent= NULL;
		struct stat statbuf;
		DIR   *dpDir = NULL;
		s8 achFileName[NVR_MAX_STR512_LEN] = {""};
		s8 achTmpName[NVR_MAX_STR512_LEN] = {""};
		s8 achTmp[NVR_MAX_STR512_LEN] = {""};
		s8 achTmpStr[NVR_MAX_STR256_LEN] = {""};

		
		BOOL32 bInclude = FALSE;
		s8* pDelimiter = " ";
		BOOL32 bHasIncludeStr = FALSE;
		BOOL32 bHasExcudeStr = FALSE;
		s8 *pchTemInclude[NVR_MAX_PACK_LOG_FILE_NUM];
		s8 *pchTemExcude[NVR_MAX_PACK_LOG_FILE_NUM];	
		s8 *buf = NULL;
		s8 *outer_ptr = NULL;

		if(NULL == (dpDir = opendir(pchDirPath)))
		{
			return;
		}
		NVRSYSDEBUG("pchDirPath :%s\n", pchDirPath);

		do
		{
			if(!pchLogName || !pchInclude || !pchExcude || !pchLogName)
			{
				break;
			}
						
			if(0 == strlen(pchInclude) && 0 == strlen(pchExcude))
			{
				snprintf(achTmpName, NVR_MAX_STR512_LEN, "%s/* ", pchDirPath);
				strcat(pchLogName, achTmpName);				
				NVRSYSDEBUG("strcat :%s\n", achTmpName);
				break;
			}

			if(0 != strlen(pchInclude))
			{
				memcpy(achTmpStr, pchInclude, sizeof(achTmpStr));
				buf = achTmpStr;
				bHasIncludeStr = TRUE;		
				while((pchTemInclude[in] = strtok_r(buf, pDelimiter, &outer_ptr))!=NULL)
				{
					in++;
					buf=NULL;
				}
			}
			
			if(0 != strlen(pchExcude))
			{
				memcpy(achTmpStr, pchExcude, sizeof(achTmpStr));
				in = 0;				
				buf = achTmpStr;
				outer_ptr = NULL;
				bHasExcudeStr = TRUE;
				while((pchTemExcude[in] = strtok_r(buf, pDelimiter, &outer_ptr))!=NULL)
				{
					in++;
					buf=NULL;
				}
			}

			while(NULL != (pDirent= readdir(dpDir)))			
			{				
				bInclude = FALSE;
				memset(&statbuf, 0, sizeof(statbuf));				
				snprintf(achTmp, NVR_MAX_STR512_LEN, "./%s", pDirent->d_name);	
				snprintf(achFileName, NVR_MAX_STR512_LEN,"%s/%s", pchDirPath, pDirent->d_name);		
				if(pDirent->d_type == DT_REG)//普通文件 			
				{	
					if(bHasIncludeStr)
					{
						if(NULL != strstr(pchInclude, achTmp))
						{
							bInclude = TRUE;						
							NVRSYSDEBUG("pchInclude:%s pDirent->d_name :%s\n", pchInclude, achTmp);
						}
						else
						{
							for(in = 0; in < NVR_MAX_PACK_LOG_FILE_NUM; in++)
							{
								if(NULL == pchTemInclude[in])
								{
									break;
								}
								if(NULL != strstr(achTmp, pchTemInclude[in]))
								{
									bInclude =	TRUE;
									break;
								}
							}						
						}
					
					}
					
					if(bHasExcudeStr && (pchExcude && NULL != strstr(pchExcude, achTmp)))
					{
						bInclude = FALSE;	
					}

					if(bInclude)
					{
						snprintf(achTmpName, NVR_MAX_STR512_LEN, "%s ", achFileName);	
						strcat(pchLogName, achTmpName);
						NVRSYSDEBUG("pchLogName strcat :%s pchLogName size:%llu\n", achTmpName, strlen(pchLogName));					
					}
									
				}				
				else if(pDirent->d_type == DT_DIR)//目录文件				
				{	
					//把 . 和.. 过滤
					if(strcmp(pDirent->d_name, ".") == 0 || strcmp(pDirent->d_name, "..") == 0)
					{
						continue;
					}
			
					if(bHasIncludeStr && (pchInclude && NULL != strstr(pchInclude, achTmp)))
					{
						bInclude = TRUE;
					}
					
					if(bHasExcudeStr && (pchExcude && NULL != strstr(pchExcude, achTmp)))
					{
						bInclude = FALSE;
					}
			
					if(bInclude)
					{
						NvrSysGetPackLogName(achFileName, pchInclude, pchExcude, pchLogName);
					}
					
				}			
			}

		}while(0);
		
		closedir(dpDir);	
#endif

}

NVRSTATUS NvrSysTarLogThread(void)
{
	NVRSTATUS eNvrRet = NVR_ERR__OK;
    TNvrDoubleListPopAttr tPopAttr;
	TNvrSysTarInfo tTarInfo;
    mzero(tPopAttr);
	mzero(tTarInfo);	
    tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
	tPopAttr.dwDataLen = sizeof(TNvrLogInfo);
    tPopAttr.pchDataBuf = (char*)(&tTarInfo);
	s8* pchCommand = NULL;
	s8* pchTarAll = NULL;
	s8 achCommand[NVR_MAX_STR2048_LEN] = {'0'};
	s8 achDestName[NVR_MAX_STR512_LEN] = {'0'};
	s8 achExternalPath[NVR_MAX_STR512_LEN] = {'0'};
	s8 achEncryptPath[NVR_MAX_STR512_LEN] = {'0'};
	BOOL32 bEncrypt = FALSE;
	s32 nLen = 0;
	s8 achKey[33] = {0};
	s8 achIv[17] = {0};
	const char *pDestStart = NULL;
	const char *pDotStart = NULL;
	const char *pSlash_pos = NULL;
	s8 achDestDir[NVR_MAX_STR256_LEN] = {'0'};
	s8 achDestFileName[NVR_MAX_STR256_LEN] = {'0'};
	s8 achNumber[NVR_MAX_STR128_LEN] = {'0'};
	s8 achTime[NVR_MAX_STR128_LEN] = {'0'};
    s8 achDst[NVR_MAX_STR64_LEN] = {'0'};
	while(TRUE)
	{
		if(tTarInfo.pchTarSrcPath)
		{
			NVRFREE(tTarInfo.pchTarSrcPath);
			tTarInfo.pchTarSrcPath = NULL;
		}
			
		///出队,且队列非空
		if(NVR_ERR__OK == (NvrQueuePop(g_ptQueueTarQueue, &tPopAttr)))
		{
			NVRSYSDEBUG("NvrQueuePop    bEncrypt: %d \n", tTarInfo.bEncrypt);		
			NVRSYSDEBUG("NvrQueuePop    achTarDestPath: %s  \n",tTarInfo.achTarDestPath,
				tTarInfo.pchTarSrcPath);
			NVRSYSDEBUG("NvrQueuePop    pchTarSrcPath: %s	 \n",tTarInfo.pchTarSrcPath,
					tTarInfo.pchTarSrcPath);
			NVRSYSDEBUG("NvrQueuePop    achExternalPath: %s \n", tTarInfo.achExternalPath);

			do
			{
				bEncrypt = tTarInfo.bEncrypt;
				if(bEncrypt)
				{
					pSlash_pos = strrchr(tTarInfo.achTarDestPath, '/');
					if (pSlash_pos == NULL)
					{
						eNvrRet = NVR_ERR__ERROR;
						break;
					} 
					else 
					{
						strncpy(achDestDir, tTarInfo.achTarDestPath, pSlash_pos - tTarInfo.achTarDestPath);
						achDestDir[pSlash_pos - tTarInfo.achTarDestPath] = '\0';
						pDestStart = pSlash_pos + 1;
					}
					pDotStart = strchr(pDestStart, '.');
					if (pDotStart == NULL) {
						eNvrRet = NVR_ERR__ERROR;
						break;
					} else {
						strncpy(achDestFileName, pDestStart, pDotStart - pDestStart);
						achDestFileName[pDotStart - pDestStart] = '\0';
					}
					if(sscanf(achDestFileName, "%[^_]%*[_log_]%[^_]", achNumber, achTime) != 2)
					{
						eNvrRet = NVR_ERR__ERROR;
						break;
					}					
					snprintf(achDestName, sizeof(achDestName), "%s/%s.tar.gz", achDestDir, achDestFileName);	
					NVRSYSDEBUG("achDestName: %s\n", achDestName);	
					snprintf(achEncryptPath, sizeof(achEncryptPath), "%s/%s.bin", achDestDir, achDestFileName);					
					NVRSYSDEBUG("achEncryptPath: %s\n", achEncryptPath);	
				}
				else
				{
				snprintf(achDestName, sizeof(achDestName), "%s", tTarInfo.achTarDestPath);
				}
				snprintf(achExternalPath, sizeof(achExternalPath), "%s", tTarInfo.achExternalPath);
				nLen = strlen(tTarInfo.pchTarSrcPath)+1;
				pchTarAll = NVRALLOC(nLen);
				if(!pchTarAll)
				{
					eNvrRet = NVR_ERR__MALLOC_FAILED;
					NVRSYSDEBUG("NVRALLOC error\n");	
					break;
				}
				
				strncpy(pchTarAll, tTarInfo.pchTarSrcPath, nLen);

				nLen = nLen+NVR_MAX_STR512_LEN;
				pchCommand = NVRALLOC(nLen);
				if(!pchCommand)
				{
					eNvrRet = NVR_ERR__MALLOC_FAILED;
					NVRSYSDEBUG("NVRALLOC error\n");	
					break;
				}

				memset(pchCommand, 0, nLen);
#ifndef _QCOM_		
				snprintf(pchCommand, nLen, "tar -zcvf %s %s", achDestName, pchTarAll);
				NVRSYSDEBUG("cmd :%s\n", pchCommand);	
				NvrSystem(pchCommand);
						
#else	
				snprintf(pchCommand, nLen, "busybox tar -zcvf %s %s", achDestName, tTarInfo.pchTarSrcPath);
				NVRSYSDEBUG("cmd :%s\n", pchCommand);
				NvrSystem(pchCommand);			
#endif	
				if(bEncrypt)
				{
					NvrSysStrEncrypt(achNumber, achDst);					
					snprintf(achKey, sizeof(achKey), "%s", achDst);
					while (strlen(achKey) < 32) {
						strcat(achKey, "0"); // 在末尾补零
					}
					achKey[32] = '\0';				
					snprintf(achIv, sizeof(achIv), "%s", achTime);
					NVRSYSDEBUG("strlen(achIv):%d \n", strlen(achIv));
					while (strlen(achIv) < 16) {
						strcat(achIv, "0"); // 在末尾补零
					}
					achIv[16] = '\0';		
					NVRSYSDEBUG("achDevSerialNum %s  len:%d\n", achKey, strlen(achKey));
					NVRSYSDEBUG("achTime %s  len:%d\n", achIv, strlen(achIv));

					NvrSysEncptFile(achDestName, achEncryptPath, (const unsigned char *)achKey, (const unsigned char *)achIv);
					unlink(achDestName);					
					snprintf(achDestName, sizeof(achEncryptPath), "%s", achEncryptPath);
				
				}
				
				nLen = strlen(achExternalPath);
				if(nLen)
				{
#ifndef _QCOM_
					snprintf(achCommand, sizeof(achCommand), "mv -f %s %s",  achDestName,  achExternalPath);
#else
					snprintf(achCommand, sizeof(achCommand), "busybox mv -f %s %s", achDestName, achExternalPath);
#endif
					NVRSYSDEBUG("cmd :%s\n", achCommand);
					NvrSystem(achCommand);			
				}
			}while(0);
			NVRSYSDEBUG("tTarInfo.achTarDestPath:%s	 g_tNvrSysPack.achTarName:%s\n", 
			achDestName,  g_tNvrSysPack.achTarName);
			OsApi_SemTake(g_hSysLogExportSem);
			FILE* fp = fopen(achDestName, "r");
			if(NULL != fp)
			{
				fclose(fp);
				if(0 != strlen(g_tNvrSysPack.achTarName) && NULL != strstr(achDestName, g_tNvrSysPack.achTarName))
				{
					g_tNvrSysPack.eTarType = NVR_SYS_TAR_SUCCESS;
				}
			}
			else
			{
				if(0 != strlen(g_tNvrSysPack.achTarName) && NULL != strstr(achDestName,  g_tNvrSysPack.achTarName))
				{
					g_tNvrSysPack.eTarType = NVR_SYS_TAR_FAILED;
				}
			}				
			OsApi_SemGive(g_hSysLogExportSem);
			
			NVRSYSDEBUG("NvrSysTarLogThread g_tNvrSysPack.eTarType:%d\n", g_tNvrSysPack.eTarType);

			if(NULL != pchTarAll)
			{
				NVRFREE(pchTarAll);
				pchTarAll = NULL;
			}

			if(NULL != pchCommand)
			{
				NVRFREE(pchCommand);
				pchCommand = NULL;
			}

		}
	}
	OsApi_TaskExit();
	return eNvrRet;
}

NVRSTATUS NvrSysGetTarStatus(const u8* pchTarName, ENvrSysTarStatType *pState)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	s8 achFileName[NVR_MAX_STR256_LEN] = {0};
	if(NULL == pchTarName)
	{
		eRet = NVR_ERR__PARAM_INVALID;
		return eRet;
	}
	snprintf(achFileName, sizeof(achFileName), "%s", pchTarName);
	if(0 != strcmp(g_tNvrSysPack.achTarName, achFileName))
	{		
		eRet = NVR_ERR__ERROR;
	}
	else
	{
		*pState = g_tNvrSysPack.eTarType;
	}
	
	return eRet;
}

NVRSTATUS NvrSysTarLogNotify(TNvrSysTarInfo          tTarInfo)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrDoubleListPushAttr tPushAttr;
	mzero(tPushAttr);
	do
	{
		tPushAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_NORMAL;
        tPushAttr.byMergerType = NVR_QUEUE_NODE_MERGER_TYPE;
		tPushAttr.pchDataBuf = (s8*)(&tTarInfo);
		tPushAttr.dwDataLen = sizeof(TNvrSysTarInfo);
		eRet = NvrQueuePush(g_ptQueueTarQueue, &tPushAttr);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("push g_ptQueueTarQueue failed, eRet:%d\n", eRet);
			break;
		}
	}while(0);

	return eRet;
}
NVRSTATUS NvrSysExportLog(TNvrSysExportLogParam *ptExportLogParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	int i = 0;	

	TNvrSysDevInfo tDevInfo;
	TNvrBrokenDownTime tTime;
	struct statfs statfsbuf;
	TNvrCapPackLogCapInfo tNvrCapPackInfo;

	long long unsigned int qwTmpFreeSize = 0;
	long long unsigned int qwFileTotalSize = 0;
	TNvrCapSysBasic tNvrCapSysBasic;

	NVRSYSDEBUG("NvrSysExportLog start\n");	
	OsApi_SemTake(g_hSysLogExportSem);

	s8 achFileName[NVR_MAX_STR256_LEN] = {0};
	s8 achTime[NVR_MAX_STR128_LEN] = {0};
	s8 achFileFullPath[NVR_MAX_STR256_LEN] = {0};
	s8 *pchLogPath = NULL;
	s32 nLen = 20*NVR_MAX_PACK_LOG_FILE_NUM*NVR_MAX_STR512_LEN;
	TNvrSysTarInfo  tTarInfo;
	struct stat buff;
	memset(&statfsbuf, 0, sizeof(statfsbuf));
	mzero(tNvrCapSysBasic);
	mzero(tDevInfo);
	mzero(tTime);
	mzero(tNvrCapPackInfo);
	mzero(tTarInfo);

	if(NULL == g_hExportLogClearTimer)
	{
	    ///创建检测定时器
		if (0 != OsApi_TimerNew(&g_hExportLogClearTimer))
		{
	        NVRSYSERR("OsApi_TimerNew  g_hExportLogClearTimer failed\n");
			return NVR_ERR__ERROR;
		}
	}
	
	///获取设备序列号
	NvrSysGetDevInfo(&tDevInfo);
	eRet = NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC, &tNvrCapSysBasic);
	if(NVR_ERR__OK != eRet)
	{
		 NVRSYSERR(" NvrCapGetDefCapParam NVR_CAP_SYS_CAP_ID_BASIC failed, err:%d\n", eRet);
		 return eRet;
	}

	eRet = NvrCapGetCapParam(NVR_CAP_ID_PACK_LOG, &tNvrCapPackInfo);
	if(NVR_ERR__OK != eRet)
	{
		 NVRSYSERR(" NvrCapGetCapParam NVR_CAP_ID_PACK_LOG failed, err:%d\n", eRet);
		 return eRet;
	}


	do
	{
		g_nLogLinkNum++;
		if(NVR_ERR__OK == NvrSysGetLogTar(tNvrCapSysBasic.achLoghExportPath, achFileName, sizeof(achFileName)))
		{
			///<如果日志文件存在，则复用同一个日志压缩文件
			NVRSYSDEBUG("achFileName:%s\n", achFileName);
			if(0 == strcmp(achFileName, g_tNvrSysPack.achTarName))
			{
				snprintf(achFileFullPath, sizeof(achFileFullPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath,
					 g_tNvrSysPack.achTarName);
				
				NVRSYSDEBUG("achFileName:%s  eTarType:%d\n", achFileName,  g_tNvrSysPack.eTarType);
			}
			else if(g_tNvrSysPack.eTarType == NVR_SYS_TAR_PREPARING && (0 != strlen(g_tNvrSysPack.achTarName)))
			{
				snprintf(achFileName, sizeof(achFileName), "%s",
					 g_tNvrSysPack.achTarName);
				snprintf(achFileFullPath, sizeof(achFileFullPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath,
					 g_tNvrSysPack.achTarName);
			}
			else
			{				
				NVRSYSERR("error achFileName:%s  tarName:%s\n", achFileName,  g_tNvrSysPack.achTarName);
				eRet = NVR_ERR__ERROR;
				NvrSysUnlinkLog();
			}
			break;					
		}

		if(g_tNvrSysPack.eTarType == NVR_SYS_TAR_PREPARING && (0 != strlen(g_tNvrSysPack.achTarName)))
		{				
			NVRSYSDEBUG("achFileName:%s  eTarType:%d\n", achFileName,  g_tNvrSysPack.eTarType);
			snprintf(achFileName, sizeof(achFileName), "%s", g_tNvrSysPack.achTarName);
			
			snprintf(achFileFullPath, sizeof(achFileFullPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath,
				achFileName);		
			break;
		}

		///10分钟后清除所有导出的日志
		OsApi_TimerSet(g_hExportLogClearTimer, 600*1000, NvrSysClearExportLogFile, NULL);
		eRet = NvrSysGetSystemLocalTime(&tTime);
		if(NVR_ERR__OK != eRet)
		{
			break;
		}
		
		snprintf(achTime, sizeof(achTime), "%u%02u%02u%02u%02u%02u",
									tTime.wYear,
									tTime.byMonth,
									tTime.byDay,
									tTime.byHour,
									tTime.byMinute,
									tTime.bySecond);
		
		if(!tNvrCapPackInfo.bEncrypt)
		{
		snprintf(achFileName, sizeof(achFileName), "%s_log_%s.tar.gz",
			tDevInfo.achDevSerialNum, achTime);
		}
		else
		{
			snprintf(achFileName, sizeof(achFileName), "%s_log_%s.bin",
				tDevInfo.achDevSerialNum, achTime);
		}
		
		snprintf(achFileFullPath, sizeof(achFileFullPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath, achFileName);
		NVRSYSDEBUG("export Path:%s\n", achFileFullPath);

		
		///<获取磁盘剩余空间
		if (0 == statfs(tNvrCapSysBasic.achLoghExportPath, &statfsbuf))
		{
			qwTmpFreeSize = (unsigned int)(((u64)statfsbuf.f_bsize*(u64)statfsbuf.f_bavail));
		}

		NVRSYSDEBUG("qwTmpFreeSize :%llu", qwTmpFreeSize);

		for(i = 0; i<NVR_MAX_PACK_LOG_FILE_NUM; i++)
		{
			if(!tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath)
			{
				continue;
			}
			lstat(tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath, &buff);
			if(S_ISLNK(buff.st_mode))
			{
				NVRSYSDEBUG("DT_LNK achLogPackPath :%s\n", tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath); 		
				continue;
			}			
			
			NVRSYSDEBUG("achLogPackPath :%s\n", tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath);
			if(0 == access(tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath, 0))
			{
				qwFileTotalSize += NvrSysGetPackDirSize(tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath, 
					tNvrCapPackInfo.atPackLogStategy[i].achLogFitterName, tNvrCapPackInfo.atPackLogStategy[i].achLogNotExport);
				
				if(qwFileTotalSize > qwTmpFreeSize)
				{				
					NVRSYSDEBUG("qwFileTotalSize:%llu > qwTmpFreeSize :%llu\n", qwFileTotalSize, qwTmpFreeSize);
					break;
				}
			}
		}
		
		if(qwFileTotalSize > qwTmpFreeSize)
		{
			g_nLogLinkNum--;
			eRet = NVR_ERR__PACK_LOG_SPACE_LIMIT;		
			break;
		}
		else
		{
			pchLogPath = NVRALLOC(nLen);
			if(!pchLogPath)
			{
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}
			memset(pchLogPath, 0, nLen);

			for(i = 0; i<NVR_MAX_PACK_LOG_FILE_NUM; i++)
			{
				if(!tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath)
				{
					continue;
				}
				
				lstat(tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath, &buff);
				if(S_ISLNK(buff.st_mode))
				{
					NVRSYSDEBUG("DT_LNK achLogPackPath :%s\n", tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath); 		
					continue;
				}
				NVRSYSDEBUG("pack achLogPackPath :%s\n", tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath);
				NvrSysGetPackLogName(tNvrCapPackInfo.atPackLogStategy[i].achLogPackPath, tNvrCapPackInfo.atPackLogStategy[i].achLogFitterName,
					tNvrCapPackInfo.atPackLogStategy[i].achLogNotExport, pchLogPath);
			}
			
			nLen = strlen(pchLogPath)+NVR_MAX_STR16_LEN;
			tTarInfo.pchTarSrcPath = NVRALLOC(nLen);
			if(!tTarInfo.pchTarSrcPath)
			{
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}

			tTarInfo.bEncrypt = tNvrCapPackInfo.bEncrypt;
			snprintf(tTarInfo.achTarDestPath, sizeof(tTarInfo.achTarDestPath), "%s", achFileFullPath);	
			snprintf(tTarInfo.pchTarSrcPath, nLen, "%s", pchLogPath);	
			
			if(NVR_CLIENT_TYPE_GUI == ptExportLogParam->eClientType)
			{
				snprintf(tTarInfo.achExternalPath, sizeof(tTarInfo.achExternalPath), "%s/%s",
					ptExportLogParam->achExportLogPath, achFileName);
			}

			
			NVRSYSDEBUG("tTarInfo achTarDestPath :%s  \n", tTarInfo.achTarDestPath);
			mzero(g_tNvrSysPack);
			g_tNvrSysPack.eTarType = NVR_SYS_TAR_PREPARING;			
			snprintf(g_tNvrSysPack.achTarName, sizeof(g_tNvrSysPack.achTarName), "%s", achFileName);			
			eRet = NvrSysTarLogNotify(tTarInfo);
			if(NVR_ERR__OK != eRet)
			{
				NVRFREE(tTarInfo.pchTarSrcPath);
			}
			
			NVRSYSDEBUG("NvrSysTarLogNotify :%d  \n",eRet);
		}
	}while(0);

	strncpy(ptExportLogParam->achExportLogPath, achFileName, sizeof(ptExportLogParam->achExportLogPath));	///导出APP要获取导出的文件名
	NVRSYSDEBUG("ptExportLogParam->achExportLogPath:%s\n", ptExportLogParam->achExportLogPath);

	if(pchLogPath)
	{
		NVRFREE(pchLogPath);
	}	
	OsApi_SemGive(g_hSysLogExportSem);
	NVRSYSDEBUG("NvrSysExportLog end\n");
	return eRet;
}	

NVRSTATUS  NvrSysUnlinkLog()
{
	TNvrCapSysBasic tNvrCapSysBasic;
	s8 achCommand[NVR_MAX_STR1024_LEN] = {0};
	NVRSTATUS eRet = NVR_ERR__OK;
	s32 nRet = 0;
	mzero(tNvrCapSysBasic);
	eRet = NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC,&tNvrCapSysBasic);
	if(NVR_ERR__OK != eRet)
	{
		 NVRSYSERR(" NvrCapGetCapParam NVR_CAP_ID_SYS failed, err:%d\n", eRet);
		 return eRet;
	}

	g_nLogLinkNum--;
	///<当所有的下载请求都完成后。才把日志文件删除
	if(0 >= g_nLogLinkNum)
	{
		NVRSYSDEBUG("start rm tar\n");
		snprintf(achCommand, sizeof(achCommand), "rm -rf %s/*_log_*", tNvrCapSysBasic.achLoghExportPath);
		nRet = NvrSystem(achCommand);
		if(0 != nRet && 512 != nRet)
		{
			NVRSYSERR("exceve %s faild nRet:%d errro:%s\n", achCommand, nRet, strerror(errno));
			eRet = NVR_ERR__ERROR;
		}
		
		OsApi_SemTake(g_hSysLogExportSem);
		mzero(g_tNvrSysPack);
		OsApi_SemGive(g_hSysLogExportSem);
	}
	return eRet;
}


NVRSTATUS NvrSysExportCfg(TNvrSysExprotCfgParam *ptExportCfgParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	s32 nRet = 0;
	FILE *pFile  = NULL;
	char achCommand[NVR_MAX_STR1024_LEN] = {""};
	char achFileName[NVR_MAX_STR128_LEN] = {""};	
	char achCom[NVR_MAX_STR512_LEN] = {""};
	TNvrSysDevInfo tDevInfo;
	TNvrBrokenDownTime tTime;
	TNvrExportCfgInfo tCrcInfo;


	if(NULL == g_hExportCfgClearTimer)
	{
	    ///创建检测定时器
		if (0 != OsApi_TimerNew(&g_hExportCfgClearTimer))
		{
	        NVRSYSERR("OsApi_TimerNew  hExportCfgClearTimer failed\n");
			return NVR_ERR__ERROR;
		}
	}

	///10分钟后清除所有导出的配置
	OsApi_TimerSet(g_hExportCfgClearTimer, 600*1000, NvrSysClearExportCfgFile, NULL);

	mzero(tTime);
	mzero(tDevInfo);
	mzero(tCrcInfo);


    NVRSYS_ASSERT(ptExportCfgParam);

	///获取设备序列号
	NvrSysGetDevInfo(&tDevInfo);

	///暂停配置数据库写入任务，保证数据库无正在写状态
	NVRCfgTaskPause();


	do
	{
		if(FALSE == g_tSysCap.bExportIpSup)
		{
			///删除旧的打包文件
			snprintf(achCommand, sizeof(achCommand), "rm -rf %s/*_cfg_*",NVR_SYS_CFG_EXPORT_PATH);
			NvrSystem(achCommand);
#ifndef _QCOM_
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP);
			nRet = NvrSystem(achCommand);
			if(0 != nRet && 512 != nRet)
			{
				NVRSYSERR("exceve %s faild nRet:%d errro:%s\n", achCommand, nRet, strerror(errno));
				eRet = NVR_ERR__ERROR;
				break;
			}
#else
			NvrSysMvFileDeal(NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP, FALSE);
	//		sprintf(achCommand,"busybox cp -rf %s %s", NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP);
#endif
			///计算CRC
			tCrcInfo.nDatabaseCrc = crc32_file(NVR_CFG_DATABASE_PATH, 0, -1, 0);
			///计算设备型号CRC
			tCrcInfo.nDevTypeCrc = crc32_calc(tDevInfo.achDevType, strlen(tDevInfo.achDevType), 0);
			///创建CRC文件
			pFile = fopen(NVR_EXPROT_CFG_CRC,"w");
			if(NULL != pFile)
			{
				fwrite(&tCrcInfo, 1, sizeof(TNvrExportCfgInfo), pFile);
				fflush(pFile);
				fclose(pFile);
			}
			else
			{
				NVRSYSERR("open file %s faild!\n", NVR_EXPROT_CFG_CRC);
				break;
			}

			eRet = NvrSysGetSystemLocalTime(&tTime);
			if(NVR_ERR__OK != eRet)
			{
				break;
			}

			snprintf(achFileName, sizeof(achFileName), "%s_cfg_%u%02u%02u%02u%02u%02u",
								tDevInfo.achDevSerialNum,
								tTime.wYear,
								tTime.byMonth,
								tTime.byDay,
								tTime.byHour,
								tTime.byMinute,
								tTime.bySecond);
#ifndef _QCOM_
			///判断是否存在文件夹，没有则创建
			snprintf(achCom, sizeof(achCom), "mkdir %s", NVR_SYS_CFG_EXPORT_PATH);		
			if( 0 != access(NVR_SYS_CFG_EXPORT_PATH, 0))
			{
			
				NVRSYSDEBUG("NvrSysExportCfg achCommand:%s!\n", achCom);
				nRet = NvrSystem(achCom);
				if(0 != nRet)
				{
					NVRSYSERR("NvrSysExportCfg exc %s faild!\n", achCom);
					return NVR_ERR__ERROR;
				}
			}

			snprintf(achCommand, sizeof(achCommand), "tar -cvf %s%s %s %s",
								NVR_SYS_CFG_EXPORT_PATH,
								achFileName,
								NVR_EXPROT_CFG_DATABASE_TEMP,
								NVR_EXPROT_CFG_CRC);
			nRet = NvrSystem(achCommand);
			if(0 != nRet && 512 != nRet)
			{
					NVRSYSERR("exceve %s faild nRet:%d error:%s\n", achCommand, nRet, strerror(errno));
				eRet = NVR_ERR__ERROR;
				break;
			}
#else   ///高通没有ipdt
			snprintf(achCommand, sizeof(achCommand), "busybox tar -cvf /data/%s %s %s",
								achFileName,
								NVR_EXPROT_CFG_DATABASE_TEMP,
								NVR_EXPROT_CFG_CRC);
			///不校验返回值
			nRet = NvrSystem(achCommand);
			NVRSYSDEBUG("exceve %s nRet:%d errno:%s\n", achCommand, nRet, strerror(errno));
#endif
			///打包成功
			///GUI直接导出到外置设备
			if(NVR_CLIENT_TYPE_GUI == ptExportCfgParam->eClientType)
			{
				char achExportCfgPath[NVR_MAX_STR256_LEN] = {""};
				NVRSYSDEBUG("ptExportCfgParam->achExprotCfgPath :%s\n", ptExportCfgParam->achExprotCfgPath);
				snprintf(achExportCfgPath, sizeof(ptExportCfgParam->achExprotCfgPath), "%s/%s",ptExportCfgParam->achExprotCfgPath, achFileName);
				strncpy(ptExportCfgParam->achExprotCfgPath, achExportCfgPath,sizeof(ptExportCfgParam->achExprotCfgPath));
#ifndef _QCOM_
				snprintf(achCommand, sizeof(achCommand), "mv -f %s%s %s", NVR_SYS_CFG_EXPORT_PATH,achFileName, ptExportCfgParam->achExprotCfgPath);
#else
				snprintf(achCommand, sizeof(achCommand), "busybox mv -f /data/%s %s", achFileName, ptExportCfgParam->achExprotCfgPath);
#endif
				NVRSYSDEBUG("exc :%s\n", achCommand);
				nRet = NvrSystem(achCommand);
				if(0 != nRet && 512 != nRet)
				{
					NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
					eRet = NVR_ERR__ERROR;
					break;
				}
			}

			strncpy(ptExportCfgParam->achExprotCfgPath, achFileName,sizeof(ptExportCfgParam->achExprotCfgPath));	///导出APP要获取导出的文件名
			NVRSYSDEBUG("ptExportCfgParam->achExprotCfgPath:%s\n", ptExportCfgParam->achExprotCfgPath);
		}
		else
		{
			///删除旧的打包文件
			snprintf(achCommand, sizeof(achCommand), "rm -rf %s/*.tar",NVR_SYS_CFG_EXPORT_PATH);
			NvrSystem(achCommand);
#ifndef _QCOM_
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				NVRSYSERR("exceve %s faild nRet:%d errro:%s\n", achCommand, nRet, strerror(errno));
				eRet = NVR_ERR__ERROR;
				break;
			}
#else
			NvrSysMvFileDeal(NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP, FALSE);
	//		sprintf(achCommand,"busybox cp -rf %s %s", NVR_CFG_DATABASE_PATH, NVR_EXPROT_CFG_DATABASE_TEMP);
#endif
	
#ifndef _QCOM_
	
			NVRSYSDEBUG("not QCOM platform!\n");
			///导出ipdt配置
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_IPDT_CFG_FILE, NVR_EXPORT_IPDT_CFG_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_IPDT_CFG_BACKUP_FILE, NVR_EXPORT_IPDT_CFG_BACKUP_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_IPDT_CFG_CRC_FILE, NVR_EXPORT_IPDT_CFG_CRC_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_IPDT_CFG_CRC_BACKUP_FILE, NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
	
			//snprintf(achCommand, sizeof(achCommand), "cp -rf %s %s", NVR_IPDT_CFG_CHECK_FILE, NVR_EXPORT_IPDT_CFG_CHECK_FILE);
			//nRet = NvrSystem(achCommand);

#endif
			///计算CRC
			tCrcInfo.nDatabaseCrc = crc32_file(NVR_CFG_DATABASE_PATH, 0, -1, 0);
			///计算设备型号CRC
			tCrcInfo.nDevTypeCrc = crc32_calc(tDevInfo.achDevType, strlen(tDevInfo.achDevType), 0);
			///创建CRC文件
			pFile = fopen(NVR_EXPROT_CFG_CRC,"w");
			if(NULL != pFile)
			{
				fwrite(&tCrcInfo, 1, sizeof(TNvrExportCfgInfo), pFile);
				fflush(pFile);
				fclose(pFile);
			}
			else
			{
				NVRSYSERR("open file %s faild!\n", NVR_EXPROT_CFG_CRC);
				break;
			}
	
			eRet = NvrSysGetSystemLocalTime(&tTime);
			if(NVR_ERR__OK != eRet)
			{
				break;
			}
	
			snprintf(achFileName, sizeof(achFileName), "%s_cfg_%u%02u%02u%02u%02u%02u",
								tDevInfo.achDevSerialNum,
								tTime.wYear,
								tTime.byMonth,
								tTime.byDay,
								tTime.byHour,
								tTime.byMinute,
								tTime.bySecond);
#ifndef _QCOM_
			///判断是否存在文件夹，没有则创建
			snprintf(achCom, sizeof(achCom), "mkdir %s", NVR_SYS_CFG_EXPORT_PATH);		
			if( 0 != access(NVR_SYS_CFG_EXPORT_PATH, 0))
			{
			
				NVRSYSDEBUG("NvrSysExportCfg achCommand:%s!\n", achCom);
				nRet = NvrSystem(achCom);
				if(0 != nRet)
				{
					NVRSYSDEBUG("NvrSysExportCfg exc %s faild!\n", achCom);
					return NVR_ERR__ERROR;
				}
			}

			snprintf(achCommand, sizeof(achCommand), "tar -cvf %s%s %s %s %s %s %s %s",
								NVR_SYS_CFG_EXPORT_PATH,
								achFileName,
								NVR_EXPROT_CFG_DATABASE_TEMP,
								NVR_EXPROT_CFG_CRC,
								NVR_EXPORT_IPDT_CFG_FILE,
								NVR_EXPORT_IPDT_CFG_BACKUP_FILE,
								NVR_EXPORT_IPDT_CFG_CRC_FILE,
								NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
					NVRSYSERR("exceve %s faild nRet:%d error:%s\n", achCommand, nRet, strerror(errno));
				eRet = NVR_ERR__ERROR;
				break;
			}
#else   ///高通没有ipdt
			snprintf(achCommand, sizeof(achCommand), "busybox tar -cvf /data/%s %s %s",
								achFileName,
								NVR_EXPROT_CFG_DATABASE_TEMP,
								NVR_EXPROT_CFG_CRC);
			///不校验返回值
			nRet = NvrSystem(achCommand);
			NVRSYSDEBUG("exceve %s nRet:%d errno:%s\n", achCommand, nRet, strerror(errno));
#endif
			///打包成功
			///GUI直接导出到外置设备
			if(NVR_CLIENT_TYPE_GUI == ptExportCfgParam->eClientType)
			{
				char achExportCfgPath[NVR_MAX_STR256_LEN] = {""};
				NVRSYSDEBUG("ptExportCfgParam->achExprotCfgPath :%s\n", ptExportCfgParam->achExprotCfgPath);
				snprintf(achExportCfgPath, sizeof(ptExportCfgParam->achExprotCfgPath), "%s/%s",ptExportCfgParam->achExprotCfgPath, achFileName);
				strcpy(ptExportCfgParam->achExprotCfgPath, achExportCfgPath);
#ifndef _QCOM_
				snprintf(achCommand, sizeof(achCommand), "mv -f %s%s %s", NVR_SYS_CFG_EXPORT_PATH,achFileName, ptExportCfgParam->achExprotCfgPath);
#else
				snprintf(achCommand, sizeof(achCommand), "busybox mv -f /data/%s %s", achFileName, ptExportCfgParam->achExprotCfgPath);
#endif
				NVRSYSDEBUG("exc :%s\n", achCommand);
				nRet = NvrSystem(achCommand);
				if(nRet != 0)
				{
					NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
					eRet = NVR_ERR__ERROR;
					break;
				}
			}
	
			strcpy(ptExportCfgParam->achExprotCfgPath, achFileName);	///导出APP要获取导出的文件名
			NVRSYSDEBUG("ptExportCfgParam->achExprotCfgPath:%s\n", ptExportCfgParam->achExprotCfgPath);
		}
	}while(0);

	if(FALSE == g_tSysCap.bExportIpSup)
	{
	    ///删除临时文件
	    snprintf(achCommand, sizeof(achCommand), "rm -rf %s %s",NVR_EXPROT_CFG_DATABASE_TEMP,NVR_EXPROT_CFG_CRC);
	}
	else
	{
	    ///删除临时文件
	    snprintf(achCommand, sizeof(achCommand), "rm -rf %s %s %s %s %s %s",
				    NVR_EXPROT_CFG_DATABASE_TEMP,
				    NVR_EXPROT_CFG_CRC,
				    NVR_EXPORT_IPDT_CFG_FILE,
				    NVR_EXPORT_IPDT_CFG_BACKUP_FILE,
				    NVR_EXPORT_IPDT_CFG_CRC_FILE,
				    NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE);
	}

	nRet = NvrSystem(achCommand);
	if(0 != nRet && 512 != nRet)
	{
		NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
	}

	///恢复数据库写入任务
	NVRCfgTaskContinue();

	return eRet;
}


NVRSTATUS  NvrSysCfgReplace(char *pchDbPath)
{
	///网络参数 保留
	u32 dwLen = 0;
	u8 *pbyBuffer = NULL;
	NVRSTATUS eRet = NVR_ERR__OK;
	ESQLiteStatus eSqlRet = SQLITE3_OK;
	sqlite3 *pDbHandle = NULL;
	TSqlite3Param tSqlParam;
	char achSql[NVR_MAX_STR256_LEN] = {""};
	mzero(tSqlParam);
	NVRSYSERR("replace database pchDbPath:%s\n", pchDbPath);

	if(0 != access(pchDbPath, F_OK))
	{
	
		NVRSYSERR("pchDbPath:%s not exist!\n", pchDbPath);
		return NVR_ERR__ERROR;
	}

	do
	{
		///替换新数据库中的配置
		eSqlRet = SQLite3OpenDb(pchDbPath, &pDbHandle);
		if(SQLITE3_OK != eSqlRet)
		{
			eRet = NVR_ERR__ERROR;
			break;
		}
		NVRSYSDEBUG("pchDbPath:%s open succ!\n", pchDbPath);

		////用户配置保留
		eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, "UsrCfg", &dwLen);
		if(NVR_ERR__OK == eRet)
		{
			printf("%s dwLen:%lu\n", "UsrCfg", dwLen);		
			pbyBuffer  = NVRALLOC(dwLen);
			if(NULL == pbyBuffer)
			{
				eRet = NVR_ERR__ERROR;
				break;
			}
			memset(pbyBuffer, 0, dwLen);
			eRet = NVRCfgGetParam(NVR_CFG_SERVER, "UsrCfg", pbyBuffer);
			if(NVR_ERR__OK == eRet)
			{
				snprintf(achSql, sizeof(achSql), "update %s set %s = ? where %s = '%s'", "NVRCFG", "CFGDATA", "CFGID", "UsrCfg");
				NVRSYSDEBUG("exc sql:%s\n", achSql);
				eSqlRet = SQLite3Prepare(pDbHandle, &tSqlParam, achSql);
				NVRSYSDEBUG("exc sql:%s eSqlRet:%d\n", achSql, eSqlRet);
				if(SQLITE3_OK != eSqlRet)
				{
					SQLite3Finalize(&tSqlParam);
					eRet = NVR_ERR__ERROR;
					break;
				}
				SQLite3Bind(1, pbyBuffer, SQLITE3_BIND_BLOB, dwLen, &tSqlParam);
				eSqlRet = SQLite3Step(&tSqlParam);
				SQLite3Finalize(&tSqlParam);				
			}
			else
			{
				NVRSYSERR("get %s cfg faild!\n", "UsrCfg");
			}
		}
		else
		{
		
			NVRSYSERR("get %s cfg len faild!\n", "UsrCfg");
			eRet = NVR_ERR__ERROR;
			break;
		}
	}while(0);
	

	if(NULL != pbyBuffer)
		NVRFREE(pbyBuffer);
	if(NULL != pDbHandle)
		SQLite3CloseDb(pDbHandle);

	NVRSYSERR("replace database eRet:%d! pchDbPath:%s\n", eRet, pchDbPath);

	return eRet;
}


NVRSTATUS NvrSysInportCfg(const char* pchInprotCfgPath)
{
	NVRSYSMEMAPI();
	NVRSTATUS  eRet = NVR_ERR__OK;
	s32 nCfgCrc = 0;
	s32 nDevTypeCrc = 0;
	s32 nRet = 0;
	FILE *pFile  = NULL;
	char achCommand[NVR_MAX_STR1024_LEN] = {""};
	TNvrExportCfgInfo tCfgInfo;
	TNvrSysDevInfo tDevInfo;
	char achExportCrcFilePath[NVR_MAX_STR256_LEN] = {""};
	char achExportDataBaseFilePath[NVR_MAX_STR256_LEN] = {""};
	
	mzero(tDevInfo);

    NVRSYS_ASSERT(pchInprotCfgPath);

	///获取设备序列号
	NvrSysGetDevInfo(&tDevInfo);

	do
	{
		if(FALSE == g_tSysCap.bExportIpSup)
		{
#ifndef _QCOM_
			///解包
			snprintf(achCommand,sizeof(achCommand), "tar -xvf %s -C /",pchInprotCfgPath);
#else
	///解包
			snprintf(achCommand, sizeof(achCommand), "busybox tar -xvf %s -C /data/",pchInprotCfgPath);
#endif
			nRet = NvrSystem(achCommand);

	        NVRSYSIMP("exceve %s nRet:%d\n",achCommand,nRet);

			if(nRet != 0 && 512 != nRet)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n",achCommand,nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
			
#ifndef _QCOM_
			snprintf(achExportCrcFilePath, sizeof(achExportCrcFilePath), "%s", NVR_EXPROT_CFG_CRC);
			snprintf(achExportDataBaseFilePath, sizeof(achExportDataBaseFilePath), "%s", NVR_EXPROT_CFG_DATABASE_TEMP);
#else
			snprintf(achExportCrcFilePath, sizeof(achExportCrcFilePath), "%s", "/data/data/exportcfg_crc.dat");
			snprintf(achExportDataBaseFilePath, sizeof(achExportDataBaseFilePath), "%s", "/data/data/nvrcfg_export.db");
#endif
			pFile = fopen(achExportCrcFilePath,"r");
			if(NULL != pFile)
			{
				fread(&tCfgInfo, 1, sizeof(TNvrExportCfgInfo), pFile);
				fclose(pFile);
			}
			else
			{
				NVRSYSERR("open file %s faild!\n", achExportCrcFilePath);
				eRet = NVR_ERR__CFG_INPORT_CFG_CRC_FAILD;
	            break;
			}

			///计算数据库CRC
			nCfgCrc = crc32_file(achExportDataBaseFilePath, 0, -1, 0);
			///校验CRC
			if(nCfgCrc != tCfgInfo.nDatabaseCrc)
			{
				NVRSYSERR("crc check inport cfg faild ! cur database crc:%d raed datebase crc:%d\n",nCfgCrc, tCfgInfo.nDatabaseCrc);
				eRet = NVR_ERR__CFG_INPORT_CFG_CRC_FAILD;
				break;
			}

			///计算当前设备型号CRC
			nDevTypeCrc = crc32_calc(tDevInfo.achDevType, strlen(tDevInfo.achDevType), 0);
			///校验CRC
			if(nDevTypeCrc != tCfgInfo.nDevTypeCrc)
			{
				NVRSYSERR("crc check inport cfg faild ! cur dev type crc:%d raed crc:%d\n", nDevTypeCrc, tCfgInfo.nDevTypeCrc);
				eRet = NVR_ERR__CFG_INPORT_CFG_DEV_ERR;
				break;
			}

		    NVRSYSERR("check inport cfgdatabse    and dev type crc success !\n");
#if 0
			eRet = NvrSysCfgReplace(achExportDataBaseFilePath);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysCfgReplace faild!\n");			
				eRet = NVR_ERR__ERROR;
				break;
			}
#endif

			///暂停配置数据库写入任务，保证数据库无正在写状态
			NVRCfgTaskPause();

	        ///关闭数据库
	        NvrCfgCloseDb();
	        ///业务覆盖原有的数据库
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", achExportDataBaseFilePath, NVR_CFG_DATABASE_PATH);
#ifndef _QCOM_		
			nRet = NvrSystem(achCommand);
#else
			eRet = NvrSysMvFileDeal(achExportDataBaseFilePath, NVR_CFG_DATABASE_PATH, TRUE);
#endif
			if(0 != nRet && 512 != nRet)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
	            NVRCfgOpenDb();
	    		///恢复数据库写入任务
		    	NVRCfgTaskContinue();
				eRet  = NVR_ERR__ERROR;
				break;
			}
	 	    NVRSYSIMP("inport cfg db success !\n");
		}
		else
		{
#ifndef _QCOM_
			///解包
			snprintf(achCommand,sizeof(achCommand), "tar -xvf %s -C /",pchInprotCfgPath);
#else
			///解包
			snprintf(achCommand, sizeof(achCommand), "busybox tar -xvf %s -C /data/",pchInprotCfgPath);
#endif
			nRet = NvrSystem(achCommand);
	
			NVRSYSIMP("exceve %s nRet:%d\n",achCommand,nRet);
	
			if(nRet != 0 && 512 != nRet)
			{
				NVRSYSERR("exceve %s faild nRet:%d\n",achCommand,nRet);
				eRet = NVR_ERR__ERROR;
				break;
			}
			
#ifndef _QCOM_
			snprintf(achExportCrcFilePath, sizeof(achExportCrcFilePath), "%s", NVR_EXPROT_CFG_CRC);
			snprintf(achExportDataBaseFilePath, sizeof(achExportDataBaseFilePath), "%s", NVR_EXPROT_CFG_DATABASE_TEMP);
#else
			snprintf(achExportCrcFilePath, sizeof(achExportCrcFilePath), "%s", "/data/data/exportcfg_crc.dat");
			snprintf(achExportDataBaseFilePath, sizeof(achExportDataBaseFilePath), "%s", "/data/data/nvrcfg_export.db");
#endif
			pFile = fopen(achExportCrcFilePath,"r");
			if(NULL != pFile)
			{
				fread(&tCfgInfo, 1, sizeof(TNvrExportCfgInfo), pFile);
				fclose(pFile);
			}
			else
			{
				NVRSYSERR("open file %s faild!\n", achExportCrcFilePath);
				eRet = NVR_ERR__CFG_INPORT_CFG_CRC_FAILD;
				break;
			}
	
			///计算数据库CRC
			nCfgCrc = crc32_file(achExportDataBaseFilePath, 0, -1, 0);
			///校验CRC
			if(nCfgCrc != tCfgInfo.nDatabaseCrc)
			{
				NVRSYSERR("crc check inport cfg faild ! cur database crc:%d raed datebase crc:%d\n",nCfgCrc, tCfgInfo.nDatabaseCrc);
				eRet = NVR_ERR__CFG_INPORT_CFG_CRC_FAILD;
				break;
			}
	
			///计算当前设备型号CRC
			nDevTypeCrc = crc32_calc(tDevInfo.achDevType, strlen(tDevInfo.achDevType), 0);
			///校验CRC
			if(nDevTypeCrc != tCfgInfo.nDevTypeCrc)
			{
				NVRSYSERR("crc check inport cfg faild ! cur dev type crc:%d raed crc:%d\n", nDevTypeCrc, tCfgInfo.nDevTypeCrc);
				eRet = NVR_ERR__CFG_INPORT_CFG_DEV_ERR;
				break;
			}
	
			NVRSYSERR("check inport cfgdatabse	  and dev type crc success !\n");
	
			///关闭数据库
			NvrCfgCloseDb();
			///业务覆盖原有的数据库
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", achExportDataBaseFilePath, NVR_CFG_DATABASE_PATH);
#ifndef _QCOM_		
			nRet = NvrSystem(achCommand);
#else
			eRet = NvrSysMvFileDeal(achExportDataBaseFilePath, NVR_CFG_DATABASE_PATH, TRUE);
#endif
			if(nRet != 0 || NVR_ERR__OK != eRet)///执行失败重新恢复数据库任务
			{
				NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
				NVRCfgOpenDb();
				///恢复数据库写入任务
				NVRCfgTaskContinue();
				eRet  = NVR_ERR__ERROR;
				break;
			}
			
#ifndef _QCOM_
			///业务覆盖ipdt配置
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", NVR_EXPORT_IPDT_CFG_FILE, NVR_IPDT_CFG_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				eRet  = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", NVR_EXPORT_IPDT_CFG_BACKUP_FILE, NVR_IPDT_CFG_BACKUP_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				eRet  = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", NVR_EXPORT_IPDT_CFG_CRC_FILE, NVR_IPDT_CFG_CRC_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				eRet  = NVR_ERR__ERROR;
				break;
			}
	
			snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE, NVR_IPDT_CFG_CRC_BACKUP_FILE);
			nRet = NvrSystem(achCommand);
			if(nRet != 0)
			{
				eRet  = NVR_ERR__ERROR;
				break;
			}
	
			//snprintf(achCommand, sizeof(achCommand), "mv -f %s %s", NVR_EXPORT_IPDT_CFG_CHECK_FILE, NVR_IPDT_CFG_CHECK_FILE);
			//nRet = NvrSystem(achCommand);

#endif
			NVRSYSIMP("inport cfg db success !\n");
		}
	}while(0);
	if(FALSE == g_tSysCap.bExportIpSup)
	{
	    snprintf(achCommand, sizeof(achCommand), "rm -rf %s %s", achExportDataBaseFilePath, achExportCrcFilePath);
	}
	else
	{
#ifndef _QCOM_
		snprintf(achCommand, sizeof(achCommand), "rm -rf %s %s %s %s %s %s",
							achExportDataBaseFilePath,
							achExportCrcFilePath,
							NVR_EXPORT_IPDT_CFG_FILE,
							NVR_EXPORT_IPDT_CFG_BACKUP_FILE,
							NVR_EXPORT_IPDT_CFG_CRC_FILE,
							NVR_EXPORT_IPDT_CFG_CRC_BACKUP_FILE);
#else
		snprintf(achCommand, sizeof(achCommand), "rm -rf %s %s",
							achExportDataBaseFilePath,
							achExportCrcFilePath);
#endif
	}

	nRet = NvrSystem(achCommand);
	if(0 != nRet && 512 != nRet)
	{
		NVRSYSERR("exceve %s faild nRet:%d\n", achCommand, nRet);
		eRet = NVR_ERR__ERROR;
	}

	///如果校验失败则恢复数据库写入任务
	if(eRet != NVR_ERR__OK)
	{
		///恢复数据库写入任务
		NVRCfgTaskContinue();
	}

	return eRet;
}




NVRSTATUS NvrSysSerialQueryInfo(TNvrSerialInfo *ptSerialInfo)
{
	NVRSYSMEMAPI();
    NVRSTATUS  eRet = NVR_ERR__OK;

    NVRSYS_ASSERT(ptSerialInfo);

    eRet = NvrBrdApiSerialQueryInfo((TSerialInfo*)ptSerialInfo);

    return eRet;
}

s32 NvrSysSerialOpen(TNvrSerialInfo *ptSerialInfo)
{
	NVRSYSMEMAPI();
    s32 nRet = 0;
    TSerialInfo tSerialInfo;
    mzero(tSerialInfo);

    NVRSYS_ASSERT(ptSerialInfo);

    memcpy(&tSerialInfo , ptSerialInfo, sizeof(TNvrSerialInfo));

    NVRSYSIMP("dwNo:%ld dwType:%ld dwUsage:%ld dwFixBaudrate:%ld achName:%s\n",
                tSerialInfo.dwNo,
                tSerialInfo.dwType,
                tSerialInfo.dwUsage,
                tSerialInfo.dwFixBaudrate,
                tSerialInfo.achName);

    nRet = NvrBrdApiSerialOpen(&tSerialInfo);

    return nRet;
}


NVRSTATUS NvrSysSerialClose(s32 nFd)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    eRet = NvrBrdApiSerialClose(nFd);

    return eRet;
}

s32 NvrSysSerialRead(s32 nFd, u8 *pbyBuff, s32 nLength)
{
	NVRSYSMEMAPI();
    s32 nRet = 0;

    NVRSYS_ASSERT(pbyBuff);

    nRet = NvrBrdApiSerialRead(nFd,  pbyBuff, nLength);

    return nRet;
}

s32 NvrSysSerialWrite(s32 nFd, const u8 *pbyBuff, s32 nLength)
{
	NVRSYSMEMAPI();
    s32 nRet = 0;

    NVRSYS_ASSERT(pbyBuff);

    nRet = NvrBrdApiSerialWrite(nFd,  pbyBuff, nLength);

    return nRet;
}


NVRSTATUS NvrSysSerialIoctl(s32 nFd, s32 nFunc, void *pArgs)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    NVRSYS_ASSERT(pArgs);

    eRet = NvrBrdApiSerialIoctl(nFd, nFunc, pArgs);

    return eRet;
}

void NvrSysStrEncrypt(s8 * pchSrc, s8 * pchDst)
{
	NVRSYSMEMAPI();
    s8 achDst[NVR_MAX_STR64_LEN+1];
    s8 achStr[NVR_MAX_STR64_LEN+1];
    int dwKey = NVR_SYS_ENCRYPT_KEY;
    u32 dwLen = 0;
    int i=0;
    int j=0;

    memset(achStr,0x00,sizeof(achStr));
    memset(achDst,0x00,sizeof(achDst));
    strncpy(achStr,pchSrc,strlen(pchSrc)+1);
	achStr[strlen(pchSrc)] = '\0';
    dwLen = strlen(achStr);
    for(i = 0; i < dwLen; i++)
    {
        achStr[i] = (u8)achStr[i]^(dwKey>>8);
        dwKey = ((u8)achStr[i]+dwKey)*NVR_SYS_ENCRYPT_PARAM1+NVR_SYS_ENCRYPT_PARAM2;
    }
    //dwLen = strlen(achStr);
    for(i = 0; i < dwLen; i++)
    {
        j=(u8)achStr[i];
        achDst[i*2] = NVR_SYS_ENCRYPT_OFFSET+j/26;
        achDst[i*2+1] = NVR_SYS_ENCRYPT_OFFSET+j%26;
    }
    strncpy(pchDst,achDst,dwLen*2+1);
    pchDst[dwLen*2] = '\0';
    return ;
}

NVRSTATUS NvrSysAesCbcEncrypt(u8 *pszCiphertext,  s8 *pszPlaintext, u32 nPlaintextLen, const u8 *pszKey, const u8 *pszIvec)
{
    if (NULL == pszPlaintext || NULL == pszCiphertext || NULL == pszKey || NULL == pszIvec) {
        return NVR_ERR__ASSERT;
    }

    s32 nKeyLen = strlen((const s8 *)pszKey);
    s32 nIvecLen = strlen((const s8 *)pszIvec);
	u32 i = 0;
    if ((32 != nKeyLen) || (nIvecLen != 16))
	{
        return NVR_ERR__PARAM_INVALID;
    }

    // 检查密钥和初始化向量是否合法
    for (i = 0; i < nKeyLen; i++) {
        if (pszKey[i] < 0x00 || pszKey[i] > 0xFF) {
            return NVR_ERR__PARAM_INVALID;
        }
    }
    for (i = 0; i < nIvecLen; i++) {
        if (pszIvec[i] < 0x00 || pszIvec[i] > 0xFF) {
            return NVR_ERR__PARAM_INVALID;
        }
    }

    AES_KEY tAesKey;
    int nRet = AES_set_encrypt_key(pszKey, nKeyLen * 8, &tAesKey);
    if (nRet != 0) {
        return NVR_ERR__ERROR;
    }
	u8 *pszIvecTemp = NVRALLOC(nIvecLen);
	if(NULL == pszIvecTemp)
	{
        return NVR_ERR__MALLOC_FAILED;
	}
    

    memcpy(pszIvecTemp, pszIvec, nIvecLen);

    AES_cbc_encrypt((u8 *)pszPlaintext, pszCiphertext, nPlaintextLen, &tAesKey, pszIvecTemp, AES_ENCRYPT);
	if ( NULL != pszIvecTemp)
    {
        NVRFREE(pszIvecTemp);
        pszIvecTemp = NULL;
    }

    return NVR_ERR__OK;
}
NVRSTATUS NvrSysAesCbcDecrypt(s8 *pszPlaintext,  const u8 *pszCiphertext, const int nCiphertextLen, const s8 *pszKey, const s8 *pszIvec)
{
	s32 nKeyLen = 0;
	s32 nIvecLen = 0;
	s32 nRet = 0;	
    AES_KEY tAesKey;
    if (NULL == pszPlaintext || NULL == pszCiphertext || NULL == pszKey || NULL == pszIvec)
            return NVR_ERR__ASSERT;

    nKeyLen = strlen((const s8 *)pszKey);
    nIvecLen = strlen((const s8 *)pszIvec);
    if ((32 != nKeyLen) || (nIvecLen != 16))
	{
		return NVR_ERR__PARAM_INVALID;
	}
    nRet = AES_set_decrypt_key((u8 *)pszKey, nKeyLen * 8, &tAesKey);
    NVRSYSIMP("AES_set_decrypt_key: nRet:%d\n", nRet);
    if (nRet != 0)
            return NVR_ERR__ERROR;

    u8 *pszIvecTemp = NVRALLOC(nIvecLen);
    if (NULL == pszIvecTemp)
	{

		return NVR_ERR__MALLOC_FAILED;
	}

    memcpy(pszIvecTemp, pszIvec, nIvecLen);

    AES_cbc_encrypt(pszCiphertext, (u8 *)pszPlaintext, nCiphertextLen, &tAesKey, pszIvecTemp, AES_DECRYPT);

	if ( NULL != pszIvecTemp)
	{
		NVRFREE(pszIvecTemp);
		pszIvecTemp = NULL;
	}

    return NVR_ERR__OK;
}
//使用AES对文件进行加解密
NVRSTATUS NvrSysEncryptFile(const s8 *pszInFile, const s8 *pszOutFile, const s8* pchKey, const s8* pchIv, BOOL32 bEncrypt)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	FILE *inFile = NULL;
	FILE *outFile = NULL;
	s8 srcBuf[NVR_MAX_STR2048_LEN] = {0};
	s8 dstBuf[NVR_MAX_STR2048_LEN+16] = {0};
	u32 dwReadLen = 0;
	u32 nLen = 0;	
	u8 nIvLen;	
	u8 nKeyLen;	
    if (NULL == pszInFile || NULL == pszOutFile || NULL == pchKey || NULL == pchIv)
    	return NVR_ERR__ASSERT;

    nKeyLen = strlen((const s8 *)pchKey);
    nIvLen = strlen((const s8 *)pchIv);
    if ((32 != nKeyLen) || (nIvLen != 16))
	{
		return NVR_ERR__PARAM_INVALID;
    }
	do
	{
		inFile = fopen(pszInFile, "rb");
		if(NULL == inFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszInFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		outFile = fopen(pszOutFile, "wb");
		if(NULL == outFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszOutFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		while((dwReadLen = fread(srcBuf, 1, sizeof(srcBuf), inFile)) > 0)
		{
			if(bEncrypt)
			{
				eRet = NvrSysAesCbcEncrypt((u8*)dstBuf, srcBuf, dwReadLen, (u8*)pchKey, (u8*)pchIv);
			}
			else
			{
				eRet = NvrSysAesCbcDecrypt(dstBuf, (u8*)srcBuf, dwReadLen, pchKey, pchIv);
			}
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSDEBUG("encrypt failed %d\n", bEncrypt);
				eRet = NVR_ERR__ERROR;
				break;
			}
			if(bEncrypt)
			{
				nLen = dwReadLen;
				if(0 != nLen%16)
				{
					nLen = nLen + 16 - nLen%16;
				}
			}
			else
			{
				nLen = strlen(dstBuf);
			}
			if(fwrite(dstBuf, 1, nLen, outFile) != nLen)
			{
				NVRSYSDEBUG("write failed ! \n");
				eRet = NVR_ERR__ERROR;
				break;
			}
		}
		NVRSYSDEBUG("encrypt/decrypt success\n");
	}while(0);
	if(NULL != inFile)
	{
		fclose(inFile);
	}
	if(NULL != outFile)
	{
		fclose(outFile);
	}
	return eRet;
}

NVRSTATUS NvrSysEncptFile(const char *pszInFile, const char *pszOutFile, const unsigned char *pchKey, unsigned char *pchIv) 
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u8 nIvLen;	
	u8 nKeyLen;
	FILE *inFile = NULL;
	FILE *outFile = NULL;
	unsigned char inbuf[1024];
	unsigned char outbuf[AES_BLOCK_SIZE + 1024];
	int num_bytes_read;
	AES_KEY enc_key;

	if (NULL == pszInFile || NULL == pszOutFile || NULL == pchKey || NULL == pchIv)
    	return NVR_ERR__ASSERT;
	NVRSYSDEBUG("encrypt start\n");
    nKeyLen = strlen((const s8 *)pchKey);
    nIvLen = strlen((const s8 *)pchIv);
    if ((32 != nKeyLen) || (nIvLen != 16))
	{
		return NVR_ERR__PARAM_INVALID;
    }
	do
	{
		inFile = fopen(pszInFile, "rb");
		if(NULL == inFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszInFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		outFile = fopen(pszOutFile, "wb");
		if(NULL == outFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszOutFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		if (AES_set_encrypt_key(pchKey, 256, &enc_key) < 0)
		{	
			NVRSYSDEBUG("set_encrypt failed %s\n", pszOutFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		
		while ((num_bytes_read = fread(inbuf, 1, sizeof(inbuf), inFile)) > 0) 
		{
			AES_cbc_encrypt(inbuf, outbuf, num_bytes_read, &enc_key, pchIv, AES_ENCRYPT); 
			if(0 != num_bytes_read%16)
			{
				num_bytes_read = num_bytes_read + 16 - num_bytes_read%16;
				NVRSYSDEBUG("num_bytes_read:%d ! \n",num_bytes_read);
			}
			if (fwrite(outbuf, 1, num_bytes_read, outFile) != num_bytes_read)
			{	
				NVRSYSDEBUG("write failed ! \n");
				eRet = NVR_ERR__ERROR;
				break;
			}
		}
	}while(0);

	NVRSYSDEBUG("encrypt end\n");

	if(NVR_ERR__OK == eRet)
	{
		NVRSYSDEBUG("encrypt/decrypt success\n");
	}
	
	if(NULL != inFile)
	{
		fclose(inFile);
	}
	if(NULL != outFile)
	{
		fclose(outFile);
	}
	return eRet;
}

NVRSTATUS NvrSysDecptFile(const char *pszInFile, const char *pszOutFile, const unsigned char *pchKey, unsigned char *pchIv)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u8 nIvLen;	
	u8 nKeyLen;
	FILE *inFile = NULL;
	FILE *outFile = NULL;
	unsigned char inbuf[1024];
	unsigned char outbuf[AES_BLOCK_SIZE + 1024];
    int num_bytes_read;
	AES_KEY dec_key;

	if (NULL == pszInFile || NULL == pszOutFile || NULL == pchKey || NULL == pchIv)
    	return NVR_ERR__ASSERT;

	NVRSYSDEBUG("decrypt start\n");
    nKeyLen = strlen((const s8 *)pchKey);
    nIvLen = strlen((const s8 *)pchIv);
    if ((32 != nKeyLen) || (nIvLen != 16))
	{
		return NVR_ERR__PARAM_INVALID;
    }
	do
	{
		inFile = fopen(pszInFile, "rb");
		if(NULL == inFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszInFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		outFile = fopen(pszOutFile, "wb");
		if(NULL == outFile)
		{
			NVRSYSDEBUG("open file failed %s\n", pszOutFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		if (AES_set_decrypt_key(pchKey, 256, &dec_key) < 0)
		{	
			NVRSYSDEBUG("set_decrypt failed %s\n", pszOutFile);
			eRet = NVR_ERR__ERROR;
			break;
		}
		
		while ((num_bytes_read = fread(inbuf, 1, sizeof(inbuf), inFile)) > 0) {
			AES_cbc_encrypt(inbuf, outbuf, num_bytes_read, &dec_key, pchIv, AES_DECRYPT);
			if (fwrite(outbuf, 1, num_bytes_read, outFile) != num_bytes_read)
			{			
				NVRSYSDEBUG("fwrite failed ! \n");
				eRet = NVR_ERR__ERROR;
				break;
			}			
		}
	}while(0);

	NVRSYSDEBUG("decrypt end\n");
	if(NVR_ERR__OK == eRet)
	{
		NVRSYSDEBUG("encrypt/decrypt success\n");
	}
		
	if(NULL != inFile)
	{
		fclose(inFile);
	}
	if(NULL != outFile)
	{
		fclose(outFile);
	}
}
void NvrSysStrDecrypt(char *pchSrc, char *pchDst)
{
	NVRSYSMEMAPI();
    s8 achDst[NVR_MAX_STR64_LEN+1];
    s8 achStr[NVR_MAX_STR64_LEN+1];
    int dwKey = NVR_SYS_ENCRYPT_KEY;
    u32 dwLen = 0;
    int i = 0;
    int j = 0;


    memset(achStr,0x00,sizeof(achStr));
    memset(achDst,0x00,sizeof(achDst));
    strncpy(achStr,pchSrc,strlen(pchSrc)+1);
	achStr[strlen(pchSrc)] = '\0';
    dwLen = strlen(achStr)/2;
    for(i=0; i < dwLen; i++)
    {
        j = ((u8)achStr[2*i]-NVR_SYS_ENCRYPT_OFFSET)*26;
        j += (u8)achStr[2*i+1]-NVR_SYS_ENCRYPT_OFFSET;
        achDst[i] = j;
    }
    memset(achStr,0x00,sizeof(achStr));
    memcpy(achStr,achDst,dwLen);
    //dwLen = strlen(achStr);
    for(i=0; i<dwLen; i++)
    {
        achDst[i] = ((u8)achStr[i]^(dwKey>>8));
        dwKey = ((u8)achStr[i]+dwKey)*NVR_SYS_ENCRYPT_PARAM1+NVR_SYS_ENCRYPT_PARAM2;
    }
    strncpy(pchDst,achDst,strlen(achDst)+1);
    pchDst[strlen(achDst)] = '\0';
    return ;
}

NVRSTATUS NvrSysSetAutoRebootParam(const TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文

    NVRSYS_ASSERT(pParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	mzero(tPbAlocator);
	mzero(tPbAllocData);


	NVRSYSERR("eAutoRebootType:%d \n", pParam->eAutoRebootType);

	///天
	if(NVR_SYS_AUTO_REBOOT_DAY == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_DAY Hour:%ld dwMin:%ld  dwSec:%ld\n",
						pParam->tDayParam.dwHour,
						pParam->tDayParam.dwMin,
						pParam->tDayParam.dwSec);
	}
	///周
	else if(NVR_SYS_AUTO_REBOOT_WEEK == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_WEEK dwWeekDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
						pParam->tWeekParam.dwWeekDay,
						pParam->tWeekParam.tDayParam.dwHour,
						pParam->tWeekParam.tDayParam.dwMin,
						pParam->tWeekParam.tDayParam.dwSec);
	}
	///月
	else if(NVR_SYS_AUTO_REBOOT_MONTH == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_MONTH dwDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
						pParam->tMonthParam.dwDay,
						pParam->tMonthParam.tDayParam.dwHour,
						pParam->tMonthParam.tDayParam.dwMin,
						pParam->tMonthParam.tDayParam.dwSec);
	}

    memcpy(&g_tAutoRebootParam, pParam, sizeof(TNvrAutoRebootParam));

	///内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	do
	{
		///序列化动作
		eRet = NvrSysAutoRebootCfgStructToProto(&tPbcSimple);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("failed ret:%d\n", eRet);
			break;
		}

		///配置写入配置文件中
		eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_AUTO_REBOOT_CFG, tPbcSimple.data, tPbcSimple.len);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("failed ret:%d\n", eRet);
			break;
		}

	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	OsApi_SemGive(g_hSysCfgRWSem);

    return NVR_ERR__OK;

}

NVRSTATUS NvrSysGetAutoRebootParam(TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();

    NVRSYS_ASSERT(pParam);

	OsApi_SemTake(g_hSysCfgRWSem);

    memcpy( pParam, &g_tAutoRebootParam ,sizeof(TNvrAutoRebootParam));

	OsApi_SemGive(g_hSysCfgRWSem);

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysAutoRebootCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg)
{
    if(TRUE == ptPbNvrSysCfg->has_auto_reboot_type)
    {
        g_tAutoRebootParam.eAutoRebootType = ptPbNvrSysCfg->auto_reboot_type;
    }

    if(NULL != ptPbNvrSysCfg->day_param)
    {
        if(TRUE == ptPbNvrSysCfg->day_param->has_hour)
        {
            g_tAutoRebootParam.tDayParam.dwHour = ptPbNvrSysCfg->day_param->hour;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_minute)
        {
            g_tAutoRebootParam.tDayParam.dwMin = ptPbNvrSysCfg->day_param->minute;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_second)
        {
            g_tAutoRebootParam.tDayParam.dwSec = ptPbNvrSysCfg->day_param->second;
        }


    }

    if(NULL != ptPbNvrSysCfg->week_param)
    {
        if(TRUE == ptPbNvrSysCfg->week_param->has_weekday)
        {
            g_tAutoRebootParam.tWeekParam.dwWeekDay = ptPbNvrSysCfg->week_param->weekday;
        }

        if(NULL != ptPbNvrSysCfg->week_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_hour)
            {
                g_tAutoRebootParam.tWeekParam.tDayParam.dwHour = ptPbNvrSysCfg->week_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_minute)
            {
                g_tAutoRebootParam.tWeekParam.tDayParam.dwMin= ptPbNvrSysCfg->week_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_second)
            {
                g_tAutoRebootParam.tWeekParam.tDayParam.dwSec = ptPbNvrSysCfg->week_param->day_param->second;
            }
        }
   }
   /* if(TRUE == ptPbNvrSysCfg->month_param->has_month)
    {
        g_tAutoRebootParam.tMonthParam.dwMonth = ptPbNvrSysCfg->month_param->month;
    }*/


    if(NULL != ptPbNvrSysCfg->month_param)
    {
        if(TRUE == ptPbNvrSysCfg->month_param->has_day)
        {
            g_tAutoRebootParam.tMonthParam.dwDay = ptPbNvrSysCfg->month_param->day;
        }

        if(NULL != ptPbNvrSysCfg->month_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_hour)
            {
                g_tAutoRebootParam.tMonthParam.tDayParam.dwHour = ptPbNvrSysCfg->month_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_minute)
            {
                g_tAutoRebootParam.tMonthParam.tDayParam.dwMin= ptPbNvrSysCfg->month_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_second)
            {
                g_tAutoRebootParam.tMonthParam.tDayParam.dwSec = ptPbNvrSysCfg->month_param->day_param->second;
            }
        }
    }
    return NVR_ERR__OK;

}


NVRSTATUS NvrSysAutoRebootCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
    NVRSYS_ASSERT(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwBufLen = 0;

    TPbNvrSysAutoDayParam tPbNvrSysAutoDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoWeekDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoMonthDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoWeekParam tPbNvrSysAutoWeekParam = TPB_NVR_SYS_AUTO_WEEK_PARAM__INIT;
    TPbNvrSysAutoMonthParam tPbNvrSysAutoMonthParam = TPB_NVR_SYS_AUTO_MONTH_PARAM__INIT;
    TPbNvrAutoRebootParam tPbNvrAutoRebootParam = TPB_NVR_AUTO_REBOOT_PARAM__INIT;

    tPbNvrAutoRebootParam.has_auto_reboot_type = TRUE;
    tPbNvrAutoRebootParam.auto_reboot_type = g_tAutoRebootParam.eAutoRebootType;

    tPbNvrAutoRebootParam.day_param = &tPbNvrSysAutoDayParam;
    tPbNvrAutoRebootParam.day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.day_param->hour = g_tAutoRebootParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.day_param->minute = g_tAutoRebootParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.day_param->second = g_tAutoRebootParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.week_param = &tPbNvrSysAutoWeekParam;
    tPbNvrAutoRebootParam.week_param->has_weekday = TRUE;
    tPbNvrAutoRebootParam.week_param->weekday = g_tAutoRebootParam.tWeekParam.dwWeekDay;
    tPbNvrAutoRebootParam.week_param->day_param = &tPbNvrSysAutoWeekDayParam;
    tPbNvrAutoRebootParam.week_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->hour = g_tAutoRebootParam.tWeekParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.week_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->minute = g_tAutoRebootParam.tWeekParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.week_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->second = g_tAutoRebootParam.tWeekParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.month_param = &tPbNvrSysAutoMonthParam;
    tPbNvrAutoRebootParam.month_param->day_param = &tPbNvrSysAutoMonthDayParam;
    tPbNvrAutoRebootParam.month_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->hour = g_tAutoRebootParam.tMonthParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.month_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->minute = g_tAutoRebootParam.tMonthParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.month_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->second = g_tAutoRebootParam.tMonthParam.tDayParam.dwSec;
    tPbNvrAutoRebootParam.month_param->has_day = TRUE;
    tPbNvrAutoRebootParam.month_param->day = g_tAutoRebootParam.tMonthParam.dwDay;


    ///获取结构体序列化后的二进制buffer大小
	dwBufLen = tpb_nvr_auto_reboot_param__get_packed_size(&tPbNvrAutoRebootParam);

    NVRSYSDEBUG("get pack size :%lu \n", dwBufLen);

	///为ptPbcSimple->data申请内存，外部释放
	ptPbcSimple->data = NVRALLOC(dwBufLen);
	if(NULL == ptPbcSimple->data)
	{
	   NVRSYSERR("malloc pack buffer failed.\n");
	   return NVR_ERR__ERROR;
	}

    ///序列化tPbNvrSysCfg到buffer中
    ptPbcSimple->len = tpb_nvr_auto_reboot_param__pack(&tPbNvrAutoRebootParam, ptPbcSimple->data);
    if(dwBufLen != ptPbcSimple->len)
    {
        NVRSYSERR("pack buffer failed, pack len:%lu \n", ptPbcSimple->len);
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

NVRSTATUS NvrSysAutoRebootInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwSysCfgDataLen = 0;
    ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型
    u8  *pbySysCfgBuf = NULL;    				///<获取配置时保存配置数据的buf
	TPbNvrAutoRebootParam *ptPbNvrSysCfg = NULL;
    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;    		///<分配器
    TNvrPbAllocData tPbAllocData; 				///<分配器上下文

    mzero(tPbAllocData);
    ///内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    do
	{
	    NvrSysGetRecoveryFlag(&eRecoveryType,NVR_BASE_PARAM_CFG_RESET);
		///初始化系统模块配置参数，获取失败则创建默认配置
		eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, NVR_SYS_AUTO_REBOOT_CFG, &dwSysCfgDataLen);
        if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
		{
			NVRSYSDEBUG("get unpack size :%lu \n", dwSysCfgDataLen);

			pbySysCfgBuf = (u8 *)NVRALLOC(dwSysCfgDataLen);
			if(NULL == pbySysCfgBuf)
			{
				NVRSYSERR("malloc SysCfgBuf failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}

			eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_AUTO_REBOOT_CFG, pbySysCfgBuf);
			if(NVR_ERR__OK == eRet)
			{
				ptPbNvrSysCfg = tpb_nvr_auto_reboot_param__unpack(tPbcSimple.allocator, dwSysCfgDataLen, pbySysCfgBuf); 			///<反序列化动作
			}
			else
			{
				NVRSYSERR("NVRCfgGetParam get %s failed\n", NVR_SYS_AUTO_REBOOT_CFG);
				break;
			}

			NvrSysAutoRebootCfgProtoToStruct(ptPbNvrSysCfg);

	 		tpb_nvr_auto_reboot_param__free_unpacked(ptPbNvrSysCfg, tPbcSimple.allocator);	///<释放ptPbNvrSysCfg

		}
		else
		{

			NVRSYSDEBUG("create default cfg\n");

			///获取配置失败，创建默认配置  TODO 不应该用NvrSysCfgDefaultInit 需要另建一个配置默认值的函数
			//NvrSysCfgDefaultInit();

			///序列化动作
			eRet = NvrSysAutoRebootCfgStructToProto(&tPbcSimple);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysAutoRebootCfgStructToProto failed ret:%d\n", eRet);
				break;
			}

			///默认配置写入配置文件中
			eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_AUTO_REBOOT_CFG, tPbcSimple.data, tPbcSimple.len);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
				break;
			}
		}
	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	///释放pbySysCfgBuf空间
	if(pbySysCfgBuf != NULL)
	{
	   NVRFREE(pbySysCfgBuf);
	   pbySysCfgBuf = NULL;
	}

	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("success\n");
	}
	else
	{
	   NVRSYSDEBUG("failed ret:%d\n", eRet);
	}

    return eRet;
}

void NvrSysAutoRebootDeal()
{
    struct tm *ptCurTime = NULL;

    TNvrBrokenDownTime tUtcTime;
    time_t dwCurTime = 0;
    BOOL32 bRebootFlag = FALSE;
    TNvrAutoRebootParam tRebootParam;
    mzero(tRebootParam);
    mzero(tUtcTime);

	OsApi_SemTake(g_hSysCfgRWSem);
    memcpy(&tRebootParam, &g_tAutoRebootParam, sizeof(TNvrAutoRebootParam));
	OsApi_SemGive(g_hSysCfgRWSem);
	if (NVR_SYS_AUTO_REBOOT_DISABLE == tRebootParam.eAutoRebootType)
	{
		NVRSYSTEMP("NVR_SYS_AUTO_REBOOT_DISABLE DO NOTHING\n");
		return;
	}


    NvrSysGetSystemLocalTime(&tUtcTime);
    NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT,(u32*)&dwCurTime, &tUtcTime);

    ptCurTime = gmtime((time_t *)&dwCurTime);

    NVRSYSTEMP("%d-%d-%d weekday:%d %d:%d:%d\n",
                ptCurTime->tm_year + 1900,
                ptCurTime->tm_mon+1,
                ptCurTime->tm_mday,
                ptCurTime->tm_wday,
                ptCurTime->tm_hour,
                ptCurTime->tm_min,
                ptCurTime->tm_sec);

    ///系统周日为0表示
    if(7 == tRebootParam.tWeekParam.dwWeekDay)
    {
        tRebootParam.tWeekParam.dwWeekDay = 0;
    }

    switch(tRebootParam.eAutoRebootType)
    {
        case NVR_SYS_AUTO_REBOOT_DAY:
        {
            NVRSYSTEMP("tDayParam: %lu:%lu:%lu\n",
                            tRebootParam.tDayParam.dwHour,
                            tRebootParam.tDayParam.dwMin,
                            tRebootParam.tDayParam.dwSec);
            if(tRebootParam.tDayParam.dwHour ==ptCurTime->tm_hour&&
               tRebootParam.tDayParam.dwMin == ptCurTime->tm_min &&
               tRebootParam.tDayParam.dwSec == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_WEEK:
        {
            NVRSYSTEMP("tWeekParam:dwWeekDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tWeekParam.dwWeekDay,
                            tRebootParam.tWeekParam.tDayParam.dwHour,
                            tRebootParam.tWeekParam.tDayParam.dwMin,
                            tRebootParam.tWeekParam.tDayParam.dwSec);
            if(tRebootParam.tWeekParam.dwWeekDay == ptCurTime->tm_wday &&
               tRebootParam.tWeekParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tWeekParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tWeekParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_MONTH:
        {
            NVRSYSTEMP("tMonthParam:dwDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tMonthParam.dwDay,
                            tRebootParam.tMonthParam.tDayParam.dwHour,
                            tRebootParam.tMonthParam.tDayParam.dwMin,
                            tRebootParam.tMonthParam.tDayParam.dwSec);
            if(tRebootParam.tMonthParam.dwDay == ptCurTime->tm_mday &&
               tRebootParam.tMonthParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tMonthParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tMonthParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        default:
            break;
    }

    if(TRUE == bRebootFlag)
    {
        NVRSYSIMP("system will auto reboot !\n");
        TNvrSysShutDownInfo tInfo;
        mzero(tInfo);

        snprintf(tInfo.achOperator, sizeof(tInfo.achOperator), "Local");
        NvrSysReboot(&tInfo);
    }
}

NVRSTATUS NvrSysPtzMaintainPuiToParam(TNvrAutoRebootParam* pParam, TNvrPuiPtzAutoMaintain *pPuiParam)
{
	TNvrSysAutoDayParam tDayParam;
	u32 dwTmp = 0;

	mzero(tDayParam);

	if(FALSE == pPuiParam->bEnable)
	{
		pParam->eAutoRebootType = NVR_SYS_AUTO_REBOOT_DISABLE;
	}
	else
	{
		tDayParam.dwHour = pPuiParam->nRestoreTime / (60*60);
		dwTmp = pPuiParam->nRestoreTime % (60*60);
		
		tDayParam.dwMin = dwTmp / 60;
		tDayParam.dwSec = dwTmp % 60;
				
	
		if(TRUE == pPuiParam->bEveryday)
		{
			pParam->eAutoRebootType = NVR_SYS_AUTO_REBOOT_DAY;
			memcpy(&(pParam->tDayParam), &tDayParam, sizeof(tDayParam));
		}
		else
		{
			pParam->eAutoRebootType = NVR_SYS_AUTO_REBOOT_WEEK;
			if(NVR_PUI_WEEKDAY_SUNDAY == pPuiParam->eWeekDay)
			{
				pParam->tWeekParam.dwWeekDay = 7;
			}
			else
			{
				pParam->tWeekParam.dwWeekDay = (s32)pPuiParam->eWeekDay;
			}
			memcpy(&(pParam->tWeekParam.tDayParam), &tDayParam, sizeof(tDayParam));
		}
	}
	return NVR_ERR__OK;
}



NVRSTATUS NvrSysPtzMaintainParamToPui(const TNvrAutoRebootParam* pParam, TNvrPuiPtzAutoMaintain *pPuiParam)
{
	if(NVR_SYS_AUTO_REBOOT_DISABLE == pParam->eAutoRebootType)
	{
		pPuiParam->bEnable = FALSE;
	}
	else
	{
		pPuiParam->bEnable = TRUE;
	}

	if(NVR_SYS_AUTO_REBOOT_DAY == pParam->eAutoRebootType)
	{
		pPuiParam->bEveryday = TRUE;
		pPuiParam->nRestoreTime = (pParam->tDayParam.dwHour)*60*60 +  (pParam->tDayParam.dwMin) *60 + (pParam->tDayParam.dwSec);
	}
	else
	{
		if(NVR_SYS_AUTO_REBOOT_WEEK == pParam->eAutoRebootType)
		{
			if(7 == pParam->tWeekParam.dwWeekDay)
			{
				pPuiParam->eWeekDay = NVR_PUI_WEEKDAY_SUNDAY;
			}
			else
			{
				pPuiParam->eWeekDay = pParam->tWeekParam.dwWeekDay ;
			}

			pPuiParam->nRestoreTime = (pParam->tWeekParam.tDayParam.dwHour)*60*60 +  (pParam->tWeekParam.tDayParam.dwMin) *60 + (pParam->tWeekParam.tDayParam.dwSec);
		}

		if(NVR_SYS_AUTO_REBOOT_MONTH == pParam->eAutoRebootType)
		{
			///<默认设置为周日;
			pPuiParam->eWeekDay = NVR_PUI_WEEKDAY_SUNDAY;

			pPuiParam->nRestoreTime = (pParam->tMonthParam.tDayParam.dwHour)*60*60 +  (pParam->tMonthParam.tDayParam.dwMin) *60 + (pParam->tMonthParam.tDayParam.dwSec);
					
		}

	}

	return NVR_ERR__OK;
}


NVRSTATUS NvrSysSetPtzMaintainAutoRebootParam(const TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文

	TNvrPuiPtzAutoMaintain tPuiPtzMaintain;

	mzero(tPuiPtzMaintain);

    NVRSYS_ASSERT(pParam);

	NVRSYSDEBUG("ePtzType:%d !!\n", g_tSysCap.tUpgradePtz.ePtzType);

	
	#if 0
    ///<根据能力来,如果是下联ipc云台,则配置给下联,如果是本地云台,则保存在sys当中
    if(NVR_PTZ_UPDATE_TYPE_IPC == g_tSysCap.tUpgradePtz.ePtzType)
    {
    	
  		NvrSysPtzMaintainParamToPui(pParam, &tPuiPtzMaintain);
		
    	eRet = NvrPuiSetDevParam(g_tSysCap.tUpgradePtz.wUpgradeChID, 0, NVR_PUI_CFG_PTZ_AUTO_MAINTAIN, &tPuiPtzMaintain);
    	if(NVR_ERR__OK != eRet)
    	{
			NVRSYSERR("NvrPuiSetDevParam err, ret=%d, wUpgradeChID:%d !!\n", eRet, g_tSysCap.tUpgradePtz.wUpgradeChID);
    	}

    }
    else if(NVR_PTZ_UPDATE_TYPE_LOCAL == g_tSysCap.tUpgradePtz.ePtzType)
	{
	#endif
		OsApi_SemTake(g_hSysCfgRWSem);

		mzero(tPbAlocator);
		mzero(tPbAllocData);


		NVRSYSERR("eAutoRebootType:%d \n", pParam->eAutoRebootType);

		///天
		if(NVR_SYS_AUTO_REBOOT_DAY == pParam->eAutoRebootType)
		{
			NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_DAY Hour:%ld dwMin:%ld  dwSec:%ld\n",
							pParam->tDayParam.dwHour,
							pParam->tDayParam.dwMin,
							pParam->tDayParam.dwSec);
		}
		///周
		else if(NVR_SYS_AUTO_REBOOT_WEEK == pParam->eAutoRebootType)
		{
			NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_WEEK dwWeekDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
							pParam->tWeekParam.dwWeekDay,
							pParam->tWeekParam.tDayParam.dwHour,
							pParam->tWeekParam.tDayParam.dwMin,
							pParam->tWeekParam.tDayParam.dwSec);
		}
		///月
		else if(NVR_SYS_AUTO_REBOOT_MONTH == pParam->eAutoRebootType)
		{
			NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_MONTH dwDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
							pParam->tMonthParam.dwDay,
							pParam->tMonthParam.tDayParam.dwHour,
							pParam->tMonthParam.tDayParam.dwMin,
							pParam->tMonthParam.tDayParam.dwSec);
		}

	    memcpy(&g_tPtzAutoRebootParam, pParam, sizeof(TNvrAutoRebootParam));

		///内存分配器初始化
		tPbcSimple.allocator = &tPbAlocator;
		NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

		do
		{
			///序列化动作
			eRet = NvrSysPtzMaintainAutoRebootCfgStructToProto(&tPbcSimple);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("failed ret:%d\n", eRet);
				break;
			}

			///配置写入配置文件中
			eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG, tPbcSimple.data, tPbcSimple.len);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("failed ret:%d\n", eRet);
				break;
			}

		}while(0);

		///释放tPbcSimple中malloc出来的空间
		if(NULL != tPbcSimple.data)
		{
			NVRFREE(tPbcSimple.data);
			tPbcSimple.data = NULL;
		}
		///判断被分配的内存是否全部被释放，没全部释放则记录日志
		if(0 != tPbAllocData.dwAllocCount)
		{
			NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
		}

		OsApi_SemGive(g_hSysCfgRWSem);

	//}

    return eRet;

}

NVRSTATUS NvrSysGetPtzMaintainAutoRebootParam(TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
    NVRSYS_ASSERT(pParam);

    TNvrPuiPtzAutoMaintain tPuiPtzMaintain;

    mzero(tPuiPtzMaintain);

	#if 0
    ///<根据能力来,如果是下联ipc云台,则配置给下联,如果是本地云台,则保存在sys当中
    if(NVR_PTZ_UPDATE_TYPE_IPC == g_tSysCap.tUpgradePtz.ePtzType)
    {
		eRet = NvrPuiGetDevParam(g_tSysCap.tUpgradePtz.wUpgradeChID, 0, NVR_PUI_CFG_PTZ_AUTO_MAINTAIN, &tPuiPtzMaintain);
    	if(NVR_ERR__OK != eRet)
    	{
			NVRSYSERR("NvrPuiGetDevParam err, ret=%d, wUpgradeChID:%d !!\n", eRet, g_tSysCap.tUpgradePtz.wUpgradeChID);
    	}
    
		NvrSysPtzMaintainPuiToParam(pParam, &tPuiPtzMaintain);
    }
    else if(NVR_PTZ_UPDATE_TYPE_LOCAL == g_tSysCap.tUpgradePtz.ePtzType)
    {
    #endif

		OsApi_SemTake(g_hSysCfgRWSem);

	    memcpy( pParam, &g_tPtzAutoRebootParam ,sizeof(TNvrAutoRebootParam));

		OsApi_SemGive(g_hSysCfgRWSem);
	//}

    return eRet;
}

NVRSTATUS NvrSysBallCtrMaintainRegCB(PFNvrSysBallCtrMaintainCB pfBallCtrMaintainCB       )
{
	g_pfNvrSysBallCtrMaintainCB = pfBallCtrMaintainCB;
 	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSetBallCtrMaintainParam(const TNvrBallCtrMatinParam* pParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(pParam);
	BOOL32 bChange = FALSE;
	OsApi_SemTake(g_hSysCfgRWSem);

	do
	{
		if(pParam->byLightEnbale != g_tNvrSysCfg.tBallCtrMatinParam.byLightEnbale ||
			pParam->byShutDownEnable != g_tNvrSysCfg.tBallCtrMatinParam.byShutDownEnable ||
			pParam->byVehicleModeEnbale != g_tNvrSysCfg.tBallCtrMatinParam.byVehicleModeEnbale)
		{
			bChange = TRUE;
		}
		g_tNvrSysCfg.tBallCtrMatinParam = *pParam;
		NVRSYSDEBUG("NvrSysSetBallCtrMaintainParam byShutDownEnable:%d,byLightEnbale:%d,byVehicleModeEnbale:%d\n",
			g_tNvrSysCfg.tBallCtrMatinParam.byShutDownEnable,
			g_tNvrSysCfg.tBallCtrMatinParam.byLightEnbale,
			g_tNvrSysCfg.tBallCtrMatinParam.byVehicleModeEnbale);
		eRet = NvrSysCfgSave();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
		}
	}while(0);

	OsApi_SemGive(g_hSysCfgRWSem);
	if(bChange)
	{
		if(NULL != g_pfNvrSysBallCtrMaintainCB)
		{
			g_pfNvrSysBallCtrMaintainCB(&g_tNvrSysCfg.tBallCtrMatinParam);
		}
		NvrSysCfgChangeNotify(NVR_SYS_BALL_CTR_PARAM,&g_tNvrSysCfg.tBallCtrMatinParam,sizeof(TNvrBallCtrMatinParam));
	}
	return eRet;
}

NVRSTATUS NvrSysGetBallCtrMaintainParam(TNvrBallCtrMatinParam* pParam)
{
	
	NVRSTATUS eRet = NVR_ERR__OK;
	OsApi_SemTake(g_hSysCfgRWSem);	
	memcpy(pParam , &g_tNvrSysCfg.tBallCtrMatinParam,sizeof(TNvrBallCtrMatinParam));
	NVRSYSDEBUG("NvrSysGetBallCtrMaintainParam byShutDownEnable:%d,byLightEnbale:%d,byVehicleModeEnbale:%d\n",
			pParam->byShutDownEnable,
			pParam->byLightEnbale,
			pParam->byVehicleModeEnbale);
	OsApi_SemGive(g_hSysCfgRWSem);
	return eRet;
}

NVRSTATUS NvrSysPtzMaintainAutoRebootCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg)
{
    if(TRUE == ptPbNvrSysCfg->has_auto_reboot_type)
    {
        g_tPtzAutoRebootParam.eAutoRebootType = ptPbNvrSysCfg->auto_reboot_type;
    }

    if(NULL != ptPbNvrSysCfg->day_param)
    {
        if(TRUE == ptPbNvrSysCfg->day_param->has_hour)
        {
            g_tPtzAutoRebootParam.tDayParam.dwHour = ptPbNvrSysCfg->day_param->hour;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_minute)
        {
            g_tPtzAutoRebootParam.tDayParam.dwMin = ptPbNvrSysCfg->day_param->minute;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_second)
        {
            g_tPtzAutoRebootParam.tDayParam.dwSec = ptPbNvrSysCfg->day_param->second;
        }


    }

    if(NULL != ptPbNvrSysCfg->week_param)
    {
        if(TRUE == ptPbNvrSysCfg->week_param->has_weekday)
        {
            g_tPtzAutoRebootParam.tWeekParam.dwWeekDay = ptPbNvrSysCfg->week_param->weekday;
        }

        if(NULL != ptPbNvrSysCfg->week_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_hour)
            {
                g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwHour = ptPbNvrSysCfg->week_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_minute)
            {
                g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwMin= ptPbNvrSysCfg->week_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_second)
            {
                g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwSec = ptPbNvrSysCfg->week_param->day_param->second;
            }
        }
   }
   /* if(TRUE == ptPbNvrSysCfg->month_param->has_month)
    {
        g_tAutoRebootParam.tMonthParam.dwMonth = ptPbNvrSysCfg->month_param->month;
    }*/


    if(NULL != ptPbNvrSysCfg->month_param)
    {
        if(TRUE == ptPbNvrSysCfg->month_param->has_day)
        {
            g_tPtzAutoRebootParam.tMonthParam.dwDay = ptPbNvrSysCfg->month_param->day;
        }

        if(NULL != ptPbNvrSysCfg->month_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_hour)
            {
                g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwHour = ptPbNvrSysCfg->month_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_minute)
            {
                g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwMin= ptPbNvrSysCfg->month_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_second)
            {
                g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwSec = ptPbNvrSysCfg->month_param->day_param->second;
            }
        }
    }
    return NVR_ERR__OK;
}


NVRSTATUS NvrSysPtzMaintainAutoRebootCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
    NVRSYS_ASSERT(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwBufLen = 0;

    TPbNvrSysAutoDayParam tPbNvrSysAutoDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoWeekDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoMonthDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoWeekParam tPbNvrSysAutoWeekParam = TPB_NVR_SYS_AUTO_WEEK_PARAM__INIT;
    TPbNvrSysAutoMonthParam tPbNvrSysAutoMonthParam = TPB_NVR_SYS_AUTO_MONTH_PARAM__INIT;
    TPbNvrAutoRebootParam tPbNvrAutoRebootParam = TPB_NVR_AUTO_REBOOT_PARAM__INIT;

    tPbNvrAutoRebootParam.has_auto_reboot_type = TRUE;
    tPbNvrAutoRebootParam.auto_reboot_type = g_tPtzAutoRebootParam.eAutoRebootType;

    tPbNvrAutoRebootParam.day_param = &tPbNvrSysAutoDayParam;
    tPbNvrAutoRebootParam.day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.day_param->hour = g_tPtzAutoRebootParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.day_param->minute = g_tPtzAutoRebootParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.day_param->second = g_tPtzAutoRebootParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.week_param = &tPbNvrSysAutoWeekParam;
    tPbNvrAutoRebootParam.week_param->has_weekday = TRUE;
    tPbNvrAutoRebootParam.week_param->weekday = g_tPtzAutoRebootParam.tWeekParam.dwWeekDay;
    tPbNvrAutoRebootParam.week_param->day_param = &tPbNvrSysAutoWeekDayParam;
    tPbNvrAutoRebootParam.week_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->hour = g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.week_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->minute = g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.week_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->second = g_tPtzAutoRebootParam.tWeekParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.month_param = &tPbNvrSysAutoMonthParam;
    tPbNvrAutoRebootParam.month_param->day_param = &tPbNvrSysAutoMonthDayParam;
    tPbNvrAutoRebootParam.month_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->hour = g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.month_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->minute = g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.month_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->second = g_tPtzAutoRebootParam.tMonthParam.tDayParam.dwSec;
    tPbNvrAutoRebootParam.month_param->has_day = TRUE;
    tPbNvrAutoRebootParam.month_param->day = g_tPtzAutoRebootParam.tMonthParam.dwDay;


    ///获取结构体序列化后的二进制buffer大小
	dwBufLen = tpb_nvr_auto_reboot_param__get_packed_size(&tPbNvrAutoRebootParam);

    NVRSYSDEBUG("get pack size :%lu \n", dwBufLen);

	///为ptPbcSimple->data申请内存，外部释放
	ptPbcSimple->data = NVRALLOC(dwBufLen);
	if(NULL == ptPbcSimple->data)
	{
	   NVRSYSERR("malloc pack buffer failed.\n");
	   return NVR_ERR__ERROR;
	}

    ///序列化tPbNvrSysCfg到buffer中
    ptPbcSimple->len = tpb_nvr_auto_reboot_param__pack(&tPbNvrAutoRebootParam, ptPbcSimple->data);
    if(dwBufLen != ptPbcSimple->len)
    {
        NVRSYSERR("pack buffer failed, pack len:%lu \n", ptPbcSimple->len);
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

NVRSTATUS NvrSysPtzMainTainAutoRebootInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwSysCfgDataLen = 0;
    ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型
    u8  *pbySysCfgBuf = NULL;    				///<获取配置时保存配置数据的buf
	TPbNvrAutoRebootParam *ptPbNvrSysCfg = NULL;
    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;    		///<分配器
    TNvrPbAllocData tPbAllocData; 				///<分配器上下文

    mzero(tPbAllocData);
    ///内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    do
	{
	    NvrSysGetRecoveryFlag(&eRecoveryType,NVR_BASE_PARAM_CFG_RESET);
		///初始化系统模块配置参数，获取失败则创建默认配置
		eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG, &dwSysCfgDataLen);
        if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
		{
			NVRSYSDEBUG("get unpack size :%lu \n", dwSysCfgDataLen);

			pbySysCfgBuf = (u8 *)NVRALLOC(dwSysCfgDataLen);
			if(NULL == pbySysCfgBuf)
			{
				NVRSYSERR("malloc SysCfgBuf failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}

			eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG, pbySysCfgBuf);
			if(NVR_ERR__OK == eRet)
			{
				ptPbNvrSysCfg = tpb_nvr_auto_reboot_param__unpack(tPbcSimple.allocator, dwSysCfgDataLen, pbySysCfgBuf); 			///<反序列化动作
			}
			else
			{
				NVRSYSERR("NVRCfgGetParam get %s failed\n", NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG);
				break;
			}

			NvrSysPtzMaintainAutoRebootCfgProtoToStruct(ptPbNvrSysCfg);

	 		tpb_nvr_auto_reboot_param__free_unpacked(ptPbNvrSysCfg, tPbcSimple.allocator);	///<释放ptPbNvrSysCfg

		}
		else
		{

			NVRSYSDEBUG("create default cfg\n");

			///获取配置失败，创建默认配置  TODO 不应该用NvrSysCfgDefaultInit 需要另建一个配置默认值的函数
			//NvrSysCfgDefaultInit();

			///序列化动作
			eRet = NvrSysPtzMaintainAutoRebootCfgStructToProto(&tPbcSimple);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysAutoRebootCfgStructToProto failed ret:%d\n", eRet);
				break;
			}

			///默认配置写入配置文件中
			eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_PTZ_MAINTAIN_AUTO_REBOOT_CFG, tPbcSimple.data, tPbcSimple.len);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
				break;
			}
		}
	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	///释放pbySysCfgBuf空间
	if(pbySysCfgBuf != NULL)
	{
	   NVRFREE(pbySysCfgBuf);
	   pbySysCfgBuf = NULL;
	}

	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("success\n");
	}
	else
	{
	   NVRSYSDEBUG("failed ret:%d\n", eRet);
	}

    return eRet;
}


void NvrSysPtzMaintainAutoRebootDeal()
{
    struct tm *ptCurTime = NULL;

    TNvrBrokenDownTime tUtcTime;
    time_t dwCurTime = 0;
    BOOL32 bRebootFlag = FALSE;
    TNvrAutoRebootParam tRebootParam;
    TNvrPuiPtzTaskParam tTask;
	TNvrPtzCtrlInfo tCtrlInfo;

    mzero(tRebootParam);
    mzero(tUtcTime);
    mzero(tTask);
    mzero(tCtrlInfo);


    NvrSysGetSystemLocalTime(&tUtcTime);
    NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT, (u32*)&dwCurTime, &tUtcTime);

    ptCurTime = gmtime((time_t *)&dwCurTime);

    NVRSYSTEMP("%d-%d-%d weekday:%d %d:%d:%d\n",
                ptCurTime->tm_year + 1900,
                ptCurTime->tm_mon+1,
                ptCurTime->tm_mday,
                ptCurTime->tm_wday,
                ptCurTime->tm_hour,
                ptCurTime->tm_min,
                ptCurTime->tm_sec);

	OsApi_SemTake(g_hSysCfgRWSem);
    memcpy(&tRebootParam, &g_tPtzAutoRebootParam, sizeof(TNvrAutoRebootParam));
	OsApi_SemGive(g_hSysCfgRWSem);

    ///系统周日为0表示
    if(7 == tRebootParam.tWeekParam.dwWeekDay)
    {
        tRebootParam.tWeekParam.dwWeekDay = 0;
    }

    switch(tRebootParam.eAutoRebootType)
    {
        case NVR_SYS_AUTO_REBOOT_DAY:
        {
            NVRSYSTEMP("tDayParam: %lu:%lu:%lu\n",
                            tRebootParam.tDayParam.dwHour,
                            tRebootParam.tDayParam.dwMin,
                            tRebootParam.tDayParam.dwSec);
            if(tRebootParam.tDayParam.dwHour ==ptCurTime->tm_hour&&
               tRebootParam.tDayParam.dwMin == ptCurTime->tm_min &&
               tRebootParam.tDayParam.dwSec == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_WEEK:
        {
            NVRSYSTEMP("tWeekParam:dwWeekDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tWeekParam.dwWeekDay,
                            tRebootParam.tWeekParam.tDayParam.dwHour,
                            tRebootParam.tWeekParam.tDayParam.dwMin,
                            tRebootParam.tWeekParam.tDayParam.dwSec);
            if(tRebootParam.tWeekParam.dwWeekDay == ptCurTime->tm_wday &&
               tRebootParam.tWeekParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tWeekParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tWeekParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_MONTH:
        {
            NVRSYSTEMP("tMonthParam:dwDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tMonthParam.dwDay,
                            tRebootParam.tMonthParam.tDayParam.dwHour,
                            tRebootParam.tMonthParam.tDayParam.dwMin,
                            tRebootParam.tMonthParam.tDayParam.dwSec);
            if(tRebootParam.tMonthParam.dwDay == ptCurTime->tm_mday &&
               tRebootParam.tMonthParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tMonthParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tMonthParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        default:
            break;
    }

    if(TRUE == bRebootFlag)
    {	
    	tTask.eSrcType = ENVR_PUI_PTZ_SRC_NVR;

    	tCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_RESET;

    	
		///<云台维护自动维护操作,TODO
		NvrPuiPtzCtrl(&tTask, g_tSysCap.tUpgradePtz.wUpgradeChID, &tCtrlInfo);
    }
}
NVRSTATUS NvrSysSetAutoLensResetParam(const TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;			///<分配器
	TNvrPbAllocData tPbAllocData;				///<分配器上下文

    NVRSYS_ASSERT(pParam);

	OsApi_SemTake(g_hSysCfgRWSem);

	mzero(tPbAlocator);
	mzero(tPbAllocData);


	NVRSYSERR("eAutoRebootType:%d \n", pParam->eAutoRebootType);

	///天
	if(NVR_SYS_AUTO_REBOOT_DAY == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_DAY Hour:%ld dwMin:%ld  dwSec:%ld\n",
						pParam->tDayParam.dwHour,
						pParam->tDayParam.dwMin,
						pParam->tDayParam.dwSec);
	}
	///周
	else if(NVR_SYS_AUTO_REBOOT_WEEK == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_WEEK dwWeekDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
						pParam->tWeekParam.dwWeekDay,
						pParam->tWeekParam.tDayParam.dwHour,
						pParam->tWeekParam.tDayParam.dwMin,
						pParam->tWeekParam.tDayParam.dwSec);
	}
	///月
	else if(NVR_SYS_AUTO_REBOOT_MONTH == pParam->eAutoRebootType)
	{
		NVRSYSDEBUG("NVR_SYS_AUTO_REBOOT_MONTH dwDay:%ld Hour:%ld dwMin:%ld dwSec:%ld\n",
						pParam->tMonthParam.dwDay,
						pParam->tMonthParam.tDayParam.dwHour,
						pParam->tMonthParam.tDayParam.dwMin,
						pParam->tMonthParam.tDayParam.dwSec);
	}

    memcpy(&g_tLenAutoMaintainParam, pParam, sizeof(TNvrAutoRebootParam));

	///内存分配器初始化
	tPbcSimple.allocator = &tPbAlocator;
	NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

	do
	{
		///序列化动作
		eRet = NvrSysLenAutoMaintainCfgStructToProto(&tPbcSimple);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("failed ret:%d\n", eRet);
			break;
		}

		///配置写入配置文件中
		eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_LEN_AUTO_MAINTAIN_CFG, tPbcSimple.data, tPbcSimple.len);
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("failed ret:%d\n", eRet);
			break;
		}

	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	OsApi_SemGive(g_hSysCfgRWSem);

    return NVR_ERR__OK;

}

NVRSTATUS NvrSysGetAutoLensResetParam(TNvrAutoRebootParam* pParam)
{
	NVRSYSMEMAPI();

    NVRSYS_ASSERT(pParam);

	OsApi_SemTake(g_hSysCfgRWSem);

    memcpy( pParam, &g_tLenAutoMaintainParam ,sizeof(TNvrAutoRebootParam));

	OsApi_SemGive(g_hSysCfgRWSem);

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysLenAutoMaintainCfgProtoToStruct(const TPbNvrAutoRebootParam* ptPbNvrSysCfg)
{
    if(TRUE == ptPbNvrSysCfg->has_auto_reboot_type)
    {
        g_tLenAutoMaintainParam.eAutoRebootType = ptPbNvrSysCfg->auto_reboot_type;
    }

    if(NULL != ptPbNvrSysCfg->day_param)
    {
        if(TRUE == ptPbNvrSysCfg->day_param->has_hour)
        {
            g_tLenAutoMaintainParam.tDayParam.dwHour = ptPbNvrSysCfg->day_param->hour;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_minute)
        {
            g_tLenAutoMaintainParam.tDayParam.dwMin = ptPbNvrSysCfg->day_param->minute;
        }

        if(TRUE == ptPbNvrSysCfg->day_param->has_second)
        {
            g_tLenAutoMaintainParam.tDayParam.dwSec = ptPbNvrSysCfg->day_param->second;
        }


    }

    if(NULL != ptPbNvrSysCfg->week_param)
    {
        if(TRUE == ptPbNvrSysCfg->week_param->has_weekday)
        {
            g_tLenAutoMaintainParam.tWeekParam.dwWeekDay = ptPbNvrSysCfg->week_param->weekday;
        }

        if(NULL != ptPbNvrSysCfg->week_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_hour)
            {
                g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwHour = ptPbNvrSysCfg->week_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_minute)
            {
                g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwMin= ptPbNvrSysCfg->week_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->week_param->day_param->has_second)
            {
                g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwSec = ptPbNvrSysCfg->week_param->day_param->second;
            }
        }
   }

    if(NULL != ptPbNvrSysCfg->month_param)
    {
        if(TRUE == ptPbNvrSysCfg->month_param->has_day)
        {
            g_tLenAutoMaintainParam.tMonthParam.dwDay = ptPbNvrSysCfg->month_param->day;
        }

        if(NULL != ptPbNvrSysCfg->month_param->day_param)
        {
            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_hour)
            {
                g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwHour = ptPbNvrSysCfg->month_param->day_param->hour;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_minute)
            {
                g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwMin= ptPbNvrSysCfg->month_param->day_param->minute;
            }

            if(TRUE == ptPbNvrSysCfg->month_param->day_param->has_second)
            {
                g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwSec = ptPbNvrSysCfg->month_param->day_param->second;
            }
        }
    }
    return NVR_ERR__OK;
}


NVRSTATUS NvrSysLenAutoMaintainCfgStructToProto(ProtobufCBufferSimple *ptPbcSimple)
{
    NVRSYS_ASSERT(ptPbcSimple);
	NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwBufLen = 0;

    TPbNvrSysAutoDayParam tPbNvrSysAutoDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoWeekDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoDayParam tPbNvrSysAutoMonthDayParam = TPB_NVR_SYS_AUTO_DAY_PARAM__INIT;
    TPbNvrSysAutoWeekParam tPbNvrSysAutoWeekParam = TPB_NVR_SYS_AUTO_WEEK_PARAM__INIT;
    TPbNvrSysAutoMonthParam tPbNvrSysAutoMonthParam = TPB_NVR_SYS_AUTO_MONTH_PARAM__INIT;
    TPbNvrAutoRebootParam tPbNvrAutoRebootParam = TPB_NVR_AUTO_REBOOT_PARAM__INIT;

    tPbNvrAutoRebootParam.has_auto_reboot_type = TRUE;
    tPbNvrAutoRebootParam.auto_reboot_type = g_tLenAutoMaintainParam.eAutoRebootType;

    tPbNvrAutoRebootParam.day_param = &tPbNvrSysAutoDayParam;
    tPbNvrAutoRebootParam.day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.day_param->hour = g_tLenAutoMaintainParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.day_param->minute = g_tLenAutoMaintainParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.day_param->second = g_tLenAutoMaintainParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.week_param = &tPbNvrSysAutoWeekParam;
    tPbNvrAutoRebootParam.week_param->has_weekday = TRUE;
    tPbNvrAutoRebootParam.week_param->weekday = g_tLenAutoMaintainParam.tWeekParam.dwWeekDay;
    tPbNvrAutoRebootParam.week_param->day_param = &tPbNvrSysAutoWeekDayParam;
    tPbNvrAutoRebootParam.week_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->hour = g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.week_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->minute = g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.week_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.week_param->day_param->second = g_tLenAutoMaintainParam.tWeekParam.tDayParam.dwSec;

    tPbNvrAutoRebootParam.month_param = &tPbNvrSysAutoMonthParam;
    tPbNvrAutoRebootParam.month_param->day_param = &tPbNvrSysAutoMonthDayParam;
    tPbNvrAutoRebootParam.month_param->day_param->has_hour = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->hour = g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwHour;
    tPbNvrAutoRebootParam.month_param->day_param->has_minute = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->minute = g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwMin;
    tPbNvrAutoRebootParam.month_param->day_param->has_second = TRUE;
    tPbNvrAutoRebootParam.month_param->day_param->second = g_tLenAutoMaintainParam.tMonthParam.tDayParam.dwSec;
    tPbNvrAutoRebootParam.month_param->has_day = TRUE;
    tPbNvrAutoRebootParam.month_param->day = g_tLenAutoMaintainParam.tMonthParam.dwDay;


    ///获取结构体序列化后的二进制buffer大小
	dwBufLen = tpb_nvr_auto_reboot_param__get_packed_size(&tPbNvrAutoRebootParam);

    NVRSYSDEBUG("get pack size :%lu \n", dwBufLen);

	///为ptPbcSimple->data申请内存，外部释放
	ptPbcSimple->data = NVRALLOC(dwBufLen);
	if(NULL == ptPbcSimple->data)
	{
	   NVRSYSERR("malloc pack buffer failed.\n");
	   return NVR_ERR__ERROR;
	}

    ///序列化tPbNvrSysCfg到buffer中
    ptPbcSimple->len = tpb_nvr_auto_reboot_param__pack(&tPbNvrAutoRebootParam, ptPbcSimple->data);
    if(dwBufLen != ptPbcSimple->len)
    {
        NVRSYSERR("pack buffer failed, pack len:%lu \n", ptPbcSimple->len);
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

NVRSTATUS NvrSysLenAutoMainTainInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u32 dwSysCfgDataLen = 0;
    ENvrCfgRecoveryType eRecoveryType = NVR_CFG_RESET_NO;   ///<配置恢复类型
    u8  *pbySysCfgBuf = NULL;    				///<获取配置时保存配置数据的buf
	TPbNvrAutoRebootParam *ptPbNvrSysCfg = NULL;
    ProtobufCBufferSimple tPbcSimple = PROTOBUF_C_BUFFER_SIMPLE_INIT(NULL);    ///<序列化buf初始化
	ProtobufCAllocator	tPbAlocator;    		///<分配器
    TNvrPbAllocData tPbAllocData; 				///<分配器上下文

    mzero(tPbAllocData);
    ///内存分配器初始化
    tPbcSimple.allocator = &tPbAlocator;
    NvrSrvPbAllocatorInit(tPbcSimple.allocator, &tPbAllocData);

    do
	{
	    NvrSysGetRecoveryFlag(&eRecoveryType,NVR_BASE_PARAM_CFG_RESET);
		///初始化系统模块配置参数，获取失败则创建默认配置
		eRet = NVRCfgGetParamLen(NVR_CFG_SERVER, NVR_SYS_LEN_AUTO_MAINTAIN_CFG, &dwSysCfgDataLen);
        if(NVR_ERR__OK == eRet && NVR_CFG_RESET_NO == eRecoveryType)
		{
			NVRSYSDEBUG("get unpack size :%lu \n", dwSysCfgDataLen);

			pbySysCfgBuf = (u8 *)NVRALLOC(dwSysCfgDataLen);
			if(NULL == pbySysCfgBuf)
			{
				NVRSYSERR("malloc SysCfgBuf failed\n");
				eRet = NVR_ERR__MALLOC_FAILED;
				break;
			}

			eRet = NVRCfgGetParam(NVR_CFG_SERVER, NVR_SYS_LEN_AUTO_MAINTAIN_CFG, pbySysCfgBuf);
			if(NVR_ERR__OK == eRet)
			{
				ptPbNvrSysCfg = tpb_nvr_auto_reboot_param__unpack(tPbcSimple.allocator, dwSysCfgDataLen, pbySysCfgBuf); 			///<反序列化动作
			}
			else
			{
				NVRSYSERR("NVRCfgGetParam get %s failed\n", NVR_SYS_LEN_AUTO_MAINTAIN_CFG);
				break;
			}

			NvrSysLenAutoMaintainCfgProtoToStruct(ptPbNvrSysCfg);

	 		tpb_nvr_auto_reboot_param__free_unpacked(ptPbNvrSysCfg, tPbcSimple.allocator);	///<释放ptPbNvrSysCfg

		}
		else
		{

			NVRSYSDEBUG("create default cfg\n");

			///获取配置失败，创建默认配置  TODO 不应该用NvrSysCfgDefaultInit 需要另建一个配置默认值的函数
			//NvrSysCfgDefaultInit();

			///序列化动作
			eRet = NvrSysLenAutoMaintainCfgStructToProto(&tPbcSimple);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NvrSysLenAutoMaintainCfgStructToProto failed ret:%d\n", eRet);
				break;
			}

			///默认配置写入配置文件中
			eRet = NVRCfgSetParam(NVR_CFG_SERVER, NVR_SYS_LEN_AUTO_MAINTAIN_CFG, tPbcSimple.data, tPbcSimple.len);
			if(NVR_ERR__OK != eRet)
			{
				NVRSYSERR("NVRCfgSetParam failed ret:%d\n", eRet);
				break;
			}
		}
	}while(0);

	///释放tPbcSimple中malloc出来的空间
	if(NULL != tPbcSimple.data)
	{
		NVRFREE(tPbcSimple.data);
		tPbcSimple.data = NULL;
	}
	///判断被分配的内存是否全部被释放，没全部释放则记录日志
	if(0 != tPbAllocData.dwAllocCount)
	{
		NVRSYSERR("all allocated memory not be released,count=%lu.\n", tPbAllocData.dwAllocCount);
	}

	///释放pbySysCfgBuf空间
	if(pbySysCfgBuf != NULL)
	{
	   NVRFREE(pbySysCfgBuf);
	   pbySysCfgBuf = NULL;
	}

	if(NVR_ERR__OK == eRet)
	{
	   NVRSYSDEBUG("success\n");
	}
	else
	{
	   NVRSYSDEBUG("failed ret:%d\n", eRet);
	}

    return eRet;
}


void NvrSysLenAutoMaintainDeal()
{
    struct tm *ptCurTime = NULL;

    TNvrBrokenDownTime tUtcTime;
    time_t dwCurTime = 0;
    BOOL32 bRebootFlag = FALSE;
    TNvrAutoRebootParam tRebootParam;
    TNvrPuiPtzTaskParam tTask;
	TNvrPtzCtrlInfo tCtrlInfo;

    mzero(tRebootParam);
    mzero(tUtcTime);
    mzero(tTask);
    mzero(tCtrlInfo);


    NvrSysGetSystemLocalTime(&tUtcTime);
    NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT, (u32*)&dwCurTime, &tUtcTime);

    ptCurTime = gmtime((time_t *)&dwCurTime);

    NVRSYSTEMP("%d-%d-%d weekday:%d %d:%d:%d\n",
                ptCurTime->tm_year + 1900,
                ptCurTime->tm_mon+1,
                ptCurTime->tm_mday,
                ptCurTime->tm_wday,
                ptCurTime->tm_hour,
                ptCurTime->tm_min,
                ptCurTime->tm_sec);

	OsApi_SemTake(g_hSysCfgRWSem);
    memcpy(&tRebootParam, &g_tLenAutoMaintainParam, sizeof(TNvrAutoRebootParam));
	OsApi_SemGive(g_hSysCfgRWSem);

    ///系统周日为0表示
    if(7 == tRebootParam.tWeekParam.dwWeekDay)
    {
        tRebootParam.tWeekParam.dwWeekDay = 0;
    }

    switch(tRebootParam.eAutoRebootType)
    {
        case NVR_SYS_AUTO_REBOOT_DAY:
        {
            NVRSYSTEMP("tDayParam: %lu:%lu:%lu\n",
                            tRebootParam.tDayParam.dwHour,
                            tRebootParam.tDayParam.dwMin,
                            tRebootParam.tDayParam.dwSec);
            if(tRebootParam.tDayParam.dwHour ==ptCurTime->tm_hour&&
               tRebootParam.tDayParam.dwMin == ptCurTime->tm_min &&
               tRebootParam.tDayParam.dwSec == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_WEEK:
        {
            NVRSYSTEMP("tWeekParam:dwWeekDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tWeekParam.dwWeekDay,
                            tRebootParam.tWeekParam.tDayParam.dwHour,
                            tRebootParam.tWeekParam.tDayParam.dwMin,
                            tRebootParam.tWeekParam.tDayParam.dwSec);
            if(tRebootParam.tWeekParam.dwWeekDay == ptCurTime->tm_wday &&
               tRebootParam.tWeekParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tWeekParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tWeekParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        case NVR_SYS_AUTO_REBOOT_MONTH:
        {
            NVRSYSTEMP("tMonthParam:dwDay:%lu %lu:%lu:%lu\n",
                            tRebootParam.tMonthParam.dwDay,
                            tRebootParam.tMonthParam.tDayParam.dwHour,
                            tRebootParam.tMonthParam.tDayParam.dwMin,
                            tRebootParam.tMonthParam.tDayParam.dwSec);
            if(tRebootParam.tMonthParam.dwDay == ptCurTime->tm_mday &&
               tRebootParam.tMonthParam.tDayParam.dwHour == ptCurTime->tm_hour&&
               tRebootParam.tMonthParam.tDayParam.dwMin  == ptCurTime->tm_min&&
               tRebootParam.tMonthParam.tDayParam.dwSec  == ptCurTime->tm_sec)
               {
                    bRebootFlag = TRUE;
               }
        }
        break;

        default:
            break;
    }

    if(TRUE == bRebootFlag)
    {	
    	tTask.eSrcType = ENVR_PUI_PTZ_SRC_NVR;

    	tCtrlInfo.eCtrlType = NVR_PTZCTRL_CMD_LENS_RESET;

    	
		///<云台维护自动维护操作,TODO
		NvrPuiPtzCtrl(&tTask, 0, &tCtrlInfo);
    }
}


NVRSTATUS NvrSysGetIOUse(u32 *pdwCurIo)
{
	NVRSYSMEMAPI();
	s32 nRet = 0;
	char achCmd[NVR_MAX_STR256_LEN] = {""};
	char achBuf[NVR_MAX_STR256_LEN] = {""};
	char *p = NULL;
	char achTmp[4] = {""};
	u32 dwCurIo = 0;
	FILE *pFile = NULL;

	snprintf(achCmd, sizeof(achCmd), "top -b -d 0 -n 1 |grep \"%% io\" > %s", NVR_SYS_CUR_IO);

	///输出当前io
	nRet = NvrSystem(achCmd);
	if(0 != nRet)
	{
		NVRSYSIMP("exc :%s faild!\n", achCmd);
		return NVR_ERR__ERROR;
	}

	pFile = fopen(NVR_SYS_CUR_IO, "r");
	if(NULL == pFile)
	{
		NVRSYSIMP("fopen file :%s faild!!\n", NVR_SYS_CUR_IO);
		return NVR_ERR__ERROR;
	}

	///字符串解析
	if(NULL != fgets(achBuf, NVR_MAX_STR256_LEN, pFile))
	{
		p = strstr(achBuf,"idle");
		if(NULL == p)
		{
			NVRSYSIMP("buf:%s cannot find idle\n", achBuf);
			fclose(pFile);
			return NVR_ERR__ERROR;
		}
		achTmp[0] = p[4];
		achTmp[1] = p[5];
		achTmp[2] = p[6];

		///百位
		if(achTmp[0] != ' ')
		{
			dwCurIo += (achTmp[0] - '0') * 100;
		}
		///十位
		if(achTmp[1] != ' ')
		{
			dwCurIo += (achTmp[1] - '0') * 10;
		}
		///个位
		dwCurIo += achTmp[2] - '0';
	}
	else
	{
		NVRSYSIMP("fgets faild   no str!\n");
		fclose(pFile);
		return NVR_ERR__ERROR;
	}

	*pdwCurIo = dwCurIo;
	NVRSYSDEBUG("Cur IO: %ld\n", dwCurIo);

	fclose(pFile);
	return NVR_ERR__OK;

}

void NvrSysDelStrSymobol(char chDstSymobol, char* pchSrcStr)
{
	NVRSYSMEMAPI();
	s32 t,i=0;
	char *q=pchSrcStr;
	char ss[120];
	while(*q)
		ss[i++]=*q++;
	ss[i]='\0';
	for(i=0,t=0;ss[i];i++)
	if(ss[i]!= chDstSymobol)
		pchSrcStr[t++]=ss[i];
	pchSrcStr[t]='\0';
}

NVRSTATUS NvrSysShutDownRegistCB(PFNvrSysSystemDownCB pfCallBack)
{
	NVRSYSMEMAPI();
	static u8 byRegId = 0;

	if (byRegId >= NVR_SYS_SHUTDOWN_CB_TASK_MAX)
	{
		NVRSYSERR("error, regid:%d\n", byRegId);
		return NVR_ERR__PARAM_INVALID;
	}

	g_apfSysTimingTaskTegInfo[byRegId] = pfCallBack;

	NVRSYSDEBUG("success reg id:%d,   fun:%p\n", byRegId, pfCallBack);

	byRegId++;

    return NVR_ERR__OK;
}

void NvrSysSysShutdownNotify()
{
    s32 i = 0;
	
	///关闭数据配置
	NvrCfgCloseDb();

    for(i = 0; i < NVR_SYS_SHUTDOWN_CB_TASK_MAX; i++)
    {
        if(NULL != g_apfSysTimingTaskTegInfo[i])
    	{
			NVRSYSFILE("NvrSysSysShutdownNotify start call fun:%p\n", g_apfSysTimingTaskTegInfo[i]);
    	    g_apfSysTimingTaskTegInfo[i](NULL, 0);
			NVRSYSFILE("NvrSysSysShutdownNotify end call fun:%p\n", g_apfSysTimingTaskTegInfo[i]);			
        }
    }
}

void NvrSysRecoveryNotify(TNvrSysRecoverParam *ptRecoveryParam)
{
    s32 i = 0;

    for(i = 0; i < NVR_SYS_SHUTDOWN_CB_TASK_MAX; i++)
    {
        if(NULL != g_apfSysRecoveryTegInfo[i])
    	{
    	    g_apfSysRecoveryTegInfo[i](ptRecoveryParam);
        }
    }
}


NVRSTATUS NvrSysRecoveryRegistCB(PFNvrSysRecoveryCB pfCallBack)
{
	NVRSYSMEMAPI();
	static u8 byRegId = 0;

	if (byRegId >= NVR_SYS_SHUTDOWN_CB_TASK_MAX)
	{
		NVRSYSERR("error, regid:%d, interval:%lu!\n", byRegId);
		return NVR_ERR__PARAM_INVALID;
	}

	g_apfSysRecoveryTegInfo[byRegId] = pfCallBack;

	NVRSYSDEBUG("success reg id:%d, interval:%lu\n", byRegId);

	byRegId++;

    return NVR_ERR__OK;
}



/**
 * @brief       注册JNI消息回调
 * @param[in]   pfJavaMsgCb pfFuncCB  回调函数指针
 * @return      成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR
 * @ref         nvrdef.h
 * @note
 */
NVRSTATUS NvrSysSetJniMsgCb(pfJavaMsgCb pfFuncCB)
{
	NVRSYSMEMAPI();
	g_javaMsgCb = pfFuncCB;
	NVRSYSDEBUG("NvrSysSetJniMsgCb, func:%p\n",pfFuncCB);
	return NVR_ERR__OK;
}
/**
 * @brief       传递JNI消息
 * @param[in]   PTNvrJniMsgInfo pMsgInfo 消息体
 * @return      成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR
 * @ref         nvrjnistruct.h
 * @note
 */
NVRSTATUS NvrSysSndJniMsg(PTNvrJniMsgInfo pMsgInfo, const char* pchFuncName, u32 dwLine)
{
	if (NULL != g_javaMsgCb)
	{

		g_javaMsgCb(pMsgInfo->wMsgType,pMsgInfo->pData,pMsgInfo->nSize,NULL);
		NVRSYSDEBUG("Call function %s, at line %d\n", pchFuncName, dwLine);
		return NVR_ERR__OK;
	}
	else
	{
		NVRSYSDEBUG("NvrSysSndJniMsg, callback g_javaMsgCb null\n");
		return NVR_ERR__ERROR;
	}


}
/**
 * @brief       格式化SD卡
 * @param[in]   TNvrSysDiskInfo tDiskInfo sd卡信息
 * @return      成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR
 * @ref         nvrjnistruct.h
 * @note
 */
NVRSTATUS NvrSysFormatSD(TNvrSysDiskInfo tDiskInfo)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;

	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	tMsgData.nMsgID = MSG_SET_SYS_FORMAT;

    memcpy(tMsgData.chData, &tDiskInfo, sizeof(TNvrSysDiskInfo));
	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(NVR_JNI_ERR == tMsgData.errCD)
	{
		eRet = NVR_ERR__ERROR;
	}

	return eRet;
}

/**
 * @brief       获取SD卡列表信息
 * @param[in]   TNvrSysDiskList* ptDiskList sd卡列表信息
 * @return      成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR
 * @ref         nvrjnistruct.h
 * @note
 */
NVRSTATUS NvrSysGetDiskList(TNvrSysDiskList *pDiskParam)
{

	NVRSTATUS eRet = NVR_ERR__OK;
	s32 i = 0;
	s32 nRetryCount = 0;
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	tMsgData.nMsgID = MSG_GET_SYS_DISK_LIST;

//retry_get_disk:
	memcpy(tMsgData.chData, &pDiskParam, sizeof(TNvrSysDiskList));
	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	memcpy(pDiskParam, tMsgData.chData,   sizeof(TNvrSysDiskList));

	NVRSYSFILE("NvrSysGetDiskList get sdcard num:%d\n", pDiskParam->nDiskNum);
	
	if(NVR_JNI_ERR == tMsgData.errCD)
	{
		NVRSYSFILE("NvrSysGetDiskList NVR_JNI_ERR\n");
		eRet = NVR_ERR__ERROR;
	}
    ///<不再多次尝试获取，jni层会自行重试，业务侧不做重试--2021-03-19
    /*
	else
	{
		if(nRetryCount < 3)
		{
			for(i = 0; i < pDiskParam->nDiskNum; i++)
			{
				///拿到是空路径 说明系统没有挂载上,延长3s，尝试再次获取 最多尝试3次
				if(0 == strlen(pDiskParam->atNvrSysDiskInfo[i].achPaht))
				{
					nRetryCount++;
					OsApi_TaskDelay(1000*3);
					goto retry_get_disk;
				}
				else
					return eRet;
			}
		}
	}
    */
    
	return eRet;
}

/**
 * @brief       SD卡异常通知
 * @param[in]   ENvrSysSDCardException eType 异常类型
 * @param[in]   const s8 *pchExceptionInfo 异常附加信息，异常的sd卡信息（目录名or usb）
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysSDCardExceptionNotify(ENvrSysSDCardException eType, const s8 *pchExceptionInfo)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYSIMP("sd card exception! type:%d(1-ioerr,2-readonly)\n",eType);

	if(NVR_SYS_SD_EXCEPTION_NO_ERR != eType && pchExceptionInfo)
	{
		NVRSYSIMP("exceptioninfo:%s\n",pchExceptionInfo);
		///<记录日志，重启设备
		TNvrLogInfo tLogInfo;
		mzero(tLogInfo);

		tLogInfo.eLogType = NVR_LOG_REBOOT_DEV;
		snprintf(tLogInfo.achSourceId, sizeof(tLogInfo.achSourceId), "NVR");
		if(NVR_SYS_SD_EXECPTION_IO_ERR == eType)
		{
			snprintf(tLogInfo.achLogDatail, sizeof(tLogInfo.achLogDatail), "%s I/O err", pchExceptionInfo);
		}
		else if(NVR_SYS_SD_EXCEPTION_READ_ONLY == eType)
		{
			snprintf(tLogInfo.achLogDatail, sizeof(tLogInfo.achLogDatail), "%s read only", pchExceptionInfo);
		}

		eRet = NvrLogWrite(&tLogInfo);
		NVRSYSIMP("log write eRet:%d\n", eRet);
		NvrSysReboot(NULL);
	}
	return eRet;
}

/**
 *@brief  注册SD卡格式化回调通知
 *@baram  pfNvrSysFormatSdResultCB pfCallBack
 *@ref	  nvrdef.h
 *@see
 *@note
 */
NVRSTATUS NvrSysFormatSdRegisterCB(TNvrSysFormatSdInfo* pfCallBack)
{
	NVRSYSMEMAPI();
	if(pfCallBack == NULL)
	{
		NVRSYSERR("NvrSysFormatSdRegisterCB, callback  null\n");
		return NVR_ERR__ERROR;
	}
	g_pfFormatSdResult = pfCallBack->pfFormatSdResultCB;
	g_dwContext = *pfCallBack->pContext;
	return NVR_ERR__OK;
}
/**
 * @brief       SD卡格式化结果回调
 * @param[in]   成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR

 * @return      成功        NVR_ERR__OK
 				失败        NVR_ERR_ERROR
 * @ref         nvrjnistruct.h
 * @note
 */
NVRSTATUS NvrSysSndFormatResultMsg(NVRSTATUS eResult)
{
	NVRSYSMEMAPI();
	if(NULL != g_pfFormatSdResult)
	{
		g_pfFormatSdResult(eResult, &g_dwContext);
	}
	else
	{
		NVRSYSERR("NvrSysSndFormatResultMsg, callback  null\n");
		return NVR_ERR__ERROR;
	}
	return NVR_ERR__OK;

}
/**
 * @brief       设置升级服务器地址和端口
 * @param[in]   ptServerInfo
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysSetUpgradeServerInfo(TNvrSysUpgradeServerParam *ptServerInfo)
{
	NVRSYSMEMAPI();
	memcpy(&g_tNvrSysCfg.tUpgradeServerParam, ptServerInfo, sizeof(TNvrSysUpgradeServerParam));

	if(TRUE == ptServerInfo->bAutoUpgrade)
	{
		TNvrJniMsgData tMsgData;
		TNvrJniMsgInfo tCallback;
		mzero(tMsgData);
		mzero(tCallback);

		tMsgData.nMsgID = MSG_SET_SYS_UPGRADE;

		memcpy(tMsgData.chData, ptServerInfo, sizeof(TNvrSysUpgradeServerParam));

		tCallback.wMsgType = JNIMSG_TYPE_SYS;
		tCallback.pData=&tMsgData;

		NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);
	}

	NvrSysCfgSave();

	return NVR_ERR__OK;
}
/**
 * @brief       获取升级服务器地址和端口
 * @param[out]   ptServerInfo
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysGetUpgradeServerInfo(TNvrSysUpgradeServerParam *ptServerInfo)
{
	NVRSYSMEMAPI();
	memcpy(ptServerInfo, &g_tNvrSysCfg.tUpgradeServerParam, sizeof(TNvrSysUpgradeServerParam));
	NVRSYSDEBUG("upgrade server autoUpgrade=%d,ip=%s,port=%d\n", ptServerInfo->bAutoUpgrade, ptServerInfo->achServerIP,
		ptServerInfo->wServerPort);
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysUpgradeByIpdtDataCallback(NvrSysUpgradeByIpdtDataCB pfInteractiveData)
{
	NVRSYSMEMAPI();
	g_pfUpgradeByIpdtDCallback = pfInteractiveData;

	NVRSYSDEBUG("NvrSysUpgradeByIpdtDataCallback reg success\n");

	return NVR_ERR__OK;
}

void NvrSysUpgradeByIpdtDataNotify(ENvrSysUpgradeByIpdtDataType eDataType, void *pInParam)
{
	NVRSYSMEMAPI();
	if (g_pfUpgradeByIpdtDCallback)
	{
		g_pfUpgradeByIpdtDCallback(eDataType, pInParam);
	}
	NVRSYSINFO("NvrSysUpgradeByIpdtDataNotify success eDataType:%d\n",eDataType);

	return;
}

/**
 * @brief       设置以太网IP
 * @param[in]   ethParam 以太网参数
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysSetEtherIP(u32 dwEthId,TNvrSysEthParam ethParam)
{
    NVRSTATUS eRet = NVR_ERR__OK;    ///<返回值
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);
	
	u32 dwWifiIdLen = sizeof(u32);
	u32 dwApCfgLen = sizeof(TNvrSysEthParam);
	tMsgData.nLen = dwWifiIdLen + dwApCfgLen;

//	memcpy(tMsgData.chData, &ethParam, sizeof(ethParam));
	memcpy(tMsgData.chData + 0, &dwEthId, sizeof(dwWifiIdLen));
	memcpy(tMsgData.chData + dwWifiIdLen,&ethParam , dwApCfgLen);
	tMsgData.nMsgID = MSG_SET_SYS_ETHER_IP;

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	eRet = NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);
	DebugLogPrint(DEBUG_LOG_MOD_NETWORK, LOG_LEVEL_ERR, "NvrSysSetEtherIP eRet:%d!\n",eRet);
	return NVR_ERR__OK;
}
/**
 * @brief       获取以太网IP，实际读配置文件
 * @param[in]   ethParam 以太网参数
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysGetEtherIP(TNvrSysEthParam *pthParam)
{
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);
	
	tMsgData.nMsgID = MSG_GET_SYS_ETHER_IP;

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	memcpy(pthParam, tMsgData.chData, sizeof(TNvrSysEthParam));
	NVRSYSDEBUG("pthParam->nIpAdrs:0x%x,pthParam->nMask:0x%x,pthParam->nGate:0x%x,pthParam->nDns1:0x%x,pthParam->nDns2:0x%x\n",pthParam->nIpAdrs,pthParam->nMask,pthParam->nGate,pthParam->nDns1,pthParam->nDns2);

	return NVR_ERR__OK;
}
/**
 * @brief       获取网卡连接状态
 * @param[in]   eLinkState 以太网连接状态
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysGetEthLinkStatus(ENvrNetLinkState *peLinkState)
{
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);
	
	tMsgData.nMsgID = MSG_GET_SYS_EHERNET_STATE;

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);
	
	memcpy(peLinkState, tMsgData.chData, sizeof(ENvrNetLinkState));
	DebugLogPrint(DEBUG_LOG_MOD_NETWORK, LOG_LEVEL_ERR,"NvrSysGetEthLinkStatus     eth link status:%d\n",*peLinkState);
	return NVR_ERR__OK;
}
///<sd卡升级版本号校验
NVRSTATUS NvrSysOtaUpgradeCheckVer(const u8* pchPkgPath)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	#ifdef _QCOM_
	u8 achRomVer[NVR_MAX_STR256_LEN] = {""};
	char achTempPkgPath[NVR_MAX_STR256_LEN] = {""};

	memcpy(achTempPkgPath, pchPkgPath, sizeof(achTempPkgPath));
	///升级包名称校验
	if(NULL == (strstr(achTempPkgPath, "KDM2412M")))
	{
		NVRSYSINFO("ota package name not contain \"KDM2412M\", it's not valid\n");
		return NVR_ERR__ERROR;
	}
	///升级包的版本号小于rom的版本号则不需要升级
	NvrSysGetRomVer(achRomVer);
	char *pchZip = strrchr(achTempPkgPath, '/');

	char ** tokens = NvrSysStrSplit(pchZip, '-');
	if(tokens)
	{
		char *otaVer = *(tokens + 4);

		NVRSYSINFO("otaVer=%s, romVer=%s\n", otaVer, achRomVer);
		if(strncmp(otaVer, (char *)achRomVer, strlen((char *)achRomVer)) <= 0)
		{
			free(otaVer);
			free(tokens);
			NVRSYSINFO("otaVer is lower than rom version, do not upgrade\n");
			return NVR_ERR__ERROR;
		}
		else
		{
			free(otaVer);
			free(tokens);
		}

	}
	#endif
	return eRet;

}
#if 1
char** NvrSysStrSplit(char* a_str, const char a_delim)
{
    char** result    = 0;
    size_t count     = 0;
    char* tmp        = a_str;
    char* last_comma = 0;
    char delim[2];
    delim[0] = a_delim;
    delim[1] = 0;

    /* Count how many elements will be extracted. */
    while (*tmp)
    {
        if (a_delim == *tmp)
        {
            count++;
            last_comma = tmp;
        }
        tmp++;
    }

    /* Add space for trailing token. */
    count += last_comma < (a_str + strlen(a_str) - 1);

    /* Add space for terminating null string so caller
       knows where the list of returned strings ends. */
    count++;

    result = (char **)malloc(sizeof(char*) * count);

    if (result)
    {
        size_t idx  = 0;
        char* token = strtok(a_str, delim);

        while (token)
        {
            assert(idx < count);
            *(result + idx++) = strdup(token);
            token = strtok(0, delim);
        }
        assert(idx == count - 1);
        *(result + idx) = 0;
    }

    return result;
}
#endif


/**
 *@brief        OTA升级接口
 *@param[in]    pchPkgPath 升级包的绝对路径
 		[in]    eType      升级类型
		[in]	eStatus    传输状态
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 */
NVRSTATUS NvrSysOtaUpgrade(const u8* pchPkgPath, ENvrSysOtaUpgradeType eType, ENvrSysOtaPkgTransStatType eStatus)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	char achCommand[NVR_MAX_STR256_LEN] = {""};
	char achDestPath[NVR_MAX_STR256_LEN] = {""};

	NVRSYS_ASSERT(pchPkgPath);

	snprintf(achDestPath, sizeof(achDestPath), "%s", "/data/update.zip");

	switch (eStatus)
	{
		case NVR_SYS_OTA_TRANS_START:
			NVRSYSINFO("ota package transport start, package path is %s\n", pchPkgPath);
			break;
		case NVR_SYS_OTA_TRANS_SUCCESS:
			NVRSYSINFO("ota package transport success, package path is %s\n", pchPkgPath);


			///如果是CGI升级，则需要删除以前的升级包
			if(NVR_SYS_UPGRADE_TYPE_CGI == eType || NVR_SYS_UPGRADE_TYPE_FTP == eType)
			{
				snprintf(achCommand, sizeof(achCommand), "mv %s %s", pchPkgPath, achDestPath);
				NVRSYSERR("cgi or ftp exec command %s\n", achCommand);
				NvrSystem(achCommand);
				snprintf(achCommand, sizeof(achCommand), "chmod 777 %s", achDestPath);
				NVRSYSERR("cgi or ftp exec command %s\n", achCommand);
				NvrSystem(achCommand);
			}
			///如果是SD卡升级，则保留SD卡里的升级包
			else if(NVR_SYS_UPGRADE_TYPE_SD == eType)
			{
				snprintf(achCommand, sizeof(achCommand), "cp %s %s", pchPkgPath, achDestPath);
				NVRSYSINFO("exec command %s\n", achCommand);
				NvrSystem(achCommand);
			}
			///开始调用Android framework升级接口
			NVRSYSINFO("ota upgrade start!!!\n");

			TNvrJniMsgData tMsgData;
			TNvrJniMsgInfo tCallback;
			mzero(tMsgData);
			mzero(tCallback);

			tMsgData.nMsgID = MSG_SET_SYS_LOCAL_UPGRADE;

			memcpy(tMsgData.chData, achDestPath, strlen((const char *)achDestPath));

			tCallback.wMsgType = JNIMSG_TYPE_SYS;
			tCallback.pData=&tMsgData;

			NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);
			break;
		case NVR_SYS_OTA_TRANS_FAILED:
			NVRSYSINFO("ota package transport failed, package path is %s", pchPkgPath);
			break;
		default:
			break;
	}

	return eRet;
}

/**
 *@brief        获取apk version code
 *@param[out]   pdwApkVerCode  apk version code
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 */
NVRSTATUS NvrSysGetApkVerCode(s8 *pchApkVer)
{
	NVRSYSMEMAPI();
	//char achDevApkVer[NVR_MAX_STR64_LEN] = {0};
	time_t dwApkCode = 0;
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	TNvrCapHwCapInfo tHwCapInfoDsj;

	mzero(tHwCapInfoDsj);
	mzero(tMsgData);
	mzero(tCallback);

	tMsgData.nMsgID = MSG_GET_SYS_APK_VER_CODE;

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(tMsgData.errCD != -1)
	{
		NvrCapGetCapParam(NVR_CAP_ID_HW, &tHwCapInfoDsj);
    	if (NVR_DEV_TYPE_DSJ == tHwCapInfoDsj.eDevType) 
		{
			snprintf(pchApkVer, NVR_MAX_STR64_LEN, "%s", tMsgData.chData);
		}
		else
		{
			memcpy(&dwApkCode, tMsgData.chData, sizeof(u32));
			snprintf(pchApkVer,NVR_MAX_STR64_LEN,"%s",asctime(gmtime((time_t*)&dwApkCode)));
		}
		return NVR_ERR__OK;
	}

	return NVR_ERR__ERROR;

}

/**
 *@brief        获取rom version
 *@param[out]   pchRomVer  rom version
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 */
NVRSTATUS NvrSysGetRomVer(u8 *pchRomVer)
{
	NVRSYSMEMAPI();
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	tMsgData.nMsgID = MSG_GET_SYS_ROM_VER;

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(tMsgData.errCD != -1)
	{
		memcpy(pchRomVer, tMsgData.chData, strlen((const char *)tMsgData.chData));

		return NVR_ERR__OK;

	}

	return NVR_ERR__ERROR;

}

NVRSTATUS NvrSysMobileGetVerInfo(PTNvrSysMobileVerInfo ptVerInfo)
{
#ifdef _DSJ_
	TNvrJniMsgInfo tMobileCallback;
	T_DSJ_VER_INFO tVerInfo;
    T_DSJ_COM_PARAMEX tzfyParamEx;
	
	mzero(tMobileCallback);
	mzero(tVerInfo);
	mzero(tzfyParamEx);
	
	if(!ptVerInfo)
    {
        return NVR_ERR__PARAM_INVALID;
    }
    
    tzfyParamEx.nMsgID = EV_JNIMSG_DSJ_TYPE_UPDATE_VER;
    tzfyParamEx.nLen = sizeof(tVerInfo);
    tMobileCallback.wMsgType = EV_NOTIFY_JAVA_MSG;
    tMobileCallback.nSize = sizeof(tzfyParamEx);
    tMobileCallback.pData = (void*)&tzfyParamEx;
    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);

    memcpy(&tVerInfo, tzfyParamEx.chData, sizeof(tVerInfo));//lint !e419

    memcpy(ptVerInfo->abyDevType, tVerInfo.abyDevType, sizeof(ptVerInfo->abyDevType));
    memcpy(ptVerInfo->abyAPKVer, tVerInfo.abyAPKVer, sizeof(ptVerInfo->abyAPKVer));
    memcpy(ptVerInfo->abyROMVer, tVerInfo.abyROMVer, sizeof(ptVerInfo->abyROMVer));

    T_DSJ_COM_PARAM tzfyParam;
	mzero(tzfyParam);
    mzero(tMobileCallback);
    tzfyParam.nMsgID = MSG_GET_SYS_HARDWARE_VER;  //获取硬件版本号
    tMobileCallback.wMsgType = EV_NOTIFY_JAVA_MSG;
    tMobileCallback.nSize = sizeof(tzfyParam);
    tMobileCallback.pData = (void *)&tzfyParam;
    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
    snprintf(ptVerInfo->abyDevHWVer, sizeof(ptVerInfo->abyDevHWVer), "%s",tzfyParam.chData);

	NVRSYSDEBUG("abyDevType[%s] abyAPKVer[%s] abyROMVer[%s] DevHWVer[%s]\n", ptVerInfo->abyDevType, ptVerInfo->abyAPKVer, ptVerInfo->abyROMVer, ptVerInfo->abyDevHWVer);
#endif
	return NVR_ERR__OK;
}

BOOL32 NvrSysMobileCheckUpgradeVer(PTNvrSysMobileUpgradeInfo ptUpgradeInfo)
{
	BOOL32 bCheckPass = FALSE;
#ifdef _DSJ_
	TNvrSysMobileVerInfo tVerInfo = {0};
	NvrSysMobileGetVerInfo(&tVerInfo);
	
	NVRSYSDEBUG("abyROMVer[%s] abySourceVer[%s] abyDevType[%s]\n", tVerInfo.abyROMVer, ptUpgradeInfo->abySourceVer, ptUpgradeInfo->abyDevType);
	if (strcmp(tVerInfo.abyROMVer, ptUpgradeInfo->abySourceVer) == 0 
		|| (strcmp(tVerInfo.abyDevType, ptUpgradeInfo->abyDevType) == 0 
			&& strcmp(ptUpgradeInfo->abySourceVer,"0") == 0))
	{	//ROM版本一致
		bCheckPass = TRUE;
	}
#endif
	return bCheckPass;
}

NVRSTATUS NvrSysMobileUpgradeResultRsp(ENvrUpgradeErrCode eErrorCode)
{
#ifdef _DSJ_
	TPigeonAppUpgradeInfo tInfo;
	EAppResult eAppBaseRet = APP_OK;
	EAppResult eShareActionRet = APP_OK;

	mzero(tInfo);
	
	if (eErrorCode == NVR_UPGRADE_ERRCODE_SUCCESS)
	{
		tInfo.bNeedDownload = TRUE;
	}
	else
	{
		tInfo.bNeedDownload = FALSE;
	}
	strcpy(tInfo.achPath, "/storage/extdisk/upgrade/ota_upg_pack.zip");
	NVRSYSDEBUG("bNeedDownload[%d] achPath[%s]\n", tInfo.bNeedDownload, tInfo.achPath);
	//通知升级情况
	eAppBaseRet = AppRunShareAction(PIGEONAPP_NOTIFY_UPGRADE_ACTION, (void *)&tInfo, NULL, NULL, &eShareActionRet);
	if (APP_OK != eAppBaseRet)
	{
		NVRSYSERR("AppRunShareAction %s err ret:%d\n", PIGEONAPP_NOTIFY_UPGRADE_ACTION, eAppBaseRet);
	}
	if (APP_OK != eShareActionRet)
	{
		NVRSYSERR("grc App:notify method fail ret:%d\n", eShareActionRet);
	}
#endif	
	return NVR_ERR__OK;
}
/**接收国标下发的数据*/
NVRSTATUS NvrSysGbTransdataRecv(TNvrSysGBRcvTransData tTransData)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	////<把接收到的数据回调给给注册过的平台
	for(i = 0; i < NVR_SYS_GB_TRANSDATA_MAX_NUM; i++)
	{
		if(NULL != g_apfNvrSendDataCB[i])
		{
			NVRSYSDEBUG("send data!\n");
			g_apfNvrSendDataCB[i](tTransData,NULL);
		}
		else
		{
			NVRSYSDEBUG("no plat register Data cb!\n");
		}
			
	}

	return eRet;
}
NVRSTATUS NvrSysPlatSendMsg(TNvrSysGBSendTransData tTransData)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	////<把接收到的信息回复给GB
	for(i = 0; i < NVR_SYS_GB_TRANSDATA_MAX_NUM; i++)
	{
		if(NULL != g_apfSysGbTransDataCB[i])
		{
			NVRSYSDEBUG("send msg!\n");
			g_apfSysGbTransDataCB[i](tTransData,NULL);
		}
		else
		{
			NVRSYSDEBUG("no plat register msg cb!\n");
		}
			
	}

	return eRet;
}

NVRSTATUS NvrSysGbSendTransDataMsg(TNvrSysGBSendTransData tTransData)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	u32 i = 0;
	ENvrTransDataType eTransDataType = ENVR_TRANSDATA_TYPE_MAX;
	s8 achTransDatabuf[64] = {'0'};

	if (g_tAppSubscribeHeadNode == NULL)
	{
		NVRSYSERR("NvrSysPlatSendMsg g_tSubscribeHeadNode is null\n");
        return NVR_ERR__ASSERT;
	}

	NVRSYSDEBUG("NvrSysPlatSendMsg come in!!\n"); 

	NvrPuiGbTransdataRecv(tTransData.ptDataBuf,achTransDatabuf);
	eRet = NvrPuiConvTransDataPairStr2Enum(achTransDatabuf,(s32 *)&eTransDataType);
	NVRSYSDEBUG("NvrPuiGbTransdataRecv = %s ,NvrPuiConvTransDataPairStr2Enum = %d\n",achTransDatabuf,eTransDataType);

	if(NVR_ERR__OK == eRet)
	{
		NVRSYSDEBUG("NvrPuiConvTransDataPairStr2Enum success!\n");
	
		////<把接收到的信息回复给GB
		for(i = 0; i < NVR_SYS_GB_TRANSDATA_MAX_NUM; i++)
		{
			if(NULL != g_apfSysGbTransDataCB[i])
			{
				if(tTransData.dwSendType == NVR_SYS_SEND_GB_TRANSDATA_NTY)   ///<主动上报
				{	
					TNvrAppSubscribeNode *p = g_tAppSubscribeHeadNode->pNext;
					while(NULL != p) 
					{
						NVRSYSDEBUG("send msg test! type = %d,handle = %p\n",p->eTransDataType,p->pHandle);
						if(p->eTransDataType == eTransDataType )
						{
							NVRSYSDEBUG("send msg!\n");
							g_apfSysGbTransDataCB[i](tTransData,p->pHandle);
						}
						p = p->pNext; 
					}
					NVRSYSDEBUG("NTY send msg finish!\n");
				}
				else if(tTransData.dwSendType == NVR_SYS_SEND_GB_TRANSDATA_RPS) ///<信令回复
				{
					g_apfSysGbTransDataCB[i](tTransData,NULL);
					NVRSYSDEBUG("RPS send msg finish!\n");
				}
			}
			else
			{
				NVRSYSDEBUG("no plat register msg cb!\n");
			}
		} 
	}
    
	return eRet;
}

NVRSTATUS NvrSysGbSetSubcribeParam(void* pHandle, TGBTransDataSubscribeParam tSubscribeParam)
{
	NVRSYSDEBUG("NvrSysGbSetSubcribeParam come in!\n");
	NVRSTATUS eRet = NVR_ERR__ERROR;

	ENvrTransDataType eDataType  = ENVR_TRANSDATA_TYPE_MAX;
	s8 achTransDatabuf[64] = {'0'};

	NvrPuiGbTransdataRecv(tSubscribeParam.pDataBuf,achTransDatabuf); 
	eRet = NvrPuiConvTransDataPairStr2Enum(achTransDatabuf,(s32 *)&eDataType);
	NVRSYSDEBUG("achMsgType = %s,NvrPuiGbTransdataRecv = %s,eDataType = %d\n",tSubscribeParam.achMsgType,achTransDatabuf,eDataType);
	if(NVR_ERR__OK == eRet)
	{
		NvrSysAppSubcribeNodeInsertBack(pHandle,eDataType);
	}

	return eRet;
}

NVRSTATUS NvrSetSendDataCb(PFNvrSendData pfCallback, void* pContext)
{
	int i = 0;	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL == pfCallback)
	{
		return NVR_ERR__OK;
	}
	for(i = 0; i < NVR_SYS_GB_TRANSDATA_MAX_NUM; i++)
	{
		if(NULL == g_apfNvrSendDataCB[i])
		{
			g_apfNvrSendDataCB[i] = pfCallback;
			break;
		}
	}
	if(NVR_SYS_GB_TRANSDATA_MAX_NUM == i)
	{
		NVRSYSERR("Plta trans data callback array full\n");
		eRet =  NVR_ERR__ERROR;
	}
	else
	{
		NVRSYSERR("Plat trans data callback register success\n");
	}
	return eRet;
}

NVRSTATUS NvrSysSetGbTransdataSendCb(PFGbTransDataSend pfCallback, void* pContext)
{
	int i = 0;	
	NVRSTATUS eRet = NVR_ERR__OK;
	if(NULL == pfCallback)
	{
		return NVR_ERR__OK;
	}
	for(i = 0; i < NVR_SYS_GB_TRANSDATA_MAX_NUM; i++)
	{
		if(NULL == g_apfSysGbTransDataCB[i])
		{
			g_apfSysGbTransDataCB[i] = pfCallback;
			break;
		}
	}
	if(NVR_SYS_GB_TRANSDATA_MAX_NUM == i)
	{
		NVRSYSERR("Gb trans data callback array full\n");
		eRet =  NVR_ERR__ERROR;
	}
	else
	{
		NVRSYSERR("Gb trans data callback register success\n");
	}
	return eRet;
}

NVRSTATUS NvrSysMobileNtfUpgradeInfo(ENvrUpgradeType eType,void * ptInfo)
{
#ifdef _DSJ_
	TNvrJniMsgInfo tMobileCallback;
	TNvrSysMobileUpgradeNtf tNvrUpgradeNtf;

	mzero(tNvrUpgradeNtf);
	mzero(tMobileCallback);

	NVRSYSDEBUG("eType[%d]\n", eType);
	if (eType == NVR_UPGRADE_PUSH) 
	{
		PTNvrSysMobileUpgradeInfo ptUpgradeInfo = (PTNvrSysMobileUpgradeInfo)ptInfo;
		BOOL32 bCHeckPass = NvrSysMobileCheckUpgradeVer(ptUpgradeInfo);	//是否检验通过
		if (bCHeckPass == TRUE)
		{	//检验通过
			if (ptUpgradeInfo->bForceUpgrade == TRUE)
			{	//强制升级
				NvrSysMobileUpgradeResultRsp(NVR_UPGRADE_ERRCODE_SUCCESS);
			}
			else
			{	//非强制升级 通知UI->检验通过 是否升级
            	tMobileCallback.wMsgType = EV_PIGEON_UPGRADE_NTY;
                    
            	tNvrUpgradeNtf.eType = NVR_UPGRADE_PUSH;
            
            	tMobileCallback.nSize = sizeof(tNvrUpgradeNtf);
            	tMobileCallback.pData = (void*)&tNvrUpgradeNtf;
            	NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
			}
		}
		else
		{	//检验失败 回应协议->回应平台 不能下载升级
			NvrSysMobileUpgradeResultRsp(NVR_UPGRADE_ERRCODE_CHECK_FAILED);
			return NVR_ERR__PARAM_INVALID;
		}
	}
	else if (eType == NVR_UPGRADE_DOWNLOAD_START) 
	{	//通知UI->开始下载
		tMobileCallback.wMsgType = EV_PIGEON_UPGRADE_NTY;
                        
	    tNvrUpgradeNtf.eType = NVR_UPGRADE_DOWNLOAD_START;
	    
	    tMobileCallback.nSize = sizeof(tNvrUpgradeNtf);
	    tMobileCallback.pData = (void*)&tNvrUpgradeNtf;
	    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
	}
	else if (eType == NVR_UPGRADE_DOWNLOAD_PROGRESS) 
	{	//通知UI->下载进度
		PTNvrSysMobileDownloadProgress ptProgressInfo = (PTNvrSysMobileDownloadProgress)ptInfo;
		tMobileCallback.wMsgType = EV_PIGEON_UPGRADE_NTY;
                        
	    tNvrUpgradeNtf.eType = NVR_UPGRADE_DOWNLOAD_PROGRESS;
	    tNvrUpgradeNtf.nProgress = ptProgressInfo->nProgress;
	    tNvrUpgradeNtf.nFileSize = ptProgressInfo->nFileSize;
	    
	    tMobileCallback.nSize = sizeof(tNvrUpgradeNtf);
	    tMobileCallback.pData = (void*)&tNvrUpgradeNtf;
	    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
	}
	else if (eType == NVR_UPGRADE_DOWNLOAD_FINISH) 
	{	//通知UI->下载完成
		tMobileCallback.wMsgType = EV_PIGEON_UPGRADE_NTY;
                        
	    tNvrUpgradeNtf.eType = NVR_UPGRADE_DOWNLOAD_FINISH;
	    
	    tMobileCallback.nSize = sizeof(tNvrUpgradeNtf);
	    tMobileCallback.pData = (void*)&tNvrUpgradeNtf;
	    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
	}
	else if (eType == NVR_UPGRADE_DOWNLOAD_FAIL) 
	{	//通知UI->下载失败
		tMobileCallback.wMsgType = EV_PIGEON_UPGRADE_NTY;
                        
	    tNvrUpgradeNtf.eType = NVR_UPGRADE_DOWNLOAD_FAIL;
	    
	    tMobileCallback.nSize = sizeof(tNvrUpgradeNtf);
	    tMobileCallback.pData = (void*)&tNvrUpgradeNtf;
	    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);
	}
#endif
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysReadSuperFile(TNvrSuperFile *ptSuperFile)
{
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	NVRSYSIMP("super file info path:%s,content:%s\n", ptSuperFile->szFilePath,ptSuperFile->szContent);

	tMsgData.nMsgID = MSG_READ_SUPER_FILE;
	memcpy(tMsgData.chData, ptSuperFile, sizeof(TNvrSuperFile));

	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(tMsgData.errCD != -1)
	{
		memcpy(ptSuperFile, tMsgData.chData, sizeof(TNvrSuperFile));
		NVRSYSIMP("read super file info path:%s,content:%s\n", ptSuperFile->szFilePath,ptSuperFile->szContent);
		return NVR_ERR__OK;
	}

	return NVR_ERR__ERROR;
}
NVRSTATUS NvrSysWriteSuperFile(TNvrSuperFile *ptSuperFile)
{
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	NVRSYSIMP("super file info path:%s,content:%s\n", ptSuperFile->szFilePath,ptSuperFile->szContent);
	
	tMsgData.nMsgID = MSG_WRITE_SUPER_FILE;
	memcpy(tMsgData.chData, ptSuperFile, sizeof(TNvrSuperFile));
	
	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(tMsgData.errCD != -1)
	{
		NVRSYSIMP("write super file info path:%s,content:%s\n", ptSuperFile->szFilePath,ptSuperFile->szContent);
		return NVR_ERR__OK;
	}

	return NVR_ERR__ERROR;
}

/**
 *@brief        开关电源灯
 *@param[in]    stat   0 关  1 开
 *@return       NVR_OK:成功 错误码:失败，详见
 *@ref          nvrdef.h
 */
NVRSTATUS NvrSysSetPowerLedStat(u32 stat)
{
	NVRSYSMEMAPI();
	TNvrJniMsgData tMsgData;
	TNvrJniMsgInfo tCallback;
	mzero(tMsgData);
	mzero(tCallback);

	tMsgData.nMsgID = MSG_SET_SYS_POWER_LED_STAT;

	memcpy(tMsgData.chData, &stat, sizeof(u32));
	tCallback.wMsgType = JNIMSG_TYPE_SYS;
	tCallback.pData=&tMsgData;

	NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

	if(tMsgData.errCD != -1)
	{

		return NVR_ERR__OK;

	}

	return NVR_ERR__ERROR;
}

#if 0
static char** str_split(char* a_str, const char a_delim)
{
    char** result    = 0;
    size_t count     = 0;
    char* tmp        = a_str;
    char* last_comma = 0;
    char delim[2];
    delim[0] = a_delim;
    delim[1] = 0;

    /* Count how many elements will be extracted. */
    while (*tmp)
    {
        if (a_delim == *tmp)
        {
            count++;
            last_comma = tmp;
        }
        tmp++;
    }

    /* Add space for trailing token. */
    count += last_comma < (a_str + strlen(a_str) - 1);

    /* Add space for terminating null string so caller
       knows where the list of returned strings ends. */
    count++;

    result = (char **)malloc(sizeof(char*) * count);

    if (result)
    {
        size_t idx  = 0;
        char* token = strtok(a_str, delim);

        while (token)
        {
            assert(idx < count);
            *(result + idx++) = strdup(token);
            token = strtok(0, delim);
        }
        assert(idx == count - 1);
        *(result + idx) = 0;
    }

    return result;
}
#endif

//目前用于给视图库调用
NVRSTATUS NvrSysRFIDDataCallback(NvrSysRFIDDataCB pfTransData)
{
	NVRSYSMEMAPI();
    u8 i = 0;

    for(i = 0; i < NVR_APP_PROTO_MAX; i++)
    {
        if (NULL == g_apfRFIDCallback[i])
        {
            g_apfRFIDCallback[i] = pfTransData;
            break;
        }
    }

    if (NVR_APP_PROTO_MAX == i)
    {
        NVRSYSERR("callback list is full\n");
        return NVR_ERR__ERROR;
    }
    NVRSYSDEBUG("callback reg success\n");

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysSetRFIDData(ENvrAppProtoType eTye,u32 dwItemNum, TNvrRFIDData *ptDataBuf)
{
	NVRSYSMEMAPI();
    u8 i = 0;

	for(i = 0; i < NVR_APP_PROTO_MAX; i++)
	{
		if (NULL != g_apfRFIDCallback[i])
		{
			g_apfRFIDCallback[i](eTye,dwItemNum, ptDataBuf);	
			NVRSYSDEBUG("callback report success,i %u\n",i);
		}
	}
	
    NVRSYSDEBUG("callback report success,eTye %u,dwItemNum %lu,ptDataBuf %p\n",eTye,dwItemNum,ptDataBuf);

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysGetRadarInfo(NvrRadar2DTargetInfo *ptInfo)
{
	NVRSYSMEMAPI();
    NVRSYSDEBUG("get target info, s_byReadIndex = %d \n", s_byReadIndex);
    memcpy(ptInfo, &s_atTargetInfo[s_byReadIndex], sizeof(NvrRadar2DTargetInfo));

    return NVR_ERR__OK;
}

NVRSTATUS NvrSysSetRadarInfo(NvrRadar2DTargetInfo *ptInfo)
{
	NVRSYSMEMAPI();
    static u8 s_byWriteIndex = 0;

    memcpy(&s_atTargetInfo[s_byWriteIndex], ptInfo, sizeof(NvrRadar2DTargetInfo));
    s_byReadIndex = s_byWriteIndex;
    NVRSYSDEBUG("save target info, s_byReadIndex = %d, s_byWriteIndex = %d \n", s_byReadIndex, s_byWriteIndex);

    s_byWriteIndex = (s_byWriteIndex + 1) % 5;

    return NVR_ERR__OK;
}
/**
 *@brief  获取ktsm模块是否激活
 *@param[out] char *pchPinCode 模块PIN码，长度64
 *@param[out] BOOL32 *pbActive 是否激活
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note 未激活才能激活，激活过了不可再次激活，激活按钮置灰
 */
NVRSTATUS NvrSysGetKtsmActiveStatus(char *pchPinCode, BOOL32 *pbActive)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    
    NVRSYS_ASSERT(pchPinCode);
    NVRSYS_ASSERT(pbActive);

    memcpy(pchPinCode,g_tKtsmGpCfg.achPin,NVR_MAX_STR64_LEN);
    *pbActive = g_tKtsmGpCfg.bActive;

    NVRSYSDEBUG("pin:%s,bactive:%d\n",g_tKtsmGpCfg.achPin,*pbActive);
    
    return eRet;
}
/**
 *@brief  激活ktsm模块
 *@param[out] char *pchPinCode 模块PIN码，长度64,由字母和数字组成
 *@param[out] char *pchError 当返回失败时，此有效，错误码暂定64位
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  先判断返回值，返回成功，*pchError无效，当返回失败，*pchError有效，显示该字符串
 */
NVRSTATUS NvrSysActiveKtsmModule(char *pchPinCode, char *pchError)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    
    NVRSYS_ASSERT(pchPinCode);
    NVRSYS_ASSERT(pchError);

    eRet = NvrKtsmActiveModule(pchPinCode, pchError);
    if(NVR_ERR__OK == eRet)
    {
        memcpy(g_tKtsmGpCfg.achPin,pchPinCode,NVR_MAX_STR64_LEN);
        g_tKtsmGpCfg.bActive = TRUE;
        eRet = NvrSysSaveKtsmGpSecurityCfg();
        NVRSYSDEBUG("pin:%s,save:%d\n",pchPinCode,eRet);
    }
    else
    {
        NVRSYSERR("active failed:%s\n",pchError);
    }

    return eRet;
}
/**
 *@brief  获取模块用户名密码
 *@param[out] char *pchUsrName 用户名 
 *@param[out] char *pchPwd 密码
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  用户名密码暂定64位，不支持中文
 */
NVRSTATUS NvrSysGetKtsmLoginInfo(char *pchUsrName,char *pchPwd)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    
    NVRSYS_ASSERT(pchUsrName);
    NVRSYS_ASSERT(pchPwd);

    memcpy(pchUsrName,g_tKtsmGpCfg.achUsrName,sizeof(g_tKtsmGpCfg.achUsrName));
    NvrSysStrDecrypt(g_tKtsmGpCfg.achPwd,pchPwd);
    NVRSYSDEBUG("usrname:%s\n",pchUsrName);

    return eRet;
}
/**
 *@brief  设置并登录模块
 *@param[in] char *pchUsrName 用户名
 *@param[in] char *pchPwd 密码
 *@param[out] char *pchError 当返回失败时，此有效，错误码暂定64位
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  
 */
NVRSTATUS NvrSysSetKtsmLoginInfo(char *pchUsrName,char *pchPwd,char *pchError)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    NVRSYS_ASSERT(pchUsrName);
    NVRSYS_ASSERT(pchPwd);    
    NVRSYS_ASSERT(pchError);
    
    NVRSYSDEBUG("name:%s\n",pchUsrName);

    eRet = NvrKtsmSetLoginInfo(pchUsrName, pchPwd, pchError);
    if(NVR_ERR__OK == eRet)
    {
        snprintf(g_tKtsmGpCfg.achUsrName,NVR_MAX_STR32_LEN,"%s",pchUsrName);
        NvrSysStrEncrypt(pchPwd,g_tKtsmGpCfg.achPwd);
        
        eRet = NvrSysSaveKtsmGpSecurityCfg();
        NVRSYSDEBUG("name:%s,save:%d\n",pchUsrName,eRet);
    }
    else
    {
        NVRSYSERR("login failed:%s\n",pchError);
    }

    return eRet;
}

/**
 *@brief  获取/检测ktsm模块状态
 *@param[out] TNvrKtsmDevInfo *ptDevInfo
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  
 */
NVRSTATUS NvrSysGetKtsmStatus(TNvrKtsmDevInfo *ptDevInfo)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    NVRSYS_ASSERT(ptDevInfo);

    eRet = NvrKtsmGetModuleStatus(ptDevInfo);

    return eRet;
}

/**
 *@brief  获取ktsm密管服务ip和端口
 *@param[out] TNvrNetAddr *ptAddr  密管服务ip
 *@param[out] u16 *pwPort          密管服务端口
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  
 */
NVRSTATUS NvrSysGetKtsmPwdMgrSrvInfo(TNvrNetAddr *ptAddr,u16 *pwPort)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
    
    NVRSYS_ASSERT(ptAddr);
    NVRSYS_ASSERT(pwPort);

    memcpy(ptAddr,&g_tKtsmGpCfg.tIpAddr,sizeof(TNvrNetAddr));
    *pwPort = g_tKtsmGpCfg.wPort;

    NVRSYSDEBUG("ip:%x,port:%u\n",ptAddr->tV4Addr.s_addr,*pwPort);    

    return eRet;
}
/**
 *@brief  设置ktsm密管服务ip和端口
 *@param[in] TNvrNetAddr tAddr  密管服务ip
 *@param[in] u16 wPort          密管服务端口
 *@param[out] char *pchError 当返回失败时，此有效，错误码暂定64位
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  
 */
NVRSTATUS NvrSysSetKtsmPwdMgrSrvInfo(TNvrNetAddr tAddr,u16 wPort,char *pchError)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    NVRSYS_ASSERT(pchError);

    eRet = NvrKtsmSetPwdMgrSrvInfo(tAddr, wPort, pchError);
    if(NVR_ERR__OK == eRet)
    {
        g_tKtsmGpCfg.wPort = wPort;
        memcpy(&g_tKtsmGpCfg.tIpAddr,&tAddr,sizeof(TNvrNetAddr));
        eRet = NvrSysSaveKtsmGpSecurityCfg();
        NVRSYSDEBUG("port:%u,ip:%x,save:%d\n",wPort,tAddr.tV4Addr.s_addr,eRet);
    }

    return eRet;
}
/**
 *@brief  重置ktsm网络序列号
 *@param[out] char *pchError 当返回失败时，此有效，错误码暂定64位
 *@return     NVR_OK:成功 错误码:失败，详见
 *@ref        nvrdef.h
 *@see
 *@note  
 */
NVRSTATUS NvrSysResetKtsmNetLicense(char *pchError)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    NVRSYS_ASSERT(pchError);

    eRet = NvrKtsmResetNetLicense(pchError);

    return eRet;
}

///<gp安全初始化
void NvrSysInitKtsmGpSecurity(BOOL32 bReset)
{
    FILE *pf = NULL;
    
    mzero(g_tKtsmGpCfg);
    g_tKtsmGpCfg.tIpAddr.wIPType = AF_INET;

    if( 0 == access(NVR_ROOT_PATH"config/ktsmgpcfg.dat", 0)) ///<恢复出厂不删除
    {
        if(bReset)
        {
            NvrSysSaveKtsmGpSecurityCfg();
            return ;
        }
        pf = fopen(NVR_ROOT_PATH"config/ktsmgpcfg.dat", "rb");
        if(NULL != pf)
        {
            fseek(pf, 0, SEEK_SET);
        	fread(&g_tKtsmGpCfg, 1, sizeof(g_tKtsmGpCfg), pf);
            fclose(pf);
        	pf = NULL;
        }
    }
}
///<gp安全设置
NVRSTATUS NvrSysSaveKtsmGpSecurityCfg()
{
    FILE *pf = NULL;
    
    pf = fopen(NVR_ROOT_PATH"config/ktsmgpcfg.dat", "wb");
    if(NULL != pf)
    {
        fwrite(&g_tKtsmGpCfg,1,sizeof(g_tKtsmGpCfg),pf);
        fflush(pf);
        fclose(pf);
        pf = NULL;
        return NVR_ERR__OK;
    }
    else
    {
        return NVR_ERR__ERROR;
    }
}

/**
 * @brief       使能黑白名单
 * @param[in]   eNvrFilterList 黑名单或者白名单
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysEnableFilter(ENvrFilterList eNvrFilterList)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
    mzero(tMsgData);
    mzero(tCallback);

    NVRSYSFILE("eNvrFilterList:%d\n", eNvrFilterList);

    tMsgData.nMsgID = MSG_SET_FILTER_ENABLE;

    s32 nFilterListType = (s32)eNvrFilterList;
    memcpy(tMsgData.chData, &nFilterListType, sizeof(s32));
    tCallback.wMsgType = JNIMSG_TYPE_NET_FILTER;
    tCallback.pData=&tMsgData;

    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

    if(NVR_JNI_ERR == tMsgData.errCD)
    {
        NVRSYSFILE("NVR_JNI_ERR\n");
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}


/**
 * @brief       关闭黑白名单
 * @param[in]   eNvrFilterList 黑名单或者白名单
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysDisableFilter(ENvrFilterList eNvrFilterList)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
    mzero(tMsgData);
    mzero(tCallback);

    NVRSYSFILE("eNvrFilterList:%d\n", eNvrFilterList);


    tMsgData.nMsgID = MSG_SET_FILTER_DISABLE;

    s32 nFilterListType = (s32)eNvrFilterList;
    memcpy(tMsgData.chData, &nFilterListType, sizeof(s32));
    tCallback.wMsgType = JNIMSG_TYPE_NET_FILTER;
    tCallback.pData=&tMsgData;

    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

    if(NVR_JNI_ERR == tMsgData.errCD)
    {
        NVRSYSFILE("NVR_JNI_ERR\n");
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

/**
 * @brief       管理黑/白 ip 名单
 * @param[in]   eNvrFilterCmd 命令类型
 * @param[in]   tNvrIpFilterParam 黑名单或者白名单 IP
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysManageIpFilter(ENvrFilterCmd eNvrFilterCmd, TNvrIpFilterParam* ptNvrIpFilterParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
    mzero(tMsgData);
    mzero(tCallback);


    NVRSYSFILE("eNvrFilterCmd:%d eNvrIpType:%d start ip:%s end ip:%s\n", 
                eNvrFilterCmd, 
                ptNvrIpFilterParam->eNvrIpType, 
                ptNvrIpFilterParam->szStartIP,
                ptNvrIpFilterParam->szEndIP);

    tMsgData.nMsgID = MSG_MANAGE_FILTER_IP;

    s32 nFilterCmd = (s32)eNvrFilterCmd;
    memcpy(tMsgData.chData, &nFilterCmd, sizeof(s32));
    memcpy(tMsgData.chData+sizeof(nFilterCmd), ptNvrIpFilterParam, sizeof(TNvrIpFilterParam));
    tCallback.wMsgType = JNIMSG_TYPE_NET_FILTER;
    tCallback.pData=&tMsgData;

    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

    if(NVR_JNI_ERR == tMsgData.errCD)
    {
        NVRSYSFILE("NVR_JNI_ERR\n");
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

/**
 * @brief       管理黑/白 MAC 名单
 * @param[in]   eNvrFilterCmd 命令类型
 * @param[in]   szMac 黑名单或者白名单 MAC
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysManageMacFilter(ENvrFilterCmd eNvrFilterCmd, TNvrMacFilterParam* ptNvrMacFilterParam)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
    mzero(tMsgData);
    mzero(tCallback);

	NVRSYSFILE("eNvrFilterCmd:%d mac:%s\n", eNvrFilterCmd, ptNvrMacFilterParam->szMac);

    tMsgData.nMsgID = MSG_MANAGE_FILTER_MAC;

    s32 nFilterCmd = (s32)eNvrFilterCmd;
    memcpy(tMsgData.chData, &nFilterCmd, sizeof(s32));
	memcpy(tMsgData.chData+sizeof(nFilterCmd), ptNvrMacFilterParam, sizeof(TNvrMacFilterParam));
    tCallback.wMsgType = JNIMSG_TYPE_NET_FILTER;
    tCallback.pData=&tMsgData;

    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

    if(NVR_JNI_ERR == tMsgData.errCD)
    {
        NVRSYSFILE("NVR_JNI_ERR\n");
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}

NVRSTATUS NvrSysSetImpOperatePermFlag(BOOL32 bFlag)
{
	g_bJudgeOperate = bFlag;
	NvrUpgradeSetUpgradeFlag(bFlag);
    ///<关闭时间同步
    NvrSysCloseTimeSync(bFlag);
    return NVR_ERR__OK;
}

NVRSTATUS NvrSysGetImpOperatePermFlag()
{
	if(TRUE == g_bJudgeOperate)
		return  NVR_ERR__SVRSF_SERVER_IS_BURN;
	return NVR_ERR__OK;
}


/**
 * @brief       开启或关闭PING功能
 * @param[in]   eNvrFilterIcmp 命令类型
 * @return      NVR_ERR__OK:成功 错误码:失败，详见
 * @ref         nvrdef.h
 */
NVRSTATUS NvrSysManageIcmpFilter(ENvrFilterIcmp eNvrFilterIcmp)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;

    TNvrJniMsgData tMsgData;
    TNvrJniMsgInfo tCallback;
    mzero(tMsgData);
    mzero(tCallback);

	NVRSYSFILE("eNvrFilterIcmp:%d\n", eNvrFilterIcmp);

    tMsgData.nMsgID = MSG_MANAGE_FILTER_ICMP;

    s32 nFilterIcmp = (s32)eNvrFilterIcmp;
    memcpy(tMsgData.chData, &nFilterIcmp, sizeof(s32));
    tCallback.wMsgType = JNIMSG_TYPE_NET_FILTER;
    tCallback.pData=&tMsgData;

    NvrSysSndJniMsg(&tCallback, __FUNCTION__, __LINE__);

    if(NVR_JNI_ERR == tMsgData.errCD)
    {
        NVRSYSFILE("NVR_JNI_ERR\n");
        eRet = NVR_ERR__ERROR;
    }

    return eRet;
}


/**************************** 系统模块telnet测试接口 ****************************/
static void nvrsyshelp(void)
{

	NVRSYSINFO("******************************************** sys help *********************************************************\n");
	NVRSYSINFO("*   function                  command                 param                                                   *\n");
	NVRSYSINFO("*   get sys version            sysver                 void                                                    *\n");
	NVRSYSINFO("*   get dev info               getdevinfo             void                                                    *\n");
	NVRSYSINFO("*   set zero chn enc info      syssetzeroenc          en:0~1 width height framerate:0~8 bitrate:0~4 vidsrc:0~1*\n");
	NVRSYSINFO("*   get zero chn enc info      sysgetzeroenc          void                                                    *\n");
	NVRSYSINFO("*   set udp retran paran       syssetudpretran        en:0~1 first second third discard [40~3000]             *\n");
	NVRSYSINFO("*   get udp retran param       sysgetudpretran        void                                                    *\n");
	NVRSYSINFO("*   set led stat               syssetled              id:0~17 stat:0~4                                        *\n");
	NVRSYSINFO("* set pkghead                  setpkghead             clear userdata and reset pkg head                       *\n");
	NVRSYSINFO("* get pkghead                  getpkghead             										                  *\n");
	NVRSYSINFO("*   get manual event           getmevent               void  								                  *\n");
	NVRSYSINFO("***************************************************************************************************************\n");
}

static void nvrsystimegetver(void)
{
	char achBuf[16] = {0};
	s32 nLen = 0;
    NVRSYSINFO("sys compile time: %s, %s\n",  __TIME__, __DATE__ );
	NvrSysGetSqliteKey(achBuf, &nLen);
	NVRSYSINFO("%s,%d\n",achBuf,nLen);
}

static void nvrsysgetdevinfo(void)
{
	TNvrSysDevInfo tDevInfo;

	mzero(tDevInfo);

	NvrSysGetDevInfo(&tDevInfo);

	NVRSYSINFO("devtype  :%s\n", tDevInfo.achDevType);
	NVRSYSINFO("serialnum:%s\n", tDevInfo.achDevSerialNum);
	NVRSYSINFO("Mdate    :%lu\n", tDevInfo.dwDevMDate);
	NVRSYSINFO("softver  :%s\n", tDevInfo.achDevSoftVer);
	NVRSYSINFO("cpuused  :%d\n", tDevInfo.byCpuUsed);
	NVRSYSINFO("memused  :%d\n", tDevInfo.byMemUsed);
	s32 nslot = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
	eRet = NvrBrdApiGetExGpioInput(0,0,&nslot);
	NVRSYSINFO("slot:%d, eRet:%d\n", nslot, eRet);
}

static void syssetzeroenc(u8 byEnable, u16 wWidth, u16 wHeight, u32 dwFrame, u32 dwBitRate, u32 dwVideoSrc)
{
	TNvrSysZeroChnEncParam tZeroChnEncParam;
	mzero(tZeroChnEncParam);

	tZeroChnEncParam.byEnable = byEnable;
	tZeroChnEncParam.tEncRes.wWidth = wWidth;
	tZeroChnEncParam.tEncRes.wHeight = wHeight;
	tZeroChnEncParam.eFrameRate = dwFrame;
	tZeroChnEncParam.eBitRate = dwBitRate;
	tZeroChnEncParam.eVideoSrc = dwVideoSrc;

	NVRSYSINFO("set zero chn enc param enable:%d, wWidth:%lu, wHeight:%lu, framerate:%d, bitrate:%d, videosrc:%d\n",
	tZeroChnEncParam.byEnable,
	tZeroChnEncParam.tEncRes.wWidth,
	tZeroChnEncParam.tEncRes.wHeight,
	tZeroChnEncParam.eFrameRate,
	tZeroChnEncParam.eBitRate,
	tZeroChnEncParam.eVideoSrc);

	NvrSysSetSysZeroChnEncParam(&tZeroChnEncParam);

}

static void sysgetzeroenc()
{
	TNvrSysZeroChnEncParam tZeroChnEncParam;
	mzero(tZeroChnEncParam);

	NvrSysGetSysZeroChnEncParam(&tZeroChnEncParam);

	NVRSYSINFO("get zero chn enc param enable:%d, wWidth:%lu, wHeight:%lu, framerate:%d, bitrate:%d, videosrc:%d\n",
	tZeroChnEncParam.byEnable,
	tZeroChnEncParam.tEncRes.wWidth,
	tZeroChnEncParam.tEncRes.wHeight,
	tZeroChnEncParam.eFrameRate,
	tZeroChnEncParam.eBitRate,
	tZeroChnEncParam.eVideoSrc);

}

static void syssetudpretran(u8 byEn, u32 dwFirst, u32 dwSecond, u32 dwThird, u32 dwDiscard)
{
	TNvrSysUdpReTranParam tUdpReTran;

	mzero(tUdpReTran);

	tUdpReTran.byEnable = byEn;
	tUdpReTran.dwFirstCheckPoint = dwFirst;
	tUdpReTran.dwSecondCheckPoint = dwSecond;
	tUdpReTran.dwThirdCheckPoint = dwThird;
	tUdpReTran.dwOverdueDiscard = dwDiscard;

	NvrSysSetUdpReTranParam(&tUdpReTran);
}

static void sysgetudpretran()
{

	TNvrSysUdpReTranParam tUdpReTran;

	mzero(tUdpReTran);

	NvrSysGetUdpReTranParam(&tUdpReTran);
}

static void syssetled(u32 dwId, u32 dwStat)
{
	if(65535 == dwId)
	{	u32 i = 0;
		for(i=0; i<NVR_SYS_MAX_LED_NUM; i++)
		{
			if(1 == g_abyLedSup[i])
			{
				NvrSysSetLedStatus(i, dwStat);
			}
		}
	}
	else
	{
		NvrSysSetLedStatus(dwId, dwStat);
	}
}
static void sysshutdown(void)
{
	TNvrSysShutDownInfo tInfo;
	mzero(tInfo);

	snprintf(tInfo.achOperator, sizeof(tInfo.achOperator), "Local");
	NvrSysShutDown(&tInfo);
    return;
}

static void sysgetholiday(void)
{
	TNvrBrokenDownTime tLocalTime;
	mzero(tLocalTime);

	NvrSysGetSystemLocalTime(&tLocalTime);
	NVRSYSINFO("cur time :%d-%d-%d %d:%d:%d week:%d\n", tLocalTime.wYear, tLocalTime.byMonth, tLocalTime.byDay, tLocalTime.byHour, tLocalTime.byMinute, tLocalTime.bySecond, tLocalTime.byWeek);

	BOOL32 bHoliday = NvrSysGetCurTimeIsHoliday();

	if (bHoliday)
	{
		NVRSYSINFO("Is Holiday\n");
	}
	else
	{
		NVRSYSINFO("Not Holiday\n");
	}

	return ;
}

static void sysexportcfg(const char* pchFilePath)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSysExprotCfgParam tExportCfgParam;
    mzero(tExportCfgParam);

    tExportCfgParam.eClientType = NVR_CLIENT_TYPE_WEB;

    eRet = NvrSysExportCfg(&tExportCfgParam);
    if(NVR_ERR__OK != eRet)
    {
        NVRSYSIMP("sysinportcfg faild\n");
        return;
    }

    NVRSYSIMP("sysexportcfg path :%s \n",tExportCfgParam.achExprotCfgPath);

    return;
}

static void sysinportcfg(const char* pchFilePath)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    eRet = NvrSysInportCfg(pchFilePath);
    if(NVR_ERR__OK != eRet)
    {
        NVRSYSIMP("sysinportcfg faild\n");
        return;
    }
    return;
}
static void sysgetiotest()
{
	u32 dwIO = 0;
	NvrSysGetIOUse(&dwIO);
	NVRSYSINFO("IO:%ld\n",dwIO);
}

void NvrSysGetPkgHead(char* pchPkgHead)
{
	NVRSYSMEMAPI();
	TNvrBrdPrdInfo tPinfo;
	char achPkgHead[17];
    u64 dwT1 = 0;
    u64 dwT2 = 0;
	
	mzero(tPinfo);
	memset(achPkgHead, 0, sizeof(achPkgHead));
	dwT1 = NvrSysGetCurTimeMSec();
	NvrBrdApiBrdPinfoQuery(&tPinfo);
	dwT2 = NvrSysGetCurTimeMSec();
	NVRSYSINFO("BrdPinfoQuery spend time:%llums\n",dwT2 -dwT1);

	memcpy(achPkgHead,&tPinfo.abyUserData[4], 16);

	achPkgHead[16]= '\0';
    strncpy(pchPkgHead, achPkgHead,17);
    #ifndef __I18N__
	NVRSYSINFO("cur pkg head:%s\n",achPkgHead);
    #endif
	g_bUpdate = TRUE;
}

static void NvrSysPrintPkgHead()
{
	TNvrBrdPrdInfo tPinfo;
	char achPkgHead[17];

	mzero(tPinfo);
	memset(achPkgHead, 0, sizeof(achPkgHead));

	NvrBrdApiBrdPinfoQuery(&tPinfo);

	memcpy(achPkgHead,&tPinfo.abyUserData[4], 16);

	achPkgHead[16]= '\0';
#ifndef __I18N__
	NVRSYSINFO("cur pkg head:%s\n",achPkgHead);
#endif
}
NVRSTATUS NvrLenUpgradeStart(u32 dwPkgTotalSize, u32 dwSendLenMax, u8 *pbyPkgHeadBuf, u32 dwPkgHeadBufSize)
{
	///<校验能力和包头
    NVRSTATUS eRet = NVR_ERR__OK;
	TNvrCapSysInfo tSysCap;

	mzero(tSysCap);
	if(NULL == pbyPkgHeadBuf)
	{
		NVRSYSERR("NvrLenUpgradeStart pbyPkgHeadBuf is NULL !\n");
        return NVR_ERR__ERROR;
	}

	eRet = NvrCapGetCapParam(NVR_CAP_ID_SYS, &tSysCap);
	if(NVR_ERR__OK != eRet)
	{
		NVRSYSERR("NvrLenUpgradeStart Get SysCap Failed !!\n");
		return eRet;
	}	

	if(FALSE == tSysCap.tUpgradeLens.bSupUpgrade)
	{
		NVRSYSERR("NvrLenUpgradeStart unsupport camera lens upgrade!\n");
		return NVR_ERR__ERROR;
	}
#ifndef __I18N__
	NVRSYSINFO("NvrLenUpgradeStart File head1: %s,head2:%s, HeadLen: %lu\n", pbyPkgHeadBuf, tSysCap.tUpgradeLens.achPkgHead, dwPkgHeadBufSize);
#endif		
	if (0 != strncmp((s8 *)pbyPkgHeadBuf, tSysCap.tUpgradeLens.achPkgHead, strlen(tSysCap.tUpgradeLens.achPkgHead)))
	{
		NVRSYSERR("NvrLenUpgradeStart File head check fail\n");
		return NVR_ERR__ERROR;
	}	
	g_tLensUpgradeDate.dwPkgTotalSize = dwPkgTotalSize;
	g_tLensUpgradeDate.dwSendLenMax = dwSendLenMax;
	if(NULL != g_tLensUpgradeDateCB)
	{
		g_tLensUpgradeDateCB(&g_tLensUpgradeDate,NVR_DEV_UPGRADE_CHECK_SUCC);
	}

	return eRet;
}





NVRSTATUS NvrLensUpgradeSendPkgData(u32 dwDataBeginPos, u8 * pbyDataBuf, u32 dwDataBufSize)
{

	///<回调通知41模块升级包数据
	g_tLensUpgradeDate.dwDataBeginPos = dwDataBeginPos;
	g_tLensUpgradeDate.pbyDataBuf = pbyDataBuf;
    g_tLensUpgradeDate.dwDataBufSize = dwDataBufSize;
	if(NULL != g_tLensUpgradeDateCB)
	{
		g_tLensUpgradeDateCB(&g_tLensUpgradeDate,NVR_DEV_UPGRADE_DATE_SENDING);
	}
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysLensUpgradeDateTransCB(PFNvrSysLensUpgradeDateCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tLensUpgradeDateCB = pfCallBack;
	
    return NVR_ERR__OK;
}
NVRSTATUS NvrSysLensUpgradeStatusTransCB(PFNvrSysLensUpgradeStatusCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tLensUpgradeStatusCB = pfCallBack;
	
    return NVR_ERR__OK;
}
///<web一直在获取状态
NVRSTATUS NvrGetLensUpgradeState(TNvrLensUpgradeState *pState)
{
	if(NULL != g_tLensUpgradeStatusCB)
	{
		g_tLensUpgradeStatusCB(pState);
	}  

	return NVR_ERR__OK;
}

NVRSTATUS NvrLensUpgradeStop()
{
	return NVR_ERR__OK;
}

NVRSTATUS NvrPowerUpgradeStart(u32 dwPkgTotalSize, u32 dwSendLenMax, u8 *pbyPkgHeadBuf, u32 dwPkgHeadBufSize)
{
	///<校验能力和包头
    NVRSTATUS eRet = NVR_ERR__OK;
	TNvrCapSysInfo tSysCap;

	mzero(tSysCap);
	if(NULL == pbyPkgHeadBuf)
	{
		NVRSYSERR("NvrPowerUpgradeStart pbyPkgHeadBuf is NULL !\n");
        return NVR_ERR__ERROR;
	}

	eRet = NvrCapGetCapParam(NVR_CAP_ID_SYS, &tSysCap);
	if(NVR_ERR__OK != eRet)
	{
		NVRSYSERR("NvrPowerUpgradeStart Get SysCap Failed !!\n");
		return eRet;
	}	

	if(FALSE == tSysCap.tUpgradePower.bSupUpgrade)
	{
		NVRSYSERR("NvrPowerUpgradeStart unsupport power upgrade!\n");
		return NVR_ERR__ERROR;
	}
#ifndef __I18N__
	NVRSYSINFO("NvrPowerUpgradeStart File head1: %s,head2:%s, HeadLen: %lu\n", pbyPkgHeadBuf, tSysCap.tUpgradePower.achPkgHead, dwPkgHeadBufSize);
#endif		
	if (0 != strncmp((s8 *)pbyPkgHeadBuf, tSysCap.tUpgradePower.achPkgHead, strlen(tSysCap.tUpgradePower.achPkgHead)))
	{
		NVRSYSERR("NvrPowerUpgradeStart File head check fail\n");
		return NVR_ERR__ERROR;
	}	
	g_tPowerUpgradeDate.dwPkgTotalSize = dwPkgTotalSize;
	g_tPowerUpgradeDate.dwSendLenMax = dwSendLenMax;
	if(NULL != g_tPowerUpgradeDateCB)
	{
		g_tPowerUpgradeDateCB(&g_tPowerUpgradeDate,NVR_DEV_UPGRADE_CHECK_SUCC);
	}

	return eRet;
}





NVRSTATUS NvrPowerUpgradeSendPkgData(u32 dwDataBeginPos, u8 * pbyDataBuf, u32 dwDataBufSize)
{

	///<回调通知41模块升级包数据
	g_tPowerUpgradeDate.dwDataBeginPos = dwDataBeginPos;
	g_tPowerUpgradeDate.pbyDataBuf = pbyDataBuf;
    g_tPowerUpgradeDate.dwDataBufSize = dwDataBufSize;
	if(NULL != g_tPowerUpgradeDateCB)
	{
		g_tPowerUpgradeDateCB(&g_tPowerUpgradeDate,NVR_DEV_UPGRADE_DATE_SENDING);
	}
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysPowerUpgradeDateTransCB(PFNvrSysPowerUpgradeDateCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tPowerUpgradeDateCB = pfCallBack;
	
    return NVR_ERR__OK;
}
NVRSTATUS NvrSysPowerUpgradeStatusTransCB(PFNvrSysPowerUpgradeStatusCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tPowerUpgradeStatusCB = pfCallBack;
	
    return NVR_ERR__OK;
}
///<web一直在获取状态
NVRSTATUS NvrGetPowerUpgradeState(TNvrPowerUpgradeState *pState)
{
	if(NULL != g_tPowerUpgradeStatusCB)
	{
		g_tPowerUpgradeStatusCB(pState);
	}  

	return NVR_ERR__OK;
}

NVRSTATUS NvrPowerUpgradeStop()
{
	return NVR_ERR__OK;
}


NVRSTATUS NvrSysSlaveUpgradeRegistCB(PFNvrSysSlaveUpgradeCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tSlaveUpgradeCB = pfCallBack;

	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSlaveUpgradeStateRegistCB(PFNvrSysSlaveUpgradeStatusCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tSlaveUpgradeStatusCB = pfCallBack;
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSlaveUpgradeNeedRegistCB(PFNvrSysSlaveNeedUpgradeCB pfCallBack)
{
	NVRSYSMEMAPI();
	g_tSlaveNeedUpgradeCB = pfCallBack;
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSlaveUpgrade()
{
	if(g_tSlaveUpgradeCB)
	{
		g_tSlaveUpgradeCB();
	}
	return NVR_ERR__OK;
}


NVRSTATUS NvrSysSlaveNeedUpgrade(BOOL32 *pbNeedUpgrade)
{
	if(g_tSlaveNeedUpgradeCB)
	{
		g_tSlaveNeedUpgradeCB(pbNeedUpgrade);
	}
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSlaveUpgradeState(TNvrSlaveUpgradeState *ptSlaveUpgradeState)
{
	if(g_tSlaveUpgradeStatusCB)
	{
		g_tSlaveUpgradeStatusCB(ptSlaveUpgradeState);
	}
	return NVR_ERR__OK;
}

static void NvrSysSetPkgHead(char *pchHead)
{
	NvrBrdApiSetPkgHead(pchHead);
	return;
}

static void NvrSysSetDevType(char *pchDevType)
{
	TNvrBrdPrdInfo tPinfo;
	memset(&tPinfo, 0, sizeof(tPinfo));

	if(NULL == pchDevType)
	{
		NVRSYSINFO("no param input!\n");
		return;
	}

	///设备型号不能超过32字符长度
	if(strlen(pchDevType) > 32 || 0 == strlen(pchDevType))
	{
		NVRSYSINFO("pchDevType   length:%d over flow max len 32 !\n", strlen(pchDevType));
		return;
	}

	NvrBrdApiBrdPinfoQuery(&tPinfo);
	tPinfo.abyUserData[20] = strlen(pchDevType);
	memcpy(&tPinfo.abyUserData[21], pchDevType, strlen(pchDevType));

	///写入e2prom，同时清除之前的userdata信息
	NvrBrdApiPinfoSetUserdata(tPinfo.abyUserData, sizeof(tPinfo.abyUserData));

	return;
}

static void NvrSysSetUserData(u8 byUserData, u32 dwOffSet)
{
	TNvrBrdPrdInfo tPinfo;

	memset(&tPinfo, 0, sizeof(tPinfo));

	if(dwOffSet > 63)
	{
		NVRSYSINFO("offset must < 64 !\n");
		return;
	}

	NvrBrdApiBrdPinfoQuery(&tPinfo);

	tPinfo.abyUserData[dwOffSet] = byUserData;

	NvrBrdApiPinfoSetUserdata(tPinfo.abyUserData, sizeof(tPinfo.abyUserData));

	return;
}


static void NvrSysSetUserdataOffset(u8 byOffset, u8 byUserData)
{
	if(byOffset > 64 || 0 == byOffset )
	{
		NVRSYSINFO("userdata must   > 0 and < 65\n");
		return;
	}
	byOffset = byOffset -1;
	NvrSysSetUserData(byUserData, byOffset);
	return;
}


static void NvrSysTestTranData(char* pchTestMsg)
{
    char achTest[NVR_MAX_STR4096_LEN] = {""};
    u16 wReturnBufLen = 0;

    NvrProductTransData(pchTestMsg, strlen(pchTestMsg), achTest, &wReturnBufLen);

	return;
}

static void NvrSysGetSysParamTest(u32 dwType)
{
	switch (dwType)
	{
		case 0:
		{
			NVRSYSINFO("-------------- get sys param help --------------\n");
			NVRSYSINFO("1: get manual event param\n");
			NVRSYSINFO("2: get vehicle param\n");
			NVRSYSINFO("3: get local server info\n");
		}
		break;

		case 1:
		{
			TNvrSysManualEventParam tParam;
			mzero(tParam);

			NvrSysGetManualEventParam(&tParam);
		}
		break;

		case 2:
		{
			TNvrSysVehicleParam tVehicleParam;
			char achCarPlateNum[NVR_MAX_STR32_LEN+2];

			mzero(tVehicleParam);
			mzero(achCarPlateNum);

			NvrSysGetVehicleParam(&tVehicleParam);

			CharConvConvertUnicodetoUtf8((u8*)tVehicleParam.achCarPlateNum, tVehicleParam.dwCarPlateNumLen, achCarPlateNum, sizeof(achCarPlateNum));

			NVRSYSINFO("car plate num:%s len:%lu synclable:%d\n", achCarPlateNum, tVehicleParam.dwCarPlateNumLen, tVehicleParam.bSyncLable);

			NVRSYSINFO("shutdown poe sup:%d\n", tVehicleParam.bShutdownPoeSup);
		}
		break;

		case 3:
		{
			TNvrNetLocationServiceInfo tInfo;
			mzero(tInfo);

			NvrNetGetLocationServiceInfo(&tInfo);

			NVRSYSINFO("deviceid:%s\n", tInfo.achDeviceId);
			NVRSYSINFO("usercode:%s\n", tInfo.achUsercode);
			NVRSYSINFO("loctype:%d\n", tInfo.eLocType);
			NVRSYSINFO("------ mobile info ------\n");

			NVRSYSINFO("valid:%d\n", tInfo.tMobileInfo[0].bValid);
			NVRSYSINFO("mnc:%s\n", tInfo.tMobileInfo[0].achMnc);
			NVRSYSINFO("mcc:%lu\n", tInfo.tMobileInfo[0].dwMcc);
			NVRSYSINFO("phonetype:%d\n", tInfo.tMobileInfo[0].ePhoneType);
			NVRSYSINFO("lac:%lu\n", tInfo.tMobileInfo[0].dwLac);
			NVRSYSINFO("cid:%lu\n", tInfo.tMobileInfo[0].dwCid);
			NVRSYSINFO("sid:%lu\n", tInfo.tMobileInfo[0].dwSid);
			NVRSYSINFO("nid:%lu\n", tInfo.tMobileInfo[0].dwNid);
			NVRSYSINFO("bid:%lu\n", tInfo.tMobileInfo[0].dwBid);
		}
		break;

		default:
		NVRSYSINFO("type:%lu not sup\n", dwType);
		break;
	}

	return;
}

static void NvrSysSetPid(u32 dwPid)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	TNvrBrdPrdInfo tPinfo;

	mzero(tPinfo);

	eRet = NvrBrdApiBrdPinfoQuery(&tPinfo);
	if (NVR_ERR__OK == eRet)
	{
		NVRSYSINFO("cur pid:0x%x target pid:0x%x \n", tPinfo.dwPid, dwPid);

		tPinfo.dwPid = dwPid;
		eRet = NvrBrdApiBrdPinfoUpdate(&tPinfo);
		if (NVR_ERR__OK == eRet)
		{
			NVRSYSINFO("update pid success, please delete all config!!!\n");
		}
		else
		{
			NVRSYSINFO("set brd info failed!!!\n");
		}
	}
	else
	{
		NVRSYSINFO("get brd info failed!!!\n");
	}

	return ;
}

static void NvrSysOtaUpgradeTest()
{
	const u8 achPkgPath[]= {"/data/misc/update.zip"};
	NvrSysOtaUpgrade(achPkgPath, NVR_SYS_UPGRADE_TYPE_CGI, NVR_SYS_OTA_TRANS_SUCCESS);
}

static void NvrSysLinkLedCtrl(u8 byOpen)
{
	if (byOpen > 0)
	{
		NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_ON);
	}
	else
	{
		NvrSysSetLedStatus(LED_ID_LINK, LED_MODE_OFF);
	}

	return;
}

static void NvrSysGetVerTest()
{
	u8 achRomVer[NVR_MAX_STR32_LEN];
	char achDevApkVer[NVR_MAX_STR64_LEN] = {0};
	NvrSysGetApkVerCode(achDevApkVer);
	NvrSysGetRomVer(achRomVer);

	NVRSYSINFO("apk code =%s rom ver= %s\n",achDevApkVer, achRomVer);

}

NVRSTATUS NvrSysMvFileDeal(const char *pchSrvFilePath ,const char* pchDstFilePath, BOOL32 bRemove)
{
	NVRSYSMEMAPI();
    NVRSTATUS eRet = NVR_ERR__OK;
	s32 nRet = 0;
    FILE * pFile = NULL;
    u32    dwFileLen = 0;
    char * pchbuffer = NULL;
    s32 fd = -1;
    u32 dwCurOffSet = 0;
    u32 dwReadCount = 0;
    u64 dwCacheSize = 512*1024;
    NVRSYSDEBUG("srcfile :%s\n", pchSrvFilePath);
    NVRSYSDEBUG("dstfile :%s\n", pchDstFilePath);
    pchbuffer = (char*)NVRALLOC(dwCacheSize);///osp内存池大小限制，此处用系统的malloc 10mb

    do
    {
        pFile = fopen(pchSrvFilePath, "rb");
        if (NULL == pFile)
        {
            NVRSYSERR(" fopen file %s faild!\n", pchSrvFilePath);
            eRet = NVR_ERR__ERROR;
            break;
        }
        fd = open(pchDstFilePath, O_RDWR | O_CREAT | O_TRUNC | O_SYNC , 0777);
        if(-1 == fd)
        {
            NVRSYSERR(" open file %s faild!\n", pchDstFilePath);
            NVRSYSERR(" error:%s\n", strerror(errno));
            eRet = NVR_ERR__ERROR;
            break;
        }

        //获取文件大小
        fseek (pFile , 0 , SEEK_END);
        dwFileLen = ftell(pFile);
        if(NULL == pchbuffer)
        {
            NVRSYSERR(" nvr malloc faild len:%ld \n", dwFileLen);
            eRet = NVR_ERR__ERROR;
            break;
        }
        NVRSYSDEBUG("dwFileLen %lu \n", dwFileLen);
        fseek(pFile, 0, SEEK_SET);
        while(dwCurOffSet <= dwFileLen)
        {
            memset(pchbuffer, 0, dwCacheSize);
            ///抓包内容读入内存
            dwReadCount = fread(pchbuffer, 1, dwCacheSize, pFile);
            NVRSYSDEBUG("dwReadCount:%lu\n",  dwReadCount);
            dwCurOffSet = dwCurOffSet + dwReadCount;
            NVRSYSDEBUG("dwFileLen:%lu dwCurOffSet:%lu \n", dwFileLen, dwCurOffSet);
            nRet = write(fd, pchbuffer, dwReadCount);
            if(-1 == nRet)
            {
                eRet = NVR_ERR__ERROR;
                break;
            }///读取到文件末尾
            if(0 == dwReadCount)
            {
                break;
            }
        }
    }while(0);

    if(NULL != pchbuffer)
    {
        NVRFREE(pchbuffer);///osp内存池大小限制，此处用系统的malloc
    }

    if(-1 != fd)
    {
        nRet = close(fd);
        if(-1 == nRet)
        {
            NVRSYSERR(" close file %s faild!\n", pchDstFilePath);
        }
    }

    if(NULL != pFile)
    {
        fclose(pFile);
        pFile = NULL;
    }

    if(NVR_ERR__OK == eRet && TRUE == bRemove)
    {
        ///删除旧文件
        unlink(pchSrvFilePath);
    }

	return eRet;
}

void NvrSysCheckMuteDown()
{
	u32 dwStatus = 0;
	static u32 m_dwCount = 0;
	NvrBrdApiBrdButtonGetStatus(BUTTON_ID_AFMOD, &dwStatus);
	if(BUTTON_STATE_OFF == dwStatus)
	{
		m_dwCount++;
		if(4 == m_dwCount)
		{
			///<蜂鸣器不再报警
			NvrBrdApiSetIgnoreSpeaker(TRUE);
		}
	}
	else
	{
		m_dwCount = 0;
	}
	NVRSYSDEBUG("mute button, btnid:%d, status:%d\n", BUTTON_ID_AFMOD, dwStatus);
}

NVRSTATUS NvrSysSetFanSpeed(u8 bySpeed)
{
	NVRSYSMEMAPI();
	NVRSYSDEBUG("set fan speed:%u\n", bySpeed);
	return NvrBrdApiSetFanSpeed(bySpeed);
}

NVRSTATUS NvrSysSetSoftVer(char* pchSoftVer)
{
	NVRSYSMEMAPI();
	if(NULL != pchSoftVer)
	{
#if 0
        s32 nVer1 = 0;
		s32 nVer2 = 0;
		s32 nVer3 = 0;
		s32 nVer1Temp = 0;
		s32 nVer2Temp = 0;
		s32 nVer3Temp = 0;		
		sscanf(NVR_SYS_SOFT_VER,"%d.%d.%d", &nVer1, &nVer2, &nVer3);
		sscanf(pchSoftVer,"%d.%d.%d", &nVer1Temp, &nVer2Temp, &nVer3Temp);

		NVRSYSDEBUG("nVer1:%d nVer2:%d nVer3:%d nVer1Temp:%d nVer2Temp2:%d nVer3Temp:%d set pchSoftVer:%s real ver:%s \n",
						nVer1,
						nVer2,
						nVer3,
						nVer1Temp,
						nVer2Temp,
						nVer3Temp,
						pchSoftVer,
						NVR_SYS_SOFT_VER);

		if(!(nVer1 == nVer1Temp && nVer2 == nVer2Temp && nVer3 == nVer3Temp))
		{
			NVRSYSDEBUG("nVer1:%d nVer2:%d nVer3:%d nVer1Temp:%d nVer2Temp2:%d nVer3Temp:%d set pchSoftVer:%s faild real ver:%s \n",
							nVer1,
							nVer2,
							nVer3,
							nVer1Temp,
							nVer2Temp,
							nVer3Temp,
							pchSoftVer,
							NVR_SYS_SOFT_VER);
			return NVR_ERR__ERROR;
		}
		else
#endif
			strncpy(g_achSoftVerSeted, pchSoftVer, sizeof(g_achSoftVerSeted));
	}
	else
		return NVR_ERR__ERROR;
	return NVR_ERR__OK;
}


void NvrSysOspPrintCallBack(char* pchBuffer)
{
	TNvrDoubleListPushAttr tNodeAttr;
	mzero(tNodeAttr);

	/*printf("g_dwNvrSysConID:%lu g_bStartOutputOspPrintf:%d bWebTelnetEnable:%d pchBuffer:%s\n",
			g_dwNvrSysConID,
			g_bStartOutputOspPrintf,
			g_tNvrSysCfg.tAdvanceParam.tSysParam.bWebTelnetEnable,
			pchBuffer);	*/
	if(0 == g_dwNvrSysConID || 
	   FALSE == g_bStartOutputOspPrintf || 
	   FALSE == g_tNvrSysCfg.tAdvanceParam.tSysParam.bWebTelnetEnable ||
	   FALSE == g_bOspPrintfStart)
	{
		/*printf("g_dwNvrSysConID:%lu g_bStartOutputOspPrintf:%d bWebTelnetEnable:%d\n",
				g_dwNvrSysConID,
				g_bStartOutputOspPrintf,
				g_tNvrSysCfg.tAdvanceParam.tSysParam.bWebTelnetEnable);*/
		return;
	}
	
	if(NULL == g_ptOspQueuePrintQueue && NULL != g_pfOspAppPrintf)
	{	
		NvrQueueCreate(&g_ptOspQueuePrintQueue);
		///创建告警模块状态读写信号量
		if(!OsApi_SemBCreate(&g_hNvrSysOspPrintSem))
		{
			printf("NvrSysOspPrintCallBack g_hNvrSysOspPrintSem failed \n");
			return;
		}
		if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSysOspPrintThread, "NvrSysOspPrintThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
		{
			printf("NvrSysOspPrintThread create failed\n");
			return;
		}		
	}
	
	//printf("NvrSysOspPrintCallBack pchBuffer:%s g_dwCurPrintListNodeNum:%lu\n", pchBuffer, g_dwCurPrintListNodeNum);
	tNodeAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_NORMAL;
	tNodeAttr.byMergerType = NVR_QUEUE_NODE_MERGER_NONE;
	tNodeAttr.dwType = 1;
	tNodeAttr.pchDataBuf = pchBuffer;
	tNodeAttr.dwDataLen = strlen(pchBuffer) + 1;
	g_dwCurPrintListNodeNum++;
	NvrQueuePush(g_ptOspQueuePrintQueue,&tNodeAttr);
	return;
}

void NvrSysOspPrintThread()
{
	NVRSTATUS eRet = NVR_ERR__OK;
	TNvrDoubleListPopAttr tPopAttr;
	char achBuf[(NVR_MAX_STR1024_LEN+1)*6] = {""};
#ifndef WIN32
	prctl(PR_SET_NAME, "NvrSysOspPrintThread", 0, 0, 0);
#endif

#ifdef _QCOM_
	prctl(PR_SET_DUMPABLE, 1, 0, 0, 0);
#endif

	mzero(tPopAttr);
	tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
	tPopAttr.pchDataBuf = achBuf;
	tPopAttr.dwDataLen = NVR_MAX_STR4096_LEN*2;

	while(TRUE)
	{
		eRet = NvrQueuePop(g_ptOspQueuePrintQueue, &tPopAttr);
		if(NVR_ERR__OK == eRet)
		{
			if(NULL != g_pfOspAppPrintf)
			{			
				g_dwCurPrintListNodeNum--;
				//printf("NvrSysOspPrintThread g_dwNvrSysConID:%lu g_bStartOutputOspPrintf:%d\n", g_dwNvrSysConID, g_bStartOutputOspPrintf);
				if(0 == g_dwNvrSysConID || FALSE == g_bStartOutputOspPrintf)
					continue;
				//printf("start call back!\n");
				g_pfOspAppPrintf(achBuf, g_dwNvrSysConID);
				//printf("end call back!\n");
			}
		}
	}
}


/**
 * 执行osp命令
 * @param	  const char* szCmd : 已经注册的telnet命令
 * @param	  u8 byCmdLen : 命令长度
 * @return
 * @ref 	  nvrdef.h
 * @see
 * @note
 */
BOOL32 NvrSysOspRunCmd(const char* szCmd, u8 byCmdLen)
{
	NVRSYSMEMAPI();
	ERunCmdState eState = RUNCMD_AUTHORIZED;
	OspRunRegistCmd(szCmd, byCmdLen);
	if(TRUE == OspRunCmdAuthor(&eState))
	{
		if(RUNCMD_NEED_USERNAME == eState)
		    OsApi_Printf( TRUE, FALSE, "%s\n", "username:");
		else if(RUNCMD_NEED_PASSWORD == eState)
		    OsApi_Printf( TRUE, FALSE, "%s\n", "password:");
	}
	return TRUE;
}

/**
 * 设置osp打印回调
 * @param	  nModId   [IN]
 * @param	  pchFilePath  [IN]
 * @return
 * @ref 	  nvrdef.h
 * @see 	  回调函数 原型为void (*fun)(char*)
 * @note
 */
void NvrSysSetOspPrintfCallBack(PFNvrSysOspPrintfCallBack pfCB)
{
	NVRSYSMEMAPI();
	g_pfOspAppPrintf = pfCB;
//	printf("pfCB:%p\n",pfCB);
//	OspSetOspPrintfCallBack(pfCB);
	return;
}

void NvrSysSetStartOspPrintf(BOOL32 bOpen)
{
	g_bOspPrintfStart = bOpen;
}


NVRSTATUS NvrSysSetOspPrintContext(u32 dwConID, BOOL32 bStartOutput)
{
	NVRSYSMEMAPI();
	if(FALSE == bStartOutput)
	{
		char achBye[NVR_MAX_STR16_LEN] = {"bye"};
		g_dwNvrSysConID = 0;
		g_bStartOutputOspPrintf = FALSE;
		OspRunRegistCmd(achBye, strlen(achBye));
	}
	else
	{
		g_dwNvrSysConID = dwConID;
		g_bStartOutputOspPrintf = bStartOutput;		
		ERunCmdState eState = RUNCMD_AUTHORIZED;
		if(TRUE == OspRunCmdAuthor(&eState))
		{
			if(RUNCMD_NEED_USERNAME == eState)
			    OsApi_Printf( TRUE, FALSE, "%s\n", "username:");
			else if(RUNCMD_NEED_PASSWORD == eState)
			    OsApi_Printf( TRUE, FALSE, "%s\n", "password:");
		}		
	}
	return NVR_ERR__OK;
}


NVRSTATUS NvrSysShowOspNode()
{
	NVRSYSDEBUG("g_dwCurPrintListNodeNum:%lu\n", g_dwCurPrintListNodeNum);
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysSDEFtest(u8 bytesttype, u8 byOn, char *achBin)
{
	s32 nRet = 0;
#ifndef _QCOM_
#ifndef _SKYLATE_
	NVRSYSDEBUG("SDEF test, type:%d, byOn:%d !!\n", bytesttype, byOn);
	if(0 == bytesttype)
	{
		nRet = SDEFTaskWhitelistWork(byOn);
		NVRSYSDEBUG("SDEFTaskWhitelistWork ret=%d !!\n");
		
	}
	else if(1 == bytesttype)
	{
		nRet = SDEFTaskWhitelistDebug(byOn);
		NVRSYSDEBUG("SDEFTaskWhitelistDebug ret=%d !!\n");
	}
	else if(2 == bytesttype)
	{
		if(NULL != achBin)
		{
			nRet = SDEFTaskWhitelistAdd(achBin);
			NVRSYSDEBUG("SDEFTaskWhitelistAdd %s ret=%d !!\n", achBin, nRet);
		}
		else
		{
			NVRSYSDEBUG("bin is null !\n");
		}
	}
#endif
#endif
	
	return NVR_ERR__OK;
}



NVRSTATUS NvrGetDsjSysInfo(TNvrSysDsjInfo *ptDsjInfo)
{
#ifndef __I18N__
    //执法仪设备信息，用户信息从jni层获取
    TNvrCapHwCapInfo tHwCapInfoDsj;
    mzero(tHwCapInfoDsj);
    NvrCapGetCapParam(NVR_CAP_ID_HW, &tHwCapInfoDsj);
    if (NVR_DEV_TYPE_DSJ == tHwCapInfoDsj.eDevType)
    {
        TNvrJniMsgInfo tCBInfo;
        mzero(tCBInfo);
        ///设备序列号
        T_DSJ_COM_PARAMEX tzfyParamEx;
        T_DSJ_INFO_LOCAL tParam;
        mzero(tParam);
        mzero(tCBInfo);
        memset(&tzfyParamEx, 0, sizeof(T_DSJ_COM_PARAMEX));
        tzfyParamEx.nMsgID = EV_JNIMSG_DSJ_TYPE_GET_INFO;
        snprintf(tzfyParamEx.chPwd, sizeof(tzfyParamEx.chPwd), "##_kedacom_ipw_##");
        tCBInfo.pData = &tzfyParamEx;
        tCBInfo.wMsgType = EV_NOTIFY_JAVA_MSG;
        tCBInfo.nSize = sizeof(T_DSJ_COM_PARAMEX);
        NvrSysSndJniMsg(&tCBInfo, __FUNCTION__, __LINE__);
        memcpy(&tParam, tzfyParamEx.chData, sizeof(T_DSJ_INFO_LOCAL));
		strcpy(ptDsjInfo->unitNo, tParam.unitNo);
		strcpy(ptDsjInfo->userNo, tParam.userNo);
		strcpy(ptDsjInfo->userName, tParam.userName);
		strcpy(ptDsjInfo->unitName, tParam.unitName);
		strcpy(ptDsjInfo->cSerial, tParam.cSerial);
    }
#endif
	return NVR_ERR__OK;
}

NVRSTATUS NvrSysNotifyGbStateRegistCB(PFNvrSysNotifyGbStateCB pfCallBack)
{
	NVRSYS_ASSERT(pfCallBack);
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYSDEBUG("g_pfGbStateCB:%p\n", g_pfGbStateCB);
	g_pfGbStateCB = pfCallBack;
	return eRet;
}

NVRSTATUS NvrSysAppSubcribeNodeInsertBack(void *pHandle,ENvrTransDataType eTransDataType)
{
    NVRSTATUS eRet = NVR_ERR__OK;

	if (g_tAppSubscribeHeadNode == NULL)
	{
		NVRSYSERR("NvrSysSubcribeNodeInsertBack g_tSubscribeHeadNode is null\n");
        return NVR_ERR__ASSERT;
	}

	OsApi_SemTakeByTime(g_tAppSubscribeHeadNode->hAppSubcribeSem,1000);
    TNvrAppSubscribeNode *p = g_tAppSubscribeHeadNode->pNext;
    TNvrAppSubscribeNode *pNode = NULL;

    pNode = (TNvrAppSubscribeNode *)NVRALLOC(sizeof(TNvrAppSubscribeNode));
    if(NULL == pNode)
    {
        NVRSYSERR("NvrSysSubcribeNodeInsertBack malloc TNvrSubscribeNode failed\n");
		OsApi_SemGive(g_tAppSubscribeHeadNode->hAppSubcribeSem);
        return NVR_ERR__MALLOC_FAILED;
    }

    pNode->pNext = NULL;
	pNode->pHandle = pHandle;
	pNode->eTransDataType = eTransDataType;
 
    if(NULL == g_tAppSubscribeHeadNode->pNext)
    {
        g_tAppSubscribeHeadNode->pNext = pNode;
		OsApi_SemGive(g_tAppSubscribeHeadNode->hAppSubcribeSem);
        return NVR_ERR__OK;
    }

    while(NULL != p)
    {
        if(pHandle == p->pHandle && eTransDataType == p->eTransDataType)
        {
            NVRSYSERR("NvrSysSubcribeNodeInsertBack repeat!!\n");
            NVRFREE(pNode);
            eRet = NVR_ERR__ERROR;
            break;
        }
        if(NULL == p->pNext)
        {
            p->pNext = pNode;
            break;
        }
        else
        {
            p = p->pNext;
        }
    }

	OsApi_SemGive(g_tAppSubscribeHeadNode->hAppSubcribeSem);
    return eRet;
}

void NvrSysSubNodeDump()
{
	NVRSYSDEBUG("NvrSysSubNodeDump Come In\n");
    
    if (g_tAppSubscribeHeadNode == NULL)
	{
		NVRSYSERR("NvrPuiSubcribeNodeInsertBack g_tSubscribeHeadNode is null\n");
        return ;
	}

    TNvrAppSubscribeNode *p = g_tAppSubscribeHeadNode->pNext;

    while(NULL != p)
    {
        NVRSYSDEBUG("NvrSysSubNodeDump Dump: Plate Handle = %p,SubscribeType = %d\n",p->pHandle,p->eTransDataType);

        p = p->pNext;
    }
}

NVRSTATUS NvrSysSetLanEncidBindParam(TNvrSysLanEncIdBindParam *ptLanEncIdBind)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptLanEncIdBind);

	OsApi_SemTake(g_hSysCfgRWSem);
	do
	{
		NVRSYSDEBUG("set lanencidbind param\n");

		g_tNvrSysCfg.tLanEncIdBindParam = *ptLanEncIdBind;

		eRet = NvrSysCfgSave();
		if(NVR_ERR__OK != eRet)
		{
			NVRSYSERR("save cfg failed ret:%d\n", eRet);
		}
	}while(0);
	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

NVRSTATUS NvrSysGetLanEncidBindParam(TNvrSysLanEncIdBindParam *ptLanEncIdBind)
{
	NVRSYSMEMAPI();
	NVRSTATUS eRet = NVR_ERR__OK;
	NVRSYS_ASSERT(ptLanEncIdBind);

	OsApi_SemTake(g_hSysCfgRWSem);

	*ptLanEncIdBind = g_tNvrSysCfg.tLanEncIdBindParam;

	NVRSYSDEBUG("get lanencidbind param\n");

	OsApi_SemGive(g_hSysCfgRWSem);

	return eRet;
}

void gbsubtest()
{
	s8 * pbyBuf = "hh";
	TGBTransDataSubscribeParam tSubscribeParam;
	mzero(tSubscribeParam);
	strcpy(tSubscribeParam.achMsgType,"serial");
	NVRSYSDEBUG("sipsubtest come in!\n");

	NvrSysGbSetSubcribeParam(pbyBuf,tSubscribeParam);
	NVRSYSDEBUG("sipsubtest finish!\n");
}

typedef struct tagDefaultCfgItem
{
	u8 byMode;			// 获取方式 0:key-val，others:key-line
	const s8* szName;	// 字段名
}TDefaultCfgItem;

// 业务从 default_cfg.ini 中获取配置项，用于测试检测配置是否正确
// cmd: getdefaultcfg, nvrlog 5 4 1 打开模块日志级别
void NvrSysGetDefaultCfgItems()
{
	NVRSYSDEBUG("===== start =====\n");

	u32 i = 0;
	u32 dwLen = 0;
	NVRSTATUS eRet = NVR_ERR__OK;
	static s8 achBuf[NVR_MAX_STR1024_LEN] = {0};
	const s8* cfgPath = "/usr/config/default/default_cfg.ini";

	/* ===== 新增的配置项在此后添加 ===== */
	//

	TDefaultCfgItem atItems[] = {
		{ 0, "DeviceType" },
		{ 1, "LIB_APP_NOT_LOAD" },
		{ 0, "CompanySipInfo" },
		{ 0, "CompanyOnvifInfo" },
		{ 0, "OnvifAllDetectCgi" },
		{ 0, "AcceptPortOffset" },
		{ 0, "RealmCfgInfo" },
		{ 0, "DEFAULT_FRAME_RATE" },
		{ 0, "DEFAULT_MAX_KEY_RATE" },
		{ 0, "DEFAULT_ENCTYPE" },
		{ 0, "DEFAULT_FREQUENCY_MODE" },
		{ 0, "SNMP_COMPANY_NAME" },
		{ 0, "COMPANY_SNMP_OID" },
		{ 0, "CUSTOM_VERSION" },
		{ 1, "CustomPlugUrl" },
		{ 0, "VclientProShow" },
		{ 0, "KoreanOsd" },
		{ 0, "IpdtSetEnvIP" },
		{ 0, "TcpBindPortRangeStart" },
		{ 0, "TcpBindPortRangeEnd" },
	};

	u32 dwNum = sizeof(atItems) / sizeof(TDefaultCfgItem);

	for (i = 0; i < dwNum; i++)
	{
		mzero(achBuf);
		if (0 == atItems[i].byMode)
		{
			eRet = NvrCapGetDefaultCfgKeyValue(cfgPath, atItems[i].szName, achBuf, sizeof(achBuf));
		}
		else
		{
			eRet = NvrCapGetDefaultCfgKeyLine(cfgPath, atItems[i].szName, achBuf, sizeof(achBuf));
		}
		dwLen = strlen(achBuf);
		if (dwLen > 0 && ('\r' == achBuf[dwLen-1] || '\n' == achBuf[dwLen-1])) achBuf[dwLen-1] = '\0';
		NVRSYSDEBUG("%s:%s, len:%lu, eRet:%d\n", atItems[i].szName, achBuf, dwLen, eRet);
	}

	NVRSYSDEBUG("===== end =====\n");
}

void sysspacetest(s8 *pchName)
{
	if(!pchName) return;
	
	struct statfs statfsbuf;
	long long unsigned int qwTmpFreeSize = 0;
	
	memset(&statfsbuf, 0, sizeof(statfsbuf));
	///<获取磁盘剩余空间
	if (0 == statfs(pchName, &statfsbuf))
	{
		qwTmpFreeSize = (unsigned int)(((u64)statfsbuf.f_bsize*(u64)statfsbuf.f_bavail));
	}
	NVRSYSDEBUG("qwTmpFreeSize :%llu statfsbuf.f_bavail:%llu  statfsbuf.f_bsize:%llu\n", qwTmpFreeSize, (u64)statfsbuf.f_bavail, (u64)statfsbuf.f_bsize);

}

static void nvrsysdecfile(s8 *pchStr, BOOL32 bEnc)
{
	s8 achTmp[100] = {0};
	s8 achTime[100] = {0};
	s8 achFile[1024] ={0};
	s8 achFilePath[1024] = {0};
	s8 achDstPath[1024] ={0};
	
	s8 achKey[33] ={0};
	s8 achIv[17] ={0};
	s8 achDst[64] ={0};
	TNvrCapSysBasic tNvrCapSysBasic;
	if(NULL == pchStr)
	{
		return;
	}
	mzero(tNvrCapSysBasic);
	NvrCapGetDefCapParam(NVR_CAP_SYS_CAP_ID_BASIC,&tNvrCapSysBasic);
	if(!bEnc)
	{
		
		if (sscanf(pchStr, "%[^_]%*[_log_]%[^.]", achTmp, achTime) == 2) 
		{
			NVRSYSDEBUG("achTmp: %s, achTime: %s\n", achTmp, achTime);

			NvrSysStrEncrypt(achTmp, achDst);			
			snprintf(achKey, sizeof(achKey), "%s", achDst);
			while (strlen(achKey) < 32) {
				strcat(achKey, "0"); // 在末尾补零
			}
			achKey[32] = '\0';
			snprintf(achIv, sizeof(achIv), "%s", achTime);
			NVRSYSDEBUG("strlen(achIv):%d \n", strlen(achIv));
			while (strlen(achIv) < 16) {
				strcat(achIv, "0"); // 在末尾补零
			}
			achIv[16] = '\0';		
			NVRSYSDEBUG("achDevSerialNum %s  len:%d\n", achKey, strlen(achKey));
			NVRSYSDEBUG("achTime %s  len:%d\n", achIv, strlen(achIv));
			
			snprintf(achFile, sizeof(achFile), "%s_log_%s.tar.gz", achTmp, achTime);
			snprintf(achFilePath, sizeof(achFilePath), "%s/%s", tNvrCapSysBasic.achLoghExportPath, pchStr);
			snprintf(achDstPath, sizeof(achDstPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath, achFile);
			NVRSYSDEBUG("achFilePath: %s\n", achFilePath);
			NVRSYSDEBUG("achDstPath: %s\n",achDstPath);
			NVRSYSDEBUG("achKey: %s\n",achKey);
			NVRSYSDEBUG("achTime: %s\n",achIv);

			NvrSysDecptFile(achFilePath, achDstPath, achKey, achIv);
		} 
		else 
		{
			NVRSYSDEBUG("dec file error\n");
		}
	}
	else
	{
		if (sscanf(pchStr, "%[^_]%*[_log_]%[^.]", achTmp, achTime) == 2)
		{
			NVRSYSDEBUG("achTmp: %s, achTime: %s\n", achTmp, achTime);

			NvrSysStrEncrypt(achTmp, achDst);			
			snprintf(achKey, sizeof(achKey), "%s", achDst);
			while (strlen(achKey) < 32) {
				strcat(achKey, "0"); // 在末尾补零
			}
			achKey[32] = '\0';
			snprintf(achIv, sizeof(achIv), "%s", achTime);
			NVRSYSDEBUG("strlen(achIv):%d \n", strlen(achIv));
			while (strlen(achIv) < 16) {
				strcat(achIv, "0"); // 在末尾补零
			}
			achIv[16] = '\0';		
			NVRSYSDEBUG("achDevSerialNum %s  len:%d\n", achKey, strlen(achKey));
			NVRSYSDEBUG("achTime %s  len:%d\n", achIv, strlen(achIv));
			
			snprintf(achFile, sizeof(achFile), "%s_log_%s.bin", achTmp, achTime);
			snprintf(achFilePath, sizeof(achFilePath), "%s/%s", tNvrCapSysBasic.achLoghExportPath, pchStr);
			snprintf(achDstPath, sizeof(achDstPath), "%s/%s", tNvrCapSysBasic.achLoghExportPath, achFile);

			NVRSYSDEBUG("achFilePath: %s\n", achFilePath);
			NVRSYSDEBUG("achDstPath: %s\n",achDstPath);
			NVRSYSDEBUG("achKey: %s\n",achKey);
			NVRSYSDEBUG("achTime: %s\n",achIv);
			
			NvrSysEncptFile(achFilePath, achDstPath, achKey, achIv);
		} 
		else 
		{
			NVRSYSDEBUG("enc file error\n");
		}	
	}
}

static void NvrSysTimeRegfun()
{


#ifdef WIN32
	return;
#else
	OsApi_RegCommand( "sys", (void *)nvrsyshelp, "sys help" );
	OsApi_RegCommand( "sysver", (void *)nvrsystimegetver, "get version");
	OsApi_RegCommand( "getdevinfo", (void *)nvrsysgetdevinfo, "get dev ifno");
	OsApi_RegCommand( "syssetzeroenc", (void *)syssetzeroenc, "syssetzeroenc");
	OsApi_RegCommand( "sysgetzeroenc", (void *)sysgetzeroenc, "sysgetzeroenc");
	OsApi_RegCommand( "syssetudpretran", (void *)syssetudpretran, "syssetudpretran");
	OsApi_RegCommand( "sysgetudpretran", (void *)sysgetudpretran, "sysgetudpretran");
	OsApi_RegCommand( "syssetled", (void *)syssetled, "syssetled");
	OsApi_RegCommand( "sysshutdown", (void *)sysshutdown, "sysshutdown");
	OsApi_RegCommand( "sysgetholiday", (void *)sysgetholiday, "sysshutdown");
	OsApi_RegCommand( "exportcfg", (void *)sysexportcfg, "sysexportcfg");
	OsApi_RegCommand( "inportcfg", (void *)sysinportcfg, "inportcfg");
	OsApi_RegCommand( "getio", (void *)sysgetiotest, "getio");
	OsApi_RegCommandEx( "setpkghead", (void *)NvrSysSetPkgHead, "clear user data and reset pkghead", "s");
	OsApi_RegCommand( "getpkghead", (void *)NvrSysPrintPkgHead, "get cur pkghead");
	OsApi_RegCommandEx( "setdevtype", (void *)NvrSysSetDevType, "set dev type", "s");
	OsApi_RegCommand( "setuserdata", (void *)NvrSysSetUserdataOffset, "set dev userdata : eq:setuserdata 1 1");
	OsApi_RegCommand( "producttest", (void *)NvrSysTestTranData, "Get userdata type");
	OsApi_RegCommand( "getsysprm", (void *)NvrSysGetSysParamTest, "Get userdata type");
    OsApi_RegCommand( "sdcardrwtest", (void *)NvrProductTestSDCardRWDebug, "Sdcard test");
    OsApi_RegCommand( "setpid", (void *)NvrSysSetPid, "set pid");
	OsApi_RegCommand( "otatest", (void *)NvrSysOtaUpgradeTest, "ota test");
	OsApi_RegCommand( "linkled", (void *)NvrSysLinkLedCtrl, "ota test");
	OsApi_RegCommand( "getver", (void *)NvrSysGetVerTest, "get version");
	OsApi_RegCommand( "setfanspeed", (void *)NvrSysSetFanSpeed, "set fan speed %1~100");
	OsApi_RegCommand( "show ospprint list node num", (void *)NvrSysShowOspNode, "ospnodenum");	
	OsApi_RegCommandEx( "setsoftver", (void *)NvrSysSetSoftVer, "setsoftver", "s");			
	OsApi_RegCommand( "setsoft", (void *)NvrSysSetSoftExtendVer, "set soft extend ver");
	OsApi_RegCommand( "testtop", (void *)NvrSysTopPrintTimerCallback, "NvrSysTopPrintTimerCallback");	
	OsApi_RegCommand( "SDEFtest", (void *)NvrSysSDEFtest, "NvrSysTopPrintTimerCallback");
	OsApi_RegCommand( "RegGbCBtest", (void *)NvrSysRegGbCBInit, "NvrSysRegGbCBInit");
	
	OsApi_RegCommand( "sysdump", (void *)NvrSysSubNodeDump, "NvrSysSubNodeDump");
	OsApi_RegCommand( "sipsubtest", (void *)gbsubtest, "sipsubtest");
	OsApi_RegCommand( "syssecu", (void *)NvrSysSetSecurity, "NvrSysSetSecurity");
	OsApi_RegCommand("getdefaultcfg", (void *)NvrSysGetDefaultCfgItems, "list items from default_cfg.ini"); // 业务从 default_cfg.ini 中获取配置项，用于测试检测配置是否正确
	
	OsApi_RegCommand( "spacefunctest", (void *)sysspacetest, "space func test");
	OsApi_RegCommand( "decfile", (void *)nvrsysdecfile, "decfile");
#endif
}

/************************** 系统模块telnet测试接口结束 **************************/


