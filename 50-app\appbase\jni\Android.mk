LOCAL_PATH := $(call my-dir)
SRC_DIR   := ../src

include $(CLEAR_VARS)
# current path, local path jni/
COMN_PATH := $(LOCAL_PATH)/../../../10-common

# Here we give our module name and source file(s)
LOCAL_MODULE    := appbase

LOCAL_C_INCLUDES := ../../10-common/include/cbb/osp 		   \
					../../10-common/include/cbb/debuglog       \
					../../10-common/include/cbb/mxml           \
					../../10-common/include/cbb/protobuf       \
					../../10-common/include/cbb/charconversion \
					../../10-common/include/hal                \
					../../10-common/include/hal/drvlib         \
					../../10-common/include/hal/netcbb         \
					../../10-common/include/hal/ispctrl        \
					../../10-common/include/hal/mediactrl      \
					../../10-common/include/system             \
					../../10-common/include/service            \
					../../10-common/include/app                \
					./include
	
LOCAL_SRC_FILES := $(SRC_DIR)/appbase_utils.c					\
				   $(SRC_DIR)/appbase_utils_unicode.c			\
				   $(SRC_DIR)/appbase_mempool_implement.c		\
				   $(SRC_DIR)/appbase_mempool.c					\
				   $(SRC_DIR)/appbase_task.c					\
				   $(SRC_DIR)/appbase_cross_linux.c				\
				   $(SRC_DIR)/appbase_socket.c					\
				   $(SRC_DIR)/appbase_enum_str_conv.c 			\
					
LOCAL_CFLAGS += -D_LINUX_ 
LOCAL_CFLAGS += -Wall 
LOCAL_CFLAGS += -fno-omit-frame-pointer 
LOCAL_CFLAGS += -D_ANDROID_ 
LOCAL_CFLAGS += -DNO_SERVICE_LAYER
LOCAL_CFLAGS += -D_APP_MEMPOOL_USE_OSP

LOCAL_LDLIBS += -pthread
LOCAL_LDLIBS += -Wl,-Bdynamic -L$(COMN_PATH)/lib/release/x510/  \
				-ldebuglog\
				-losp_edgeos\

include $(BUILD_SHARED_LIBRARY)