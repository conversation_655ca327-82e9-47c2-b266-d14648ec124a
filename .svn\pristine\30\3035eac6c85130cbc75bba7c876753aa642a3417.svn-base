#ifndef _KNET_CMD_NETUTILS_H
#define _KNET_CMD_NETUTILS_H

#define DOMAIN_LEN_MAX 8

enum FwOperations {
    FwClean = 0,
    FwAsWhiteList,
    FwAsBlackList,
};

enum ConflictType {
    ConflictIp = 1,
    ConflictMac,
};

enum PingCodes {
    PingReply = 0,
    PingTimeout,
    PingError,
    PingNetUnreach,
};

typedef struct {
		int32_t byEthId;
		int64_t dwBandSended;/* (bit/s) 1Gbit/s == 1000Mbit/s == 1000*1000kbit/s == 1000*1000*1000 bit/s */
		int64_t dwBandRecved;/* (bit/s) */
}net_dev_stat;


typedef void(*ip_conflict_cb_t)(uint32_t type, uint8_t macAddr[], char* ipAddr);

typedef void (*get_net_stat_cb_t)(net_dev_stat aptdevStat[], int32_t dwEthNum);


int32_t RegisterIpConflictCallback(ip_conflict_cb_t invokeRoutine);
int32_t CheckIfIpConflict(const char *ip, uint32_t timeout, bool *result);

typedef struct {
	int32_t mPktSizes;
	int32_t mTimeouts;
	int32_t mMaxTTL;
	int32_t mCount;
} ping_opt_t;

typedef struct {
    int32_t mResponseCode;
    int32_t mTTL;
    int32_t mTripTime;
    int32_t mSeq;
    int32_t mUserId;
    int32_t mErrorType;
    int32_t mErrorCode;
} ping_result_t;

typedef int(*ping_cb_t)(ping_result_t* result);

bool IpPingStart(char* destIp, ping_opt_t* option, int32_t userId, ping_cb_t invokeRoutine);

bool IpPingStop(int32_t userId);

typedef int(*ntpc_cb_t)(uint32_t msgId, void* msgData);

int32_t NtpcCheckServer(char* serverIp);
int32_t NtpcStart(char* serverIp, uint32_t syncInterval, ntpc_cb_t invokeRoutine);
int32_t NtpcStop();
int32_t NetGetStatStart(int32_t syncInterval,get_net_stat_cb_t  state);
int32_t NetGetStatStop( );

/*
 * trace route API structure
 */
typedef struct {
    char    mDstIp[IP_ADDR_SIZE];
    uint32_t mTtl;
} trace_route_param;

typedef struct {
    int32_t result;
    int32_t ttl;
    uint32_t tripTime;
    char ip[IP_ADDR_SIZE];
    int32_t userId;
    int32_t errType;
    int32_t errCode;
    char achDomName[DOMAIN_LEN_MAX];
} trace_result_t;

typedef int(*trace_cb_t)(trace_result_t *result);

int32_t TraceRouteStart(trace_route_param *para, int32_t userId, trace_cb_t invokeRoutine);
int32_t TraceRouteStop(int32_t userId);

int32_t ChangeNetPassword(const char* username, const char* passwd);
int32_t NetUserAdd(const char* username, const char* passwd);
int32_t NetUserDel(const char* username);

int32_t ConfigFirewall(FwOperations ops, unsigned long reserved,
                       const char *addrs[], int32_t addrNum);

/*
 * @param dscp (0-63)
 */
int32_t SetDscp(int32_t sock, uint8_t dscp);

#endif
