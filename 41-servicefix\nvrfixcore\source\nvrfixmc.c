/**
* @file 	NvrFixMc.c
* @brief    本地cameral mc模块
* <AUTHOR>
* @date 	2021-3-26
* @version  1.0
*/
#include "nvrfixcore.h"
#include "nvrfixdev.h"
#include "lcamclt.h"
#include "isp_interface.h"
#include "nvrfixcfg.h"
#include "nvrfixmc.h"
#include "nvrfixisp.h"

TNvrCapLcamVidEncChn g_tVidEncChn;

static s32 __TeleZoomLeveltoInRatio(PTNvrIspVidTeleZoomCfg ptTeleZoomCfg)
{
    s32 nInRatio = 0;
    switch (ptTeleZoomCfg->eLevel)
    {
        case NVR_TELE_ZOOM_LEVEL_VERYHIGH:
        {
            nInRatio = 600;
            break;
        }

        case NVR_TELE_ZOOM_LEVEL_HIGH:
        {
            nInRatio = 450;
            break;
        }

        case NVR_TELE_ZOOM_LEVEL_MIDDLE:
        {
            nInRatio = 300;
            break;
        }

        case NVR_TELE_ZOOM_LEVEL_LOW:
        {
            nInRatio = 150;
            break;
        }

        default:
        {
            nInRatio = ptTeleZoomCfg->nTeleZoomInRatio;
            break;
        }

    }

    return nInRatio;
}

NVRSTATUS NvrFixMcSetLdcParam(u16 wChnId,TNvrIspVidLdcParam tLdc)
{
    u32 dwSensorNum = 0;
    EMediaCtrlVidChnType eMediaCtrlVidChnType = MEDIACTRL_VID_CHN_VIN_SENSOR_0;
    TNvrCapLcam tNvrCapLcam;
    TMediaCtrlLdcParam tVidLdcParam;
    s32 nRet = 0;
    TLcamIspParam tLcamIspParam;

    mzero(tLcamIspParam);
    mzero(tNvrCapLcam);
    mzero(tVidLdcParam);

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
    LcamIspGetParam(wChnId, &tLcamIspParam);

    if (wChnId >= tNvrCapLcam.tLcamMcInfo.byLcamChnNum)
    {
        MCMEMNOTICE("chnid invalid:%d", wChnId);
        return NVR_ERR__ERROR;
    }

    eMediaCtrlVidChnType = (EMediaCtrlVidChnType)tNvrCapLcam.tLcamMcInfo.atVidCap[wChnId].dwMediaCtrlCapChn;

    dwSensorNum = eMediaCtrlVidChnType;
    dwSensorNum -= MEDIACTRL_VID_CHN_VIN_BASE;

    tVidLdcParam.bEnable = NVR_ISP_VID_LDC_OPEN == tLdc.eSwitch ? TRUE : FALSE;
    tVidLdcParam.dwRatio = tLdc.dwRatio1;

    PRINTDBG("wChnId:%u,bEnable:%u,dwRatio:%u\n", wChnId, tVidLdcParam.bEnable, tVidLdcParam.dwRatio);

    if (tNvrCapLcam.tLcamIspInfo.atLcamIsp[wChnId].tVidTeleZoom.bSupport)
    {
        tVidLdcParam.bZoomEnable = NVR_TELE_ZOOM_MODE_OPEN == tLcamIspParam.tVidTeleZoom.eMode ? TRUE : FALSE;
        tVidLdcParam.nTeleZoomInRatio = __TeleZoomLeveltoInRatio(&tLcamIspParam.tVidTeleZoom);
        PRINTDBG("telezoom bEnable:%u,dwRatio:%u\n", tVidLdcParam.bZoomEnable, tVidLdcParam.nTeleZoomInRatio);
    }

    nRet = MediaCtrlSetLdcParam(dwSensorNum, &tVidLdcParam);
    if (nRet != 0)
    {
        PRINTERR("MediaCtrlSetLdcParam error,nRet %d\n", nRet);
        return NVR_ERR__ERROR;
    }

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixMcSetTeleZoomParam(u16 wChnId, TNvrIspVidTeleZoomCfg tTeleZoomCfg)
{
    u32 dwSensorNum = 0;
    EMediaCtrlVidChnType eMediaCtrlVidChnType = MEDIACTRL_VID_CHN_VIN_SENSOR_0;
    TNvrCapLcam tNvrCapLcam;
    TMediaCtrlLdcParam tVidLdcParam;
    s32 nRet = 0;
    TLcamIspParam tLcamIspParam;

    mzero(tLcamIspParam);
    mzero(tNvrCapLcam);
    mzero(tVidLdcParam);

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
    LcamIspGetParam(wChnId, &tLcamIspParam);

    if (wChnId >= tNvrCapLcam.tLcamMcInfo.byLcamChnNum)
    {
        MCMEMNOTICE("chnid invalid:%d", wChnId);
        return NVR_ERR__ERROR;
    }

    eMediaCtrlVidChnType = (EMediaCtrlVidChnType)tNvrCapLcam.tLcamMcInfo.atVidCap[wChnId].dwMediaCtrlCapChn;

    dwSensorNum = eMediaCtrlVidChnType;
    dwSensorNum -= MEDIACTRL_VID_CHN_VIN_BASE;

    tVidLdcParam.bZoomEnable = NVR_TELE_ZOOM_MODE_OPEN == tTeleZoomCfg.eMode ? TRUE : FALSE;
    tVidLdcParam.nTeleZoomInRatio =  __TeleZoomLeveltoInRatio(&tTeleZoomCfg);

    PRINTDBG("tele zoom wChnId:%u, telezoom bEnable:%u, dwRatio:%u\n", wChnId, tVidLdcParam.bZoomEnable, tVidLdcParam.nTeleZoomInRatio);

    if (tNvrCapLcam.tLcamIspInfo.atLcamIsp[wChnId].tVidLdc.bSupport)
    {
        tVidLdcParam.bEnable = NVR_ISP_VID_LDC_OPEN == tLcamIspParam.tVidLdc.eSwitch ? TRUE : FALSE;
        tVidLdcParam.dwRatio = tLcamIspParam.tVidLdc.dwRatio1;
        PRINTDBG("ldc bEnable:%u,dwRatio:%u \n", tVidLdcParam.bEnable, tVidLdcParam.dwRatio);
    }

    nRet = MediaCtrlSetLdcParam(dwSensorNum, &tVidLdcParam);
    if (nRet != 0)
    {
        PRINTERR("MediaCtrlSetLdcParam error,nRet %d\n", nRet);
        return NVR_ERR__ERROR;
    }

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixMcSetStablizerByCfg(u16 wChnId,TNvrIspStablizer	tStablizer,TNvrIspStablizer	tSrcStablizer)
{
#ifdef _CV2X_             
	u32 dwParam = 0;
	s32 nRet = 0;
	
	///<用于安霸平台陀螺仪防抖功能判断当前是否只是修改了防抖等级（只是修改防抖等级不需要重启，修改陀螺仪防抖开关需要重启）
	if(tStablizer.dwVideoStablizerLevel != tSrcStablizer.dwVideoStablizerLevel && tStablizer.eVideoStablizerMode == tSrcStablizer.eVideoStablizerMode)	///<只是修改防抖等级，不需要重启
	{   
        TMediaCtrlDisParam tDisParam;
        mzero(tDisParam);

		NvrFixIspSetKeyParam(wChnId,NVR_FIX_ISP_ACT_GET_FOCAL_LENGTH,&dwParam);

        tDisParam.dwDisLevel = tStablizer.dwVideoStablizerLevel;
        tDisParam.fHorFov = (float)dwParam;

        if (NVR_ISP_VIDEO_STABLIZER_CLOSE == tStablizer.eVideoStablizerMode)
        {
            tDisParam.bDisEnable = FALSE;
        }
        else if (NVR_ISP_VIDEO_STABLIZER_GYRO_ENABLE == tStablizer.eVideoStablizerMode)
        {
            tDisParam.bDisEnable = TRUE;
        }
        PRINTDBG("Stablizer bDisEnable:%u, StablizerLevel:%u, FocalLength:%f\n",tDisParam.bDisEnable, tDisParam.dwDisLevel, tDisParam.fHorFov);
        nRet = MediaCtrlSetDisParam(wChnId, &tDisParam);
		if(nRet != 0)
		{
        	PRINTERR("MediaCtrlSetDisParam error,nRet:%d\n",nRet);
			return NVR_ERR__ERROR;
		}
	}
	else	///<开关陀螺仪防抖，需要重启
	{
		PRINTDBG("DstMode %u,SrcMode %u,Start Reboot\n",tStablizer.eVideoStablizerMode,tSrcStablizer.eVideoStablizerMode);
		///<安霸平台陀螺仪防抖会影响AF，在媒控初始化的时候就需要通知AF当前陀螺仪防抖是否开启，所以不支持动态切换，需要重启生效
		NvrSysReboot(NULL);
	}

#endif

	return NVR_ERR__OK;
}

NVRSTATUS NvrFixMcSetStablizerByZoom(u16 wChnId,u32 dwZoom)
{
#ifdef _CV2X_           
    TLcamIspParam tIspParam;
	TNvrCapFixInernalCapInfo tFixInterCapInfo;
	s32 nRet = 0;
	
	LcamIspGetParam(wChnId, &tIspParam);

	///<每次调整zoom都需要重新设置防抖参数
	if (NVR_ISP_VIDEO_STABLIZER_GYRO_ENABLE == tIspParam.tStablizer.eVideoStablizerMode)
	{
		u32 dwZoomRatio = dwZoom;
		TMediaCtrlDisParam tDisParam;
		mzero(tDisParam);

		NvrCapGetFixInterCapParam(&tFixInterCapInfo);

		NvrFixIspSetKeyParam(wChnId,NVR_FIX_ISP_ACT_GET_APPOINTED_ZOOM_RATIO,&dwZoomRatio);

		tDisParam.bDisEnable = TRUE;
		tDisParam.dwDisLevel = tIspParam.tStablizer.dwVideoStablizerLevel;
		tDisParam.bDisStillEnable = FALSE;
		tDisParam.fHorFov = (float)(tFixInterCapInfo.tFixHwInternalCap.dwLensFocalLength*dwZoomRatio)/100.0;

        PRINTDBG("Stablizer bDisEnable:%u, StablizerLevel:%u, FocalLength:%f,dwZoomRatio %u,dwZoom %u\n",tDisParam.bDisEnable, tDisParam.dwDisLevel, tDisParam.fHorFov,dwZoomRatio,dwZoom);
		nRet = MediaCtrlSetDisParam(wChnId, &tDisParam);
		if(nRet != 0)
		{
        	PRINTERR("MediaCtrlSetDisParam error,nRet:%d\n",nRet);
			return NVR_ERR__ERROR;
		}
	}
#endif

	return NVR_ERR__OK;
}


static void NvrFixMcSetCrop(BOOL32 bCrop, u16 wEncId, u16 x, u16 y, u16 w, u16 h)
{
    TMediaCtrlCropParam tMediaCtrlCropParam;
    mzero(tMediaCtrlCropParam);
    tMediaCtrlCropParam.bCrop = bCrop;
    tMediaCtrlCropParam.tCropRect.wStartX = x;
    tMediaCtrlCropParam.tCropRect.wStartY = y;
    tMediaCtrlCropParam.tCropRect.wWidth = w;
    tMediaCtrlCropParam.tCropRect.wHeight = h;
    MediaCtrlSetVidEncCropParam(wEncId, &tMediaCtrlCropParam);
}

NVRSTATUS NvrFixMcSetCustomCropParam(u16 wChnId, TLcamMcCustomCropChn *ptCfg)
{
    static TNvrCapFixInernalCapInfo tFixInterCapInfo;  mzero(tFixInterCapInfo);
    static TNvrPuiVidEncParam tNvrPuiVidEncParam;  mzero(tNvrPuiVidEncParam);
    static TNvrCapLcam tNvrCapLcam;  mzero(tNvrCapLcam);
    static TNvrCapDefLcamCfg tLcamDefCfg;  mzero(tLcamDefCfg);

    NvrCapGetDefCfgParam(NVR_CAP_DEF_CFG_ID_LCAM, &tLcamDefCfg);
    NvrCapGetFixInterCapParam(&tFixInterCapInfo);

    //当前辅流分辨率
    NvrPuiGetDevParam(wChnId, 1, NVR_PUI_VID_ENC_PARAM, &tNvrPuiVidEncParam);
    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
    //设置辅流编码分辨率能力,自定义裁剪使能时固定1080P
    if(ptCfg->bEnable)
    {
        //变更辅流支持的分辨率列表
        tNvrCapLcam.tLcamMcInfo.atVidEnc[wChnId].atVidEncChn[1].dwResNumSup = 1;
        tNvrCapLcam.tLcamMcInfo.atVidEnc[wChnId].atVidEncChn[1].atResInfo[0].bySup = 1;
        tNvrCapLcam.tLcamMcInfo.atVidEnc[wChnId].atVidEncChn[1].atResInfo[0].tResParam.wWidth = tFixInterCapInfo.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wWidth;
        tNvrCapLcam.tLcamMcInfo.atVidEnc[wChnId].atVidEncChn[1].atResInfo[0].tResParam.wHeight = tFixInterCapInfo.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wHeight;

        //变更当前辅流编码分辨率
        tNvrPuiVidEncParam.wVidWide = tFixInterCapInfo.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wWidth;
        tNvrPuiVidEncParam.wVidHeight = tFixInterCapInfo.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wHeight;
        tNvrPuiVidEncParam.wBitRate = tFixInterCapInfo.tFixSoftInternalCap.tCustormCropCap.wBitRate;
    }
    else
    {
        //恢复辅流支持的分辨率列表
        memcpy(&tNvrCapLcam.tLcamMcInfo.atVidEnc[wChnId].atVidEncChn[1], &g_tVidEncChn, sizeof(g_tVidEncChn));

        //恢复当前辅流编码分辨率为默认分辨率
        tNvrPuiVidEncParam.wVidWide = tLcamDefCfg.tLcamMc.atVidEnc[wChnId][1].wResWidth;
        tNvrPuiVidEncParam.wVidHeight = tLcamDefCfg.tLcamMc.atVidEnc[wChnId][1].wResHeight;
        tNvrPuiVidEncParam.wBitRate = tLcamDefCfg.tLcamMc.atVidEnc[wChnId][1].wBitRate;
    }

    NvrCapSetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
    NvrPuiSetDevParam(wChnId, 1, NVR_PUI_VID_ENC_PARAM, &tNvrPuiVidEncParam);

    //保存裁剪参数
    LcamMcSetCustomCropParam(wChnId, ptCfg);
    //设置媒控裁剪
    NvrFixMcSetCrop(ptCfg->bEnable, 1, ptCfg->tRegion.wStartX, ptCfg->tRegion.wStartY, ptCfg->tRegion.wWidth, ptCfg->tRegion.wHeight);

    return NVR_ERR__OK;
}

NVRSTATUS NvrFixMcGetCustomCropParam(u16 wChnId, TLcamMcCustomCropChn *ptCfg)
{
    return LcamMcGetCustomCropParam(wChnId, ptCfg);
}

NVRSTATUS NvrFixMcModuleInit()
{
	printf("NvrFixMcModuleInit suc\n");

	TMediaCtrlDualChnOverlay tOverLay;
	TNvrCapFixInernalCapInfo tInterCap;
	TNvrFixCfg tFixCfg;
    static TNvrCapLcam tNvrCapLcam;
    static TLcamMcCustomCropChn tLcamMcCustomCropChn;

    OsApi_RegCommand( "fixcrop", (void *)NvrFixMcSetCrop, "fixcrop" );

		//<开启热成像叠加
	memset(&tOverLay, 0, sizeof(TMediaCtrlDualChnOverlay));
	memset(&tFixCfg,0,sizeof(TNvrFixCfg));
	memset(&tInterCap,0,sizeof(TNvrCapFixInernalCapInfo));
    mzero(tNvrCapLcam);
    mzero(g_tVidEncChn);

	NvrFixCfgGetParam(&tFixCfg);
	NvrCapGetFixInterCapParam(&tInterCap);
    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);

    memcpy(&g_tVidEncChn, &tNvrCapLcam.tLcamMcInfo.atVidEnc[0].atVidEncChn[1], sizeof(g_tVidEncChn));

    if(tInterCap.tFixSoftInternalCap.tCustormCropCap.bEncDoubleCropSup)
    {
        NvrFixMcGetCustomCropParam(0, &tLcamMcCustomCropChn);
        if(tLcamMcCustomCropChn.tRegion.wHeight == 0 || tLcamMcCustomCropChn.tRegion.wWidth == 0)
        {
            tLcamMcCustomCropChn.tRegion.wWidth = tInterCap.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wWidth * tInterCap.tFixSoftInternalCap.tCustormCropCap.wCanvasWidth / tNvrCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wWidth;
            tLcamMcCustomCropChn.tRegion.wHeight = tInterCap.tFixSoftInternalCap.tCustormCropCap.tCroupEncRes.wHeight * tInterCap.tFixSoftInternalCap.tCustormCropCap.wCanvasHeight / tNvrCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wHeight;
        }
        NvrFixMcSetCustomCropParam(0, &tLcamMcCustomCropChn);
    }

	if(tInterCap.tFixOverlayCap.bSup)
	{
		tOverLay.bEnable = TRUE;
		tOverLay.dwDstEncChn = 0;
		tOverLay.dwSrcEncChn = 4;

		if(tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType == NVR_PUI_THER_UP_LEFT_POS)
		{
			tOverLay.tRegionParam.wPosX = 0;
			tOverLay.tRegionParam.wPosY  =0;
		}

		if(tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType == NVR_PUI_THER_UP_RIGHT_POS)
		{
			tOverLay.tRegionParam.wPosX = 1920-800;
			tOverLay.tRegionParam.wPosY  =0;
		}

		if(tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType == NVR_PUI_THER_DOWN_LEFT_POS)
		{
			tOverLay.tRegionParam.wPosX = 0;
			tOverLay.tRegionParam.wPosY  =1080-600;
		}

		if(tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType == NVR_PUI_THER_DOWN_RIGHT_POS)
		{
			tOverLay.tRegionParam.wPosX = 1920-800;
			tOverLay.tRegionParam.wPosY  =1080-600;
		}

		MediaCtrlSetDualChnOverlay(0, &tOverLay);
	}
	
	return NVR_ERR__OK;
}


