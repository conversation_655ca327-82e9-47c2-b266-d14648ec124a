#ifndef _MANFACEFEATURE_H_
#define _MANFACEFEATURE_H_

#include "VideoAlg.h"


typedef struct tagManFaceFeatureOpen
{
    EMImageFormat emFormat;
    char s8GPUBinPath[200];
    char s8ManRecogBinPath[200];
}TKEDAManFaceFeatureOpen;

// roi info (input)
typedef struct tagManFaceFeatureinfo	//人员检测的范围（是否需要使用待确认）
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
    float x[5];
    float y[5];
}TManFaceFeatureInfo;



typedef struct tagManFaceFeatureOutput
{
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo;

#endif