#!/bin/sh
#set -x
path="../../10-common/version/compileinfo/nvrlib_netra8107.txt"
date>>$path

module_name=$(basename $PWD)
cd ./prj_linux

echo ==============================================
echo =      "$module_name"_linux for netra8107           =
echo ==============================================

echo "============compile lib$module_name netra8107============">>../$path

make -e DEBUG=0 -f makefile_netra8107 clean
make -e DEBUG=0 -f makefile_netra8107 2>&1 1>/dev/null |tee -a ../$path


cd ..

