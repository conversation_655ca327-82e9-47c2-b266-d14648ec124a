#ifndef GPU_BC_H
#define GPU_BC_H
#include "VideoAlg.h"

typedef struct tagBCOpen
{
	unsigned int u32InputWidth;                // ����ͼ������
	unsigned int u32InputHeight;               // ����ͼ���߶�
	unsigned int u32InputPitchY;               // ����ͼ��Y ��������
	unsigned int u32InputPitchUV;             // ����ͼ��UV��������
	unsigned int u32OutputWidth;              // ����ͼ������
	unsigned int u32OutputHeight;             // ����ͼ���߶�

	unsigned int flagROIMode;					//0Ϊ�����ţ�1Ϊ�ȱ������ţ���ѡ���򰴱����ü��������Ķ������ź�����;2Ϊ��ѡ��ֱ�Ӷ�Ӧ����
	float f32x0;							//ROI�������ϽǶ�Ӧԭͼ�ĺ������Ĺ�һ������
	float f32y0;							//ROI�������ϽǶ�Ӧԭͼ���������Ĺ�һ������
	float f32w;								//ROI�������ȶ�Ӧԭͼ�Ŀ��ȵĹ�һ������
	float f32h;								//ROI�����߶ȶ�Ӧԭͼ�ĸ߶ȵĹ�һ������

	unsigned int u32SharpThre;                 // ����ϵ��
	unsigned int u32Ratio1;
	unsigned int u32Ratio2;
	EMImageFormat emFormat;
}TBCOpen;


// ------------------------------------�㷨ģ���ṹ��---------------------------
// --------------------------Զ�˷Ŵ��㷨�ṹ��----------------------------
// Զ�˷Ŵ�����
typedef struct tagBC_VSParam
{
	int s32BarrelParam1;               //��������1
	int s32BarrelParam2;               //��������2	
	int s32TeleZoomInRatio;            //���ű���
	int s32TeleCurvLevel;              //�ϲ�����
	int s32TeleMultVal;                //�ϲ�б�ʳ���
	int s32WideRatio;                  //�²�����
	int s32MidWidth;                   //����У����������
	int s32MidHeight;                  //����У�������߶�
	int s32AdjHeight;                  //Զ�˷Ŵ�������ʼ��
	float f32WidthScale;               //������Ӧ���ű���
}TBC_VSParam;

// Զ�˷Ŵ���ʼ���ṹ��
typedef struct tagBC_VSOpen
{
	unsigned int u32InputWidth;                // ����ͼ������
	unsigned int u32InputHeight;               // ����ͼ���߶�
	unsigned int u32InputPitchY;               // ����ͼ��Y ��������
	unsigned int u32InputPitchUV;              // ����ͼ��UV��������
	unsigned int u32OutputWidth;               // ����ͼ������
	unsigned int u32OutputHeight;              // ����ͼ���߶�
	EMImageFormat emFormat;                    // ͼ����ʽ
	TBC_VSParam tBC_VSParam;                   // ���ڲ���
}TBC_VSOpen;
// -----------------------------------------------------------------------

typedef struct tagBarrelCorrectionOpen
{
	TBCOpen tBCOpen;
	TBC_VSOpen tBC_VSOpen;
	int flag;                                             // ��flag=0ʱ�����û���У������
									// ��flag = 1ʱ������Զ�˷Ŵ�����
	char s8BinaryPath[200];
}TBarrelCorrectionOpen;

#endif
