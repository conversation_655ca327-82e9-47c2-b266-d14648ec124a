#ifndef AGENT_CALLBACKS_H
#define AGENT_CALL<PERSON>CKS_H

#define SNMPD_CALLBACK_ACM_CHECK         0
#define SNMPD_CALLBACK_REGISTER_OID      1
#define SNMPD_CALLBACK_UNREGISTER_OID    2
#define SNMPD_CALLBACK_REG_SYSOR         3
#define SNMPD_CALLBACK_UNREG_SYSOR       4
#define SNMPD_CALLBACK_ACM_CHECK_INITIAL 5
#define SNMPD_CALLBACK_SEND_TRAP1        6
#define SNMPD_CALLBACK_SEND_TRAP2        7
#define SNMPD_CALLBACK_REGISTER_NOTIFICATIONS 8
#define SNMPD_CALLBACK_PRE_UPDATE_CONFIG 9
#define SNMPD_CALLBACK_INDEX_START	 10
#define SNMPD_CALLBACK_INDEX_STOP	 11
#define SNMPD_CALLBACK_ACM_CHECK_SUBTREE 12
#define SNMPD_CALL<PERSON>CK_REQ_REG_SYSOR     13
#define SNMPD_CALLBACK_REQ_UNREG_SYSOR   14
#define SNMPD_CALLBACK_REQ_UNREG_SYSOR_SESS 15

#endif                          /* AGENT_CALLBACKS_H */
