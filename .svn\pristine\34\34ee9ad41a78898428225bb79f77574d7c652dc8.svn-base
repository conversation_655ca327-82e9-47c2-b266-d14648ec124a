path="../../10-common/version/compileinfo/nvrlib_his3536.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_cap_linux for his3536             =
echo ==============================================

echo "============compile libnvrcap his3536============">>../$path

make -j4 -e DEBUG=0 -f makefile_his3536 clean
make -j4 -e DEBUG=0 -f makefile_his3536 2>>../$path

cp -L -r -f libnvrcap.so ../../../10-common/lib/release/his3536/

make -j4 -e DEBUG=0 -f makefile_his3536_cust clean
make -j4 -e DEBUG=0 -f makefile_his3536_cust 2>>../$path
cp -L -r -f libnvrcap.so ../../../10-common/lib/release/his3536/capedgeos/

cd ..
