

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := basicintellialgctrl


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _CV2X_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

C_SRC_LIST := $(foreach dir,$(SRC_DIR), $(wildcard $(dir)/*.c))
OBJS := $(basename $(C_SRC_LIST))

## Libraries to include in shared object file
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
        $(CURDIR)/../../lcamclt/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
     	$(CURDIR)/../../../10-common/include/cbb/wmf \
     	$(CURDIR)/../../../40-service/lcamclt/include
		
CFLAGS += -D_CV2X_

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

INSTALL_LIB_PATH = ../../../10-common/lib/release/cv2x

LDFLAGS += -L$(INSTALL_LIB_PATH)
LDFLAGS += -lwmf
LDFLAGS += -lturbojpeg

include $(COMM_DIR)/common.mk
