path="../../10-common/version/compileinfo/basicintellialgctrl_cv2x.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_basicintellialgctrl_linux for cv2x    =
echo ==============================================

echo "============compile libbasicintellialgctrl cv2x============">>../$path

make -e DEBUG=0 -f makefile_cv2x clean
make -e DEBUG=0 -f makefile_cv2x 2>>../$path

cd ..
