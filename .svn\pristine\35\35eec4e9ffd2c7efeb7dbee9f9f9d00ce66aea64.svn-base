

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrproto
ARC_TARGET	      := nvrproto

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _AX603A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := \
	$(SRC_DIR)/nvrhttpscfg.pb-c \
	$(SRC_DIR)/nvrosdcfg.pb-c \
	$(SRC_DIR)/nvrais.pb-c \
	$(SRC_DIR)/nvrlcammccfg.pb-c \
	$(SRC_DIR)/nvrispcfg.pb-c \
	$(SRC_DIR)/nvrwifimgr.pb-c \
	$(SRC_DIR)/nvrmbnetwork.pb-c \
	$(SRC_DIR)/nvrpui.pb-c \
	$(SRC_DIR)/nvrmpu.pb-c \
	$(SRC_DIR)/nvrsmtp.pb-c \
	$(SRC_DIR)/nvrdev.pb-c \
	$(SRC_DIR)/dmsrv.pb-c \
	$(SRC_DIR)/nvrrec.pb-c \
	$(SRC_DIR)/nvrlog.pb-c \
	$(SRC_DIR)/nvralarm.pb-c \
	$(SRC_DIR)/nvrcap.pb-c \
	$(SRC_DIR)/nvrsys.pb-c \
	$(SRC_DIR)/nvrnetwork.pb-c \
	$(SRC_DIR)/nvrusrmgr.pb-c \
	$(SRC_DIR)/nvrerrno.pb-c \
	$(SRC_DIR)/nvrftp.pb-c \
	$(SRC_DIR)/aisvapd.pb-c \
	$(SRC_DIR)/nvrdynamicplugin.pb-c\
	$(SRC_DIR)/lcambasicintel.pb-c\
	$(SRC_DIR)/nvrgeo.pb-c\
	$(SRC_DIR)/nvrbluetooth.pb-c\
	$(SRC_DIR)/nvrdhcpserver.pb-c\
	$(SRC_DIR)/nvrextdev.pb-c\
	$(SRC_DIR)/nvrunifiedlog.pb-c \

## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/system \
		$(CURDIR)/../../../10-common/include/cbb/protobuf \

CFLAGS += -D_AX603A_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/ax603a
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


