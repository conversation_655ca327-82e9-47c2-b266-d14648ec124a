/******************************************************************************
模块名      ： alg_public_drawLine
文件名      ： alg_public_drawLine.h
相关文件    ： alg_public_drawLine.h
文件实现功能： NV12图像画线BangC实现库的公共类型和宏定义
-------------------------------------------------------------------------------
修改记录:
日  期        版本        修改人        走读人    修改内容
2021/01/14    1.0         张鑫垒                    创建
******************************************************************************/
#ifndef _ALG_PUBLIC_DRAWLINE_H_
#define _ALG_PUBLIC_DRAWLINE_H_

#if defined (ARCH_X64_LINUX) || defined (ARCH_X86_LINUX)
#define Y_CHANNEL_API
#else
#ifdef CVEHATTRIBUTE_EXPORTS
#define Y_CHANNEL_API __declspec(dllexport)
#else
#define Y_CHANNEL_API __declspec(dllimport)
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif
#include "ai_defs.h"

#define MAX_LIN_NUM 400                         // 每张图片最大的画线数量


typedef struct TMluDrawLineInput{
    u32 u32StructSize;

    void* pvMluY;                                // Y通道
    void* pvMluUV;                               // UV通道
    l32 l32Stride;                               // 原图的Stride、宽高
    l32 l32SrcW;                                    
    l32 l32SrcH;
    l32 al32LineRGBValue[3];                     // 画的线R、G、B三个值
    l32 l32LineNum;                              // 画的线的数量
    TRect atImgPos[MAX_LIN_NUM];                 // 线的左顶点坐标及宽高
}TMluDrawLineInput;

typedef struct TMluDrawLineOutput{
    u32 u32StructSize;

    void* pvOutY;                             
    void* pvOutUV;                             
}TMluDrawLineOutput;

typedef struct TMluDrawLineParam{
    u32 u32StructSize;

    l32 l32GpuDevID;                               // GPU的ID
    l32 l32MaxHeight;                              // 图像的最大宽高 
    l32 l32MaxWidth;
}TMluDrawLineParam;

/*=========================== common ========================*/
typedef enum
{
	DL_OPEN_ERR,								    //DrawLineOpen
	DL_HANDLE_ERR,							        //参数错误，DrawLineOpen输入句柄为空
	DL_PARAMS_ERR,							        //参数错误，图像宽高必须是偶数
};
/*===========================================================*/

Y_CHANNEL_API l32 DrawLineOpen(void **ppvHandle, TMluDrawLineParam *pvParam);
Y_CHANNEL_API l32 DrawLineProcess(void *pvHandle, void *pvInput, void *pvOutput);
Y_CHANNEL_API l32 DrawLineClose(void *pvHandle);

#ifdef __cplusplus
}
#endif
#endif