path="../../10-common/version/compileinfo/nvrlib_his3559a.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_usrmgr_linux for his3559a         =
echo ==============================================

echo "============compile libnvrusrmgr his3559a============">>../$path

make -e DEBUG=0 -f makefile_his3559a clean
make -e DEBUG=0 -f makefile_his3559a 2>>../$path

cp -L -r -f libnvrusrmgr.so ../../../10-common/lib/release/his3559a/

echo "============compile libnvrusrmgr_ipdt his3559a============">>../$path

make -e DEBUG=0 STATIC=0 -f makefile_his3559a_ipdt clean
make -e DEBUG=0 STATIC=0 -f makefile_his3559a_ipdt 2>>../$path

make -e DEBUG=0 STATIC=1 -f makefile_his3559a_ipdt clean
make -e DEBUG=0 STATIC=1 -f makefile_his3559a_ipdt 2>>../$path

cp -L -r -f libnvrusrmgr_ipdt.so ../../../10-common/lib/release/his3559a/
cp -L -r -f libnvrusrmgr_ipdt.a ../../../10-common/lib/release/his3559a/
cd ..
