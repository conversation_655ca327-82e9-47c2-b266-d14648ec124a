/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrunifiedlog.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrunifiedlog.pb-c.h"
void   tpb_nvr_unified_log_info__init
                     (TPbNvrUnifiedLogInfo         *message)
{
  static TPbNvrUnifiedLogInfo init_value = TPB_NVR_UNIFIED_LOG_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_unified_log_info__get_packed_size
                     (const TPbNvrUnifiedLogInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_unified_log_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_unified_log_info__pack
                     (const TPbNvrUnifiedLogInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_unified_log_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_unified_log_info__pack_to_buffer
                     (const TPbNvrUnifiedLogInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_unified_log_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrUnifiedLogInfo *
       tpb_nvr_unified_log_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrUnifiedLogInfo *)
     protobuf_c_message_unpack (&tpb_nvr_unified_log_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_unified_log_info__free_unpacked
                     (TPbNvrUnifiedLogInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_unified_log_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_unified_log_info__field_descriptors[6] =
{
  {
    "unified_log_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUnifiedLogInfo, has_unified_log_enable),
    offsetof(TPbNvrUnifiedLogInfo, unified_log_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "unified_log_url",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUnifiedLogInfo, unified_log_url),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "apm_log_enable",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrUnifiedLogInfo, has_apm_log_enable),
    offsetof(TPbNvrUnifiedLogInfo, apm_log_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "apm_log_url",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUnifiedLogInfo, apm_log_url),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "apm_service_name",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUnifiedLogInfo, apm_service_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "apm_name_space",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrUnifiedLogInfo, apm_name_space),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_unified_log_info__field_indices_by_name[] = {
  2,   /* field[2] = apm_log_enable */
  3,   /* field[3] = apm_log_url */
  5,   /* field[5] = apm_name_space */
  4,   /* field[4] = apm_service_name */
  0,   /* field[0] = unified_log_enable */
  1,   /* field[1] = unified_log_url */
};
static const ProtobufCIntRange tpb_nvr_unified_log_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_unified_log_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrUnifiedLogInfo",
  "TPbNvrUnifiedLogInfo",
  "TPbNvrUnifiedLogInfo",
  "",
  sizeof(TPbNvrUnifiedLogInfo),
  6,
  tpb_nvr_unified_log_info__field_descriptors,
  tpb_nvr_unified_log_info__field_indices_by_name,
  1,  tpb_nvr_unified_log_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_unified_log_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
