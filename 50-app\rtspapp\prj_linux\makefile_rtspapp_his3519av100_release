

TOP := ../

COMM_DIR := ./

SRC_DIR := $(TOP)/src


## Name and type of the target for this Makefile

SO_TARGET      := rtspapp

## Define debugging symbols
DEBUG = 1
LINUX_COMPILER= _ARM_HIS3519AV100_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -Wall -fno-omit-frame-pointer

OBJS := $(SRC_DIR)/rtspapp\
        $(SRC_DIR)/rtspapp_stack\
        $(SRC_DIR)/rtspapp_service\
        $(SRC_DIR)/rtspapp_config	\
		$(SRC_DIR)/rtspapp_tool \
		$(SRC_DIR)/rtspapp_context	\
		$(SRC_DIR)/rtspapp_json
		

## Libraries to include in shared object file
        

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../../../10-common/include/cbb/appclt        \
            ../../../10-common/include/cbb               \
			../../../10-common/include/cbb/cjson               \
            ../../../10-common/include/cbb/osp               \
            ../../../10-common/include/cbb/debuglog      \
			../../../10-common/include/cbb/protobuf      \
            ../../../10-common/include/hal               \
            ../../../10-common/include/hal/drvlib        \
            ../../../10-common/include/hal/netcbb        \
            ../../../10-common/include/hal/ispctrl       \
            ../../../10-common/include/hal/mediactrl     \
            ../../../10-common/include/system            \
            ../../../10-common/include/service           \
            ../../../10-common/include/app               \
            ../../../10-common/include/cbb/goahead/linux \
            ../include


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3519av100/applib/

LIB_PATH := ../../../10-common/lib/release/his3519av100
LIBS := go appbase nvrvtductrl debuglog rtsp   cjson 


include $(COMM_DIR)/makelib.mk

