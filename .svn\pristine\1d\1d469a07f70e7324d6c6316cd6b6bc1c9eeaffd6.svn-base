/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrsmtp.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrsmtp.pb-c.h"
void   tpb_nvr_smtp_recv_param__init
                     (TPbNvrSmtpRecvParam         *message)
{
  static TPbNvrSmtpRecvParam init_value = TPB_NVR_SMTP_RECV_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_smtp_recv_param__get_packed_size
                     (const TPbNvrSmtpRecvParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_recv_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_smtp_recv_param__pack
                     (const TPbNvrSmtpRecvParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_recv_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_smtp_recv_param__pack_to_buffer
                     (const TPbNvrSmtpRecvParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_recv_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSmtpRecvParam *
       tpb_nvr_smtp_recv_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSmtpRecvParam *)
     protobuf_c_message_unpack (&tpb_nvr_smtp_recv_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_smtp_recv_param__free_unpacked
                     (TPbNvrSmtpRecvParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_recv_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_smtp_cfg_param__init
                     (TPbNvrSmtpCfgParam         *message)
{
  static TPbNvrSmtpCfgParam init_value = TPB_NVR_SMTP_CFG_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_smtp_cfg_param__get_packed_size
                     (const TPbNvrSmtpCfgParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_cfg_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_smtp_cfg_param__pack
                     (const TPbNvrSmtpCfgParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_cfg_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_smtp_cfg_param__pack_to_buffer
                     (const TPbNvrSmtpCfgParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_cfg_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSmtpCfgParam *
       tpb_nvr_smtp_cfg_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSmtpCfgParam *)
     protobuf_c_message_unpack (&tpb_nvr_smtp_cfg_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_smtp_cfg_param__free_unpacked
                     (TPbNvrSmtpCfgParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_smtp_cfg_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_smtp_recv_param__field_descriptors[2] =
{
  {
    "receivers_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSmtpRecvParam, has_receivers_name),
    offsetof(TPbNvrSmtpRecvParam, receivers_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "receivers_addr",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSmtpRecvParam, receivers_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_smtp_recv_param__field_indices_by_name[] = {
  1,   /* field[1] = receivers_addr */
  0,   /* field[0] = receivers_name */
};
static const ProtobufCIntRange tpb_nvr_smtp_recv_param__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 3, 1 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_smtp_recv_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSmtpRecvParam",
  "TPbNvrSmtpRecvParam",
  "TPbNvrSmtpRecvParam",
  "",
  sizeof(TPbNvrSmtpRecvParam),
  2,
  tpb_nvr_smtp_recv_param__field_descriptors,
  tpb_nvr_smtp_recv_param__field_indices_by_name,
  2,  tpb_nvr_smtp_recv_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_smtp_recv_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_smtp_cfg_param__field_descriptors[10] =
{
  {
    "smtp_domain",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSmtpCfgParam, smtp_domain),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSmtpCfgParam, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_password",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSmtpCfgParam, user_password),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sender_addr",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSmtpCfgParam, sender_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sender_name",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSmtpCfgParam, has_sender_name),
    offsetof(TPbNvrSmtpCfgParam, sender_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "receivers_param",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSmtpCfgParam, n_receivers_param),
    offsetof(TPbNvrSmtpCfgParam, receivers_param),
    &tpb_nvr_smtp_recv_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "port",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSmtpCfgParam, has_port),
    offsetof(TPbNvrSmtpCfgParam, port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "receivers_num",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSmtpCfgParam, has_receivers_num),
    offsetof(TPbNvrSmtpCfgParam, receivers_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ssl_enable",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrSmtpCfgParam, has_ssl_enable),
    offsetof(TPbNvrSmtpCfgParam, ssl_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "attachment_enable",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrSmtpCfgParam, has_attachment_enable),
    offsetof(TPbNvrSmtpCfgParam, attachment_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_smtp_cfg_param__field_indices_by_name[] = {
  9,   /* field[9] = attachment_enable */
  6,   /* field[6] = port */
  7,   /* field[7] = receivers_num */
  5,   /* field[5] = receivers_param */
  3,   /* field[3] = sender_addr */
  4,   /* field[4] = sender_name */
  0,   /* field[0] = smtp_domain */
  8,   /* field[8] = ssl_enable */
  1,   /* field[1] = user_name */
  2,   /* field[2] = user_password */
};
static const ProtobufCIntRange tpb_nvr_smtp_cfg_param__number_ranges[3 + 1] =
{
  { 1, 0 },
  { 7, 5 },
  { 12, 7 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor tpb_nvr_smtp_cfg_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSmtpCfgParam",
  "TPbNvrSmtpCfgParam",
  "TPbNvrSmtpCfgParam",
  "",
  sizeof(TPbNvrSmtpCfgParam),
  10,
  tpb_nvr_smtp_cfg_param__field_descriptors,
  tpb_nvr_smtp_cfg_param__field_indices_by_name,
  3,  tpb_nvr_smtp_cfg_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_smtp_cfg_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
