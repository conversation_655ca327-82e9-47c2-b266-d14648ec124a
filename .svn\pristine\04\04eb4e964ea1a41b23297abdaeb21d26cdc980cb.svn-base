#ifndef _UPNP_API_H_
#define _UPNP_API_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "kdvtype.h"

#define UPNP_API_SYNC    1  /* Synchroization */
#define UPNP_API_ASYNC   2  /* Asynchronous */

#define UPNP_DEC_LEN        40
#define UPNP_PROTO_LEN      4

#define UPNP_PROTO_TCP "TCP"
#define UPNP_PROTO_UDP "UDP"

/* UPNP ERROR NUMBER */
#define UPNP_OK                     (0) /* successful */
#define UPNP_ERROR                  (1) /* other error */
#define UPNP_ERROR_INVALID_PARAM    (2) /* the param is invalid */
#define UPNP_ERROR_NO_MEMORY        (3) /* no memory */
#define UPNP_ERROR_SYS_FAILED       (4) /* system error */
#define UPNP_ERROR_NO_INIT          (5) /* uninitialized */
#define UPNP_ERROR_PORT_IS_USED     (6) /* the in or ex port has been used */
#define UPNP_ERROR_NO_THE_ENTRY     (7) /* the upnp entry is not existed */
#define UPNP_ERROR_SERVER_ABNORMAL  (8) /* the server's upnp is abnormal : closed,
										   abnormal, port is used by other device
										 */
#define UPNP_ERROR_NO_FOUND_IGD     (9) /* not found the IGD */

/* config mode */
#define UPNP_CONFIG_AUTO    (1)
#define UPNP_CONFIG_MANUAL  (2)

/* the map node state */
#define UPNP_STATE_INVALID  (0)
#define UPNP_STATE_VALID    (1)

/* upnp callback type */
#define UPNP_CALLBACK_RECOVER (0)   /* always callback */
#define UPNP_CALLBACK_ADD     (1)   /* only Asynchronousn */
#define UPNP_CALLBACK_REMOVE  (2)   /* only Asynchronousn */
#define UPNP_CALLBACK_OTHER   (0xFFFFFFFF)

/*  the member which is pointer can not be NULL  */
typedef struct tagUpnpParamConfig
{
	u32   dwUpnpConfigMode;  /* configure mode : UPNP_CONFIG_AUTO or UPNP_CONFIG_MANUAL */
	s8  *pchAppDesc;        /* application description */
	s8  *pchProto;          /* protocol : PROTO_TCP or PROTO_UDP */
	u32*  pdwExIPaddr;       /* extern ip address : always be outgoing param */
	u16*  pwExPort;          /* extern Port : auto mode : outgoing param, manual mode : incoming param */
	u32   dwInIPaddr;        /* inside IP address */
	u16   wInPort;           /* inside port */
	void* reserve[0];        /* reserved */
}TUpnpParamConfig,*PTUpnpParamConfig;

typedef struct tagUpnpMapPort
{
	struct tagUpnpMapPort *ptUpnpNext;
	u16 wExtPort;                  /* extern port */
	u16 wInPort;                   /* inside port */
	u32 dwInIPaddr;                /* inside IP address */
	u32 dwExIPaddr;                /* extern IP address */
	s8 achAppDesc[UPNP_DEC_LEN]; /* application description */
	s8 achProto[UPNP_PROTO_LEN]; /* tcp or udp */
	u32 dwState;                   /* UPNP_STATE_VALID or UPNP_STATE_INVALID */

	void* reserve[0];              /* reserved */

}TUMPortNode,*PTUMPortNode;

typedef struct tagUpnpDeviceInfo
{
	u32  dwLocalIPaddr;          /* local IP address */
	u32  dwIPaddrMask;           /* local IP address mask */
	s8 * pchFriendlyName;
	s8 * pchManufacturer;       /* eg : kedacom */
	s8 * pchManufacturerUrl;    /* eg : www.kedacom.com */
	s8 * pchDeviceTypeName;     /* eg : kedacom IPC-113-AN */    
	s8 * pchDeviceTypeNum;      /* eg : IPC-113-AN */
	s8 * pchDeviceDesc;         /* description of device */   
	s8 * pchDeviceWebUrl;       /* http://************:80 */
	s8 * pchDeviceInfoUrl;      /* eg : www.kedacom.com/IPC-113-AN or www.kedacom.com */
	s8 * pchDeviceUSN;          /* the unique number of device */ 
	s8 * pchSerialNumber;
}TUpnpDeviceInfo,*PTUpnpDeviceInfo;

/* the "UpnpCallBack" is used to notify the */
typedef void (*UpnpCallBack)(u32 dwCallBackType, PTUMPortNode ptHeadNode, u32 dwNum);

/********************************************************
 *  name : UpnpInit
 *
 *  function : initialize UPNP package
 *
 *  description:
 *      1.if the param dwLocalIPaddr is zero, UpnpInit can not
 *        recover the map entries from the gateway device.
 *      2.the "UPNP_CALLBACK_REMOVE" callback function will be
 *        executed, if one of the belows:
 *        a.there is a invalid node in the map list.
 *        b.there is a invalid node which to be valid from invalid
 *        c.the external IP address is changed.
 *        d.the recover is successful.
 *        e.the recover is failed( it be thought for being failed,
 *          if it recover failly three timers).
 *
 *  parameters:
 *      @param dwApiType	 - api type
 *      @param dwLocalIPaddr - local IP address
 *      @param dwGateWayIP   - the GateWay IP address
 *      @param pfCallBack    - the callback function
 *  return :
 *      successful : UPNP_OK
 *      failed     : UPNP_ERROR
 *                   UPNP_ERROR_INVALID_PARAM
 *                   UPNP_ERROR_SYS_FAILD
 *******************************************************/
u32 UpnpInit(u32 dwApiType, u32 dwLocalIPaddr, u32 dwGateWayIP, UpnpCallBack pfCallBack);

/********************************************************
 *  name : UpnpDestroy
 *
 *  function : destroy the Upnp
 *
 *  description :
 *      it is used to release the resource which the upnp
 *      applied
 *
 *  parameters:
 *      no
 *  return :
 *      @return  UPNP_OK : sucessful, UPNP_ERROR : failed
 *******************************************************/
u32 UpnpDestroy(void);

/********************************************************
*  name : UpnpBeDiscovered
*
*  function : device can be discovered by others .
*
*  description:
*
*  parameters:
*       @param ptDeviceInfo     - UPNP device infomation
*
*  return :
*       successful : UPNP_OK
*       failed     : UPNP_ERROR_INVALID_PARAM
*                    UPNP_ERROR_NO_MEMORY
*                    UPNP_ERROR_SYS_FAILED
*******************************************************/
u32 UpnpBeDiscovered(PTUpnpDeviceInfo ptDeviceInfo);

/********************************************************
 *  name : UpnpAddConfig
 *
 *  function : add the upnp config
 *
 *  description:
 *
 *  parameters:
 *      @param dwLocalIPaddr    - local IP address
 *      @param ptUpnpConfig     - UPNP configure
 *
 *  return :
 *      successful : UPNP_OK
 *      failed     : UPNP_ERROR
 *                   UPNP_ERROR_INVALID_PARAM
 *                   UPNP_ERROR_NO_MEMORY
 *                   UPNP_ERROR_NO_INIT
 *                   UPNP_ERROR_PORT_IS_USED
 *                   UPNP_ERROR_SERVER_ABNORMAL
 *                   UPNP_ERROR_SYS_FAILED
 *                   UPNP_ERROR_NO_FOUND_IGD
 *******************************************************/
u32 UpnpAddConfig(u32 dwLocalIPaddr, PTUpnpParamConfig ptUpnpConfig);

/********************************************************
 *  name : UpnpRemoveConfig
 *
 *  function : remove the upnp configure
 *
 *  description :
 *
 *  parameters :
 *      @param dwLocalIPaddr    - local IP address
 *      @param ptUpnpConfig     - UPNP configure
 *
 *  return :
 *      successful : UPNP_OK
 *      failed     : UPNP_ERROR
 *                   UPNP_ERROR_INVALID_PARAM
 *                   UPNP_ERROR_NO_MEMORY
 *                   UPNP_ERROR_NO_INIT
 *                   UPNP_ERROR_NO_THE_ENTRY
 *                   UPNP_ERROR_SERVER_ABNORMAL
 *                   UPNP_ERROR_SYS_FAILED
 *                   UPNP_ERROR_NO_FOUND_IGD
 *******************************************************/
u32 UpnpRemoveConfig(u32 dwLocalIPaddr, PTUpnpParamConfig ptUpnpConfig);

/********************************************************
 *  name : UpnpShowAllConfig
 *
 *  fucntion : show all the upnp configure
 *
 *  description:
 *
 *  parameters:
 *      @param dwLocalIPaddr    - local IP address
 *
 *  return :
 *      @return  UPNP_OK : successful, UPNP_ERROR : failed
 *******************************************************/
u32 UpnpShowAllConfig(u32 dwLocalIPaddr);

/********************************************************
 *  name : UpnpGetAllConfig
 *
 *  function : get all upnp confiure
 *
 *  description :
 *
 *  parameters :
 *      @param pptHeadNode    - the pointer of the list's header
 *      @param pdwNum         - the number of the list
 *
 *  return :
 *      successful : UPNP_OK
 *      failed     : UPNP_ERROR
 *                   UPNP_ERROR_INVALID_PARAM
 *                   UPNP_ERROR_NO_INIT
 *******************************************************/
u32 UpnpGetAllConfig(PTUMPortNode* pptHeadNode, u32* pdwNum);

/********************************************************
 *  name : UpnpQueryVersion
 *
 *  function : get the upnp version
 *
 *  description:
 *     recommend :
 *        "pchVer"'s len(dwBufLen) is more than or equal to 256 bytes
 *  parameters:
 *      @param pchVer    -the pointer of the buffer
 *      @param dwBufLen  -the length of the buffer
 *
 *  return :
 *      @return  UPNP_OK : successful, UPNP_ERROR : failed
 *******************************************************/
u32 UpnpQueryVersion(s8* pchVer, u32 dwBufLen);

/********************************************************
 *  name : UpnpDebug
 *
 *  function : open or close the debug
 *
 *  description:
 *      default to open
 *
 *  parameters:
 *      @param bOn    - true:open, false:close
 *
 *  return :
 *      @return  UPNP_OK : successful, UPNP_ERROR : failed
 *******************************************************/
u32 UpnpDebug(BOOL32 bOn);
#ifdef __cplusplus
}
#endif

#endif /* _UPNP_API_H_ */
