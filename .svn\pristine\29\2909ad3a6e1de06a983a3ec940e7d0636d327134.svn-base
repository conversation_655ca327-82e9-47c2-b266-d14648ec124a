#ifndef _CARPLATEDETECT_H_
#define _CARPLATEDETECT_H_

#include "VideoAlg.h"


//OPEN info for pvOpen in IMGVideoAlgOpen
// 车牌检测推荐区域（分为上中下和全图四种，其中上中下均约为原图的一半高度区域）
// 为手持设备新添加三种模式，分别为HANDHELD_FOCUS（图像中心：横向5/7，纵向5/7空间） 和 HANDHELD_W_5_7_H_3_5（图像中偏下局部区域:横向占5/7纵向占3/5空间）
// 根据测试需求增加：HANDHELD_FOCUS_PLUS在HANDHELD_FOCUS区域基础上，左右下三个方向各拓宽约50像素
// 需要说明的是上述除了FULL_PIC模式外其他均为1080P固定参数。FULL_PIC模式为全图，与输入分辨率无关
typedef enum tagCarPlateDetectRegion
{             
	DOWN=0,
	MIDDLE,                   
	UP,                   
	FULL_PIC,
	HANDHELD_FOCUS,
	HANDHELD_W_5_7_H_3_5,
	CUSTOM,
	HANDHELD_FOCUS_PLUS,
}EMCarPlateDetectRegion;

/*仅配合上述CUSTOM模式使用，在自定义区间内进行车牌检测*/
/*自定义区间为经过下述裁剪后剩下的矩形区域*/
typedef struct
{
	int Height_CutUp;				// 高度方向，上部分被裁剪去掉的高度像素
	int Height_CutDown;				// 高度方向，下部分被裁剪去掉的高度像素
	int Width_CutLeft;				// 宽度方向，左侧部分被裁剪去掉的宽度像素
	int Width_CutRight;				// 宽度方向，右侧部分被裁剪去掉的宽度像素
}TRECT_CUSTOM;

typedef struct tagCarPlateDetectOpen
{
	unsigned int u32Prob;               	//用户设置置信度参数
	EMCarPlateDetectRegion emPlateRegion;	//车牌检测推荐区域
	TRECT_CUSTOM rectCustom;				//当且仅当EMCarPlateDetectRegion使用CUSTOM宏时该结构体才会被解析。

	/*add for deeplearning method*/
    EMImageFormat emFormat;
    char s8GPUBinPath[200];
    char s8CarPlateDLDetectBinPath[3][200];
}TKEDACarPlateDetectOpen;


//INPUT for TImageInfo.pvImageInfo
/*暂留*/



//OUTPUT for TImageInfo.pvImageInfo
//车牌检测相关输出的信息

/*兼容传统检测算法结构体*/
typedef struct
{
	int up;					// 区域上部 Y 轴坐标
	int down;				// 区域下部 Y 轴坐标
	int left;				// 区域左部 X 轴坐标
	int right;				// 区域右部 X 轴坐标
}TRECT;
typedef struct  
{
	TRECT plateRect;
	float plateProb;
	int plateColor;
	int angle;
}TPLATEOUTPUT;
typedef struct tagVehicleProjetInfo            //20170804
{
	int Flag;                                  //1:需要调用车牌识别，0：不需调用车牌识别
	int plateNum;	                           //检测出的牌照数量     
	TPLATEOUTPUT plate[40];	                   //牌照属性 
} TVehicleProjetInfo;

/*深度学习检测算法结构体：Attention！其中TRECT_DL结构体中的up/down/left/right四个量在现有深度学习检测方法中不使用、暂保留*/
typedef struct
{
    int x;
    int y;
}TPlatePoint;
typedef struct
{
	int up;					// 区域上部 Y 轴坐标(暂不使用)
	int down;				// 区域下部 Y 轴坐标(暂不使用)
	int left;				// 区域左部 X 轴坐标(暂不使用)
	int right;				// 区域右部 X 轴坐标(暂不使用)
	TPlatePoint pt[4];      // 4个角点的坐标x、y值(顺序依次为左上、右上、左下、右下)
}TRECT_DL;
typedef struct  
{
	TRECT_DL plateRectDL;
	float plateProb;
	int plateColor;
	int angle;
    void* pvbuffertag;
}TPLATEOUTPUT_DL;
typedef struct tagVehicleProjetInfo_DL           //20171031
{
	int Flag;                                  //1:需要调用车牌识别，0：不需调用车牌识别
	int plateNum;	                           //检测出的牌照数量                  
	TPLATEOUTPUT_DL plate[40];	               //牌照属性 
} TVehicleProjetInfo_DL;

#endif
