/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrbluetooth.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrbluetooth.pb-c.h"
void   tpb_nvr_blue_tooth_dev_info__init
                     (TPbNvrBlueToothDevInfo         *message)
{
  static TPbNvrBlueToothDevInfo init_value = TPB_NVR_BLUE_TOOTH_DEV_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_blue_tooth_dev_info__get_packed_size
                     (const TPbNvrBlueToothDevInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_dev_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_blue_tooth_dev_info__pack
                     (const TPbNvrBlueToothDevInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_dev_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_blue_tooth_dev_info__pack_to_buffer
                     (const TPbNvrBlueToothDevInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_dev_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrBlueToothDevInfo *
       tpb_nvr_blue_tooth_dev_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrBlueToothDevInfo *)
     protobuf_c_message_unpack (&tpb_nvr_blue_tooth_dev_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_blue_tooth_dev_info__free_unpacked
                     (TPbNvrBlueToothDevInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_dev_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_blue_tooth_param__init
                     (TPbNvrBlueToothParam         *message)
{
  static TPbNvrBlueToothParam init_value = TPB_NVR_BLUE_TOOTH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_blue_tooth_param__get_packed_size
                     (const TPbNvrBlueToothParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_blue_tooth_param__pack
                     (const TPbNvrBlueToothParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_blue_tooth_param__pack_to_buffer
                     (const TPbNvrBlueToothParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrBlueToothParam *
       tpb_nvr_blue_tooth_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrBlueToothParam *)
     protobuf_c_message_unpack (&tpb_nvr_blue_tooth_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_blue_tooth_param__free_unpacked
                     (TPbNvrBlueToothParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_blue_tooth_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_blue_tooth_dev_info__field_descriptors[2] =
{
  {
    "achAddress",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrBlueToothDevInfo, achaddress),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "achUnicodeBtName",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrBlueToothDevInfo, has_achunicodebtname),
    offsetof(TPbNvrBlueToothDevInfo, achunicodebtname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_blue_tooth_dev_info__field_indices_by_name[] = {
  0,   /* field[0] = achAddress */
  1,   /* field[1] = achUnicodeBtName */
};
static const ProtobufCIntRange tpb_nvr_blue_tooth_dev_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_blue_tooth_dev_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrBlueToothDevInfo",
  "TPbNvrBlueToothDevInfo",
  "TPbNvrBlueToothDevInfo",
  "",
  sizeof(TPbNvrBlueToothDevInfo),
  2,
  tpb_nvr_blue_tooth_dev_info__field_descriptors,
  tpb_nvr_blue_tooth_dev_info__field_indices_by_name,
  1,  tpb_nvr_blue_tooth_dev_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_blue_tooth_dev_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_blue_tooth_param__field_descriptors[3] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrBlueToothParam, has_enable),
    offsetof(TPbNvrBlueToothParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "achUnicodeDevName",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrBlueToothParam, has_achunicodedevname),
    offsetof(TPbNvrBlueToothParam, achunicodedevname),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "PairedDevList",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrBlueToothParam, n_paireddevlist),
    offsetof(TPbNvrBlueToothParam, paireddevlist),
    &tpb_nvr_blue_tooth_dev_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_blue_tooth_param__field_indices_by_name[] = {
  0,   /* field[0] = Enable */
  2,   /* field[2] = PairedDevList */
  1,   /* field[1] = achUnicodeDevName */
};
static const ProtobufCIntRange tpb_nvr_blue_tooth_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_blue_tooth_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrBlueToothParam",
  "TPbNvrBlueToothParam",
  "TPbNvrBlueToothParam",
  "",
  sizeof(TPbNvrBlueToothParam),
  3,
  tpb_nvr_blue_tooth_param__field_descriptors,
  tpb_nvr_blue_tooth_param__field_indices_by_name,
  1,  tpb_nvr_blue_tooth_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_blue_tooth_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
