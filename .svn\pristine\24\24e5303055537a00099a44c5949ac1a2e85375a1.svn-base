path="../../10-common/version/compileinfo/nvrlib_cv2x.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =     nvr_usrmgr_linux for cv2x      =
echo ==============================================

echo "============compile libnvrusrmgr cv2x============">>../$path

make -e DEBUG=0 -f makefile_cv2x clean
make -e DEBUG=0 -f makefile_cv2x 2>>../$path

cp -L -r -f libnvrusrmgr.so ../../../10-common/lib/release/cv2x/

echo "============compile libnvrusrmgr_ipdt cv2x============">>../$path

make -e DEBUG=0 STATIC=0 -f makefile_cv2x_ipdt clean
make -e DEBUG=0 STATIC=0 -f makefile_cv2x_ipdt 2>>../$path

make -e DEBUG=0 STATIC=1 -f makefile_cv2x_ipdt clean
make -e DEBUG=0 STATIC=1 -f makefile_cv2x_ipdt 2>>../$path

cp -L -r -f libnvrusrmgr_ipdt.so ../../../10-common/lib/release/cv2x/
cp -L -r -f libnvrusrmgr_ipdt.a ../../../10-common/lib/release/cv2x/
cd ..
