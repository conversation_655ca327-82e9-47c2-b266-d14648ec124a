#ifndef _KNET_CMD_SERVICE_H
#define _KNET_CMD_SERVICE_H

#define PATHNAME_MAX 128

enum ServiceId {
    SVC_TELNETD = 1,
    SVC_FTPD,
    SVC_HTTPD,
    SVC_SSHD,
    /* The max service kinds */
    SVC_KIND_MAX = 8
};

enum SvcAction {
    SVC_START = 1,
    SVC_STOP,
    SVC_RESTART,
};

typedef struct {
    uint16_t mPort;

    union {
        struct {
            char mRoot[PATHNAME_MAX];
        } ftp;

        struct {
            char mRoot[PATHNAME_MAX];
        } http;
    };
} svc_params_t;

typedef struct {
    bool mIsRunning;
} svc_stat_t;

int32_t ServiceCtrl(ServiceId service, SvcAction act, svc_params_t* params = NULL);

int32_t ServiceGetStat(ServiceId service, svc_stat_t* stat);

#endif
