/********************************************************************
    created:	2018/07/24
    created:	5:6:2014   15:38
    file base:	onvifapp_analytics
    file ext:	c
    author:		wk

    purpose:
*********************************************************************/

#include "onvifapp_onvif.h"
#include "onvifapp_common.h"
#include "onvifapp_tool.h"
#include "onvif_codec_onvif.h"
#include "mxml.h"
#include "appbase_xml.h"
#include "appbase_enum_str_conv.h"
#include "nvrpui.h"


extern HAppMemPool g_hOnvifAppMemPool;
extern HAppTask g_hOnvifAppTask[ONVIFAPP_TASK_MAX];
extern TOnvifCapInfo g_ptOnvifAppCapList[];

//////////////////////// common //////////////////////////

#define ONVIFAPP_ANALYTICS_XML_HEAD  "<?xml version=\"1.0\" encoding=\"utf-8\"?><Envelope xmlns=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:"ONVIF_ANALYTICS_NAMESPACE_NAME"=\""ONVIF_ANALYTICS_NAMESPACE"\" xmlns:tt=\"http://www.onvif.org/ver10/schema\" xmlns:xs=\"http://www.w3.org/2001/XMLSchema\" xmlns:axt=\"http://www.onvif.org/ver20/analytics\"><Header/><Body>"
#define ONVIFAPP_ANALYTICS_XML_TAIL  "</Body></Envelope>"


//////////////////////// Analytics //////////////////////////

#define OnvifAppEventHandlerAnalyticsBegin() ONVIFAPP_EVENT_HANDLER_BEGIN(Analytics, ANALYTICS)
#define OnvifAppEventHandlerAnalyticsFillXML(szHead, ptRsp, szTail) ONVIFAPP_EVENT_HANDLER_FILLXML(Analytics, ANALYTICS, szHead, ptRsp, szTail)
#define OnvifAppEventHandlerAnalyticsCheckNewElement(ptNewElement) ONVIFAPP_EVENT_HANDLER_CHECK_NEW_ELEMENT(Analytics, ANALYTICS, ptNewElement)
#define OnvifAppEventHandlerAnalyticsSendFault(eFaultEnum) ONVIFAPP_EVENT_HANDLER_SEND_FAULT(Analytics, ANALYTICS, eFaultEnum)
#define OnvifAppEventHandlerAnalyticsCheckFault() ONVIFAPP_EVENT_HANDLER_CHECK_FAULT(Analytics, ANALYTICS)
#define OnvifAppEventHandlerAnalyticsDone() ONVIFAPP_EVENT_HANDLER_DONE(Analytics, ANALYTICS);
#define OnvifAppEventHandlerAnalyticsEnd() ONVIFAPP_EVENT_HANDLER_END(Analytics, ANALYTICS);


typedef enum
{
    ONVIF_ANALYTICS_FAULT_NOERROR = 0,
    ONVIF_ANALYTICS_FAULT_OUT_OF_MEMORY,
    ONVIF_ANALYTICS_FAULT_INVALID_ARGVAL,
    ONVIF_ANALYTICS_FAULT_ACTION_FAILED,
    ONVIF_ANALYTICS_FAULT_NO_RECORDING,
    ONVIF_ANALYTICS_FAULT_INVALID_SETUP,
    ONVIF_ANALYTICS_FAULT_STREAM_CONFLICT,
    ONVIF_ANALYTICS_FAULT_CFG_MODIFY,
    ONVIF_ANALYTICS_FAULT_NO_CONFIG,
} EOnvifAnalyticsFaults;

static TOnvifAppOnvifFault g_tOnvifAnalyticsFaults[] =
{
    { "", "", "", "", "", 200 },
    { "env:Receiver", "ter:OutofMemory", NULL, "Out of Memory", "The device does not have sufficient memory to complete the action.", 500},
    { "env:Sender", "ter:InvalidArgVal", NULL, "Argument Value Invalid", "The argument value is invalid.", 400},
    { "env:Receiver", "ter:Action", NULL, "Action Failed", "The requested SOAP action failed.", 500},
    { "env:Sender", "ter:InvalidArgVal", "ter:NoRecording", "Recording token does not exist", "The RecordingToken provided in the request does not exist.", 400 },
    { "env:Sender", "ter:InvalidArgVal", "ter:InvalidStreamSetup", "Invalid Stream setup", "Specification of StreamType or Transport part in StreamSetup is not supported.", 400 },
    { "env:Sender", "ter:OperationProhibited", "ter:StreamConflict", "Stream conflict", "Specification of StreamType or Transport part in StreamSetup causes conflict with other streams.", 400 },
    { "env:Sender", "ter:InvalidArgVal", "ter:ConfigModify", "Parameters cannot be set", "The configuration parameters cannot be set.", 400 },
    { "env:Sender", "ter:InvalidArgVal", "ter:NoConfig", "Configuration token does not exist", "The requested configuration indicated by the ConfigurationToken does not exist.", 400},
    { NULL, NULL, NULL, NULL, 0}
};

extern void __OnvifAppFillAnalyticsModuleToNode(mxml_node_t *ptTemp, u32 dwSrcId);
extern void __OnvifAppFillRuleToNode(mxml_node_t *ptTemp, u32 dwSrcId);


void _OnvifAppTaskEventHandlerDealAnalytics(_IN HAppTask hTask, _IN EAppTaskEventType eType, _IN void *pvTaskArg, _IN void *pvContext)
{
    _OnvifAppEventDealModuleHelper(hTask, eType, pvTaskArg, pvContext, 
                                g_tOnvifAnalyticsFaults, ARR_LEN(g_tOnvifAnalyticsFaults));
}

// ******* GetAnalyticsModules
s32 _OnvifAppEventHandlerGetAnalyticsModules(_IN mxml_node_t *ptReq, _IN mxml_mempool_func_set_t *ptMemFuncSet, _IN const s8 *szUsername, _IN const TAppIpAddr *ptSrcAddr, _OUT s8 *szOutXML, _IN u32 dwOutXMLLen, _INOUT void *pvContext)
{
    EAppResult eAppRet = APP_OK;
    NVRSTATUS eNvrRet = NVR_ERR__OK;

    mxml_node_t *ptRoot = NULL, *ptRsp = NULL;

    TNvrPuiMDParam tMDPar = {FALSE};
    const s8 *szToken = NULL;
    u32 dwChnIndex = 0;

    OnvifAppEventHandlerAnalyticsBegin();

    // ConfigurationToken 
    eAppRet = AppXmlGetSonNodeValue_Str(ptReq, "ConfigurationToken", &szToken);
    if (APP_OK != eAppRet)
    {
        OnvifAppErrLog("AppXmlGetSonNodeValue_Str() failed %d\n", eAppRet);
        OnvifAppEventHandlerAnalyticsSendFault(ONVIF_ANALYTICS_FAULT_OUT_OF_MEMORY);
    }
    OnvifAppDbgLog("token {%s}\n", szToken);

    if (1 != AppSscanf(szToken, ONVIFAPP_MEDIA_VACFG_TOKEN_PREFIX, &dwChnIndex))
    {
        OnvifAppErrLog("AppSscanf() failed\n");
        OnvifAppEventHandlerAnalyticsSendFault(ONVIF_ANALYTICS_FAULT_INVALID_ARGVAL);
    }
    OnvifAppDbgLog("dwChnIndex %lu\n", dwChnIndex);

    eNvrRet = NvrPuiGetDevParam(dwChnIndex, 0, NVR_PUI_MD_PARAM, &tMDPar);    
    if(NVR_ERR__OK != eNvrRet)
    {
        OnvifAppErrLog("NvrPuiGetDevParam() failed %d, chnid %lu\n", eNvrRet, dwChnIndex);
    }

    OnvifAppDbgLog("dwSensitivity %lu\n", tMDPar.dwSensitivity);

    ptRoot = mxmlNewEmptyNode(ptMemFuncSet);
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRoot);
    ptRsp = mxmlNewElement(ptRoot, ONVIF_ANALYTICS_NAMESPACE_NAME":GetAnalyticsModulesResponse");
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRsp);

    OnvifCodecAddSonNode_AnalyticsEngineCfg(ptRsp, tMDPar.dwSensitivity);
    
    OnvifAppEventHandlerAnalyticsFillXML(ONVIFAPP_ANALYTICS_XML_HEAD, ptRoot, ONVIFAPP_ANALYTICS_XML_TAIL);
    OnvifAppEventHandlerAnalyticsDone();
    mxmlRelease(ptRoot);
    OnvifAppEventHandlerAnalyticsEnd();
}

// ******* Get Rules
s32 _OnvifAppEventHandlerGetRules(_IN mxml_node_t *ptReq, _IN mxml_mempool_func_set_t *ptMemFuncSet, _IN const s8 *szUsername, _IN const TAppIpAddr *ptSrcAddr, _OUT s8 *szOutXML, _IN u32 dwOutXMLLen, _INOUT void *pvContext)
{
    mxml_node_t *ptRoot = NULL, *ptRsp = NULL;
    
    OnvifAppEventHandlerAnalyticsBegin();

    ptRoot = mxmlNewEmptyNode(ptMemFuncSet);
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRoot);
    ptRsp = mxmlNewElement(ptRoot, ONVIF_ANALYTICS_NAMESPACE_NAME":GetRulesResponse");
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRsp);

    OnvifCodecAddSonNode_RuleEngineCfg(ptRsp);    

    OnvifAppEventHandlerAnalyticsFillXML(ONVIFAPP_ANALYTICS_XML_HEAD, ptRoot, ONVIFAPP_ANALYTICS_XML_TAIL);
    OnvifAppEventHandlerAnalyticsDone();
    mxmlRelease(ptRoot);
    OnvifAppEventHandlerAnalyticsEnd();
}

// GetServiceCapabilities
s32 _OnvifAppEventHandlerAnalyticsGetServiceCapabilities(_IN mxml_node_t *ptReq, _IN mxml_mempool_func_set_t *ptMemFuncSet, _IN const s8 *szUsername, _IN const TAppIpAddr *ptSrcAddr, _OUT s8 *szOutXML, _IN u32 dwOutXMLLen, _INOUT void *pvContext)
{
    mxml_node_t *ptRoot = NULL, *ptRsp = NULL;
    
    OnvifAppEventHandlerAnalyticsBegin();

    ptRoot = mxmlNewEmptyNode(ptMemFuncSet);
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRoot);
    ptRsp = mxmlNewElement(ptRoot, ONVIF_ANALYTICS_NAMESPACE_NAME":GetServiceCapabilitiesResponse");
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRsp);

    if (g_ptOnvifAppCapList[ONVIF_CAP_Analytics].ptServiceCap)
    {
        mxmlRetain(g_ptOnvifAppCapList[ONVIF_CAP_Analytics].ptServiceCap);
        mxmlInsert(ptRsp, MXML_ADD_AFTER, MXML_ADD_TO_PARENT, g_ptOnvifAppCapList[ONVIF_CAP_Analytics].ptServiceCap);
    }

    OnvifAppEventHandlerAnalyticsFillXML(ONVIFAPP_ANALYTICS_XML_HEAD, ptRoot, ONVIFAPP_ANALYTICS_XML_TAIL);
    OnvifAppEventHandlerAnalyticsDone();
    mxmlRelease(ptRoot);
    OnvifAppEventHandlerAnalyticsEnd();
}

// ******* GetSupportedRules
s32 _OnvifAppEventHandlerAnalyticsGetSupportedRules(_IN mxml_node_t *ptReq, _IN mxml_mempool_func_set_t *ptMemFuncSet, _IN const s8 *szUsername, _IN const TAppIpAddr *ptSrcAddr, _OUT s8 *szOutXML, _IN u32 dwOutXMLLen, _INOUT void *pvContext)
{
    mxml_node_t *ptRoot = NULL, *ptRsp = NULL, *ptTmpNode = NULL, *ptTemp = NULL, *ptTemp1 = NULL, *ptTemp2 = NULL;
    const s8 *szTmpStr = NULL;
    char szCfgToken[ONVIF_TOKEN_MAXSIZE] = {0};
    
    OnvifAppEventHandlerAnalyticsBegin();

    // ConfigurationToken
    ptTmpNode = mxmlFindElement(ptReq, ptReq, "ConfigurationToken", NULL, NULL, MXML_DESCEND);
    if(ptTmpNode && (szTmpStr = mxmlGetText(ptTmpNode, NULL)))
    {
        AppStrncpy(szCfgToken, szTmpStr, sizeof(szCfgToken));
    }
    else
    {
        OnvifAppEventHandlerAnalyticsSendFault(ONVIF_ANALYTICS_FAULT_NO_CONFIG);
    }
    AppDbgLog("szCfgToken{%s}\n", szCfgToken);

    ptRoot = mxmlNewEmptyNode(ptMemFuncSet);
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRoot);
    ptRsp = mxmlNewElement(ptRoot, ONVIF_ANALYTICS_NAMESPACE_NAME":GetSupportedRulesResponse");
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptRsp);
    ptTmpNode = mxmlNewElement(ptRsp, ONVIF_ANALYTICS_NAMESPACE_NAME":SupportedRules");
    OnvifAppEventHandlerAnalyticsCheckNewElement(ptTmpNode);

    {// tt:CellMotionDetector
        ptTemp = mxmlNewElement(ptTmpNode, "tt:RuleDescription");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp);

        mxmlElementSetAttr(ptTemp, "Name", "tt:CellMotionDetector");
        mxmlElementSetAttr(ptTemp, "fixed", "true");
        mxmlElementSetAttr(ptTemp, "maxInstances", "1");

        ptTemp1 = mxmlNewElement(ptTemp, "tt:Parameters");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp1);

        ptTemp2 = mxmlNewElement(ptTemp1, "tt:SimpleItemDescription");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp2);
        mxmlElementSetAttr(ptTemp2, "Name", "MinCount");
        mxmlElementSetAttr(ptTemp2, "Type", "xs:integer");

        ptTemp2 = mxmlNewElement(ptTemp1, "tt:SimpleItemDescription");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp2);
        mxmlElementSetAttr(ptTemp2, "Name", "AlarmOnDelay");
        mxmlElementSetAttr(ptTemp2, "Type", "xs:integer");

        ptTemp2 = mxmlNewElement(ptTemp1, "tt:SimpleItemDescription");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp2);
        mxmlElementSetAttr(ptTemp2, "Name", "AlarmOffDelay");
        mxmlElementSetAttr(ptTemp2, "Type", "xs:integer");

        ptTemp2 = mxmlNewElement(ptTemp1, "tt:SimpleItemDescription");
        OnvifAppEventHandlerAnalyticsCheckNewElement(ptTemp2);
        mxmlElementSetAttr(ptTemp2, "Name", "ActiveCells");
        mxmlElementSetAttr(ptTemp2, "Type", "xs:base64Binary");   
    }

    OnvifAppEventHandlerAnalyticsFillXML(ONVIFAPP_ANALYTICS_XML_HEAD, ptRoot, ONVIFAPP_ANALYTICS_XML_TAIL);
    OnvifAppEventHandlerAnalyticsDone();
    mxmlRelease(ptRoot);
    OnvifAppEventHandlerAnalyticsEnd();
}

static _TOnvifAppMoudleFuncInfo g_ptOnvifAppAnalyticsFuncInfo[] =
{
    {
        0, 
        "GetAnalyticsModules",
        _OnvifAppEventHandlerGetAnalyticsModules,
        ONVIFAPP_POLICY_READ_MEDIA,
        ONVIF_ACCESS_CLASS_READ_MEDIA
    },

    {
        0, 
        "GetRules",
        _OnvifAppEventHandlerGetRules,
        ONVIFAPP_POLICY_READ_MEDIA,
        ONVIF_ACCESS_CLASS_READ_MEDIA
    },

    {
        0, 
        "GetServiceCapabilities",
        _OnvifAppEventHandlerAnalyticsGetServiceCapabilities,
        ONVIFAPP_POLICY_PRE_AUTH,
        ONVIF_ACCESS_CLASS_PRE_AUTH
    },

    {
        0, 
        "GetSupportedRules",
        _OnvifAppEventHandlerAnalyticsGetSupportedRules,
        ONVIFAPP_POLICY_READ_MEDIA,
        ONVIF_ACCESS_CLASS_READ_MEDIA
    },

    { 0, NULL, NULL, ONVIFAPP_POLICY_PRE_AUTH, ONVIF_ACCESS_CLASS_PRE_AUTH }
};

void _OnvifAppAnalyticsShowHandler(PFAppShowMsg pfShowMsg, void *pvContext)
{
    __OnvifAppShowHandler(g_ptOnvifAppAnalyticsFuncInfo, pfShowMsg, pvContext);
}

EAppResult _OnvifAppAnalyticsInit()
{
    //s32 nIndex = 0;

    _OnvifAppInitFuncInfo(g_ptOnvifAppAnalyticsFuncInfo);
    /*while (g_ptOnvifAppAnalyticsFuncInfo[nIndex].szFuncName)
    {
        AppDbgLog("%u %s\n", g_ptOnvifAppAnalyticsFuncInfo[nIndex].dwHash, g_ptOnvifAppAnalyticsFuncInfo[nIndex].szFuncName);
        nIndex++;
    }*/

    return APP_OK;
}

void _OnvifAppAnalyticsUnInit()
{
    return;
}

_TOnvifAppMoudleFuncInfo *_OnvifAppAnalyticsCheckAction(mxml_node_t *ptInXmlDoc)
{
    return _OnvifAppFindFuncInfo(g_ptOnvifAppAnalyticsFuncInfo, mxmlGetElement(ptInXmlDoc));
}

EAppResult _OnvifAppAnalyticsServe(HOnvifAppWebReq hReq, const s8 *szUsername, const TAppIpAddr *ptSrcAddr, mxml_node_t *ptInXmlDoc, mxml_node_t *ptRootXmlDoc, _TOnvifAppMoudleFuncInfo *ptFuncInfo)
{
    return _OnvifAppModuleServeHelper(hReq, szUsername, ptSrcAddr, ptInXmlDoc, ptRootXmlDoc, 
                                        ptFuncInfo, _OnvifAppTaskEventHandlerDealAnalytics);
}

