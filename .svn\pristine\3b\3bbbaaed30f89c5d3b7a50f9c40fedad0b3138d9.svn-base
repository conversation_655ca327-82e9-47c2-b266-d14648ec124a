

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

ARC_TARGET	      := nvrusrmgr_ipdt


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531D_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrusrmgr_ipdt\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531d
SLIBS += osp debuglog
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/crc_check\
		$(CURDIR)/../../../10-common/include/hal/netcbb\
		$(CURDIR)/../../../10-common/include/hal/netcbb/1.x\
		$(CURDIR)/../../../10-common/include/cbb/charconversion\

CFLAGS += -D_HIS3531D_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3531d
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


