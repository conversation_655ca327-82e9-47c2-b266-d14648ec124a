#ifndef __MEDIAKDVALG_H__
#define __MEDIAKDVALG_H__

#ifdef __cplusplus
extern "C" {
#endif

#define MEDIA_KDV_MAX_CAP_NUM (2)     /* 最大采集数量（一片CPU） */

typedef enum
{
    MEDIA_KDV_MOON_BASE,
    MEDIA_KDV_MOON90,
    MEDIA_KDV_MOON51,
}EMediaKdvCameraMode;

typedef enum
{
    MEDIA_KDV_ALG_FACE_DETECT,            /* 人脸检测 */
    MEDIA_KDV_ALG_EYE_CORRECTION,         /* 人眼矫正 */
}EMediaKdvAlgType;                    /* 算法类型，这两种算法处理方法不一样 */

/* ============================================================================================
dwCapId:采集通道号
dwAlgType：算法类型，参考mai_video.h&mai_define.h
pchMsg:    算法结果
nLen:      算法结果长度
============================================================================================== */
typedef int (*TMediaKdvAlgCb)(unsigned int dwCapId, unsigned int dwAlgType, char *pchMsg, int nLen);

typedef struct
{
    unsigned int           dwChipId;                           /* 芯片ID */
    unsigned int           dwCapNum;                           /* 需要开启几路采集（一片CPU）,目前只支持视频 */
    unsigned int           adwWidth[MEDIA_KDV_MAX_CAP_NUM];    /* 送入算法数据的宽,目前只支持视频 */
    unsigned int           adwHeight[MEDIA_KDV_MAX_CAP_NUM];   /* 送入算法数据的高,目前只支持视频 */
    unsigned int           adwFrameRate[MEDIA_KDV_MAX_CAP_NUM];/* 送入算法的帧率,默认为:10,目前只支持视频 */
    EMediaKdvCameraMode    eMode;                              /* 相机模式 */
    EMediaKdvAlgType       aeAlgType[MEDIA_KDV_MAX_CAP_NUM];   /* 算法类型 */
    TMediaKdvAlgCb         tAlgCb;                             /* 算法结果回调,目前只支持视频 */
}TMediaKdvParam;

typedef struct
{
    char *pchData;              /* 帧数据 */
    int   nLen;                 /* 帧长度 */
    int   nIndex;
    long  lPts;                /* 时间戳 */
}TMediaKdvAudioFrameInfo;      /* 音频帧,参考mai_audio */

typedef struct
{
    int nSampleRate;                 /* 采样率 */
    int nChannels;                   /* 通道 */
    int nBitPerSample;               /* 位宽 */
    int nFrameRate;                  /* 帧率 */
}TMediaKdvAudioFrameParam;           /* 音频feed参数，参考mai_audio */

/* ============================================================================================
函数名：MediaKdvVidCreateAlg
用途: 创建视频算法
ptParam: 算法参数
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidCreateAlg(TMediaKdvParam *ptParam);

/* ============================================================================================
函数名：MediaKdvDestoryAlg
用途: 销毁算法
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidDestoryAlg(void);

/* ============================================================================================
函数名：MediaKdvStartFeedAlg
用途: 开始送帧给算法,配合I模块开启算法使用
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidStartFeedAlg(unsigned int dwCapChn);

/* ============================================================================================
函数名：MediaKdvVidStopFeedAlg
用途: 停止送帧给算法,配合I模块关闭算法使用
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidStopFeedAlg(unsigned int dwCapChn);

/* ============================================================================================
函数名：MediaKdvVidSwitchAlgType
用途: 切换算法类型
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidSwitchAlgType(unsigned int dwCapChn, EMediaKdvAlgType eAlgType);

/* ============================================================================================
函数名：MediaKdvVidSetFeedFrameRate
用途: 设置送算法帧率
返回：0：success other:fail
============================================================================================== */
int MediaKdvVidSetFeedFrameRate(unsigned int dwCapChn, unsigned int dwFrameRate);

/* ============================================================================================
函数名：MediaKdvAudCreateAlg
用途: 创建音频算法
返回：0：success other:fail
============================================================================================== */
int MediaKdvAudCreateAlg(TMediaKdvParam *ptParam);

/* ============================================================================================
函数名：MediaKdvAudDestoryAlg
用途: 销毁音频算法
返回：0：success other:fail
============================================================================================== */
int MediaKdvAudDestoryAlg(void);

/* ============================================================================================
函数名：MediaKdvAudFeedFrame
用途: 透传音频数据以及参数给mai_audio
返回：0：success other:fail
============================================================================================== */
int MediaKdvAudFeedFrame(TMediaKdvAudioFrameInfo *ptFrame, TMediaKdvAudioFrameParam *ptParam);

/* ============================================================================================
函数名：MediaKdvAudFeedFrameJson
用途: 透传音频数据以及参数给mai_audio_json
返回：0：success other:fail
============================================================================================== */
int MediaKdvAudFeedFrameJson(char *pchData, int nLen);

#ifdef __cplusplus
}
#endif

#endif
