#ifndef _KNET_CMD_ROUTE_H
#define _KNET_CMD_ROUTE_H

#define KNET_ROUTE_MAX_NUM 32

typedef struct {
    uint32_t mIface;
    char     mDestNet[IP_ADDR_SIZE];
    char     mGateway[IP_ADDR_SIZE];
    uint8_t  mDestMask;
    uint8_t  mPriority;
} route_info_t;


int32_t  AddIpRoute(route_info_t* route);
int32_t  DelIpRoute(route_info_t* route);
int32_t  GetAllIpRoutes(route_info_t* routes, int32_t* routeCount);
int32_t  SetDefaultGateway(uint32_t ifaceId, char *gateway);
int32_t  DelDefaultGateway(void);

/*
 * The string is returned in a statically allocated buffer,
 * which subsequent calls will overwrite
 */
char* GetDefaultGateway(void);

#endif
