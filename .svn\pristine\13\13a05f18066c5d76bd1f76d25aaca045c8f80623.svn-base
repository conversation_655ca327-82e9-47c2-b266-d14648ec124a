
#include "kdvtype.h"
#include "netcbb_wrapper.h"

#include "netcbb_sntp.h"


s32 NetInit(void){return 0;}
s32 NetQueryVersion(char* pchVer, u32 dwBufLen){return 0;}
s32 NetLogSwitch(u32 dwSwitch){return 0;}
/****netcbb_ntpc****/
s32 NetcbbNtpcStart(TNetcbbAddr dwServerIp, u32 dwSyncTimeInterval, NetCbbNtpcSyncTimeCallBack  ptCallBack){return 0;}
s32 NetcbbNtpcStop(u16 wSinFamily){return 0;}
s32 NetcbbNtpcSerCheck(TNetcbbAddr tServerIp){return 0;}

/****netcbb_interface****/
s32 NetGetEthParam(u32 byEthId, TNetEthParam* ptNetEthParam){return 0;}
s32 NetGetEthMac(u32 byEthId, TNetEthMac* ptNetEthMac){return 0;}
s32 NetGetEthParamAll(u32 byEthId, TNetEthParamAll* ptNetEthParamAll){return 0;}
s32 NetGetEthParamSecIP(u32 byEthId, TNetEthParamAll* ptNetEthParamAll){return 0;}
s32 NetSetEthParam(u32 byEthId, u32 byIpOrMac, TNetEthParam* ptNetEthParam){return 0;}
s32 NetDelEthParamSecIP(u32 byEthId, TNetEthParam* ptNetEthParam){return 0;}
s32 NetAddEthParamIPAndMask(u32 byEthId, TNetEthParam* ptNetEthParam){return 0;}
s32 NetDelEthParamIPAndMask(u32 byEthId, TNetEthParam* ptNetEthParam){return 0;}
s32 NetDelEthParam(u32 byEthId){return 0;}
s32 NetSetDefGateway(u32 byEthId, u32 dwGateway){return 0;}
s32 NetGetDefGateway(u32 byEthId, u32* pdwGateway){return 0;}
s32 NetDelDefGateway(u32 byEthId){return 0;}
s32 NetSetMtu(u32 byEthId, u32 dwMtu){return 0;}
s32 NetGetMtu(u32 byEthId, u32* pdwMtu){return 0;}
s32 NetcbbIfaceSetMtu(char *pchIfaceName, u16 wSinFamily, u32 dwMtu){return 0;}
s32 NetcbbIfaceGetMtu(char *pchIfaceName, u32* pdwMtu){return 0;}

u32 NetGetNextHopIpAddr(u32 dwDstIpAddr, u32 dwDstMask){return 0;}
s32 NetcbbAddOneIpRoute(TNetcbbIpRouteParam *ptNetIpRouteParam){return 0;}
s32 NetcbbDelOneIpRoute(TNetcbbIpRouteParam *ptNetIpRouteParam){return 0;}
s32 NetcbbPingStart(TNetcbbAddr tDestIp, TNetcbbPingOpt* ptPingOpt, s32 nUserID, TNetcbbPingCallBack ptCallBackFunc){return 0;}
s32 NetcbbPingStop(s32 nUserID){return 0;}

s32 NetDelOneIpRoute(u32 byEthId, TNetIpRouteParam* ptNetIpRouteParam){return 0;}
s32 NetGetAllIpRoute(TNetAllIpRouteInfo* ptNetAllIpRouteInfo){return 0;}
s32 NetIpConflictCallBackReg(TIpConflictCallBack ptFunc){return 0;}
s32 NetcbbIpConflictCheckStart(u16 wSinFamily, TNetcbbConflictCallBack ptFunc){return 0;}

s32 NetIpConflictCallBackRegEx(TIpConflictCallBackEx ptFunc){return 0;}
BOOL32 NetIpOnceConflicted(void){return TRUE;}
s32 NetSetDscp(s32 nSockfd, u8 byDscp){return 0;}
s32 NetInterfaceShutdown(u32 byEthId){return 0;}
s32 NetInterfaceNoShutdown(u32 byEthId){return 0;}
s32 NetCheckTheSameNet(u32 dwIpAddr, u32 dwMask, u32 dwGateway){return 0;}
s32 NetcbbCheckIsSameNet(TNetcbbAddr tIpAddr, u32 dwPrefix, TNetcbbAddr tGateway){return 0;}


s32 NetGetEthNum(s32* pnEthNum){return 0;}
s32 NetGetEthState(s32 nEthNum, u32* pdwFlag){return 0;}
s32 NetModifyNetPwd(s8* pUser, s8* pPasswd){return 0;}
s32 NetAddNetUser(s8* pUser, s8* pPasswd){return 0;}
s32 NetAddNetCommonUser(s8* pUser, s8* pPasswd){return 0;}
s32 NetDelNetUser(s8* pUser){return 0;}
s32 NetModifyIpRouteMetric(TNetIpRouteParam* ptNetIpRouteParam){return 0;}
s32 NetStatNotifyCallbackReg(int nInterval, TDevStatNotifyCallBack pfNotifyCb){return 0;}
s32 NetStatStop(void){return 0;}

/****netcbb_interface v2****/
s32 NetcbbDelDefGateway(char *pchIfaceName, u16 wSinFamily){return 0;}
s32 NetcbbSetDefGateway(char *pchIfaceName, TNetcbbAddr tGateWay){return 0;}
s32 NetcbbGetDefGatewayEx(char *pchIfaceName, u16 wSinFamily, TNetcbbAddr *ptGateWay, u32 *pdwRa){return 0;}
s32 NetcbbIfaceSetIp(char *pchIfaceName, u16 wSinFamily, TNetcbbIfaceAllIp *ptNetIfaceIp){return 0;}
s32 NetcbbIfaceGetMac(char *pchIfaceName, TNetcbbIfaceMac *ptNetIfaceMac){return 0;}
s32 NetcbbIfaceGetIp(char *pchIfaceName, u16 wSinFamily, TNetcbbIfaceAllIp *ptNetIfaceIp){return 0;}
s32 NetcbbRAOnOff(char *pchIfaceName, s32 bSwitch){return 0;}
s32 NetcbbRouterAdvertiseStart(char *pchIfaceName, TNetcbbRouterAdvertiseCallBack pfRACallback){return 0;}
s32 NetcbbRouterAdvertiseStop(char *pchIfaceName){return 0;}
s32 NetcbbIfaceFlushIp(char *pchIfaceName, u16 wSinFamily){return 0;}
s32 NetcbbLogSwitch(ENetcbbDebugLevel eDebugLevel){return 0;}
s32 NetcbbQueryVersion(char* pchVer, u32 dwBufLen){return 0;}
s32 NetcbbIfaceAddIp(char *pchIfaceName, TNetcbbIfaceIp *ptNetIfaceIp){return 0;}
s32 NetcbbIfaceDelIp(char *pchIfaceName, TNetcbbIfaceIp *ptNetIfaceIp){return 0;}






/****netcbb_dhcpc****/
s32 NetDhcpcStart(u32 byEthId, NetDhcpcNotifyCallBack ptDhcpcCallBack, BOOL32 bSetDefautGate){return 0;}
s32 NetDhcpcRenew(u32 byEthId){return 0;}
s32 NetDhcpcGetLeaseInfo(u32 byEthId, TNetDhcpcLeaseInfo* ptNetDhcpcLeaseInfo){return 0;}
s32 NetDhcpcStop(u32 byEthId){return 0;}
s32 NetDhcpcGetIfstate(u32 byEthId, TNetDhcpcState* ptNetdhcpcIfState){return 0;}
s32 NetDNSSet(u32 byEthId, s32 DNSCmd, s8* pchDNSset[], s32 dwNum){return 0;}
s32 NetDNSSetEx(u32 byEthId, u32 adwDns[], s32 dwNum){return 0;}
s32 NetDNSDel(u32 dwEthId, u32 adwDNSset[], s32 dwNum){return 0;}
s32 NetDNSAdd(u32 dwEthId, u32 adwDNSset[], s32 dwNum){return 0;}

s32 NetcbbDhcpcGetIfstate(char *pchIfaceName, u16 wSinFamily, TNetcbbDhcpcState* ptNetdhcpcIfState){return 0;};
s32 NetcbbDhcpcStart(char *pchIfaceName, u16 wSinFamily, NetcbbDhcpcNotifyCallBack pfDhcpcCallBack){return 0;};
s32 NetcbbDhcpcStartEx(char *pchIfaceName, u16 wSinFamily, u16 wDnsDynmic, NetcbbDhcpcNotifyCallBack ptDhcpcCallBack){return 0;};

s32 NetcbbDhcpcStop(char *pchIfaceName, u16 wSinFamily){return 0;};
s32 NetcbbDNSSet(char *pchIfaceName, u16 wSinFamily, TNetcbbAddr atDnsIP[], u32 dwNum){return 0;};

s32 NetcbbIfaceDisableIpv6(char *pchIfaceName, s8 nDisable){return 0;};



/****netcbb_pppoe****/
s32 NetPppoeStart(TPppoeInitParam* ptPppoeInitParam, TPppoeCallBack ptNotifyCallBack){return 0;}
s32 NetPppoeStop(void){return 0;}
s32 NetcbbPppoeStop(char *pchIfaceName, u16 wSinFamily){return 0;}
s32 NetcbbPppoeStart(TNetcbbPppoeParam* ptPppoeParam, u16 wSinFamily, TNetcbbPppoeCallBack ptNotifyCallBack){return 0;}

//void NetPppoeStatesDump(TPppoeStates* ptPppoeStates){return ;}
s32 NetPppoeVer(TVersion* ptVersion){return 0;}

/****netcbb_sntp****/
s32 NetSNTPStart(u32 dwServerIp, u32 dwSyncTimeInterval, NetSNTPSyncTimeCallBack ptCallBack){return 0;}
s32 NetSNTPStop(void){return 0;}

/****netcbb_webs****/
s32 NetWebsEnable(s32 port){return 0;}
s32 NetWebsDisable(void){return 0;}

/****netcbb_dhcps****/
s32 NetDhcpsStart(TDhcpServerConfInfo* ptDhcpServerConfInfo, NetDhcpsNotifyCallBack ptDhcpsCallBack){return 0;}
s32 NetDhcpsStop(){return 0;}
s32 NetDhcpsStopOneEth(u32 dwEthId){return 0;}
s32 NetDhcpsGetState(u32 dwEthId, TNetDhcpsState* ptNetdhcpsState){return 0;}


/****netcbb_serv****/
#ifndef NETCBBS_OS_ANDROID
s32 NetServiceStart(s8 *pchServName, TServInfo* ptParaM){return 0;}
s32 NetServiceStop(s8* pchServName, u16 wPort){return 0;}
#else   
s32 NetServiceFtpStart(){return 0;}
s32 NetServiceFtpStop(){return 0;}
s32 NetServiceTelnetStart(){return 0;}
s32 NetServiceTelnetStop(){return 0;}
s32 NetServiceHttpStart(){return 0;}
s32 NetServiceHttpStop(){return 0;}
#endif 

/****netcbb_e1****/
u32 NetGetE1Bandwidth(char* ifname, int ifunit){return 0;}
s32 NetSetE1Clock(int e1, char* clock){return 0;}
s32 NetOpenE1SingleLinkChan(u32 dwChanID, TNetE1SingleLinkChanInfo* ptChanParam){return 0;}
s32 NetCloseE1SingleLinkChan(u32 dwChanID){return 0;}
s32 NetOpenE1MultiLinkChan(u32 dwChanID, TNetE1MultiLinkChanInfo* ptMpParam){return 0;}
s32 NetCloseE1MultiLinkChan(u32 dwChanID){return 0;}
u32 NetGetE1Remoteip(char* ifname , int ifunit){return 0;}

/****netcbb_ping****/
s32 NetPingStart(u32 dwDestIP, TPingOpt* ptPingOpt, s32 nUserID, TNetPingCallBack ptCallBackFunc){return 0;}
s32 NetPingStop(s32 nUserID){return 0;}

/****netcbb_traceroute****/
s32 NetTracerouteStart(TTraceroutePara* ptTraceroutePara, s32 nUserID, TNetTracerouteCallBack ptCallBackFunc){return 0;}
s32 NetTracerouteStop(s32 nUserID){return 0;}


/****netcbb_bond****/
s32 NetcbbBondStart(u32 dwBondMode, TNetcbbBondParam *ptBondParam){return 0;}
s32 NetcbbBondStop(){return 0;}


/****netcbb_Filter****/

s32 NetcbbPingFilterManage(ENetcbbIcmpCmd eCmd){return 0;}
s32 NetcbbEnableFilter(ENetcbbFilterList eFilterList){return 0;}



s32 NetcbbDisableFilter(ENetcbbFilterList eFilterList){return 0;}
s32 NetcbbIpFilterManage(ENetcbbFilterCmd eCmd, TNetcbbIpFilterParams *ptIpParams){return 0;}

s32 NetcbbMacFilterManage(ENetcbbFilterCmd eCmd, char * pchMac){return 0;}

s32 NetcbbSetNetworkPriority(TNetcbbNetworkPrioInfo *ptConfig){return 0;}

s32 NetcbbLogFileManage(TNetcbbDebugParams *ptParams){return 0;}



