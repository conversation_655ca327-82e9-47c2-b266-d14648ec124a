# 数字变倍线程化功能测试指南

## 测试环境准备

1. 确保设备支持数字变倍功能
2. 确保物理zoom位置可以达到2342以上
3. 准备调试工具查看日志输出

## 基本功能测试

### 测试1: 光学变倍测试
**目的**: 验证物理zoom位置小于2342时的光学变倍功能

**步骤**:
1. 确保当前zoom位置 < 2342
2. 发送ZOOMTELE命令
3. 观察是否执行光学变倍
4. 发送ZOOMWIDE命令
5. 观察是否执行光学变倍

**预期结果**: 
- 不启动数字变倍线程
- 执行原有的光学变倍逻辑

### 测试2: 数字变倍线程启动测试
**目的**: 验证物理zoom位置大于等于2342时数字变倍线程的启动

**步骤**:
1. 将zoom位置调整到2342以上
2. 发送ZOOMTELE命令
3. 检查日志输出确认线程启动
4. 观察NvrFixMcSetCrop的调用

**预期结果**:
- 日志显示"Digital zoom thread started successfully, type: TELE"
- 开始连续调用NvrFixMcSetCrop
- posDiff值逐渐增加

### 测试3: 数字变倍线程停止测试
**目的**: 验证ZOOMSTOP命令能正确停止数字变倍线程

**步骤**:
1. 在数字变倍线程运行时
2. 发送ZOOMSTOP命令
3. 检查日志输出确认线程停止

**预期结果**:
- 日志显示"Digital zoom thread stopped successfully"
- 停止调用NvrFixMcSetCrop

### 测试4: ZOOMWIDE数字变倍测试
**目的**: 验证ZOOMWIDE操作的数字变倍功能

**步骤**:
1. 确保当前有数字变倍效果(posDiff > 0)
2. 发送ZOOMWIDE命令
3. 观察posDiff值的变化

**预期结果**:
- posDiff值逐渐减少
- 当posDiff减少到0时，线程自动停止

## 边界条件测试

### 测试5: 边界值测试
**目的**: 测试zoom位置正好等于2342时的行为

**步骤**:
1. 将zoom位置调整到正好2342
2. 发送ZOOMTELE命令
3. 观察系统行为

**预期结果**: 启动数字变倍线程

### 测试6: 快速操作测试
**目的**: 测试快速连续的zoom操作

**步骤**:
1. 快速连续发送ZOOMTELE和ZOOMSTOP命令
2. 观察线程的启动和停止

**预期结果**: 
- 线程能正确启动和停止
- 不出现资源泄漏

### 测试7: 裁剪区域边界测试
**目的**: 测试裁剪区域的边界值

**步骤**:
1. 持续执行ZOOMTELE直到裁剪区域接近最小值
2. 观察线程是否自动停止

**预期结果**: 当裁剪区域过小时，线程自动停止

## 性能测试

### 测试8: 平滑性测试
**目的**: 验证50ms间隔的平滑效果

**步骤**:
1. 启动数字变倍
2. 观察画面变化的平滑性
3. 测量实际的调用间隔

**预期结果**: 画面变化平滑，无明显跳跃

### 测试9: 资源使用测试
**目的**: 验证线程的资源使用情况

**步骤**:
1. 监控系统资源使用
2. 启动和停止数字变倍线程多次
3. 检查是否有内存泄漏

**预期结果**: 无内存泄漏，资源正确释放

## 异常情况测试

### 测试10: 线程异常退出测试
**目的**: 测试线程异常情况的处理

**步骤**:
1. 在线程运行时模拟异常情况
2. 观察系统的恢复能力

**预期结果**: 系统能正确处理异常，不影响其他功能

## 调试信息检查

在测试过程中，注意检查以下调试信息：

1. **线程启动**: "Digital zoom thread started successfully, type: TELE/WIDE"
2. **数字变倍执行**: "Digital zoom: posDiff=X, crop region=(X,X,X,X)"
3. **线程停止**: "Digital zoom thread stopped successfully"
4. **zoom位置**: "isp do cmd get zoom position:X"

## 测试通过标准

1. 所有基本功能测试通过
2. 边界条件处理正确
3. 性能满足要求（平滑性良好）
4. 无资源泄漏
5. 异常情况处理正确
6. 调试信息输出正常

## 常见问题排查

1. **线程无法启动**: 检查zoom位置是否 >= 2342
2. **线程无法停止**: 检查ZOOMSTOP命令是否正确发送
3. **画面不平滑**: 检查50ms延时是否正确
4. **资源泄漏**: 检查线程退出逻辑是否正确
