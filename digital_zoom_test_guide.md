# 数字变倍常驻线程功能测试指南

## 测试环境准备

1. 确保设备支持数字变倍功能
2. 确保物理zoom位置可以达到2342以上
3. 准备调试工具查看日志输出
4. 确认系统初始化时常驻线程已创建

## 基本功能测试

### 测试1: 光学变倍测试
**目的**: 验证物理zoom位置小于2342时的光学变倍功能

**步骤**:
1. 确保当前zoom位置 < 2342
2. 发送ZOOMTELE命令
3. 观察是否执行光学变倍
4. 发送ZOOMWIDE命令
5. 观察是否执行光学变倍

**预期结果**: 
- 不启动数字变倍线程
- 执行原有的光学变倍逻辑

### 测试2: 数字变倍操作激活测试
**目的**: 验证物理zoom位置大于等于2342时数字变倍操作的激活

**步骤**:
1. 将zoom位置调整到2342以上
2. 发送ZOOMTELE命令
3. 检查日志输出确认操作激活
4. 观察NvrFixMcSetCrop的调用频率

**预期结果**:
- 日志显示"Digital zoom operation activated, type: TELE"
- 开始以200ms间隔调用NvrFixMcSetCrop（每秒5次）
- posDiff值每次增加50个单位

### 测试3: 数字变倍操作停止测试
**目的**: 验证ZOOMSTOP命令能正确停止数字变倍操作

**步骤**:
1. 在数字变倍操作激活时
2. 发送ZOOMSTOP命令
3. 检查日志输出确认操作停止

**预期结果**:
- 日志显示"Digital zoom operation stopped"
- 停止调用NvrFixMcSetCrop
- 线程进入等待状态

### 测试4: ZOOMWIDE数字变倍测试
**目的**: 验证ZOOMWIDE操作的数字变倍功能

**步骤**:
1. 确保当前有数字变倍效果(posDiff > 0)
2. 发送ZOOMWIDE命令
3. 观察posDiff值的变化

**预期结果**:
- posDiff值每次减少50个单位
- 当posDiff减少到0时，操作自动停止

## 边界条件测试

### 测试5: 边界值测试
**目的**: 测试zoom位置正好等于2342时的行为

**步骤**:
1. 将zoom位置调整到正好2342
2. 发送ZOOMTELE命令
3. 观察系统行为

**预期结果**: 激活数字变倍操作

### 测试6: 快速操作测试
**目的**: 测试快速连续的zoom操作

**步骤**:
1. 快速连续发送ZOOMTELE和ZOOMSTOP命令
2. 观察线程的启动和停止

**预期结果**:
- 操作能正确激活和停止
- 常驻线程稳定运行

### 测试7: 裁剪区域边界测试
**目的**: 测试裁剪区域的边界值

**步骤**:
1. 持续执行ZOOMTELE直到裁剪区域接近最小值
2. 观察线程是否自动停止

**预期结果**: 当裁剪区域过小时，操作自动停止

## 性能测试

### 测试8: 平滑性测试
**目的**: 验证200ms间隔（每秒5次）的平滑效果

**步骤**:
1. 激活数字变倍操作
2. 观察画面变化的平滑性
3. 测量实际的调用间隔和步长

**预期结果**:
- 画面变化平滑，无明显跳跃
- 每秒执行5次NvrFixMcSetCrop调用
- 每次偏移50个单位

### 测试9: 资源使用测试
**目的**: 验证常驻线程的资源使用情况

**步骤**:
1. 监控系统资源使用
2. 激活和停止数字变倍操作多次
3. 检查线程CPU使用率和内存使用

**预期结果**:
- 无操作时线程CPU使用率接近0
- 无内存泄漏
- 常驻线程稳定运行

## 异常情况测试

### 测试10: 线程异常退出测试
**目的**: 测试线程异常情况的处理

**步骤**:
1. 在线程运行时模拟异常情况
2. 观察系统的恢复能力

**预期结果**: 系统能正确处理异常，不影响其他功能

## 调试信息检查

在测试过程中，注意检查以下调试信息：

1. **线程初始化**: "Digital zoom thread initialized successfully"
2. **线程运行**: "Digital zoom thread started and running"
3. **操作激活**: "Digital zoom operation activated, type: TELE/WIDE"
4. **数字变倍执行**: "Digital zoom: posDiff=X, crop region=(X,X,X,X)"
5. **操作停止**: "Digital zoom operation stopped"
6. **zoom位置**: "isp do cmd get zoom position:X"

## 测试通过标准

1. 所有基本功能测试通过
2. 边界条件处理正确
3. 性能满足要求（平滑性良好）
4. 无资源泄漏
5. 异常情况处理正确
6. 调试信息输出正常

## 常见问题排查

1. **操作无法激活**: 检查zoom位置是否 >= 2342
2. **操作无法停止**: 检查ZOOMSTOP命令是否正确发送
3. **画面不平滑**: 检查200ms延时和50单位步长是否正确
4. **线程CPU占用高**: 检查等待逻辑是否正确
5. **常驻线程未创建**: 检查系统初始化时是否调用了初始化函数
